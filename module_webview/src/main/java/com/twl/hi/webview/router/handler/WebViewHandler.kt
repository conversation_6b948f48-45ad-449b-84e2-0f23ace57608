package com.twl.hi.webview.router.handler

import android.content.Context
import android.net.Uri
import android.os.Bundle
import com.sankuai.waimai.router.annotation.RouterRegex
import com.sankuai.waimai.router.common.StartUriHandler
import com.sankuai.waimai.router.core.UriCallback
import com.sankuai.waimai.router.core.UriRequest
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.api.request.TemporaryTokenRequest
import com.twl.hi.basic.api.response.TemporaryTokenResponse
import com.twl.hi.basic.model.WebViewBean
import com.twl.hi.export.webview.WebViewPageRouter
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.hi.router.base.ContextUriHandler
import com.twl.hi.router.base.RouterBaseConstant
import com.twl.hi.router.base.RouterBaseConstant.WEB_STYLE
import com.twl.hi.webview.router.interceptor.ZhiShuInterceptor
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.error.ErrorReason
import com.twl.http.error.TypicalRequestException
import com.twl.utils.URLUtils
import hi.kernel.Constants
import lib.twl.common.callback.OuterCallback
import lib.twl.common.ext.dp
import lib.twl.common.ext.getStatusBarsHeightCache
import lib.twl.common.ext.pxToDp
import lib.twl.common.util.AppUtil
import lib.twl.common.util.QMUIDisplayHelper

/**
 *@author: musa on 2023/7/11
 *@e-mail: <EMAIL>
 *@desc: 最终打开网页Handler，正常打开webView需要走的流程，可以用来兜底
 * 是否兜底要看配置，如果设置了tryStartUri(), 则以tryStartUri为准，否则以tryDefault为准
 * 这个Handler会被自动挂载，也会被手动挂载
 */
@RouterRegex(regex = RouterBaseConstant.URL_REGEX)
class WebViewHandler @JvmOverloads constructor(private val tryDefault: Boolean = true) : ContextUriHandler(){
    companion object {
        private const val TAG = "WebViewHandler"
    }

    init {
        addInterceptor(ZhiShuInterceptor())
    }

    override fun innerShouldHandle(request: UriRequest)  = request.getBooleanField(StartUriHandler.FIELD_TRY_START_URI, tryDefault)

    override fun handleInternal(request: UriRequest, callback: UriCallback) {
        TLog.info(TAG, "打开网页")
        openWebView(request, callback)
    }

    private fun openWebView(request: UriRequest, callback: UriCallback) {
        val context = contextRef.get() ?: return

        val bean = WebViewBean()
        bean.style = request.getIntField(WEB_STYLE, Constants.WEB_STYLE_HAS_NO_SHARE)

        val webUri = request.getField(Uri::class.java ,RouterBaseConstant.LINK_ORIGIN_URI) ?: request.uri
        bean.url = webUri.toString()
        TLog.info(TAG, "打开网页: ${bean.url}")
        if (webUri.isHierarchical.not()) { //非hierarchical的uri，直接打开
            startWebView(context, bean)
            callback.onComplete(UriCallback.CODE_SUCCESS)
            return
        }

        val noHead = (webUri.getQueryParameter("noHead")?.takeIf { noHead -> noHead.isNotEmpty() }
            ?: "0")

        noHead.let {
                if (noHead == "1") { //不要原生titleBar
                    bean.style = Constants.WEB_STYLE_HAS_NO_TITLE_AND_TRANSLUCENT
                }
            }

        webUri.getQueryParameter("browserOpen")?.let { browserOpen ->
            if (browserOpen == "1") {
                bean.isBrowserOpen = true
            }
        }
        //拼接参数传入titlebar的高度
        bean.url = URLUtils.addParam(
            bean.url,
            "barheight",
            getStatusBarsHeightCache().pxToDp.toString()
        )
        TLog.debug(TAG, "添加参数 url : ${bean.url}")

        val hiAppId = webUri.getQueryParameter(MessageConstants.HI_APP_ID) ?: ""
        if (hiAppId.isNotBlank() ||
            MessageConstants.KAN_ZHUN == webUri.getQueryParameter(MessageConstants.OC)
        ) {
            getTemporaryToken(
                hiAppId,
                object : OuterCallback<String?>{
                    override fun onSuccess(t: String?) {
                        bean.url = t?.let {URLUtils.addParam(bean.url, "ot", t)} ?: bean.url
                        startWebView(context, bean)
                        callback.onComplete(UriCallback.CODE_SUCCESS)
                    }

                    override fun onFailure(exception: Exception) {
                        TLog.error(TAG,"请求ot报错: ${exception.message}")
                        callback.onComplete(UriCallback.CODE_ERROR)
                    }
                }
            )
        } else {
            startWebView(context, bean)
            callback.onComplete(UriCallback.CODE_SUCCESS)
        }
    }

    private fun startWebView(context: Context, bean: WebViewBean) {
        val bundle = Bundle()
        bundle.putSerializable(Constants.DATA_WEB_BEAN, bean)
        AppUtil.startUri(context, WebViewPageRouter.WEB_VIEW_ACTIVITY, bundle)
    }

    private fun getTemporaryToken(hiAppId: String, listener: OuterCallback<String?>) =
        TemporaryTokenRequest(object : BaseApiRequestCallback<TemporaryTokenResponse>() {
            override fun onSuccess(data: ApiData<TemporaryTokenResponse>?) {
                data?.resp?.let {
                    listener.onSuccess(it.ot)
                } ?: let {
                    listener.onFailure(TypicalRequestException("参数异常"))
                }
            }

            override fun onComplete() {
                //nothing
            }

            override fun onFailed(reason: ErrorReason?) {
                listener.onFailure(TypicalRequestException(reason?.errReason?:""))
            }

        }).let {
            it.hiAppId = hiAppId
            HttpExecutor.execute(it)
        }
}