package com.twl.hi.webview.compat.webkit.jsbridge

import android.app.Activity
import com.alibaba.fastjson.JSONObject
import com.github.lzyzsd.jsbridge.CallBackFunction

/**
 * Author : Xuweixiang .
 * Date   : On 2023/11/28
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

class CloseWindowBridgeHandler(private val context: Activity) : AbsBridgeHandler() {

    override val bridgeName: String
        get() = "CloseWindowBridgeHandler"

    override fun handler(params: JSONObject?, function: CallBackFunction?) {
        context.finish()
        function?.onSuccess()
    }
}