package com.twl.hi.webview;

import android.media.MediaScannerConnection;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.ProgressBar;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.sankuai.waimai.router.annotation.RouterUri;
import com.twl.hi.basic.BottomListDialog;
import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.basic.model.SelectBottomBean;
import com.twl.hi.basic.model.SendPicBean;
import com.twl.hi.basic.model.WebVideoPlayBean;
import com.twl.hi.basic.util.OpenFileByOuterUtils;
import com.twl.hi.basic.viewmodel.FileDownLoadViewModel;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.export.select.bean.SelectBaseParams;
import com.twl.hi.export.select.bean.SelectConversationParams;
import com.twl.hi.export.select.router.SelectPageRouter;
import com.twl.hi.export.webview.WebViewPageRouter;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.webview.callback.VideoPlayCallback;
import com.twl.hi.webview.databinding.WebviewActivityVideoPlayBinding;
import com.twl.hi.webview.viewmodel.VideoPlayViewModel;
import com.twl.http.FileProgress;
import com.twl.http.OkHttpClientFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.permission.PermissionAvoidManager;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.HiPermissionUtil;
import lib.twl.common.util.MediaFileHelper;
import lib.twl.common.util.ToastUtils;

/**
 * <AUTHOR>
 * @date 2020/10/21.
 */
@RouterUri(path = WebViewPageRouter.VIDEO_PLAY_ACTIVITY)
public class VideoPlayActivity extends FoundationVMActivity<WebviewActivityVideoPlayBinding, VideoPlayViewModel>
        implements BottomListDialog.OnBottomItemClickListener, IWebVideoView, VideoPlayCallback {
    private BottomListDialog selectBottomView;
    private List<SelectBottomBean> mList = new ArrayList<>();
    private WebViewVideoCommon common;
    private WebVideoPlayBean mBean;
    private boolean hindIvOption;//在chatId>0时候，是否隐藏右上角操作
    private boolean hideICloud;

    @Override
    public int getContentLayoutId() {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        return R.layout.webview_activity_video_play;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBean = (WebVideoPlayBean) getIntent().getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE);
        hindIvOption = getIntent().getBooleanExtra(BundleConstants.BUNDLE_DATA_BOOLEAN, false);
        hideICloud = getIntent().getBooleanExtra(BundleConstants.BUNDLE_HIDE_I_CLOUD, false);
        common = new WebViewVideoCommon();
        if (!common.onCreate(this, this, getIntent())) {
            AppUtil.finishActivity(this);
        }
        mList.clear();
        //TODO 因有适配问题，目前在家办公，无法适配，暂时屏蔽
        mList.add(new SelectBottomBean(Constants.SAVE_TO_PHONE, getString(R.string.webview_save_to_phone), R.drawable.ic_icon_save_to_phone));
        mList.add(new SelectBottomBean(Constants.SEND_FILE, getResources().getString(R.string.file_send_to_message), R.drawable.ic_icon_send_to_chat));
        mList.add(new SelectBottomBean(Constants.OPEN_WITH_OTHER_APP, getResources().getString(R.string.file_open_with_other), R.drawable.ic_icon_open_file));
        if (getViewModel().hasChatId()) {
            mList.add(new SelectBottomBean(Constants.CHECK_CONTENT, getResources().getString(R.string.check_content), R.drawable.ic_icon_check_content));
        }
        getDataBinding().ivVideoOption.setVisibility(getViewModel().hasChatId() && !hindIvOption ? View.VISIBLE : View.GONE);
        getViewModel().getSaveFileLiveData().observe(this, new Observer<File>() {
            @Override
            public void onChanged(File file) {
                if (file != null && file.exists()) {
                    sendToPhone(file);
                }
            }
        });
    }

    @Override
    public void onIWebCreate(WebVideoPlayBean webViewBean, boolean downLoad) {
        getViewModel().init(webViewBean);
        if (downLoad) {
            getViewModel().setDownloadStatus(FileDownLoadViewModel.STATUS_DOWNLOAD);
        } else {
            if (!TextUtils.isEmpty(getViewModel().getThumbnailUrl())) {
                getDataBinding().imgThumbnail.setImageURI(getViewModel().getThumbnailUrl());
            }
            if (OkHttpClientFactory.get().getProgress(getViewModel().getUrl()) != null) {
                getViewModel().setDownloadStatus(FileDownLoadViewModel.STATUS_DOWNLOADING);
                getViewModel().setStartDownload(true);
            } else {
                getViewModel().downloadFile();
            }
            getViewModel().getStartDownload().observe(this, new Observer<Boolean>() {
                @Override
                public void onChanged(Boolean aBoolean) {
                    MutableLiveData<FileProgress> progress = OkHttpClientFactory.get().getProgress(getViewModel().getUrl());
                    if (progress != null) {
                        progress.observe(VideoPlayActivity.this, new Observer<FileProgress>() {
                            @Override
                            public void onChanged(FileProgress progress) {
                                int downLoadProgress = (int) (progress.getProgress() * 100);
                                getViewModel().setProgress(downLoadProgress);
                                if (downLoadProgress == 100) {//下载成功
                                    if (!common.playVideo(getViewModel().getLocalPath())) {
                                        AppUtil.finishActivity(VideoPlayActivity.this);
                                    }
                                }
                            }
                        });
                    }
                }
            });
        }
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(VideoPlayActivity.this);
    }

    @Override
    public void clickRight(View view) {
        if (selectBottomView == null) {
            selectBottomView = new BottomListDialog.Builder(VideoPlayActivity.this)
                    .setData(mList)
                    .setSpanCount(3)
                    .setItemLayout(R.layout.item_bottom_new)
                    .setCanceledOnTouchOutside(true)
                    .setOnBottomItemClickListener(this)
                    .create();
        }
        selectBottomView.show(true);
    }

    @Override
    public void clickOption(View view) {
        WebVideoPlayBean mBean = getViewModel().getWebVideoPlayBean();
        if (mBean != null && !mBean.isCantGoPicVideo()) {
            Bundle bundle = new Bundle();
            bundle.putString(BundleConstants.BUNDLE_DATA_STRING, mBean.getChatId());
            bundle.putInt(BundleConstants.BUNDLE_DATA_INT, mBean.getChatType());
            AppUtil.startUri(VideoPlayActivity.this, ChatPageRouter.CHAT_RECORD_SEARCH_FOR_VIDEO_OR_PIC_ACTIVITY, bundle);
        }
        AppUtil.finishActivity(VideoPlayActivity.this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        common.onDestroy();
    }

    @Override
    public void onBottomItemClick(View view, int pos, SelectBottomBean bottomBean) {
        switch (bottomBean.type) {
            case Constants.SEND_FILE:
                File messageFile = new File(mBean.getPath());
                if (messageFile.exists()) {
                    if (MediaFileHelper.isVideoFileTypeWithMov(TextUtils.isEmpty(mBean.getUrl()) ? mBean.getPath() : mBean.getUrl())) {
                        SelectConversationParams params = new SelectConversationParams()
                                .setSendMessageType(MessageConstants.MSG_VIDEO)
                                .<SelectConversationParams>setTitle(getResources().getString(R.string.select_contact))
                                .<SelectConversationParams>setOrgVisible(SelectBaseParams.VISIBLE)
                                .<SelectConversationParams>setSearchVisible(SelectBaseParams.VISIBLE)
                                .<SelectConversationParams>setOtherVisible(SelectBaseParams.VISIBLE);
                        SendPicBean sendPicBean = new SendPicBean();
                        sendPicBean.file = messageFile;
                        sendPicBean.videoInfo = mBean.getVideoInfo();
                        params.sendMessageContent.content = sendPicBean;
                        params.sendMessageContent.msgShowContent = "[视频]";
                        Bundle bundleSelect = new Bundle();
                        bundleSelect.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
                        AppUtil.startUri(VideoPlayActivity.this, SelectPageRouter.SELECT_CONVERSATION_ACTIVITY, bundleSelect, ActivityAnimType.UP_GLIDE);
                    }
                }
                break;
            case Constants.OPEN_WITH_OTHER_APP:
                File file = new File(mBean.getPath());
                if (file.exists()) {
                    OpenFileByOuterUtils.openFile(VideoPlayActivity.this, file);
                    AppUtil.finishActivity(this);
                }
                break;
            case Constants.CHECK_CONTENT:
                ExecutorFactory.execLocalTask(new Runnable() {
                    @Override
                    public void run() {
                        int chatType = mBean.getChatType();
                        Bundle bundle = new Bundle();
                        if (chatType == MessageConstants.MSG_GROUP_CHAT) {
                            bundle.putString(BundleConstants.BUNDLE_CHAT_ID, mBean.getChatId());
                            bundle.putLong("mid", mBean.getMid());
                            bundle.putLong("seq", mBean.getSeq());
                            AppUtil.startUri(VideoPlayActivity.this, ChatPageRouter.GROUP_CHAT_SEARCH_ACTIVITY, bundle);
                        } else {
                            bundle.putString(BundleConstants.BUNDLE_USER_ID, mBean.getChatId());
                            bundle.putLong("mid", mBean.getMid());
                            bundle.putLong("seq", mBean.getSeq());
                            AppUtil.startUri(VideoPlayActivity.this, ChatPageRouter.SINGLE_CHAT_SEARCH_ACTIVITY, bundle);
                        }
                    }
                });
                break;
            case Constants.SAVE_TO_PHONE:
                String name = mBean.getName();
                File saveFile = new File(mBean.getPath());
                if (saveFile.exists()) {
                    if (TextUtils.isEmpty(name)) {
                        name = saveFile.getName();
                    }
                    String finalName = name;
                    new PermissionAvoidManager(this).requestPermission(HiPermissionUtil.getAccessSharedFilePermissions(), new PermissionAvoidManager.OnCommonPermissionCallBack() {
                        @Override
                        public void onRequestPermissionsResult(boolean hasPermission, boolean shouldShowAllRequestPermissionRationale) {
                            if (hasPermission) {
                                getViewModel().saveFile(saveFile, finalName);
                            } else {
                                ToastUtils.failure(R.string.no_sd_permission);
                            }
                        }
                    });
                }
                break;
        }
    }

    public void sendToPhone(File file) {
        MediaScannerConnection.scanFile(BaseApplication.getApplication(), new String[]{file.toString()}, null, null);
        ToastUtils.success(R.string.saved_to_album);
    }

    @Override
    public ProgressBar getProgressBar() {
        return getDataBinding().progress;
    }

    @Override
    public FrameLayout getParentView() {
        return getDataBinding().parent;
    }

    @Override
    public WebView getWebView() {
        return getDataBinding().webview;
    }

    @Override
    public void onWebViewLoadingError() {
        File file = new File(mBean.getPath());
        if (file.exists()) {
            OpenFileByOuterUtils.openFile(VideoPlayActivity.this, file);
            AppUtil.finishActivity(this);
        }
    }

    @Override
    public void downloadOrOpenFile(int status) {
        switch (status) {
            case FileDownLoadViewModel.STATUS_NONE:
                getViewModel().downloadFile();
                break;
            case FileDownLoadViewModel.STATUS_DOWNLOADING:
                break;
            case FileDownLoadViewModel.STATUS_DOWNLOAD:
                break;
        }

    }
}
