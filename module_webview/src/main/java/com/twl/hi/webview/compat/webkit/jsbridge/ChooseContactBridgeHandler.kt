package com.twl.hi.webview.compat.webkit.jsbridge

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.github.lzyzsd.jsbridge.CallBackFunction
import com.twl.hi.export.select.bean.SelectContactsParams
import com.twl.hi.export.select.bean.SelectOption
import com.twl.hi.basic.util.exchangeBH2Open
import com.twl.hi.basic.util.exchangeOpen2BH
import com.twl.hi.export.select.router.SelectPageRouter
import com.twl.hi.foundation.model.Contact
import com.twl.hi.webview.R
import com.twl.hi.webview.compat.webkit.recordAppId
import hi.kernel.BundleConstants
import hi.kernel.RequestCodeConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import lib.twl.common.base.BaseApplication
import lib.twl.common.util.ActivityAnimType
import lib.twl.common.util.AppUtil

/**
 * Author : Xuweixiang .
 * Date   : On 2023/11/27
 * Email  : Contact <EMAIL>
 * Desc   : 选择联系人
 *
 */

class ChooseContactBridgeHandler(
    private val context: Context,
    private val appId: String? = ""
) : AbsBridgeHandler() {

    private var mFunction: CallBackFunction? = null

    override fun handler(params: JSONObject?, function: CallBackFunction?) {
        params ?: run {
            function?.onParamErr()
            return@handler
        }
        mFunction = function
        val isMulti = params.getBooleanValue("multi")
        val maxNum = params.getIntValue("maxNum")
        // 是否排除当前用户
        val ignore = params.getBooleanValue("ignore")
        val chosenIds = params.getJSONArray("chosenIds")
        val disableChosenIds = params.getJSONArray("disableChosenIds")
        val selectParams = SelectContactsParams()
            .setTitle<SelectContactsParams>(
                BaseApplication.getApplication().resources.getString(
                    R.string.select_contact
                )
            )
            .setMaxSelectCount<SelectContactsParams>(maxNum)
            .setMultiSelect<SelectContactsParams>(isMulti)
            .setOptionId<SelectContactsParams>(SelectOption.OPTION_H5_SELECT)
        val openIds = StringBuilder()
        if (!chosenIds.isNullOrEmpty()) {
            chosenIds.forEachIndexed { index, id ->
                openIds.append(id)
                if (index < chosenIds.size - 1) {
                    openIds.append(",")
                }
            }
        }
        if (!disableChosenIds.isNullOrEmpty()) {
            openIds.append(",")
            disableChosenIds.forEachIndexed { index, id ->
                openIds.append(id)
                if (index < disableChosenIds.size - 1) {
                    openIds.append(",")
                }
            }
        }
        scope.launch {
            val exchangedIds = withContext(Dispatchers.IO) {
                val pairs = mapOf(0 to mutableListOf<String>(), 1 to mutableListOf<String>())
                exchangeOpen2BH(
                    openIds.toString(),
                    null,
                    if (appId.isNullOrEmpty()) recordAppId else appId
                ).get().userInfos?.forEach {
                    if (chosenIds.contains(it.openId)) {
                        pairs[0]?.add(it.userId ?: "")
                    } else {
                        pairs[1]?.add(it.userId ?: "")
                    }
                }
                pairs
            }
            selectParams.setAddIds<SelectContactsParams>(
                exchangedIds.getOrDefault(
                    0,
                    listOf<String>()
                )
            )
            selectParams.setDisableIds<SelectContactsParams>(
                exchangedIds.getOrDefault(
                    1,
                    listOf<String>()
                )
            )
            val bundle = Bundle()
            bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, selectParams)
            justRequestedResult = true
            AppUtil.startUriForResult(
                context,
                SelectPageRouter.SELECT_CONTACT_ACTIVITY,
                RequestCodeConstants.REQUEST_CODE_SELECT_CONTACT,
                bundle,
                ActivityAnimType.UP_GLIDE
            )
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        if (requestCode == RequestCodeConstants.REQUEST_CODE_SELECT_CONTACT) {
            if (resultCode == Activity.RESULT_OK) {
                (data?.getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE_2) as? ArrayList<Contact>)?.let { contacts ->
                    scope.launch {
                        val contactArray = withContext(Dispatchers.IO) {
                            val chatIds = StringBuilder()
                            contacts.forEachIndexed { index, contact ->
                                chatIds.append(contact.userId)
                                if (index < contacts.size - 1) {
                                    chatIds.append(",")
                                }
                            }
                            val jsonArray = JSONArray()
                            exchangeBH2Open(
                                chatIds.toString(),
                                appId = if (appId.isNullOrEmpty()) recordAppId else appId
                            ).get().userInfos?.forEachIndexed { index, userInfo ->
                                val contact = contacts[index]
                                jsonArray.add(JSONObject().apply {
                                    put("openId", userInfo.openId)
                                    put("name", contact.showName)
                                    put("unionId", userInfo.unionId)
                                    put("avatarUrls", JSONArray().apply {
                                        if (TextUtils.isEmpty(contact.tinyAvatar).not()) {
                                            add(contact.tinyAvatar)
                                        }
                                        add(contact.avatar)
                                    })
                                })
                            }
                            jsonArray
                        }
                        mFunction?.onSuccess(JSONObject().apply {
                            put("data", contactArray)
                        })
                    }
                }
                    ?: (data?.getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE_3) as? Contact)?.let { contact ->
                        scope.launch {
                            val openId = withContext(Dispatchers.IO) {
                                exchangeBH2Open(
                                    contact.userId,
                                    appId = appId
                                ).get().userInfos?.firstOrNull()?.openId
                            }
                            mFunction?.onSuccess(JSONObject().apply {
                                put("data", JSONArray().apply {
                                    add(JSONObject().apply {
                                        put("openId", openId)
                                        put("name", contact.showName)
                                        put("avatarUrls", JSONArray().apply {
                                            if (TextUtils.isEmpty(contact.tinyAvatar)
                                                    .not()
                                            ) {
                                                add(contact.tinyAvatar)
                                            }
                                            add(contact.avatar)
                                        })
                                    })
                                })
                            })
                        }
                    }
            } else {
                mFunction?.onParamErr("取消选择")
            }
            justRequestedResult = false
            return true
        }
        return super.onActivityResult(requestCode, resultCode, data)
    }

}