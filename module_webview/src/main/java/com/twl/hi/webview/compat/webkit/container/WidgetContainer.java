package com.twl.hi.webview.compat.webkit.container;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Author : Xuweixiang .
 * Date   : On 2022/7/21
 * Email  : Contact <EMAIL>
 * Desc   : dialog、toast 等组件的容器
 */

public class WidgetContainer extends FrameLayout {

    private boolean addMask;

    public WidgetContainer(@NonNull Context context) {
        super(context);
    }

    public WidgetContainer(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public WidgetContainer(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public WidgetContainer(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public void setAddMask(boolean addMask) {
        this.addMask = addMask;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (addMask) {
            return true;
        }
        return super.onTouchEvent(event);
    }
}
