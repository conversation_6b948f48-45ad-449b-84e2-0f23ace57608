package com.twl.hi.webview;

import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;

import com.techwolf.lib.tlog.TLog;
import com.twl.utils.process.ProcessUtil;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.channels.FileLock;

import lib.twl.common.util.ExecutorFactory;

/**
 * <AUTHOR>
 * bugfix： https://jira.kanzhun-inc.com/browse/BFA-2266
 *
 * 参考文档：
 * https://chromium.googlesource.com/chromium/src/+/refs/heads/main/android_webview/java/src/org/chromium/android_webview/AwBrowserProcess.java
 * https://chromium.googlesource.com/chromium/src/+/refs/heads/main/android_webview/java/src/org/chromium/android_webview/AwDataDirLock.java
 *
 * 描述：
 * Android9.0行为变更不允许多进程操作webview
 *
 * 思路：
 * 既然会尝试获取 webview_data.lock 文件的锁，那么我在启动的时候尝试获取这个锁，如果发现能获取成功，则认为不会存在有问题，
 * 然后立马释放掉锁，如果尝试获取锁失败，说明被上一个进程锁住，这个时候删除当前文件重新创建AwDataDirLock会重新往文件写入
 * 进程信息。
 */
public class WebViewDataLock {

    private static final String TAG = "WebViewDataLock";

    /**
     * 处理 webview_data.lock 文件
     */
    public static void handleWebViewDataFile(Context context) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.P) {
            // android 9.0 新增多进程处理webview问题，9.0以下没有这个问题
            return;
        }

        ExecutorFactory.execWorkTask(new Runnable() {
            @Override
            public void run() {
                // 目前崩溃日志发现都是主进程导致
                if (ProcessUtil.isMainProcess(context)) {
                    tryLockWebViewDataFile(context);
                }
            }
        });
    }

    /**
     * 尝试给 /data/data/packageName/app_webview_packageName/webview_data.lock 文件加锁
     * 或者 /data/user/0/packageName/app_webview_packageName/webview_data.lock 文件加锁
     */
    @TargetApi(Build.VERSION_CODES.P)
    private static void tryLockWebViewDataFile(Context context) {
        String webViewDataFilePath = context.getDataDir() + "/app_webview_" + context.getPackageName()
                + "/webview_data.lock";
        File file = new File(webViewDataFilePath);
        if (file.exists()) {
            try {
                FileLock lock = new RandomAccessFile(file, "rw").getChannel().tryLock();
                if (lock != null) {
                    TLog.info(TAG, "WebViewDataLock->tryLockWebViewDataFile，try lock success, will release lock!");
                    // 如果加锁成功，则认为不会出现进程获取文件锁失败的情况，立马释放掉锁
                    lock.close();
                } else {
                    TLog.info(TAG, "WebViewDataLock->tryLockWebViewDataFile，try lock failed, will delete webview_data.lock and renew file");
                    /**
                     * 如果加锁不成功，则认为上一个进程还占用着，新进程获取锁会出现异常，这个时候删除文件
                     */
                    if (file.delete() && !file.exists()) {
                        file.createNewFile();
                    }
                }
            } catch (IOException e) {
                TLog.error(TAG, "WebViewDataLock->tryLockWebViewDataFile, " + e.getMessage());
            }
        }
    }
}
