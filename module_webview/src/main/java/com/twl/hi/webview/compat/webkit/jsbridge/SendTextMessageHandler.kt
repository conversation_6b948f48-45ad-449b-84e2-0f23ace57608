package com.twl.hi.webview.compat.webkit.jsbridge

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import com.alibaba.fastjson.JSONObject
import com.github.lzyzsd.jsbridge.CallBackFunction
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.api.response.ChatInfo
import com.twl.hi.basic.api.response.UserInfo
import com.twl.hi.export.select.bean.SelectBaseParams
import com.twl.hi.export.select.bean.SelectConversationParams
import com.twl.hi.export.select.bean.SelectOption
import com.twl.hi.foundation.SendMessageContent
import com.twl.hi.basic.util.exchangeOpen2BH
import com.twl.hi.export.select.router.SelectPageRouter
import com.twl.hi.foundation.MessageFactory
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.ConversationSelectBaseBean
import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.hi.webview.R
import com.twl.hi.webview.compat.webkit.jsbridge.factory.SendCardInfoJsonFactory
import com.twl.hi.webview.compat.webkit.recordAppId
import hi.kernel.BundleConstants
import hi.kernel.RequestCodeConstants
import lib.twl.common.ext.getResourceString
import lib.twl.common.util.ActivityAnimType
import lib.twl.common.util.AppUtil
import lib.twl.common.util.ExecutorFactory
import java.util.concurrent.ExecutionException
import com.twl.hi.foundation.utils.RichTextEscapes

/**
 *@author: musa on 2023/9/19
 *@e-mail: <EMAIL>
 *@desc: 发送文本消息
 */
private const val TAG = "SendTextMessageHandler"

class SendTextMessageHandler(val context: Context, private val appId: String? = "") : AbsBridgeHandler() {
    private var function: CallBackFunction? = null;
    override val bridgeName: String = "SendTextMessageHandler"

    /**
     * 发送文本消息
     * @param jsonString
     *        {
     *          shouldChooseChat?: boolean, // 是否在选择会话页面中发送，如果该字段设置为true则会跳转到选择会话页面并进行后续操作来完成消息的发送。
     *          chooseChatParams?: object, // 若 shouldChooseChat 输入为true，则可以定制选择会话的入参，参考选择会话chooseChat。
     *          openChatIDs?: string[], // 会话的open_chat_id列表。最大数量为10
     *          content?: string, // 消息内容
     *        }
     *
     * @param callback
     *  返回值: jsonString
     * {
     *     errMsg: '',
     *     status: 200,
     *     data: {
     *         "sendCardInfo": [ // 发送消息卡片的message信息（只有消息发送动作产生时才会返回该字段；例如没有传openChatIDs，用户取消发送，解析卡片内容失败等情况都不会返回该字段）。
     *             {
     *                 "id": "", // id
     *                 "chatType": 1 //1-群聊 0-单聊
     *                 "userType": 0  // 单聊用户类型，0：用户，1：机器人。chatType为0时，必须传该参数
     *                 "status": 0 // 发送消息的状态码，0表示发送成功
     *             }
     *         ],
     *     }
     * }
     */
    override fun handler(params: JSONObject?, function: CallBackFunction?) {
        params ?: run {
            function?.onParamErr()
            return
        }
        ExecutorFactory.execWorkTask{
            internalHandler(params, function)
        }
    }

    private fun internalHandler(params: JSONObject, function: CallBackFunction?) {
        this.function = function
        var shouldChooseChat = false
        var chooseChatParams: JSONObject? = null
        val openChatIDs: MutableList<String?> = ArrayList()
        val openIDs: MutableList<String?> = ArrayList()
        var content = ""

        // 解析jsonString
        shouldChooseChat = params.getBooleanValue("shouldChooseChat")
        chooseChatParams = params.getJSONObject("chooseChatParams")
        params.getJSONArray("openChatIDs")?.let { ids ->
            for (i in 0 until ids.size) {
                val openChatID = ids.getString(i)
                if (!TextUtils.isEmpty(openChatID)) {
                    openChatIDs.add(openChatID)
                }
            }
        }
        params.getJSONArray("openIDs")?.let { ids ->
            for (i in 0 until ids.size) {
                val openId = ids.getString(i)
                if (!TextUtils.isEmpty(openId)) {
                    openIDs.add(openId)
                }
            }
        }
        content = params.getString("content")
        //处理预选ids，将开放平台的id 置换成 Hi 的id
        val hiChatIds = mutableListOf<String>()
        val hiIds = mutableListOf<String>()
        if (openChatIDs.isNotEmpty() || openIDs.isNotEmpty()) {
            val exchangeOpen2BH = exchangeOpen2BH(
                TextUtils.join(",", openIDs),
                TextUtils.join(",", openChatIDs), ""
            )
            try {
                exchangeOpen2BH.get()?.chatInfos?.let { chatInfos ->
                    chatInfos.forEach { (_, bossHiChatId): ChatInfo ->
                        if (!bossHiChatId.isNullOrEmpty()) {
                            hiChatIds.add(bossHiChatId)
                        }
                    }
                }

                exchangeOpen2BH.get()?.userInfos?.let { userInfos ->
                    userInfos.forEach { userInfo: UserInfo ->
                        userInfo.userId.takeIf { !it.isNullOrEmpty() }
                            ?.let { hiIds.add(it) }
                    }
                }

            } catch (e: ExecutionException) {
                TLog.error(TAG, "sendTextMessage,e:" + e.message)
                return
            } catch (e: InterruptedException) {
                TLog.error(TAG, "sendTextMessage,e:" + e.message)
                return
            }
        }

        //还需要调用选择器选人
        if (shouldChooseChat) {
            val selectParams = chooseChatParams ?: JSONObject()
            val params: SelectConversationParams = SelectConversationParams()
                .setSendMessageType(SendMessageContent.TYPE_FORWARD_MESSAGE_TEXT)
                .setTitle<SelectConversationParams>(R.string.select_contact.getResourceString())
                .setMultiSelect<SelectBaseParams>(selectParams.getBooleanValue("multiSelect"))
                .setAddIds<SelectConversationParams>(hiIds + hiChatIds)
                .setCreateGroupVisible(SelectBaseParams.INVISIBLE)
                .setOptionId<SelectBaseParams>(SelectOption.OPTION_FORWARD_MESSAGE)
                .setOrgVisible<SelectConversationParams>(
                    SelectBaseParams.VISIBLE)
                .setSearchVisible<SelectConversationParams>(
                    SelectBaseParams.VISIBLE)
                .setOtherVisible<SelectConversationParams>(
                    SelectBaseParams.VISIBLE)
                .setMaxSelectCount<SelectConversationParams>(50)
                .setMessageBoardVisible(SelectBaseParams.INVISIBLE)
            params.sendMessageContent.msgShowContent = content
            params.sendMessageContent.content = content
            params.messageBoardVisible = SelectBaseParams.INVISIBLE
            val bundleSelect = Bundle()
            bundleSelect.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params)
            ExecutorFactory.execMainTask  {
                AppUtil.startUriForResult(
                    context,
                    SelectPageRouter.SELECT_CONVERSATION_ACTIVITY,
                    RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_H5_SEND_MESSAGE,
                    bundleSelect,
                    ActivityAnimType.UP_GLIDE
                )
            }
            justRequestedResult = true
        } else { //直接发送
            val finalContent = RichTextEscapes.escapeKeywords(content)
            hiChatIds.map { it: String? ->
                MessageFactory.createMarkdownTextMessage(null, finalContent, it, MessageConstants.MSG_GROUP_CHAT, null, emptyList(), 1)
            }.forEach {
                ServiceManager.getInstance().messageService.sendMessage(it)
            }

            hiIds.map { it: String? ->
                MessageFactory.createMarkdownTextMessage(null, finalContent, it, MessageConstants.MSG_SINGLE_CHAT, null, emptyList(), 1)
            }.forEach {
                ServiceManager.getInstance().messageService.sendMessage(it)
            }
            //将Hi的Id置换回openId 然后组装callback
            val factory = SendCardInfoJsonFactory()
            val sendTextWebCallback = factory.createSendTextWebCallback(hiChatIds, hiIds, if (appId.isNullOrEmpty()) recordAppId else appId)
            ExecutorFactory.execMainTask {
                function?.onSuccess(sendTextWebCallback)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != Activity.RESULT_OK || data == null) return false
        if (requestCode == RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_H5_SEND_MESSAGE) {
            val conversationBeans = data.getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE) as? java.util.ArrayList<ConversationSelectBaseBean>
                ?: return false

            val singleChats = conversationBeans
                .filter { it: ConversationSelectBaseBean -> it.type == MessageConstants.MSG_SINGLE_CHAT }
                .map { obj: ConversationSelectBaseBean -> obj.chatId }

            val groupChats = conversationBeans
                .filter { it: ConversationSelectBaseBean -> it.type == MessageConstants.MSG_GROUP_CHAT }
                .map { obj: ConversationSelectBaseBean -> obj.chatId }

            //将Hi的Id置换回openId 然后组装callback
            val factory = SendCardInfoJsonFactory()
            val sendTextWebCallback = factory.createSendTextWebCallback(singleChats, groupChats, if (appId.isNullOrEmpty()) recordAppId else appId)
            ExecutorFactory.execMainTask {
                function?.onSuccess(sendTextWebCallback)
            }
            justRequestedResult = false
            return true
        }

        return false
    }

}