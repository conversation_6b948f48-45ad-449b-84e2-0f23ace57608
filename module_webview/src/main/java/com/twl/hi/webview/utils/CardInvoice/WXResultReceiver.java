package com.twl.hi.webview.utils.CardInvoice;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import hi.kernel.Constants;
import lib.twl.common.util.ToastUtils;

public class WXResultReceiver extends BroadcastReceiver {
    private static final String TAG = "WXPayResultReceiver";

    public interface OnWXResultListener {
        void onWxInvoiceResult(int code, String codeMsg, String cardItemList);
    }

    private OnWXResultListener listener;

    public WXResultReceiver(OnWXResultListener listener) {
        this.listener = listener;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if (Constants.RECEIVER_WX_INVOICE_RESULT_ACTION.equals(intent.getAction())) {
            String cardItemList = intent.getStringExtra(Constants.APP_INVOICE_RESULT);
            String codeMsg = intent.getStringExtra(Constants.APP_INVOICE_CODE_MSG);
            int code = intent.getIntExtra(Constants.APP_INVOICE_CODE, -1);
            if (listener != null) {
                listener.onWxInvoiceResult(code, codeMsg, cardItemList);
            }
        }
    }
}
