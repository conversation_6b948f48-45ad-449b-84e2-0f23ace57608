package com.twl.hi.webview.compat.webkit.jsbridge

import android.content.Context
import com.alibaba.fastjson.JSONObject
import com.bzl.security.verify.interfaces.IdentityType
import com.github.lzyzsd.jsbridge.CallBackFunction
import com.twl.hi.foundation.facade.FaceVerifyExtraModel
import com.twl.hi.foundation.facade.VerifySdkHelper
import com.twl.hi.foundation.facade.VerifySdkHelper.OnIdentityCollectListener

/**
 *
 * h5调用人脸识别js bridge
 *
 * Created by tanshi<PERSON> on 2023/10/19
 */
class FaceVerifyHandler(private val context: Context) : AbsBridgeHandler() {

    override val bridgeName: String
        get() = "FaceVerifyHandler"

    override fun handler(params: JSONObject?, function: CallBackFunction?) {
        params?.let { dataJson ->
            val userId = dataJson.getString("userId")
            val orderId = dataJson.getString("orderId")
            val name = dataJson.getString("name")
            val idCard = dataJson.getString("idCard")
            val bizId = dataJson.getString("bizId")
            val scene = dataJson.getString("scene")
            VerifySdkHelper.init(userId, FaceVerifyExtraModel(bizId, scene))
            VerifySdkHelper.openIdentityDetectPage(context, orderId, name, idCard, IdentityType.SILENCE, object : OnIdentityCollectListener {
                override fun onCollectFinish(isVerifyOk: Boolean, resultStatus: Int) {
                    function?.onSuccess(JSONObject().apply {
                        put("verifyStatus", resultStatus)
                    })
                }
            })
        } ?: run {
            function?.onParamErr()
        }
    }
}