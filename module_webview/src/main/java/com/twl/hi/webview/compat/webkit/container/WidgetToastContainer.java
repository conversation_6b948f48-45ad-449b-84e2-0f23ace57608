package com.twl.hi.webview.compat.webkit.container;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Author : Xuweixiang .
 * Date   : On 2022/7/26
 * Email  : Contact <EMAIL>
 * Desc   :
 */

public class WidgetToastContainer extends WidgetContainer {

    public static final String TAG_TOAST_WIDGET_CONTAINER = "tag-toast-widget-container";

    public WidgetToastContainer(@NonNull Context context) {
        super(context);
    }

    public WidgetToastContainer(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public WidgetToastContainer(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public WidgetToastContainer(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }
}
