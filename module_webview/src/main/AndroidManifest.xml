<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
        package="com.twl.hi.webview">

    <uses-permission android:name="android.permission.VIBRATE" />
    <application>
        <activity
                android:name=".vr.VrCaptureActivity"
                android:exported="false"
                android:hardwareAccelerated="true"/>
        <activity
                android:name=".VideoPlayActivity"
                android:configChanges="orientation|screenSize|keyboardHidden"
                android:hardwareAccelerated="true"
                android:screenOrientation="portrait" />
        <activity
                android:name=".WebViewActivity"
                android:configChanges="orientation|screenSize|keyboardHidden"
                android:hardwareAccelerated="true"
                android:screenOrientation="portrait"
                android:launchMode="standard"
                android:windowSoftInputMode="stateHidden|adjustResize" />
    </application>

</manifest>