<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="1"
        android:paddingLeft="24dp"
        android:paddingRight="24dp"
        android:singleLine="true"
        android:textColor="@color/image_color_blue"
        android:textSize="16sp"
        tools:text="item3" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#999" />

</LinearLayout>