<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/webview_title_bar" />

        <FrameLayout
            android:id="@+id/parent"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:id="@+id/webview_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextureView
                    android:id="@+id/texture_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone" />

                <ProgressBar
                    android:id="@+id/progress"
                    style="@android:style/Widget.ProgressBar.Horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:progressDrawable="@drawable/webview_bg_progressbar_horizontal" />
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/error_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/title_height"
                android:background="#f5f5f6"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="gone">

                <lib.twl.common.views.MTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="20dip"
                    android:gravity="center"
                    android:text="@string/webview_bad_net"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <lib.twl.common.views.MButton
                    android:id="@+id/btn_refresh"
                    style="@style/button_with_corner"
                    android:layout_width="150dip"
                    android:layout_marginTop="20dip"
                    android:text="@string/webview_click_refresh" />
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</layout>

