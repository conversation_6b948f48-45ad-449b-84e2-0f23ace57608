apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

apply from: rootProject.file('bzl-push.gradle')
apply from: rootProject.file('ksp_config.gradle')

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion build_versions.build_tools

    defaultConfig {
        minSdkVersion build_versions.min_sdk
        targetSdkVersion build_versions.target_sdk
        resourcePrefix 'webview_'
    }

    dataBinding {
        enabled = true
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    kotlinOptions {
        jvmTarget = '1.8'
        freeCompilerArgs += "-Xjvm-default=all"
    }
}

dependenciesForImplementation(project, ":module_foundation_business", deps.commonLibs.module_foundation_business)
dependenciesForImplementation(project, ":module_select", deps.commonLibs.module_select)
dependenciesForImplementation(project, ":module_viewer", deps.commonLibs.module_viewer)

dependenciesForImplementation(project, ":export_module_office_preview", deps.commonLibs.export_module_office_preview)
dependenciesForImplementation(project, ":export_module_organization", deps.commonLibs.export_module_organization)
dependenciesForImplementation(project, ":export_module_chat", deps.commonLibs.export_module_chat)
dependenciesForImplementation(project, ":export_module_select", deps.commonLibs.export_module_select)
dependenciesForImplementation(project, ":export_module_work", deps.commonLibs.export_module_work)
dependenciesForImplementation(project, ":export_module_webview", deps.commonLibs.export_module_webview)
dependenciesForImplementation(project, ":export_module_main", deps.commonLibs.export_module_main)
dependenciesForImplementation(project, ":export_module_audio", deps.commonLibs.export_module_audio)
dependenciesForImplementation(project, ":module_video", deps.commonLibs.module_video)
dependencies {
    implementation project(':lib_emotion')
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    implementation 'com.twl.sdk.file:zp-sdk-support:conf-3.17.1'
    //铁壁信息采集需要的 vr sdk
    implementation 'com.twl.sdk.file:zp-uikit-panocam:5.2.11'

    implementation deps.jsbridge
    compileOnly deps.fastjson
    coreLibraryDesugaring deps.desugar_jdk_libs
}