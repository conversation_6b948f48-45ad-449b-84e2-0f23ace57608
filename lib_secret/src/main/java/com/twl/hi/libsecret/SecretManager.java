package com.twl.hi.libsecret;

import android.content.Context;

public class SecretManager {

    // Used to load the 'libsecret' library on application startup.
    static {
        System.loadLibrary("secret");
    }

    public static native void initSecret(Context context);

    public static native String getAesPassword();

    public static native String getPsKeyEncrypt(boolean qa);

    public static native String getDecryptPassword(Context context);

    public static native void checkEnv(Context context);

    public static native String getTXAesPassword(Context context);

}