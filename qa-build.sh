#!/bin/bash

source android-build.sh

apiServerVersionName=$(getPropertiesVersionName "$flow_env_app_server_version_name")
branch=$(git symbolic-ref --short HEAD)
autoTestBackup="$flow_env_project_type/$flow_env_project_name/qaBuild/$apiServerVersionName/$branch"
projectDirPath=$(pwd)

startBuild
if [ $? -eq 0 ]; then
    echo "qa编译成功!"
else
    echo "qa编译失败!"
    notifyBuildFailed "qa编译失败" 0
    exit 1
fi

source bucketFile.sh

initEnv=$(initS3Env)
if [ $? -eq 0 ]; then
    echo "qa初始化mc成功!"
else
    echo "qa初始化mc失败! $initEnv"
    notifyBuildFailed "$initEnv"
    exit 1
fi

uploadAutoTestFile "$flow_app_apk_dir/$flow_app_apk_name64" "$autoTestBackup" "$flow_app_apk_name64"
uploadAutoTestFile "$flow_app_apk_dir/$flow_app_apk_name32" "$autoTestBackup" "$flow_app_apk_name32"

uploadMappingToBuglyResult=$(uploadMappingToBugly)
if [ $? -eq 0 ]; then
    echo "mapping上传Bugly成功!"
else
    echo "mapping上传Bugly失败! $uploadMappingToBuglyResult"
fi

# mapping 移动
mv "$flow_app_mapping_path" "${projectDirPath}/$flow_app_apk_dir/"
uploadMappingToApm "${projectDirPath}/$flow_app_apk_dir/"

## R.txt 移动
#mv "$flow_r_file_path" "$projectDirPath/$flow_app_apk_dir/"
#uploadRFileToApm "${projectDirPath}/$flow_app_apk_dir/"

notifyBuildSuccess 0 "${ossUrl}${BUILDID}/$flow_app_apk_name32" "${ossUrl}${BUILDID}/$flow_app_apk_name64"