package hi.kernel.api;

public interface AccountLifecycle {

    int LIFECYCLE_INIT = 1;
    int LIFECYCLE_COM_INIT = LIFECYCLE_INIT + 1;
    int LIFECYCLE_COM_RELEASE = LIFECYCLE_COM_INIT + 1;
    int LIFECYCLE_RELEASE = LIFECYCLE_COM_RELEASE + 1;

    void onAccountInitialized();

    void onCompanyInitialized();

    /**
     * 账号出现安全风险的时候会调用这个
     */
    default void onSafetyVerify() {
    }

    /**
     * 账号移除风险的时候会调用这个
     */
    default void onRemoveSafetyVerify() {
    }

    void onCompanyRelease();

    void onAccountRelease();
}
