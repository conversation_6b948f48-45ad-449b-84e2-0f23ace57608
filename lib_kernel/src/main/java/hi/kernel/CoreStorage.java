package hi.kernel;


import com.twl.utils.StringUtils;

import java.io.File;

import hi.kernel.api.AccountLifecycle;
import hi.kernel.utils.MD5Utils;
import lib.twl.common.util.ProcessHelper;

public class CoreStorage implements AccountLifecycle {

    @Override
    public void onAccountInitialized() {
        String uid = HiKernel.getHikernel().getAccount().getUserId();
        if (StringUtils.isNotEmpty(uid)) {
            initAccount(uid);
        }
    }

    @Override
    public void onCompanyInitialized() {
        Account account = HiKernel.getHikernel().getAccount();
        String companyId = account.getCompanyId();
        if (StringUtils.isNotEmpty(companyId)) {
            initCompany(HiKernel.getHikernel().getAccount().getUserId(), companyId);
//            long versionCode = ProcessHelper.getUserCompanyPreferences().getLong(Constants.MESSAGE_VERSION_CODE, 0L);
//            if (versionCode < 2290000) {//TODO 等所有人都升级到226及以上版本后，可以添加判断条件versionCode > 0
//                FileUtils.delete(ProcessHelper.getCompanyRoot());
//                CoreStorage.removeUserRoot(account.getUserId(), account.getCompanyId());
//            }
        }
    }

    private static void initAccount(String uid) {
        File file = ensureUserPath(uid);
        ProcessHelper.setUserPath(file);
    }

    private static void initCompany(String uid, String companyId) {
        File path = ensureCompanyPath(uid, companyId);
        ProcessHelper.setCompanyPath(path);
    }

    @Override
    public void onCompanyRelease() {
        ProcessHelper.setCompanyPath(null);
    }

    @Override
    public void onAccountRelease() {
        ProcessHelper.setUserPath(null);
    }

    private static File ensureUserPath(String uid) {
        File path = new File(ProcessHelper.getContext().getFilesDir(), MD5Utils.getMD5(uid));
        if (!path.exists()) {
            path.mkdirs();
        }
        return path;
    }

    private static File ensureCompanyPath(String uid, String companyId) {
        File userPath = ensureUserPath(uid);
        File path = new File(userPath, MD5Utils.getMD5(companyId));
        if (!path.exists()) {
            path.mkdirs();
        }
        return path;
    }

    public static void removeUserRoot(String uid, String companyId) {
        try {
            if (StringUtils.isNotEmpty(uid)) {
                initAccount(uid);
                if (StringUtils.isNotEmpty(companyId)) {
                    initCompany(uid, companyId);
                }
            }
            ProcessHelper.clearUser();
            File userPath = ensureUserPath(uid);
            userPath.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
