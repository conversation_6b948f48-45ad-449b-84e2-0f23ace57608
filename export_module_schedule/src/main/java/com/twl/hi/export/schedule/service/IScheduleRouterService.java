package com.twl.hi.export.schedule.service;

import android.app.Activity;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.PoiItem;
import com.twl.hi.foundation.model.Schedule;

import org.json.JSONException;

import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;
import lib.twl.common.views.calendar.Calendar;

public interface IScheduleRouterService {

    /**
     * 跳转到任务详情页
     *
     * @param scheduleId
     * @param currDate
     * @param from
     * @param msgId
     * @param isNeedRequest
     */
    void jumpScheduleDetailPage(FragmentActivity activity, String scheduleId, String calendarId, String currDate, int from, String msgId, boolean isNeedRequest);

    /**
     * 群日程列表Fragment
     * @param groupId 群组id
     * @return
     */
    Fragment getGroupScheduleListFragment(String groupId);

    /**
     * 向服务端查询日程
     */
    Schedule syncFetchSchedule(String scheduleId, String calendarId);

    void acceptSchedule(String scheduleId, Function0 callback);

    void refuseSchedule(String scheduleId, Function0 callback);

    void chooseDate(Activity activity, boolean bottomStyle, java.util.Calendar targetDate, Function1<Calendar, Void> callback);

    void chooseTime(Activity activity, FragmentManager manager, boolean isAllDaySchedule, String beginTime, String endTime, Function2<String, String, Void> callback);

    void chooseLocation(FragmentActivity activity, @Nullable PoiItem poiItem, Function1<PoiItem, Void> callback);

    void chooseRepeat(FragmentActivity activity, int selected, String beanJson, Function1<Integer, Void> callback) throws JSONException;

    void chooseRepeatEndDate(FragmentActivity activity, String targetDate, String endDate, Function2<Boolean, Calendar, Void> callback);
}
