<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.twl.hi.workflow">

    <application>
        <activity
            android:name=".TaskListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".TaskDetailEditActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".TaskEditContentActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".TaskCustomEditContentActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".TaskTagListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".TaskTagCreateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".TaskCustomMultipleSelectActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".TaskCreateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".TaskCustomMultiSelectListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".TaskCustomSelectListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".SubTaskCreateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".SubTaskListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".TaskTargetDateSetActivity"
            android:screenOrientation="portrait" />
    </application>
</manifest>