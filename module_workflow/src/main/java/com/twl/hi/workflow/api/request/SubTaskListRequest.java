package com.twl.hi.workflow.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.workflow.api.response.SubTaskListResponse;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class SubTaskListRequest extends BaseApiRequest<SubTaskListResponse> {
    @Expose
    public String taskId;  //父任务id

    public SubTaskListRequest(BaseApiRequestCallback<SubTaskListResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_TASK_SUB_LIST;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}

