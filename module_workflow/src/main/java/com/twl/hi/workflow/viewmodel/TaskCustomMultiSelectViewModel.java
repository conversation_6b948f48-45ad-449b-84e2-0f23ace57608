package com.twl.hi.workflow.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.databinding.ObservableBoolean;
import androidx.lifecycle.MutableLiveData;

import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.hi.workflow.api.response.bean.WorkFlowCustomBean;

import java.util.ArrayList;
import java.util.List;

import lib.twl.common.util.LList;

/**
 * Created by ChaiJiangpeng on 2021/2/2
 * Describe:
 */
public class TaskCustomMultiSelectViewModel extends FoundationViewModel {
    public ObservableBoolean mSaveEnable = new ObservableBoolean(true);
    private WorkFlowCustomBean mCustomBean;
    private List<WorkFlowCustomBean.WorkFlowCustomOption> mOptions;
    private List<WorkFlowCustomBean.WorkFlowCustomOption> mSelOptions = new ArrayList<>();
    private MutableLiveData<Boolean> selLiveData = new MutableLiveData<>();
    private List<String> mSelValues = new ArrayList<>();
    private boolean required;

    public TaskCustomMultiSelectViewModel(Application application) {
        super(application);
    }

    public WorkFlowCustomBean getCustomBean() {
        return mCustomBean;
    }

    public void setCustomBean(WorkFlowCustomBean customBean) {
        mCustomBean = customBean;
        if (mCustomBean != null && mCustomBean.getConfig() != null) {
            required = mCustomBean.getConfig().getRequired() == 1;
            mOptions = mCustomBean.getConfig().getOptions();
            initSelOptions(mCustomBean.getValue());
            if (required && !TextUtils.isEmpty(mCustomBean.getValue()) && !LList.isEmpty(mOptions)) {
                mSaveEnable.set(!isHaveNextOption(mCustomBean.getValue()));
            }
        }
    }

    public boolean isRequired() {
        return required;
    }

    /**
     * 初始化已选择的value
     */
    private void initSelOptions(String value) {
        WorkFlowCustomBean.WorkFlowCustomOption option = getWorkFlowCustomOption(value);
        while (option != null && option.getHide() != 1) {
            mSelOptions.add(0, option);
            option = getWorkFlowCustomOption(option.getParent());
        }
    }

    public WorkFlowCustomBean.WorkFlowCustomOption getWorkFlowCustomOption(String value) {
        if (!TextUtils.isEmpty(value) && !LList.isEmpty(mOptions)) {
            for (WorkFlowCustomBean.WorkFlowCustomOption option : mOptions) {
                if (TextUtils.equals(value, option.getValue())) {
                    return option;
                }
            }

        }
        return null;
    }

    public List<WorkFlowCustomBean.WorkFlowCustomOption> getLayerSelectList(String value) {
        if (!LList.isEmpty(mOptions)) {
            List<WorkFlowCustomBean.WorkFlowCustomOption> options = new ArrayList<>();
            for (WorkFlowCustomBean.WorkFlowCustomOption option : mOptions) {
                if (TextUtils.isEmpty(value)) {
                    if (TextUtils.isEmpty(option.getParent())) {
                        options.add(option);
                    }
                } else if (TextUtils.equals(value, option.getParent())) {
                    options.add(option);
                }
            }
            return options;
        }
        return null;
    }

    public List<WorkFlowCustomBean.WorkFlowCustomOption> getSelOptions() {
        return mSelOptions;
    }

    public WorkFlowCustomBean.WorkFlowCustomOption getSelOption(int position) {
        if (mSelOptions.size() > position) {
            return mSelOptions.get(position);
        }
        return null;
    }

    /**
     * 点击选择的option
     *
     * @param option   选择的option
     * @param position 当前的几级
     */
    public void setSelOption(WorkFlowCustomBean.WorkFlowCustomOption option, int position) {
        if (position != mSelOptions.size()) {
            if (position == 0) {
                mSelOptions.clear();
            } else {
                mSelOptions = mSelOptions.subList(0, position);
            }
        }
        mSelOptions.add(option);
        selLiveData.setValue(true);
        mSaveEnable.set(!isHaveNextOption(option.getValue()));
    }

    public MutableLiveData<Boolean> getSelLiveData() {
        return selLiveData;
    }

    public boolean isHaveNextOption(String value) {
        for (WorkFlowCustomBean.WorkFlowCustomOption option : mOptions) {
            if (TextUtils.equals(value, option.getParent())) {
                return true;
            }
        }
        return false;
    }

    public void setSelValues() {
        mSelValues.clear();
        mSelValues.add("");
        for (int i = 0; i < mSelOptions.size(); i++) {
            if (i < mSelOptions.size() - 1) {
                mSelValues.add(mSelOptions.get(i).getValue());
            } else if (i == mSelOptions.size() - 1 && isHaveNextOption(mSelOptions.get(i).getValue())) {
                mSelValues.add(mSelOptions.get(i).getValue());
            }
        }
    }

    public List<String> getSelValues() {
        return mSelValues;
    }

    public String getSelLabel() {
        if (mSelOptions.size() == 0) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < mSelOptions.size(); i++) {
            stringBuilder.append(mSelOptions.get(i).getLabel());
            if (i < mSelOptions.size() - 1) {
                stringBuilder.append("/");
            }
        }
        return stringBuilder.toString();
    }

    public String getSelValue() {
        if (mSelOptions.size() == 0) {
            return "";
        }
        return mSelOptions.get(mSelOptions.size() - 1).getValue();
    }

    public void clearOption() {
        mSelOptions.clear();
        selLiveData.setValue(true);
        mSaveEnable.set(!required);
    }
}
