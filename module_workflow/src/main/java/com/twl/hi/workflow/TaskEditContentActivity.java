package com.twl.hi.workflow;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import com.twl.hi.basic.callback.TitleBarCallback;
import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.workflow.databinding.WorkflowActivityTaskEditContentBinding;
import com.twl.hi.workflow.viewmodel.TaskEditContentViewModel;

import lib.twl.common.util.AppUtil;
import lib.twl.common.util.CommonUtils;
import lib.twl.common.util.QMUIKeyboardHelper;

/**
 * Created by ChaiJiangpeng on 2020/11/2
 * Describe: 任务编辑内容描述
 */
public class TaskEditContentActivity extends FoundationVMActivity<WorkflowActivityTaskEditContentBinding, TaskEditContentViewModel> implements TitleBarCallback {

    public static final int TYPE_TASK_EDIT_CONTENT = 1;
    public static final int TYPE_TASK_EDIT_DESC = 2;
    private int type;
    private String content;

    @Override
    public int getContentLayoutId() {
        return R.layout.workflow_activity_task_edit_content;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    public static Intent createIntent(Context context, int type, String content) {
        Intent intent = new Intent(context, TaskEditContentActivity.class);
        intent.putExtra("type", type);
        intent.putExtra("content", content);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    private void initView() {
        type = getIntent().getIntExtra("type", TYPE_TASK_EDIT_CONTENT);
        content = getIntent().getStringExtra("content");
        getViewModel().mInput.set(content);
//
        if (type == TYPE_TASK_EDIT_CONTENT) {
            getDataBinding().titleBar.tvTitle.setText(R.string.workflow_task_name);
            getDataBinding().etInput.setHint(new StringBuilder().append(" ").append(getResources().getString(R.string.workflow_add_task_name)));
            CommonUtils.lengthFilter(this, getDataBinding().etInput, 2000, getResources().getString(R.string.workflow_over_count_char, 2000));
        } else {
            getDataBinding().titleBar.tvTitle.setText(R.string.workflow_task_desc);
            getDataBinding().etInput.setHint(new StringBuilder().append(" ").append(getResources().getString(R.string.workflow_add_task_desc)));
            CommonUtils.lengthFilter(this, getDataBinding().etInput, 1000, getResources().getString(R.string.workflow_over_count_char, 1000));
        }
        QMUIKeyboardHelper.showKeyboard(getDataBinding().etInput, true);
        if (content != null) {
            getDataBinding().etInput.setText(content);
            getDataBinding().etInput.setSelection(getDataBinding().etInput.getText().length());
        }
        getDataBinding().etInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.equals(s.toString(), content)) {
                    if (type == TYPE_TASK_EDIT_CONTENT) {
                        if (!TextUtils.isEmpty(s)) {
                            getViewModel().mSaveEnable.set(true);
                            return;
                        }
                    } else {
                        getViewModel().mSaveEnable.set(true);
                        return;
                    }
                }
                getViewModel().mSaveEnable.set(false);
            }
        });
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {
        Intent intent = new Intent();
        intent.putExtra("content", getViewModel().mInput.get());
        setResult(Activity.RESULT_OK, intent);
        AppUtil.finishActivity(this);
    }
}
