package com.twl.hi.workflow.adapter;

import com.twl.hi.workflow.api.response.bean.AttachmentBean;
import com.twl.hi.workflow.R;
import com.twl.hi.workflow.databinding.WorkflowItemTaskDetailPicBinding;

import lib.twl.common.adapter.BaseDataBindingAdapter;
import lib.twl.common.adapter.BaseDataBindingViewHolder;

/**
 * Created by ChaiJiangpeng on 2020/11/4
 * Describe:
 */
public class TaskDetailPicAdapter extends BaseDataBindingAdapter<AttachmentBean, WorkflowItemTaskDetailPicBinding> {
    private boolean isCreator;

    public TaskDetailPicAdapter() {
        super(R.layout.workflow_item_task_detail_pic, null);
    }

    public void setCreator(boolean creator) {
        isCreator = creator;
    }

    @Override
    protected void bind(BaseDataBindingViewHolder<WorkflowItemTaskDetailPicBinding> helper, WorkflowItemTaskDetailPicBinding binding, AttachmentBean item) {
        binding.setItem(item);
        binding.setDelPic(isCreator);
        helper.addOnClickListener(R.id.iv_del);
    }
}
