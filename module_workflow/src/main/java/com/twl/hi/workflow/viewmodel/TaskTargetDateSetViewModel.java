package com.twl.hi.workflow.viewmodel;

import android.app.Application;

import com.twl.hi.foundation.base.FoundationViewModel;

public class TaskTargetDateSetViewModel extends FoundationViewModel {
    private String targetDate;
    private String targetTime;
    private String startDate;
    private String startTime;

    public TaskTargetDateSetViewModel(Application application) {
        super(application);
    }

    public String getTargetDate() {
        return targetDate;
    }

    public void setTargetDate(String targetDate) {
        this.targetDate = targetDate;
    }

    public String getTargetTime() {
        return targetTime;
    }

    public void setTargetTime(String targetTime) {
        this.targetTime = targetTime;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
}
