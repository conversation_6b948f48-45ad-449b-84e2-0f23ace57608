package com.twl.hi.workflow.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.workflow.api.response.TaskListMyResponse;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class TaskListMyRequest extends BaseApiRequest<TaskListMyResponse> {
    @Expose
    public int type; //0-全部，1-我负责的，2-我创建的，3-我参与的 4-我评论的
    @Expose
    public String statuses;
    @Expose
    public String offsetId;
    @Expose
    public int size = 20;
    @Expose
    public String executorId;
    @Expose
    public String partnerIds;
    @Expose
    public String creatorId;
    @Expose
    public String deadlineStart;
    @Expose
    public String deadlineEnd;
    @Expose
    public String createStart;
    @Expose
    public String createEnd;
    @Expose
    public String finishStart;
    @Expose
    public String finishEnd;
    @Expose
    public String taskGroupIds;  //工作流id 列表 ,英文逗号分隔
    @Expose
    public String startDateBegin;
    @Expose
    public String startDateEnd;

    public TaskListMyRequest(BaseApiRequestCallback<TaskListMyResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_TASK_LIST_MY;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}

