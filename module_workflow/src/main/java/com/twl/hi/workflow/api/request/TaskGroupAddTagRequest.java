package com.twl.hi.workflow.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.workflow.api.response.TaskGroupAddTagResponse;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class TaskGroupAddTagRequest extends BaseApiRequest<TaskGroupAddTagResponse> {

    @Expose
    public String taskGroupId;
    @Expose
    public String tag;
    @Expose
    public String taskId;
    @Expose
    public String msgId;

    public TaskGroupAddTagRequest(BaseApiRequestCallback<TaskGroupAddTagResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_TASK_GROUP_ADD_TAG;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}

