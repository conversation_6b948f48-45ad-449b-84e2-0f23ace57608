package com.twl.hi.workflow.api.response;

import com.twl.hi.workflow.api.response.bean.TaskExecutorStatusBean;
import com.twl.hi.workflow.api.response.bean.WorkFlowCustomBean;
import com.twl.http.client.HttpResponse;

import java.util.List;

public class TaskUpdateGroupResponse extends HttpResponse {
    /**
     * "taskGroupSubId": "zzzzz", // 工作流分组加密id
     * "taskGroupSubName": "ddddd", // 工作流分组名称
     */
    public String taskGroupSubId;
    public String taskGroupSubName;
    public List<WorkFlowCustomBean> fields;
    /**
     * "isTaskGroupMember" : 1, // 是否工作流成员，1-是，0-否
     * "isTaskGroupOwner" : 1, // 是否工作流管理员，1-是，0-否
     */
    public int isTaskGroupMember;
    public int isTaskGroupOwner;
    public TaskExecutorStatusBean taskStatus;
}