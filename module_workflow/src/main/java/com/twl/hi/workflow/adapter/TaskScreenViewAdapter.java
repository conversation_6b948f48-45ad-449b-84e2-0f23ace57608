package com.twl.hi.workflow.adapter;

import androidx.annotation.Nullable;

import com.twl.hi.workflow.R;
import com.twl.hi.workflow.databinding.WorkflowItemTaskListScreenViewBinding;
import com.twl.hi.workflow.model.TaskScreenItemBean;

import java.util.List;

import lib.twl.common.adapter.BaseDataBindingAdapter;
import lib.twl.common.adapter.BaseDataBindingViewHolder;

/**
 * Created by ChaiJ<PERSON>gpeng
 * Describe:
 */
public class TaskScreenViewAdapter extends BaseDataBindingAdapter<TaskScreenItemBean, WorkflowItemTaskListScreenViewBinding> {
    public TaskScreenViewAdapter(@Nullable List<TaskScreenItemBean> data) {
        super(R.layout.workflow_item_task_list_screen_view, data);
    }

    @Override
    protected void bind(BaseDataBindingViewHolder<WorkflowItemTaskListScreenViewBinding> helper, WorkflowItemTaskListScreenViewBinding binding, TaskScreenItemBean item) {
        binding.setBean(item);
    }
}
