package com.twl.hi.workflow.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.workflow.api.response.TaskGroupListResponse;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class TaskGroupListRequest extends BaseApiRequest<TaskGroupListResponse> {

    @Expose
    public String taskId;

    public TaskGroupListRequest(BaseApiRequestCallback<TaskGroupListResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_TASK_GROUP_LIST;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}

