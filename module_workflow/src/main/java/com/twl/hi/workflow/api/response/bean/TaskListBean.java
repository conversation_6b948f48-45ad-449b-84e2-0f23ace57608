package com.twl.hi.workflow.api.response.bean;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/10/28
 * Describe:
 */
public class TaskListBean implements Parcelable {
    /**
     * "taskId": "xxxxxx", // 任务加密id
     * "content": "xxxxx", // 内容
     * "creatorId": 100001, // 创建者用户id
     * "targetDate": "2019-06-30", // 日期
     * "targetTime": "19:00", // 结束时间
     * "taskGroupId": "xxxxxx", // 工作流加密id，可空
     * "taskGroupName": "ddddd", // 工作流名称，可空
     * "markType" : 0, // 待办的优先级 0-普通，1-紧急 2-非紧急,3-较低，默认为0
     * "executorId": 10001, // 执行人id
     * "status": 1, // 状态：1-待认领，2-进行中，3-已完成
     * "message" : {
     * "id" : 12313231, // 消息Id
     * "fromId" : 123123, // 发送者id
     * "toId" : 12313, // 接受者id，
     * "type" : 1, // 1单聊 2 群聊
     * "seq" : 123,
     * "chatId": 1233, // 会话Id
     * "chatType" : 1 // 会话类型
     * },
     */

    public String taskId;
    public String content;
    public String creatorId;
    public String targetDate;
    public String targetTime;
    public String taskGroupId;
    public String taskGroupName;
    public int markType;
    public String executorId;
    public int status;
    public TaskInfoBean.TaskInfoMessage message;
    public TaskInfoBean.TaskInfoAlert alert;

    /**
     * "uniqueTaskId": 89123123123123, // 任务唯一标识id
     * "parentTaskId":"xxx",//父级任务id
     * "parentTaskName":"xxx", //父级任务名称
     */
    public long uniqueTaskId;
    public String parentTaskId;
    public String parentTaskName;
    public String startDate;
    public String startTime;

    public TaskListBean() {
    }

    protected TaskListBean(Parcel in) {
        taskId = in.readString();
        content = in.readString();
        creatorId = in.readString();
        targetDate = in.readString();
        targetTime = in.readString();
        taskGroupId = in.readString();
        taskGroupName = in.readString();
        markType = in.readInt();
        executorId = in.readString();
        status = in.readInt();
        message = in.readParcelable(TaskInfoBean.TaskInfoMessage.class.getClassLoader());
        alert = in.readParcelable(TaskInfoBean.TaskInfoAlert.class.getClassLoader());
        uniqueTaskId = in.readLong();
        parentTaskId = in.readString();
        parentTaskName = in.readString();
        startDate = in.readString();
        startTime = in.readString();
    }

    public static final Creator<TaskListBean> CREATOR = new Creator<TaskListBean>() {
        @Override
        public TaskListBean createFromParcel(Parcel in) {
            return new TaskListBean(in);
        }

        @Override
        public TaskListBean[] newArray(int size) {
            return new TaskListBean[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(taskId);
        dest.writeString(content);
        dest.writeString(creatorId);
        dest.writeString(targetDate);
        dest.writeString(targetTime);
        dest.writeString(taskGroupId);
        dest.writeString(taskGroupName);
        dest.writeInt(markType);
        dest.writeString(executorId);
        dest.writeInt(status);
        dest.writeParcelable(message, flags);
        dest.writeParcelable(alert, flags);
        dest.writeLong(uniqueTaskId);
        dest.writeString(parentTaskId);
        dest.writeString(parentTaskName);
        dest.writeString(startDate);
        dest.writeString(startTime);
    }
}

