package com.twl.hi.workflow.model;

import androidx.databinding.ObservableBoolean;

public class TaskScreenItemBean {

    private String statuses;
    private ObservableBoolean isChecked = new ObservableBoolean();//item是否被选中
    private String statusName;

    public TaskScreenItemBean(String statuses, boolean isChecked, String statusName) {
        this.statuses = statuses;
        this.isChecked.set(isChecked);
        this.statusName = statusName;
    }

    public String getStatuses() {
        return statuses;
    }

    public void setStatuses(String statuses) {
        this.statuses = statuses;
    }

    public ObservableBoolean isChecked() {
        return isChecked;
    }

    public void setChecked(boolean checked) {
        isChecked.set(checked);
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }
}
