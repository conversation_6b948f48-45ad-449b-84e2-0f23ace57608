package com.twl.hi.workflow.dialog;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.twl.hi.basic.BottomSheetBehaviorBaseDialog;
import com.twl.hi.basic.callback.DialogCallback;
import com.twl.hi.basic.dialog.bottom.BottomSelectItemBean;
import com.twl.hi.workflow.BR;
import com.twl.hi.workflow.R;
import com.twl.hi.workflow.adapter.TaskSelectPriorityAdapter;
import com.twl.hi.workflow.databinding.WorkflowViewDialogScheduleCreateRemindBinding;
import com.twl.hi.workflow.viewmodel.ScheduleCreateRemindDialogViewModel;

import java.util.List;

import lib.twl.common.views.adapter.BaseQuickAdapter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/30
 * Describe: 任务优先级选择弹框
 */
public class TaskSelectPriorityDialog extends BottomSheetBehaviorBaseDialog<WorkflowViewDialogScheduleCreateRemindBinding, ScheduleCreateRemindDialogViewModel> implements DialogCallback {
    public static final String TAG = "TaskSelectPriorityDialog";
    public static final int TITLE_TASK_PRIORITY = 1;
    private FragmentActivity mActivity;
    private List<BottomSelectItemBean> mItems;
    private TaskSelectPriorityAdapter mAdapter;
    private int titleType;
    private OnClickSureListener mClickSureListener;

    public TaskSelectPriorityDialog(FragmentActivity activity, List<BottomSelectItemBean> items, int titleType) {
        this.mActivity = activity;
        this.mItems = items;
        this.titleType = titleType;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

    }

    @Override
    public int getContentLayoutId() {
        return R.layout.workflow_view_dialog_schedule_create_remind;
    }

    @Override
    protected void initFragment() {
        initData();
        getBehavior().setBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {

            @Override
            public void onStateChanged(@NonNull View view, int i) {
                if (i == BottomSheetBehavior.STATE_HIDDEN) {
                    dismiss();
                    if (getHideCallback() != null) {
                        getHideCallback().onHide();
                    }
                }
            }

            @Override
            public void onSlide(@NonNull View view, float v) {

            }
        });
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return 0;
    }

    private void initData() {
        getDataBinding().tvTitle.setText(getResources().getString(R.string.select_priority));
        mAdapter = new TaskSelectPriorityAdapter(mItems, titleType);
        getDataBinding().recycler.setLayoutManager(new LinearLayoutManager(mActivity));
        mAdapter.bindToRecyclerView(getDataBinding().recycler);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                BottomSelectItemBean itemBean = mItems.get(position);
                for (BottomSelectItemBean itemBean1 : mItems) {
                    itemBean1.setChecked(false);
                }
                itemBean.setChecked(true);
                mAdapter.notifyDataSetChanged();
            }
        });
    }

    /**
     * 选择提醒时间使用
     *
     * @param indexList
     */
    public void showDialog(boolean[] indexList) {
        if (isAdded() || (mActivity.getSupportFragmentManager().findFragmentByTag(TAG) != null)) {
            return;
        }
        for (BottomSelectItemBean itemBean : mItems) {
            itemBean.setChecked(indexList[itemBean.getIndex()]);
        }
        show(mActivity.getSupportFragmentManager(), TAG);
    }


    public void showDialog(int keyIndex) {
        if (isAdded() || (mActivity.getSupportFragmentManager().findFragmentByTag(TAG) != null)) {
            return;
        }
        for (BottomSelectItemBean itemBean : mItems) {
            if (keyIndex == itemBean.getKey()) {
                itemBean.setChecked(true);
            } else {
                itemBean.setChecked(false);
            }
        }
        show(mActivity.getSupportFragmentManager(), TAG);
    }

    public void showDialog() {
        if (isAdded() || (mActivity.getSupportFragmentManager().findFragmentByTag(TAG) != null)) {
            return;
        }
        show(mActivity.getSupportFragmentManager(), TAG);
    }


    @Override
    public void onClickDismiss() {
        dismiss();
    }

    @Override
    public void onClickSure() {
        dismiss();
        if (mClickSureListener != null) {
            int index = 0;
            for (BottomSelectItemBean itemBean : mItems) {
                if (itemBean.isChecked()) {
                    index = itemBean.getIndex();
                    break;
                }
            }
            mClickSureListener.onClickSure(index);
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (getHideCallback() != null) {
            getHideCallback().onHide();
        }
    }

    public void setClickSureListener(OnClickSureListener clickSureListener) {
        mClickSureListener = clickSureListener;
    }

    public interface OnClickSureListener {
        void onClickSure(boolean[] indexList);

        void onClickSure(int indexList);
    }
}
