package com.twl.hi.workflow.adapter;

import android.text.TextUtils;
import android.view.View;

import com.twl.hi.basic.bindadapter.BasicBindingAdapters;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.utils.ScheduleUtils;
import com.twl.hi.workflow.R;
import com.twl.hi.workflow.api.response.bean.TaskListBean;
import com.twl.hi.workflow.databinding.WorkflowItemSubTaskListBinding;
import com.twl.utils.StringUtils;

import org.apache.commons.lang3.text.StrBuilder;

import lib.twl.common.adapter.BaseDataBindingAdapter;
import lib.twl.common.adapter.BaseDataBindingViewHolder;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>g<PERSON>g
 * Describe:
 */
public class SubTaskListAdapter extends BaseDataBindingAdapter<TaskListBean, WorkflowItemSubTaskListBinding> {
    public SubTaskListAdapter() {
        super(R.layout.workflow_item_sub_task_list, null);
    }

    @Override
    protected void bind(BaseDataBindingViewHolder<WorkflowItemSubTaskListBinding> helper, WorkflowItemSubTaskListBinding binding, TaskListBean item) {
        binding.setItem(item);
        if (StringUtils.isNotEmpty(item.executorId)) {
            Contact contactById = ServiceManager.getInstance().getContactService().getContactById(item.executorId);
            if (contactById != null) {
                binding.tvName.setVisibility(View.VISIBLE);
                binding.vgAvatar.setVisibility(View.VISIBLE);
                binding.tvName.setText(contactById.getShowName(Contact.SHOW_NAME_SCENE_NAME_OR_NICK));
                BasicBindingAdapters.setApplyContact(binding.vgAvatar, contactById);
            } else {
                binding.tvName.setVisibility(View.GONE);
                binding.vgAvatar.setVisibility(View.GONE);
            }
        } else {
            binding.tvName.setVisibility(View.GONE);
            binding.vgAvatar.setVisibility(View.GONE);
        }

        if (TextUtils.isEmpty(item.parentTaskId)) {
            binding.tvDate.setVisibility(View.GONE);
        } else {
            binding.tvDate.setVisibility(View.VISIBLE);
            binding.tvDate.setText(setShowDate(item.targetDate, item.startDate));
        }

    }

    private String setShowDate(String targetDate, String startDate) {
        StrBuilder showDate = new StrBuilder();
        if (TextUtils.isEmpty(targetDate)) {
            return "";
        }
        if (!TextUtils.isEmpty(startDate)) {
            showDate.append(ScheduleUtils.getTaskRangeDataFormat(startDate));
            showDate.append("-");
        }
        showDate.append(ScheduleUtils.getTaskRangeDataFormat(targetDate));
        return showDate.build();
    }
}
