<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="item"
            type="com.twl.hi.workflow.api.response.bean.TaskTagBean" />

        <variable
            name="searchContent"
            type="String" />
    </data>

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="47dp"
        android:background="?android:attr/selectableItemBackground">

        <ImageView
            android:id="@+id/iv_sel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="23dp"
            android:src="@drawable/workflow_ic_icon_task_tag_sel"
            app:visibleInVisible="@{item.checked}" />

		<com.twl.hi.basic.views.multitext.ChatRecordSearchTextView
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_centerVertical="true"
			android:layout_marginLeft="13dp"
			android:layout_toRightOf="@+id/iv_sel"
			android:singleLine="true"
			android:textColor="@color/color_0D0D1A"
			android:textSize="16sp"
			app:searchContent="@{searchContent}"
			app:searchMemberContent="@{item.tagName}"
			tools:text="aa" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:background="@color/color_ECECEE" />
    </RelativeLayout>
</layout>