<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="item"
            type="String" />

    </data>

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="85dp"
        android:layout_height="75dp"
        android:paddingLeft="5dp"
        android:paddingRight="5dp">

        <com.facebook.drawee.view.SimpleDraweeView
            android:layout_width="75dp"
            android:layout_height="75dp"
            app:imageUrl="@{item}"
            app:roundedCornerRadius="6dp" />

        <ImageView
            android:id="@+id/iv_del"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/workflow_ic_icon_task_detail_pic_del" />

    </RelativeLayout>
</layout>