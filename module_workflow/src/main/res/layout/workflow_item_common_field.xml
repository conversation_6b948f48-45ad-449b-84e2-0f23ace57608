<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="name"
            type="String" />

        <variable
            name="content"
            type="String" />

        <variable
            name="contentHint"
            type="String" />

        <variable
            name="hasArrow"
            type="boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/cl_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="20dp"
        android:paddingRight="20dp">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="130dp"
            android:layout_height="wrap_content"
            tools:text="所属工作流所属工作流所属工作流"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="0"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:textSize="15sp"
            android:text="@{name}"
            android:textColor="@color/color_0D0D1A" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="210dp"
            android:layout_height="wrap_content"
            tools:text="工作流工作流工作流"
            android:text="@{content}"
            android:hint="@{contentHint}"
            android:gravity="right"
            android:textColorHint="@color/color_B1B1B8"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_name"
            android:layout_marginBottom="20dp"
            android:layout_marginRight="11dp"
            app:layout_constraintVertical_bias="0"
            android:textSize="14sp"
            android:textColor="@color/color_9999A3" />

        <ImageView
            android:id="@+id/iv_go"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/workflow_ic_icon_gray_arrow_right"
            app:visibleGone="@{hasArrow}"
            app:layout_constraintVertical_bias="0"
            android:layout_marginTop="5dp"
            app:layout_constraintTop_toTopOf="@+id/tv_content"
            app:layout_constraintBottom_toBottomOf="@+id/tv_content"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>