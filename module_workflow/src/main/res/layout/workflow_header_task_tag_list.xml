<?xml version="1.0" encoding="utf-8"?>
<layout>

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="23dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:drawableLeft="@drawable/workflow_ic_icon_task_tag_add"
            android:drawablePadding="12dp"
            android:text="@string/workflow_task_tag_create"
            android:textColor="@color/color_5D68EF"
            android:textSize="17sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_below="@+id/tv_add"
            android:background="@color/color_ECECEE" />
    </RelativeLayout>
</layout>