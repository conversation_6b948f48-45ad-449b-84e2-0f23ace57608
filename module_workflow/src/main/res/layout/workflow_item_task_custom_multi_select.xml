<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="item"
            type="com.twl.hi.workflow.api.response.bean.WorkFlowCustomBean.WorkFlowCustomOption" />

        <variable
            name="searchContent"
            type="String" />

    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="?android:attr/selectableItemBackground">


            <com.twl.hi.basic.views.multitext.ChatRecordSearchTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="20dp"
                android:textColor="@{item.hide == 1 ? @color/color_CECED2 : @color/color_0D0D1A}"
                android:textSize="15sp"
                android:layout_toLeftOf="@+id/iv_sel"
                android:singleLine="true"
                app:searchContent="@{searchContent}"
                app:searchMemberContent="@{item.label}"
                tools:text="aa" />

            <ImageView
                android:id="@+id/iv_sel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_alignParentRight="true"
                android:layout_marginRight="20dp"
                app:visibleGone="@{item.hide != 1 &amp;&amp; item.checked}"
                android:src="@drawable/workflow_ic_icon_task_tag_sel" />
        </RelativeLayout>

        <com.twl.hi.basic.views.ClearEditText
            android:id="@+id/et_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/iv_search"
            android:background="@null"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:paddingLeft="30dp"
            android:paddingRight="12dp"
            android:singleLine="true"
            android:textColor="@color/color_0D0D1A"
            android:textColorHint="@color/color_B1B1B8"
            android:text="@={item.text}"
            android:hint="@string/please_input"
            android:textSize="15sp"
            app:allowBlank="true" />


        <View
            android:id="@+id/view_line"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="2dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:background="@color/color_DFE4E4" />

    </LinearLayout>
</layout>