package com.twl.hi.webeditor.menu;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.twl.hi.webeditor.R;
import com.twl.hi.webeditor.bean.SubFontSizeMenu;

import java.util.ArrayList;
import java.util.List;

public class SubFontSizeMenuView extends RelativeLayout {

    private List<ISubMenuStyle> mISubMenuStyleList = new ArrayList<>();
    private RecyclerView mRecyclerView;

    private ImageView mImageView;

    private SubFontAdapter mFontAdapter;

    private SubMenuListener mMenuListener;

    private int subMenuType = MenuType.FontSizeMenuType.TYPE_SUB_FONT_SIZE_NONE;

    public void setMenuListener(SubMenuListener menuListener) {
        mMenuListener = menuListener;
    }

    public int getSubMenuType() {
        return subMenuType;
    }

    public void setSubMenuType(int subMenuType) {
        this.subMenuType = subMenuType;
        mFontAdapter.notifyDataSetChanged();
    }

    public void addSubMenuList(List<ISubMenuStyle> subMenuStyles) {
        mISubMenuStyleList.clear();
        mISubMenuStyleList.addAll(subMenuStyles);
        for (ISubMenuStyle subMenuStyle : subMenuStyles) {
            SubFontSizeMenu menu = (SubFontSizeMenu) subMenuStyle;
            menu.setCheckStatus(subMenuStyle.getSubMenuType() == subMenuType);
        }
        mFontAdapter.notifyDataSetChanged();
    }

    public SubFontSizeMenuView(Context context) {
        super(context);
        init(context, null);
    }

    public SubFontSizeMenuView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public SubFontSizeMenuView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attributeSet) {
        LayoutInflater.from(context).inflate(R.layout.layout_sub_font_size_menu, this, true);
        mRecyclerView = findViewById(R.id.sub_menu_list);
        mImageView = findViewById(R.id.save_update);

        mFontAdapter = new SubFontAdapter();
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        linearLayoutManager.setOrientation(RecyclerView.HORIZONTAL);
        mRecyclerView.setLayoutManager(linearLayoutManager);
        mRecyclerView.setAdapter(mFontAdapter);

        mImageView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mMenuListener != null) {
                    mMenuListener.onMenuClose();
                }
            }
        });
    }

    public class SubFontAdapter extends RecyclerView.Adapter<SubVHolder> {

        @NonNull
        @Override
        public SubVHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new SubVHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_sub_font_size_menu, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull SubVHolder holder, int position) {
            ISubMenuStyle subMenuStyle = mISubMenuStyleList.get(position);
            SubFontSizeMenu subFontSizeMenu = (SubFontSizeMenu) subMenuStyle;

            holder.itemView.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    subMenuType = subFontSizeMenu.getSubMenuType();
                    notifyDataSetChanged();
                    if (mMenuListener != null) {
                        mMenuListener.onMenuClick(MenuType.TYPE_FONT_SIZE, subMenuType);
                    }
                }
            });

            boolean checked = subFontSizeMenu.getSubMenuType() == subMenuType;
            holder.hintText.setText(subFontSizeMenu.getFontSizeText());
            if (checked) {
                holder.hintText.setBackgroundResource(subFontSizeMenu.getMenuCheckedBgRes());
                holder.hintText.setTextColor(Color.parseColor("#5D68E8"));
            } else {
                holder.hintText.setBackgroundResource(subFontSizeMenu.getMenuUnCheckedBgRes());
                holder.hintText.setTextColor(Color.parseColor("#0D0D1A"));
            }
        }

        @Override
        public int getItemCount() {
            return mISubMenuStyleList.size();
        }
    }

    public class SubVHolder extends RecyclerView.ViewHolder {

        TextView hintText;
        public SubVHolder(@NonNull View itemView) {
            super(itemView);
            hintText = itemView.findViewById(R.id.hint_text);
        }
    }

    public interface SubMenuListener {

        /**
         * 点击菜单
         * @param type
         * @param subtype
         */
        void onMenuClick(int type, int subtype);
        void onMenuClose();

    }

}
