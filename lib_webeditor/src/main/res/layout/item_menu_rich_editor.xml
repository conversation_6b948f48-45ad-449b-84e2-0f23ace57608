<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="30dp"
    android:layout_marginStart="5dp"
    android:layout_marginEnd="5dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="30dp"
        tools:background="@drawable/icon_bg_tool_menu_select"
        android:layout_height="30dp"
        android:padding="4dp">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:src="@drawable/email_ic_attach_file_unselect" />

        <TextView
            android:id="@+id/number"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:gravity="center"
            android:paddingStart="1px"
            android:textColor="#5D68E8"
            android:textSize="8sp"
            android:textStyle="bold"
            tools:text="12" />

    </RelativeLayout>

    <TextView
        android:id="@+id/description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_404A59"
        android:layout_marginStart="2dp"
        android:textSize="16sp"
        tools:text="模板" />

</LinearLayout>