package com.twl.http.interceptors;

import java.io.EOFException;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.util.concurrent.TimeUnit;

import okhttp3.Connection;
import okhttp3.Headers;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.internal.http.HttpHeaders;
import okhttp3.internal.platform.Platform;
import okio.Buffer;
import okio.BufferedSource;

import static okhttp3.internal.platform.Platform.INFO;

/**
 * Created by wangtian on 2017/7/19.
 */

public class DefaultLoggingInterceptor implements Interceptor {
    private static final Charset UTF8 = Charset.forName("UTF-8");

    public interface Logger {
        void log(String message);

        /** A {@link DefaultLoggingInterceptor.Logger} defaults output appropriate for the current platform. */
        DefaultLoggingInterceptor.Logger DEFAULT = new DefaultLoggingInterceptor.Logger() {
            @Override public void log(String message) {
                Platform.get().log(message, INFO, null);
            }
        };
    }

    public DefaultLoggingInterceptor() {
        this(DefaultLoggingInterceptor.Logger.DEFAULT);
    }

    public DefaultLoggingInterceptor(DefaultLoggingInterceptor.Logger logger) {
        this.logger = logger;
    }

    private final DefaultLoggingInterceptor.Logger logger;

    @Override public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
//        if (level == DefaultLoggingInterceptor.Level.NONE) {
//            return chain.proceed(request);
//        }

        boolean isMultipart = false;

        RequestBody requestBody = request.body();
        boolean hasRequestBody = requestBody != null;

        Connection connection = chain.connection();
        Protocol protocol = connection != null ? connection.protocol() : Protocol.HTTP_1_1;
        String requestStartMessage = "--> " + request.method() + ' ' + URLDecoder.decode(request.url().toString(),   "utf-8") + ' ' + protocol;
        if (hasRequestBody) {
            requestStartMessage += " (" + requestBody.contentLength() + "-byte body)";
        }

        logger.log(requestStartMessage);

            if (hasRequestBody) {
                // Request body headers are only present when installed as a network interceptor. Force
                // them to be included (when available) so there values are known.
                if (requestBody.contentType() != null) {
                    if(requestBody.contentType().toString().toLowerCase().contains("multipart")){
                        isMultipart = true;
                    }
                    logger.log("Content-Type: " + requestBody.contentType());
                }
                if (requestBody.contentLength() != -1) {
                    logger.log("Content-Length: " + requestBody.contentLength());
                }
            }

            Headers requestHeaders = request.headers();

            for (int i = 0, count = requestHeaders.size(); i < count; i++) {
                String name = requestHeaders.name(i);
                // Skip headers from the request body as they are explicitly logged above.
                if (!"Content-Type".equalsIgnoreCase(name) && !"Content-Length".equalsIgnoreCase(name)) {
                    logger.log(name + ": " + requestHeaders.value(i));
                }
            }

            if (!hasRequestBody) {
                logger.log("--> END " + request.method());
            } else if (bodyEncoded(requestHeaders)) {
                logger.log("--> END " + request.method() + " (encoded body omitted)");
            } else {
                Buffer buffer = new Buffer();
                requestBody.writeTo(buffer);

                Charset charset = UTF8;
                MediaType contentType = requestBody.contentType();
                if (contentType != null) {
                    charset = contentType.charset(UTF8);
                }

                logger.log("");
                if (isPlaintext(buffer)) {
                    logger.log("\n");
                    if(!isMultipart){
                        logger.log(buffer.readString(charset));
                    }
                    logger.log("\n");
                    logger.log("--> END " + request.method()
                            + " (" + requestBody.contentLength() + "-byte body)");
                } else {
                    logger.log("--> END " + request.method() + " (binary "
                            + requestBody.contentLength() + "-byte body omitted)");
                }
            }

        long startNs = System.nanoTime();
        Response response;
        try {
            response = chain.proceed(request);
        } catch (Exception e) {
            logger.log("<-- HTTP FAILED: " + e);
            throw e;
        }
        long tookMs = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startNs);

        ResponseBody responseBody = response.body();
        long contentLength = responseBody.contentLength();
        String bodySize = contentLength != -1 ? contentLength + "-byte" : "unknown-length";


        logger.log("<-- " + response.code() + ' ' + response.message() + ' '
                +  URLDecoder.decode(response.request().url().toString(),   "utf-8") + " (" + tookMs + "ms" + ", "
                + bodySize + " body"  + ')');

            Headers resopnseHeaders = response.headers();
            for (int i = 0, count = resopnseHeaders.size(); i < count; i++) {
                logger.log(resopnseHeaders.name(i) + ": " + resopnseHeaders.value(i));
            }

            if (!HttpHeaders.hasBody(response)) {
                logger.log("<-- END HTTP");
            } else if (bodyEncoded(resopnseHeaders)) {
                logger.log("<-- END HTTP (encoded body omitted)");
            } else {
                BufferedSource source = responseBody.source();
                source.request(Long.MAX_VALUE); // Buffer the entire body.
                Buffer buffer = source.buffer();

                Charset charset = UTF8;
                MediaType contentType = responseBody.contentType();
                if (contentType != null) {
                    charset = contentType.charset(UTF8);
                }

                if (!isPlaintext(buffer)) {
                    logger.log("");
                    logger.log("<-- END HTTP (binary " + buffer.size() + "-byte body omitted)");
                    return response;
                }

                if (contentLength != 0) {
                    logger.log("");
                    logger.log("\n");
                    logger.log(buffer.clone().readString(charset));
                    logger.log("\n");
                }

                logger.log("<-- END HTTP (" + buffer.size() + "-byte body)");
            }

        return response;
    }

    /**
     * Returns true if the body in question probably contains human readable text. Uses a small sample
     * of code points to detect unicode control characters commonly used in binary file signatures.
     */
    static boolean isPlaintext(Buffer buffer) {
        try {
            Buffer prefix = new Buffer();
            long byteCount = buffer.size() < 64 ? buffer.size() : 64;
            buffer.copyTo(prefix, 0, byteCount);
            for (int i = 0; i < 16; i++) {
                if (prefix.exhausted()) {
                    break;
                }
                int codePoint = prefix.readUtf8CodePoint();
                if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
                    return false;
                }
            }
            return true;
        } catch (EOFException e) {
            return false; // Truncated UTF-8 sequence.
        }
    }

    private boolean bodyEncoded(Headers headers) {
        String contentEncoding = headers.get("Content-Encoding");
        return contentEncoding != null && !contentEncoding.equalsIgnoreCase("identity");
    }
}
