package com.twl.http;


import java.util.HashMap;
import java.util.Map;

/**
 * Author: <PERSON><PERSON><PERSON>
 * Date: 2017/6/17.
 */
public class ApiData<T> {

    public T resp;

    /**
     *
     * 扩展参数
     * 仅供在handleInChildThread需要传递临时参数到onSuccess中时使用
     * ***/
    private Map<String,Object> extendParamsMap = new HashMap<>();

    public Map<String, Object> getExtendParamsMap() {
        return extendParamsMap;
    }

    public void setExtendParam(String key,Object value){
        extendParamsMap.put(key,value);
    }

    public Object getExtendParam(String key){
        return extendParamsMap.get(key);
    }
}
