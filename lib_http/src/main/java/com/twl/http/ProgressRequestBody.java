package com.twl.http;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import okio.*;

import java.io.IOException;

/**
 * 带进度监听的 RequestBody
 * 用于文件上传时监听上传进度
 */
public class ProgressRequestBody extends RequestBody {
    
    private final RequestBody requestBody;
    private final UploadProgressListener progressListener;
    private final String url;
    
    public ProgressRequestBody(RequestBody requestBody, String url, UploadProgressListener progressListener) {
        this.requestBody = requestBody;
        this.url = url;
        this.progressListener = progressListener;
    }
    
    @Override
    public MediaType contentType() {
        return requestBody.contentType();
    }
    
    @Override
    public long contentLength() throws IOException {
        return requestBody.contentLength();
    }
    
    @Override
    public void writeTo(BufferedSink sink) throws IOException {
        if (progressListener == null) {
            requestBody.writeTo(sink);
            return;
        }
        
        ProgressSink progressSink = new ProgressSink(sink);
        BufferedSink bufferedSink = Okio.buffer(progressSink);
        requestBody.writeTo(bufferedSink);
        bufferedSink.flush();
    }
    
    private class ProgressSink extends ForwardingSink {
        private long bytesWritten = 0L;
        private long contentLength = 0L;
        private long lastProgressTime = 0L;
        private static final long PROGRESS_THROTTLE_MS = 100; // 限制进度回调频率

        public ProgressSink(Sink delegate) {
            super(delegate);
            try {
                contentLength = contentLength();
            } catch (IOException e) {
                contentLength = -1;
            }
        }

        @Override
        public void write(Buffer source, long byteCount) throws IOException {
            super.write(source, byteCount);
            if (contentLength == -1) {
                // 无法获取总长度时，尝试重新获取
                try {
                    contentLength = contentLength();
                } catch (IOException e) {
                    contentLength = -1;
                }
            }

            bytesWritten += byteCount;

            // 限制进度回调频率，避免过于频繁的更新
            long currentTime = System.currentTimeMillis();
            if (progressListener != null && (currentTime - lastProgressTime >= PROGRESS_THROTTLE_MS || bytesWritten >= contentLength)) {
                float progress = contentLength > 0 ? (float) bytesWritten / contentLength : 0f;

                // 只有在进度有意义时才回调（避免网络异常时的虚假进度）
                if (progress >= 0f && progress <= 1f) {
                    progressListener.onProgress(url, progress, bytesWritten, contentLength);
                    lastProgressTime = currentTime;
                }
            }
        }
    }
    
    /**
     * 文件上传进度监听接口
     */
    public interface UploadProgressListener {
        /**
         * 上传进度回调
         *
         * @param url 上传URL
         * @param progress 上传进度 (0.0 - 1.0)
         * @param bytesWritten 已上传字节数
         * @param totalBytes 总字节数
         */
        void onProgress(String url, float progress, long bytesWritten, long totalBytes);
    }
}
