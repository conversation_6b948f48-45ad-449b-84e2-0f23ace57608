<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_corner_6_top_2_color_white"
    android:orientation="vertical"
    android:paddingLeft="20dp"
    android:paddingRight="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:paddingTop="20dp"
            android:text="@string/chat_set_notify_time"
            android:textColor="@color/color_0D0D1A"
            android:textSize="20sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_jump"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingTop="20dp"
            android:text="@string/chat_jump_over"
            android:textColor="@color/color_0D0D1A"
            android:textSize="15sp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="20dp"
        android:paddingBottom="20dp">

        <lib.twl.common.views.time.WheelView
            android:id="@+id/options1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            app:layout_constraintHorizontal_weight="3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/options2"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <lib.twl.common.views.time.WheelView
            android:id="@+id/options2"

            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:layout_constraintHorizontal_weight="2"
            app:layout_constraintLeft_toRightOf="@+id/options1"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@drawable/bg_corner_7_color_f2f2f5"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/color_0D0D1A"
            android:textSize="17sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_submit"
            app:layout_constraintTop_toBottomOf="@+id/options1" />

        <TextView
            android:id="@+id/tv_submit"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginLeft="10dp"
            android:layout_weight="1"
            android:background="@drawable/bg_corner_7_color_5d68e8"
            android:gravity="center"
            android:text="@string/chat_set_notify"
            android:textColor="@color/app_white"
            android:textSize="17sp"
            app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/options1" />
    </LinearLayout>


</LinearLayout>