<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="chatMessage"
            type="com.twl.hi.foundation.model.message.ChatMessage" />

        <variable
            name="bean"
            type="com.twl.hi.chat.model.SelectionEmojiItem" />

        <variable
            name="callback"
            type="com.twl.hi.chat.callback.OnEmojiReplyCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:onClick="@{()->callback.onEmojiItemReplyClick(bean,chatMessage)}"
        android:padding="6dp">

        <ImageView
            android:layout_width="28dp"
            android:layout_height="28dp"
            app:emojiImage="@{bean.emotionItem}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
