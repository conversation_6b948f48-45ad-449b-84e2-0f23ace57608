<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="bean"
            type="com.twl.hi.foundation.api.response.bean.FavoriteInterface" />

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.MessageForChatShare" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.message.viewmodel.FavoriteListBaseViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.message.callback.FavoriteListCallback" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:clickable="true"
        android:focusable="true"
        android:onLongClick="@{()->callback.onLongClick(bean)}"
        android:orientation="vertical"
        android:paddingLeft="20dp"
        android:paddingTop="20dp"
        android:paddingRight="20dp"
        app:callback="@{callback}"
        app:favorId="@{bean.favorId}"
        app:showFavoriteHighLight="@{viewModel.showFavoriteHighLight}">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:onClick="@{(view)->callback.onChatClick(view,bean)}"
            android:onLongClick="@{()->callback.onLongClick(bean)}"
            android:background="@drawable/chat_bg_favorite_message"
            android:padding="12dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="3dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/color_text_chat_message"
                app:scaledTextSize="@{16}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:text="@{msg.chatShareInfo.title}"
                tools:text="与周胜华的聊天记录" />

            <LinearLayout
                android:id="@+id/summaries"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="@+id/tv_title"
                app:layout_constraintTop_toBottomOf="@+id/tv_title">

                <TextView
                    style="@style/chat_share_summaries"
                    android:text="@{msg.chatShareInfo.summaries.size() > 0 ? msg.chatShareInfo.summaries.get(0) : &quot;&quot;}"
                    app:scaledTextSize="@{13}"
                    android:visibility="@{msg.chatShareInfo.summaries.size() > 0 ? View.VISIBLE : View.GONE}"
                    tools:text="hi" />

                <TextView
                    style="@style/chat_share_summaries"
                    android:text="@{msg.chatShareInfo.summaries.size() > 1 ? msg.chatShareInfo.summaries.get(1) : &quot;&quot;}"
                    app:scaledTextSize="@{13}"
                    android:visibility="@{msg.chatShareInfo.summaries.size() > 1 ? View.VISIBLE : View.GONE}"
                    tools:text="hi1" />

                <TextView
                    style="@style/chat_share_summaries"
                    android:text="@{msg.chatShareInfo.summaries.size() > 2 ? msg.chatShareInfo.summaries.get(2) : &quot;&quot;}"
                    app:scaledTextSize="@{13}"
                    android:visibility="@{msg.chatShareInfo.summaries.size() > 2 ? View.VISIBLE : View.GONE}"
                    tools:text="hi2" />

                <TextView
                    style="@style/chat_share_summaries"
                    android:text="@{msg.chatShareInfo.summaries.size() > 3 ? msg.chatShareInfo.summaries.get(3) : &quot;&quot;}"
                    app:scaledTextSize="@{13}"
                    android:visibility="@{msg.chatShareInfo.summaries.size() > 3 ? View.VISIBLE : View.GONE}"
                    tools:text="hi3" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            layout="@layout/chat_item_multi_favorite_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:bean="@{bean}"
            app:callback="@{callback}"
            app:viewModel="@{viewModel}" />

    </LinearLayout>
</layout>