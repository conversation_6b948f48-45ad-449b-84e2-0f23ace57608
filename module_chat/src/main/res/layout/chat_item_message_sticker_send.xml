<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="listener"
            type="com.twl.hi.basic.callback.ChatItemListener" />

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.MessageForSticker" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.ChatBaseViewModel" />
    </data>

    <com.twl.hi.basic.views.InterceptConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <include
            android:id="@+id/checkbox"
            layout="@layout/chat_item_msg_checkbox"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:msg="@{msg}"
            app:viewModel="@{viewModel}" />

        <include
            android:id="@+id/sdv_to_avatar"
            layout="@layout/item_avatar3"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="@dimen/message_item_avatar_margin_container"
            app:avatarContact="@{viewModel.getContact(msg)}"
            app:itemAvatarClick="@{(view)->listener.onAvatarClick(view,msg)}"
            app:itemAvatarDoubleClick="@{(view)->listener.onDoubleAvatarClick(view,msg)}"
            app:itemAvatarLongClick="@{(view)->listener.onLongAvatarClick(view,msg)}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:message="@{msg}"
            app:showPendant="@{true}" />

        <include
            android:id="@+id/sdv_sticker_send"
            layout="@layout/chat_view_message_sticker_send"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/chat_input_message_item_right_width"
            android:layout_marginTop="@dimen/message_item_content_margin_name"
            android:layout_marginEnd="@dimen/message_item_content_margin_avatar"
            app:layout_constraintEnd_toStartOf="@+id/sdv_to_avatar"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_default="wrap"
            app:listener="@{listener}"
            app:max_width_percent="@{msg.getMaxWidthPercent()}"
            app:msg="@{msg}"
            app:showDynamic="@{true}"
            app:showReply="@{true}" />

        <ImageView
            android:id="@+id/iv_to_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="5dp"
            android:layout_marginBottom="3dp"
            android:gravity="bottom"
            app:hideStatus="@{viewModel.sysChatType}"
            app:layout_constraintBottom_toBottomOf="@+id/sdv_sticker_send"
            app:layout_constraintRight_toLeftOf="@+id/sdv_sticker_send"
            app:resendChatListnner="@{listener}"
            app:resendChatMessage="@{msg}"
            app:singleMsgStatus="@{msg}" />

        <include
            android:id="@+id/layout_message_bottom"
            layout="@layout/chat_layout_message_bottom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            app:chatType="@{viewModel.getType()}"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/sdv_sticker_send"
            app:layout_constraintTop_toBottomOf="@+id/sdv_sticker_send"
            app:msg="@{msg}" />

        <include
            layout="@layout/chat_item_message_time_annotation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:bindMsgId="@{msg.mid}"
            app:layout_constraintEnd_toEndOf="@id/layout_message_bottom"
            app:layout_constraintTop_toBottomOf="@id/layout_message_bottom"
            app:timeAnnotationMsg="@{viewModel.messageNeedTimeAnnotation}" />
    </com.twl.hi.basic.views.InterceptConstraintLayout>

</layout>