<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="date"
            type="String" />

        <variable
            name="visibility"
            type="Integer" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="@{visibility}">

        <View
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:background="@color/divider_color" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:text="@{date}"
            android:textColor="#9b9b9b"
            app:scaledTextSize="@{12}" />

        <View
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:background="@color/divider_color" />

    </LinearLayout>

</layout>