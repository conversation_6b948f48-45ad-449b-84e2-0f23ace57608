<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.ChatMessage" />

        <variable
            name="listener"
            type="com.twl.hi.chat.callback.MarkListCallback" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:textColor="@color/color_9999A3"
            app:markTime="@{msg.markTime}"
            app:scaledTextSize="@{12}"
            tools:text="11:02" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxWidth="@dimen/dp_240"
            android:maxLines="1"
            android:textColor="@color/color_9999A3"
            app:markMessage="@{msg.markUserId}"
            app:scaledTextSize="@{12}"
            tools:text="张三标记了这条消息" />
    </LinearLayout>
</layout>