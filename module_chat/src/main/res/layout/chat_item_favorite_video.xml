<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="bean"
            type="com.twl.hi.foundation.api.response.bean.FavoriteInterface" />

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.MessageForVideo" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.message.viewmodel.FavoriteListBaseViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.message.callback.FavoriteListCallback" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:clickable="true"
        android:focusable="true"
        android:onLongClick="@{()->callback.onLongClick(bean)}"
        android:orientation="vertical"
        android:paddingLeft="20dp"
        android:paddingTop="20dp"
        android:paddingRight="20dp"
        app:callback="@{callback}"
        app:favorId="@{bean.favorId}"
        app:showFavoriteHighLight="@{viewModel.showFavoriteHighLight}">


        <FrameLayout
            android:id="@+id/content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="57dp"
            android:minHeight="52dp"
            android:onClick="@{(view)->callback.onChatClick(view,bean)}"
            android:onLongClick="@{()->callback.onLongClick(bean)}"
            app:layout_constraintStart_toStartOf="@id/tv_name"
            app:layout_constraintTop_toTopOf="parent">

            <com.facebook.drawee.view.SimpleDraweeView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:placeholderImage="@color/color_CFCFCF"
                app:roundedCornerRadius="6dp"
                app:roundingBorderColor="@color/color_D5D5D9"
                app:roundingBorderWidth="0.5dp"
                app:videoThumbnail="@{msg}" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@mipmap/chat_ic_broadcast" />

        </FrameLayout>


        <include
            layout="@layout/chat_item_favorite_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:bean="@{bean}"
            app:callback="@{callback}"
            app:viewModel="@{viewModel}" />
    </LinearLayout>
</layout>