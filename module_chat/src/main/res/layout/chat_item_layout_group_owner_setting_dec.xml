<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="hi.kernel.Constants" />

        <variable
            name="pos"
            type="Integer" />

        <variable
            name="model"
            type="com.twl.hi.chat.model.GroupOwnerSettingModel" />

        <variable
            name="checkChange"
            type="android.view.View.OnTouchListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="20dp"
        android:layout_marginTop="10dp"
        android:background="@color/app_white">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{model.name}"
            android:textColor="@color/color_0D0D1A"
            android:textSize="15sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Switch
            android:id="@+id/switch_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="@{model.status}"
            android:onTouchListener="@{checkChange}"
            android:thumb="@drawable/switch_thumb"
            android:track="@drawable/switch_track"
            android:tag="@{pos}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="#B1B1B8"
            android:text="@string/chat_group_owner_public_setting_tip"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/switch_btn"
            app:layout_constrainedWidth="true"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="20dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
