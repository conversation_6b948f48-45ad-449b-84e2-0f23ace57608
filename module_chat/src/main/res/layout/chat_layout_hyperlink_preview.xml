<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/preview_image"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:actualImageScaleType="fitCenter"
        app:failureImage="@drawable/chat_ic_zhishu_preview_error"
        app:layout_constraintDimensionRatio="2:1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:placeholderImage="@drawable/ic_icon_content_empty" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/preview_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/logo"
            android:layout_width="21dp"
            android:layout_height="21dp"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"
            app:placeholderImage="@drawable/chat_ic_icon_url_parse_default"
            app:roundedCornerRadius="3dp" />

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/color_0D0D1A"
            android:textSize="15sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/logo"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="这是链接的标题预览区域，最多可以显示两行的文本，超过两行后使用省略号表示还有更多内容" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="logo,title" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="6dp"
            android:background="@color/color_f0f0f0"
            app:layout_constraintTop_toBottomOf="@id/barrier" />

        <TextView
            android:id="@+id/description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:ellipsize="end"
            android:maxLines="5"
            android:textColor="@color/color_656A73"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider"
            tools:text="这是链接的描述预览区域，最多可以显示五行的文本，超过五行后使用省略号表示还有更多内容" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>
