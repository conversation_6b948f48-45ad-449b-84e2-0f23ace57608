<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.twl.hi.foundation.utils.ContactUtils" />

        <import type="com.twl.hi.basic.PageConstantsKt" />

        <variable
            name="callback"
            type="com.twl.hi.chat.callback.SpecialAttentionCallback" />

        <variable
            name="user"
            type="com.twl.hi.chat.model.SpecialAttendanceBean" />

        <variable
            name="itemCount"
            type="Integer" />

        <variable
            name="itemPos"
            type="Integer" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:orientation="horizontal"
        android:paddingVertical="10dp"
        android:paddingStart="20dp"
        app:itemCount="@{itemCount}"
        app:itemPos="@{itemPos}">

        <include
            android:id="@+id/item_avatar"
            layout="@layout/item_avatar2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:avatarContact="@{user.contact}"
            app:pageFrom="@{PageConstantsKt.GROUP_CONVERSATION_SETTING_PAGE_FOLLOW_MEMBER}" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="5dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:onClick="@{()->callback.onSpecialAttentionItemClick(user)}"
            android:singleLine="true"
            android:textColor="@color/color_0D0D1A"
            android:textSize="17sp"
            app:hideDept="@{false}"
            app:userSelectContent="@{user.contact}"
            tools:text="李诗瑶 - 设计二组- 设计三组" />

        <com.twl.hi.basic.views.CommonTextView
            android:id="@+id/tv_label"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:gravity="center_vertical"
            android:onClick="@{()->callback.onSpecialAttentionItemClick(user)}"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:textSize="12sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:btn_roundRadius="3dp"
            tools:background="@color/color_6176E7"
            tools:text="管理员"
            tools:textColor="@color/app_white"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/cb_user"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="right|center_vertical"
            android:layout_marginStart="5dp"
            android:onClick="@{()->callback.onSpecialAttentionItemDelClick(user)}"
            android:paddingVertical="10dp"
            android:paddingLeft="10dp"
            android:paddingRight="20dp"
            android:src="@drawable/hd_icon_delete_red" />
    </LinearLayout>
</layout>
