<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.MessageForUserCard" />

        <variable
            name="listener"
            type="com.twl.hi.basic.callback.ChatItemListener" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.ChatRecordViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <include
            android:id="@+id/vg_duration"
            layout="@layout/chat_view_chat_record_duration"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:date="@{viewModel.getDate(msg.time)}"
            app:visibility="@{viewModel.showTitleDate(msg.mid)}" />

        <include
            android:id="@+id/user_avatar"
            layout="@layout/item_avatar2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="12dp"
            app:avatarContact="@{viewModel.getContactAvatar(msg.sender.senderId)}"
            app:groupRobot="@{viewModel.getGroupRobot(msg.sender.senderId)}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vg_duration"
            app:visibleInVisible="@{viewModel.showContactAvatar(msg.mid)}" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{viewModel.getChatName(msg)}"
            android:textColor="#9B9B9B"
            app:scaledTextSize="@{14}"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@+id/tv_time"
            app:layout_constraintStart_toEndOf="@+id/user_avatar"
            app:layout_constraintTop_toTopOf="@+id/user_avatar"
            tools:text="特别长的发送人姓名特别长的发送人姓名特别长的发送人姓名特别长的发送人姓名" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="12dp"
            android:textColor="#9B9B9B"
            app:scaledTextSize="@{14}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_name"
            app:setTime="@{msg.time}"
            tools:text="11:23" />

        <include
            android:id="@+id/content"
            layout="@layout/chat_view_message_card_receive"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="@dimen/chat_record_message_item_right_width"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="@+id/tv_name"
            app:layout_constraintTop_toBottomOf="@id/tv_name"
            app:listener="@{listener}"
            app:max_width_percent="@{msg.getMaxWidthPercent()}"
            app:msg="@{msg}"
            app:viewModel="@{viewModel}" />

        <View
            android:layout_width="0dp"
            android:layout_height="1px"
            android:layout_marginTop="20dp"
            android:background="#ffcfcfcf"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tv_name"
            app:layout_constraintTop_toBottomOf="@id/content" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>