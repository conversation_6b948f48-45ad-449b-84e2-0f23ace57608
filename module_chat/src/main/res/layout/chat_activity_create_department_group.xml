<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".chat.message.CreateDepartmentGroupActivity">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.chat.message.viewmodel.CreateDepartmentGroupViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.message.callback.CreateDepartmentGroupCallback" />
    </data>


    <LinearLayout
        android:id="@+id/activityMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:orientation="vertical">

        <include
            app:left='@{" "}'
            layout="@layout/title_bar"
            app:callback="@{callback}"
            app:title="@{@string/chat_create_group}" />

        <TextView
            android:paddingTop="18dp"
            android:paddingBottom="8dp"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:background="@color/color_F9F9FA"
            android:id="@+id/actionTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/color_9999A3"
            android:text="@string/chat_has_no_group_department_list" />

        <androidx.recyclerview.widget.RecyclerView
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />
    </LinearLayout>


</layout>