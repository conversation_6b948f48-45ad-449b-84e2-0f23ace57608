<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.MessageForUserCard" />

        <variable
            name="listener"
            type="com.twl.hi.basic.callback.ChatItemListener" />

        <variable
            name="showReply"
            type="Boolean" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.ChatBaseViewModel" />

    </data>

    <LinearLayout
        android:id="@+id/user_card_cl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_corner_8_chat_message_to"
        android:orientation="vertical"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/chat_message_item_padding_top"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/chat_message_item_padding_bottom">

        <include
            android:id="@+id/message_quote"
            layout="@layout/chat_view_message_reply_quote_content"
            app:contentColor="@{@color/color_81818A}"
            app:dividerColor="@{@color/color_BDC0D0}"
            app:msg="@{msg}"
            app:replyListener="@{listener}"
            app:showReply="@{showReply}" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:chatListener="@{listener}"
            app:chatMessage="@{msg}"
            tools:showIn="@layout/chat_item_message_user_card_send">

            <include
                android:id="@+id/user_card_avatar"
                layout="@layout/item_avatar2"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="0dp"
                android:layout_marginRight="12dp"
                app:avatarCardContact="@{msg}"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_user_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="@{viewModel.getUserCardTitle(msg.userId)}"
                android:textColor="@color/color_text_chat_message"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@+id/user_card_avatar"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_default="wrap"
                app:scaledTextSize="@{17}"
                tools:text="周盛华周盛华周盛华周盛华周盛华周盛华周盛华" />

            <TextView
                android:id="@+id/tv_user_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:singleLine="true"
                android:text="@{viewModel.getUserCardContent(msg.userId)}"
                android:textColor="@color/color_81818A"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toRightOf="@+id/user_card_avatar"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_user_name"
                app:scaledTextSize="@{13}"
                tools:text="高级产品经理经理-FE二组" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            layout="@layout/chat_layout_message_emoji_reply"
            app:listener="@{listener}"
            app:msg="@{msg}"
            app:showReply="@{showReply}" />
    </LinearLayout>
</layout>