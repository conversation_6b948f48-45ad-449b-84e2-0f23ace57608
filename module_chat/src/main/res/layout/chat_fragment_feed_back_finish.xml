<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
                name="callback"
                type="com.twl.hi.chat.callback.FeedbackFinishCallback" />
        <variable
                name="viewModel"
                type="com.twl.hi.chat.viewmodel.FeedBackFinishViewModel" />
    </data>

    <com.twl.hi.basic.views.HiShadowFrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/app_white"
            app:lyt_topLeftRadius="20dp"
            app:lyt_topRightRadius="20dp">
        <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="320dp"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:paddingTop="12dp"
                >

            <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="搜索反馈"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/app_black"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    />

            <ImageView
                    android:id="@+id/close_icon"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:onClick="@{v -> callback.onClose()}"
                    android:src="@drawable/chat_ic_feedback_close"
                    />

            <ImageView
                    android:id="@+id/icon"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintVertical_bias="0.25"
                    android:src="@drawable/ic_toast_success_ok"/>

            <TextView
                    app:layout_constraintTop_toBottomOf="@id/icon"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginTop="24dp"
                    android:textSize="18sp"
                    android:textColor="@color/app_black"
                    android:textStyle="bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="感谢你的反馈！"/>

            <TextView
                    app:layout_constraintTop_toBottomOf="@id/icon"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginTop="60dp"
                    android:layout_width="wrap_content"
                    android:textSize="14sp"
                    android:layout_height="wrap_content"
                    android:text="我们会尽快升级你的搜索体验"/>

            <Button
                    android:id="@+id/close"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginTop="6dp"
                    android:onClick="@{v -> callback.onClose()}"
                    android:layout_marginBottom="15dp"
                    android:background="@drawable/chat_btn_bg_blue"
                    android:textColor="@color/app_white"
                    android:text="关闭"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.twl.hi.basic.views.HiShadowFrameLayout>
</layout>