<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="bean"
            type="com.twl.hi.foundation.api.response.bean.FavoriteInterface" />

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.extensioncard.MessageForExtensionCard" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.message.viewmodel.FavoriteListBaseViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.message.callback.FavoriteListCallback" />

        <variable
            name="msgContentClickListener"
            type="com.twl.hi.chat.callback.MsgContentClickListener" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:clickable="true"
        android:focusable="true"
        android:onClick="@{(view)->callback.onChatClick(view,bean)}"
        android:onLongClick="@{()->callback.onLongClick(bean)}"
        android:orientation="vertical"
        android:paddingTop="20dp"
        app:callback="@{callback}"
        app:favorId="@{bean.favorId}"
        app:showFavoriteHighLight="@{viewModel.showFavoriteHighLight}">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <include
                layout="@layout/chat_view_message_extension_card"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                app:enable="@{false}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintWidth_percent="0.7"
                app:msg="@{msg}"
                app:msgContentClickListener="@{msgContentClickListener}"
                app:msgContentWidth="@{viewModel.getExtCardMsgWidth()}"
                app:replicable="@{false}"
                app:showReply="@{false}" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            layout="@layout/chat_item_favorite_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="8dp"
            android:layout_marginRight="20dp"
            app:bean="@{bean}"
            app:callback="@{callback}"
            app:viewModel="@{viewModel}" />

    </LinearLayout>
</layout>