<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activityViewModel"
            type="com.twl.hi.chat.chatrecord.history.viewmodel.ChatRecordHistorySearchViewModel" />

        <variable
            name="callback"
            type="android.view.View.OnClickListener" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="15dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/tv_sender"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:background="@{activityViewModel.isSenderFill() ? @drawable/bg_filter_bt_not_empty : @drawable/bg_filter_bt_empty}"
                    android:textColor="@{activityViewModel.isSenderFill() ? @color/color_text_primary  : @color/color_4C596A}"
                    android:textSize="14sp"
                    tools:text="发送人" />

                <TextView
                    android:id="@+id/tv_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@{activityViewModel.isPeriodFill() ? @drawable/bg_filter_bt_not_empty : @drawable/bg_filter_bt_empty}"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@{activityViewModel.isPeriodFill() ? @color/color_text_primary  : @color/color_4C596A}"
                    android:textSize="14sp"
                    tools:text="发送时间" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_reset"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:enabled="@{activityViewModel.resetEnable}"
                android:text="@string/reset"
                android:textColor="@color/common_text_color_primary_a7adb5"
                android:textSize="14sp" />

        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="15dp">

            <LinearLayout
                android:id="@+id/ll_no_result"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="140dp"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                app:visibleGone="@{activityViewModel.isNoSearchResult}">

                <ImageView
                    android:id="@+id/image_no_result"
                    android:layout_width="88dp"
                    android:layout_height="88dp"
                    android:layout_marginBottom="10dp"
                    android:src="@drawable/chat_ic_not_search_result" />

                <TextView
                    android:id="@+id/text_no_result"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/chat_no_search_result"
                    android:textColor="#878F9B"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_no_search"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="140dp"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/image_no_search"
                    android:layout_width="88dp"
                    android:layout_height="88dp"
                    android:layout_marginBottom="10dp"
                    android:src="@drawable/chat_ic_history_chat_mg" />

                <TextView
                    android:id="@+id/text_no_search"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/chat_search_message_record_of_conversation"
                    android:textColor="#878F9B"
                    android:textSize="14sp" />

            </LinearLayout>

            <com.scwang.smartrefresh.layout.SmartRefreshLayout
                android:id="@+id/smart_refresh_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:srlEnableAutoLoadMore="false">

                <lib.twl.common.views.CloseKeyboardRecyclerView
                    android:id="@+id/rv_chat_record_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </com.scwang.smartrefresh.layout.SmartRefreshLayout>
        </FrameLayout>

    </LinearLayout>
</layout>