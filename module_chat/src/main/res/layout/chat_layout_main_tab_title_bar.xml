<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.twl.utils.status.WorkStatusBean" />

        <import type="android.text.TextUtils" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.message.viewmodel.ConversationMainTabFragmentViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.message.callback.ConversationMainTabFragmentCallback" />

        <variable
            name="user"
            type="com.twl.hi.foundation.model.User" />

        <variable
            name="connection"
            type="String" />

        <variable
            name="workStatusIcon"
            type="String" />

        <variable
            name="workStatusDescription"
            type="String" />

        <variable
            name="workStatusVisible"
            type="boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:gravity="center_vertical"
            android:onClick="@{()->callback.openLeftDraw()}"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:id="@+id/avatarContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <include
                    android:id="@+id/iv_head"
                    layout="@layout/item_avatar2"
                    android:layout_width="33dp"
                    android:layout_height="33dp"
                    app:avatarUser="@{user}" />

                <ImageView
                    android:id="@+id/img_other_company_unread"
                    android:layout_width="7dp"
                    android:layout_height="7dp"
                    android:layout_gravity="right|top"
                    android:background="@drawable/bg_circle_color_fb4f63"
                    app:visibleGone="@{viewModel.showOtherCompanyUnreadTip}" />
            </FrameLayout>

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:ellipsize="end"
                android:gravity="center"
                android:maxWidth="280dp"
                android:maxLines="1"
                android:text="@string/chat_message"
                android:textColor="#212121"
                android:textSize="22sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/avatarContainer"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/connect_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{connection}"
                android:textColor="#212121"
                android:textSize="11sp"
                android:textStyle="bold"
                app:layout_constraintBaseline_toBaselineOf="@id/title"
                app:layout_constraintStart_toEndOf="@id/title"
                tools:text="(链接中...)" />

            <ImageView
                android:id="@+id/sdv_status"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginStart="10dp"
                app:imageUrlWrap="@{workStatusIcon}"
                app:layout_constraintBottom_toBottomOf="@+id/tv_work_status"
                app:layout_constraintStart_toEndOf="@id/connect_status"
                app:layout_constraintTop_toTopOf="@+id/tv_work_status"
                app:resId="@{TextUtils.isEmpty(workStatusIcon) ? WorkStatusBean.statuesDefaultId : 0}"
                app:visibleGone="@{workStatusVisible}" />

            <TextView
                android:id="@+id/tv_work_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:drawableEnd="@drawable/ic_icon_gray_arrow_right_s"
                android:drawablePadding="4dp"
                android:singleLine="true"
                android:text="@{workStatusDescription}"
                android:textColor="@color/color_818188"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/sdv_status"
                app:layout_constraintTop_toTopOf="parent"
                app:visibleGone="@{workStatusVisible}"
                tools:text="@string/me_add_work_statues" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/img_small_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="15dp"
            android:onClick="@{()->callback.onSearchClick()}"
            android:src="@drawable/ic_icon_search_small"
            app:layout_constraintBottom_toBottomOf="@+id/img_show_more"
            app:layout_constraintEnd_toStartOf="@+id/img_show_more"
            app:layout_constraintTop_toTopOf="@+id/img_show_more" />

        <ImageView
            android:id="@+id/img_show_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="16dp"
            android:onClick="@{()->callback.onMoreClick()}"
            android:src="@drawable/chat_bg_pres_icon_more"
            app:layout_constraintBottom_toBottomOf="@id/ll_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ll_title" />

        <View
            android:layout_width="5dp"
            android:layout_height="5dp"
            android:layout_gravity="end"
            android:background="@drawable/bg_circle_color_fb4f63"
            app:layout_constraintEnd_toEndOf="@+id/img_show_more"
            app:layout_constraintTop_toTopOf="@+id/img_show_more"
            app:visibleGone="@{viewModel.canCreateDepartmentGroup > 0 &amp;&amp; viewModel.addRedNum > 0}" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>