<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".chat.GroupOwnerSettingActivity">

    <data>

        <import type="android.view.View" />

        <import type="hi.kernel.Constants" />

        <import type="com.twl.utils.StringUtils" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.GroupOwnerSettingViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.callback.GroupOwnerSettingCallback" />
    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            app:callback="@{callback}"
            app:left='@{" "}'
            app:title="@{@string/chat_group_owner_setting_title}" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/color_F9F9FA" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="@color/color_F9F9FA"
                    android:visibility="@{viewModel.adminRole != Constants.TYPE_GROUP_CREATE ? View.GONE : View.VISIBLE}" />

                <TextView
                    android:id="@+id/tv_transfer"
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/sel_item_user"
                    android:drawableRight="@drawable/ic_icon_gray_arrow_right_s"
                    android:gravity="center_vertical"
                    android:onClick="@{()->callback.groupTransfer()}"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp"
                    android:text="@string/chat_group_transfer"
                    android:textColor="@color/color_0D0D1A"
                    android:textSize="15sp"
                    android:visibility="@{viewModel.adminRole != Constants.TYPE_GROUP_CREATE || StringUtils.isNotEquals(viewModel.deptId, Constants.NOT_DEPARTMENT_GROUP) ? View.GONE : View.VISIBLE}" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:background="@drawable/sel_item_user"
                    android:onClick="@{()->callback.adminSetting()}"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp"
                    android:visibility="@{viewModel.adminRole != Constants.TYPE_GROUP_CREATE ? View.GONE : View.VISIBLE}">

                    <TextView
                        android:id="@+id/label_group_manager"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:text="@string/chat_admin_setting"
                        android:textColor="@color/color_0D0D1A"
                        android:textSize="15sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_managers_avatar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toBottomOf="@id/label_group_manager"
                        app:layout_constraintEnd_toStartOf="@id/group_manager_settings_description"
                        app:layout_constraintHorizontal_bias="1"
                        app:layout_constraintStart_toEndOf="@id/label_group_manager"
                        app:layout_constraintTop_toTopOf="@id/label_group_manager"
                        tools:listitem="@layout/chat_item_group_member" />

                    <TextView
                        android:id="@+id/group_manager_settings_description"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@drawable/ic_icon_gray_arrow_right_s"
                        android:drawablePadding="9dp"
                        android:ellipsize="end"
                        android:singleLine="true"
                        android:text="@{viewModel.managerCount == 0 ? @string/chat_goto_editing : @string/chat_person_unit(viewModel.managerCount)}"
                        android:textColor="@color/color_9999A3"
                        android:textColorHint="@color/color_9999A3"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="@id/label_group_manager"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/label_group_manager" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="@color/color_f8f8f8" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:background="@drawable/sel_item_user"
                    android:onClick="@{()->callback.clickGroupHistoryMessageVisible()}"
                    android:orientation="horizontal"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:text="@string/chat_group_newbee_history_message_visible"
                        android:textColor="@color/color_0D0D1A"
                        android:textSize="15sp" />

                    <TextView
                        android:id="@+id/tv_look_type"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:drawableRight="@drawable/ic_icon_gray_arrow_right_s"
                        android:drawablePadding="9dp"
                        android:gravity="center_vertical|right"
                        android:textColor="@color/color_9999A3"
                        android:textSize="14sp"
                        tools:text="不可查看" />

                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:minHeight="78dp"
                    android:background="@color/color_f8f8f8" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/bg_corner_7_color_f2f2f5"
            android:gravity="center"
            android:onClick="@{()->callback.dismissGroupClick()}"
            android:text="@{viewModel.getDisbandGroupStr}"
            android:textColor="@color/color_FE5150"
            android:textSize="17sp"
            android:visibility="@{viewModel.adminRole != Constants.TYPE_GROUP_CREATE? View.GONE : View.VISIBLE}"
            app:btn_strokeColor="@color/color_484CBD"
            app:btn_strokeWidth="1dp"
            tools:text="@string/chat_dismiss_group"/>
    </LinearLayout>


</layout>