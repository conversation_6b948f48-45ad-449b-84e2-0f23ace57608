<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.twl.hi.basic.views.InterceptConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/sel_item_user"
        app:intercept="@{true}">

        <include
            android:id="@+id/vg_avatar"
            layout="@layout/item_avatar2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:singleLine="true"
            android:textColor="#9B9B9B"
            app:scaledTextSize="@{14}"
            app:layout_constraintBottom_toTopOf="@+id/text_content"
            app:layout_constraintEnd_toStartOf="@+id/tv_time"
            app:layout_constraintStart_toEndOf="@+id/vg_avatar"
            app:layout_constraintTop_toTopOf="@+id/vg_avatar"
            tools:text="李李" />

        <TextView
            android:id="@+id/tv_group_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="12dp"
            android:gravity="left"
            android:singleLine="true"
            android:textColor="#9B9B9B"
            app:scaledTextSize="@{14}"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/vg_avatar"
            app:layout_constraintTop_toBottomOf="@+id/text_content"
            tools:text="twl服务群" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="12dp"
            android:paddingLeft="10dp"
            android:singleLine="true"
            android:textColor="#9B9B9B"
            app:scaledTextSize="@{14}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/tv_name"
            app:layout_constraintTop_toTopOf="@+id/tv_name"
            tools:text="11:2311:23" />

        <com.twl.hi.chat.widget.TextMessageLayout
            android:id="@+id/text_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginRight="3dp"
            app:layout_constraintEnd_toStartOf="@+id/v_un_read"
            app:layout_constraintStart_toStartOf="@+id/tv_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_name"
            app:layout_goneMarginRight="12dp"
            app:expanded="@{false}" />

        <View
            android:id="@+id/v_un_read"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:layout_marginRight="12dp"
            android:background="@drawable/bg_circle_color_fb4f63"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/text_content" />

        <View
            android:layout_width="0dp"
            android:layout_height="1px"
            android:layout_marginTop="10dp"
            android:background="@color/color_E6E7EB"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/text_content"
            app:layout_constraintTop_toBottomOf="@+id/tv_group_name" />

    </com.twl.hi.basic.views.InterceptConstraintLayout>
</layout>