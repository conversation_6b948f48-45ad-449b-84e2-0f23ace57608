<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.twl.hi.foundation.model.message.MessageConstants" />

        <import type="android.text.TextUtils" />

        <import type="lib.twl.common.util.LList" />

        <import type="android.view.View" />

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.MessageForTaskCommentCard" />

        <variable
            name="listener"
            type="com.twl.hi.basic.callback.ChatItemListener" />

    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/chat_bg_system_card"
        android:paddingLeft="12dp"
        android:paddingTop="12dp"
        android:paddingRight="15dp"
        android:paddingBottom="15dp"
        app:chatListener="@{listener}"
        app:chatMessage="@{msg}">

        <TextView
            android:id="@+id/tv_type"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingVertical="2dp"
            android:singleLine="true"
            android:text="@{msg.cardTypeShow.text}"
            android:textColor="@color/color_5D6FE8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_top_btn"
            app:layout_constraintTop_toTopOf="parent"
            app:scaledTextSize="@{12}"
            app:visibleGone="@{!TextUtils.isEmpty(msg.cardTypeShow.text)}"
            tools:text="XX创建了日" />


        <com.twl.hi.basic.views.CommonTextView
            android:id="@+id/tv_top_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick='@{()->listener.onRichButton(msg.topButtons.size() > 0 ? msg.topButtons.get(0).protocol : "" ,msg.mid)}'
            android:paddingVertical="2dp"
            android:paddingLeft="13dp"
            android:paddingRight="13dp"
            android:textColor="@color/color_5D6FE8"
            app:btn_roundRadius="13dp"
            app:btn_strokeColor="@color/color_5D6FE8"
            app:btn_strokeWidth="1dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/tv_type"
            app:layout_constraintTop_toTopOf="parent"
            app:position="@{0}"
            app:scaledTextSize="@{12}"
            app:systemRichButton="@{msg.topButtons}"
            tools:text="认领"
            tools:visibility="visible" />

        <com.twl.hi.basic.views.multitext.CommonIconTextView
            android:id="@+id/tv_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:ellipsize="end"
            android:lineSpacingExtra="5dp"
            android:maxLines="3"
            android:text="@{msg.cardContent.text}"
            android:textColor="@color/color_0D0D1A"
            android:textStyle="bold"
            app:agreements="@{msg.cardContent.agreements}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_type"
            app:messageType="@{msg.mediaType}"
            app:scaledTextSize="@{17}"
            app:setCommonIconText="@{msg.cardContent.text}"
            app:setHintCallback="@{listener}"
            app:textSize="@{17}"
            app:visibleGone="@{!TextUtils.isEmpty(msg.cardContent.text)}"
            tools:text="XX创建了日XX创建了日XX创建了日XX创建了日XX创建了日XX创建了日XX创建了日XX创建了日XX创建了日XX创建了日XX创建了日XX创建了日XX创建了日XX创建了日" />


        <TextView
            android:id="@+id/tv_task_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:textColor="@color/color_9999A3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_content"
            app:scaledTextSize="@{12}"
            app:taskCardTaskInfo="@{msg.taskInfo}"
            tools:text="时间" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_comment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_task_info"
            app:visibleGone="@{msg.taskComments.size() > 0}">

            <View
                android:id="@+id/view_line"
                android:layout_width="1dp"
                android:layout_height="0dp"
                android:background="@color/color_E1E1E5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.twl.hi.basic.views.multitext.CommonIconTextView
                android:id="@+id/tv_comment"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:singleLine="true"
                android:textColor="@color/color_B1B1B8"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:messageType="@{msg.mediaType}"
                app:scaledTextSize="@{12}"
                app:setHintCallback="@{listener}"
                app:setTaskComment="@{msg.taskComments}"
                app:textSize="@{12}"
                tools:text="琳娜：我觉得应该检查下权限问题我觉得应该检查下权限问题我觉得应该检查下权限问题我觉得应该检查下权限问题" />

            <TextView
                android:id="@+id/tv_comment_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="2dp"
                android:text="@string/chat_comment_more_time"
                android:textColor="@color/color_D2D2DB"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_comment"
                app:scaledTextSize="@{12}"
                app:visibleGone="@{msg.taskComments.size() > 1}" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.twl.hi.basic.views.CommonTextView
            android:id="@+id/tv_bottom_comment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center_vertical"
            android:onClick="@{v->listener.clickTaskComment(msg)}"
            android:paddingVertical="4dp"
            android:paddingLeft="9dp"
            android:text="@string/chat_click_comment"
            android:textColor="@color/color_B1B1B8"
            app:btn_roundRadius="3dp"
            app:btn_strokeColor="@color/color_E1E1E5"
            app:btn_strokeWidth="1dp"
            app:layout_constraintTop_toBottomOf="@+id/cl_comment"
            app:scaledTextSize="@{12}"
            app:visibleGone="@{msg.openComment}"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>