<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.text.TextUtils" />

        <variable
            name="bean"
            type="com.twl.hi.foundation.api.response.bean.FavoriteInterface" />

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.MessageForRichText" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.message.viewmodel.FavoriteListBaseViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.message.callback.FavoriteListCallback" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:onClick="@{(view)->callback.onChatClick(view,bean)}"
        android:onLongClick="@{()->callback.onLongClick(bean)}"
        android:orientation="vertical"
        android:paddingHorizontal="20dp"
        android:paddingTop="20dp"
        app:callback="@{callback}"
        app:favorId="@{bean.favorId}"
        app:showFavoriteHighLight="@{viewModel.showFavoriteHighLight}">

        <!-- 富文本消息展示 -->
        <com.twl.hi.chat.widget.TextMessageLayout
            android:id="@+id/rl_rich_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:expandListener="@{() -> viewModel.expandMessage(msg)}"
            app:expanded="@{viewModel.expandedMessages.contains(msg.getMid())}"
            app:favoriteCallback="@{callback}"
            app:favoriteMessage="@{bean}"
            app:setSpanableText="@{msg}"
            app:textLinkListener="@{callback}" />

        <include
            android:id="@+id/bottom"
            layout="@layout/chat_item_favorite_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:bean="@{bean}"
            app:callback="@{callback}"
            app:viewModel="@{viewModel}" />
    </LinearLayout>
</layout>