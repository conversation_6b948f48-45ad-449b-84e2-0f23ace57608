<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <import type="com.twl.hi.foundation.utils.ContactUtils" />

        <import type="com.twl.hi.foundation.model.message.MessageConstants" />

        <import type="com.twl.hi.basic.util.ThemeUtils" />

        <variable
            name="cwm"
            type="com.twl.hi.foundation.model.ConversationWithMessage" />

        <variable
            name="conversationContent"
            type="androidx.lifecycle.LiveData&lt;CharSequence>" />

        <variable
            name="simpleLayout"
            type="Boolean" />
        
        <variable
            name="aiRobot"
            type="Integer" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@{(!simpleLayout &amp;&amp; cwm.conversation.top &amp;&amp; !TextUtils.equals(MessageConstants.ID_COLLAPSED_CONVERSATION,cwm.conversation.chatId))?@drawable/sel_item_user_top:@drawable/sel_item_user}"
        android:baselineAligned="false"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp">

        <include
            android:id="@+id/vg_avatar"
            layout="@layout/item_avatar2"
            android:layout_width="48dp"
            android:layout_height="48dp"
            app:avatarConversation="@{cwm.profile}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="-4dp"
            android:layout_marginEnd="-4dp"
            android:background="@drawable/bg_corner_12_color_fb4f63"
            android:gravity="center"
            android:minWidth="18dp"
            android:minHeight="18dp"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:text='@{cwm.unreadCount}'
            android:textAlignment="center"
            android:textColor="@color/app_white"
            app:layout_constraintEnd_toEndOf="@id/vg_avatar"
            app:layout_constraintTop_toTopOf="@id/vg_avatar"
            app:scaledTextSize="@{11}"
            app:visibleGone="@{cwm.showUnreadCount()}"
            tools:text="9"
            tools:visibility="gone" />

        <TextView
            android:id="@+id/tv_no_count"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_marginEnd="2dp"
            android:layout_marginTop="2dp"
            android:background="@drawable/bg_circle_color_fb4f63"
            app:layout_constraintEnd_toEndOf="@+id/vg_avatar"
            app:layout_constraintTop_toTopOf="@+id/vg_avatar"
            app:visibleGone="@{cwm.showUnreadPoint()}"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:singleLine="true"
            android:text="@{ContactUtils.getDisplayNameByConversation(cwm.conversation.type, cwm.profile, false)}"
            android:textColor="#0A1B33"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toTopOf="@id/tv_msg_content"
            app:layout_constraintEnd_toStartOf="@id/tv_label"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/vg_avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:scaledTextSize="@{17}"
            tools:text="会话名称，仅显示一行，超过一行后末尾截断用省略号表示" />

        <TextView
            android:id="@+id/tv_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:background="@{ThemeUtils.useNewTheme ? @drawable/bg_corner_4_color_primary2 : @drawable/bg_corner_one_half_color_e7ecff}"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:textColor="@{ThemeUtils.getThemeTextColorInt()}"
            android:visibility="gone"
            app:groupChatTag="@{cwm.profile}"
            app:layout_constraintBottom_toBottomOf="@id/tv_name"
            app:layout_constraintEnd_toStartOf="@id/tv_ai_tag"
            app:layout_constraintStart_toEndOf="@id/tv_name"
            app:layout_constraintTop_toTopOf="@id/tv_name"
            app:scaledTextSize="@{11}"
            tools:background="@drawable/bg_corner_4_color_primary2"
            tools:text="全员"
            tools:textColor="@color/color_text_primary"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_ai_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="3dp"
            android:textColor="@color/color_6F44F2"
            android:textSize="11sp"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1dp"
            android:background="@drawable/bg_corner_4_296f44f2"
            app:visibleGone="@{cwm.profile!= null?cwm.profile.aiRobot == 1:false}"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="@+id/tv_name"
            app:layout_constraintBottom_toBottomOf="@+id/tv_name"
            app:layout_constraintEnd_toStartOf="@+id/tv_time"
            app:layout_constraintStart_toEndOf="@+id/tv_label"
            tools:visibility="visible"
            android:text="@string/title_ai_assistant" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_A7ADB5"
            app:lastTime="@{cwm.latestMessage.time}"
            app:layout_constraintBottom_toBottomOf="@id/tv_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_name"
            app:scaledTextSize="@{12}"
            app:visibleGone="@{!TextUtils.equals(cwm.conversation.chatId, MessageConstants.ID_COLLAPSED_CONVERSATION)}"
            tools:text="07：34" />

        <TextView
            android:id="@+id/tv_msg_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:layout_marginEnd="10dp"
            android:singleLine="true"
            android:text="@{conversationContent}"
            android:textColor="@color/color_9999A3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iv_remind"
            app:layout_constraintStart_toStartOf="@id/tv_name"
            app:layout_constraintTop_toBottomOf="@id/tv_name"
            app:scaledTextSize="@{14}"
            tools:text="会话内容，也仅显示一行，超过一行后末尾截断用省略号表示" />

        <ImageView
            android:id="@+id/iv_remind"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:src="@drawable/hd_icon_remind_close"
            app:layout_constraintBottom_toBottomOf="@+id/tv_msg_content"
            app:layout_constraintEnd_toStartOf="@+id/iv_mark"
            app:layout_constraintStart_toEndOf="@id/tv_msg_content"
            app:layout_constraintTop_toTopOf="@+id/tv_msg_content"
            app:visibleGone="@{cwm.conversation.silence}" />

        <ImageView
            android:id="@+id/iv_mark"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:src="@drawable/hd_icon_mark_filled"
            app:layout_constraintBottom_toBottomOf="@+id/tv_msg_content"
            app:layout_constraintEnd_toEndOf="@+id/tv_time"
            app:layout_constraintStart_toEndOf="@id/iv_remind"
            app:layout_constraintTop_toTopOf="@+id/tv_msg_content"
            app:visibleGone="@{cwm.conversation.marked}" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>