<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.BotIntroViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.basic.callback.TitleBarCallback" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white"
        android:orientation="vertical"
        tools:context=".chat.BotIntroActivity">

        <include
            layout="@layout/title_bar"
            app:callback="@{callback}"
            app:title="@{@string/chat_bot_intro_title}" />

        <TextView
            android:id="@+id/tv_intro"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="20dp"
            android:text="@{viewModel.intro}"
            android:textColor="@color/color_0D0D1A"
            android:textSize="16sp" />
    </LinearLayout>
</layout>