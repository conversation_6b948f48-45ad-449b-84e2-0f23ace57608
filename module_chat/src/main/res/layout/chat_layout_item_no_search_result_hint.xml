<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools">

    <TextView
            android:id="@+id/tv_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/chat_ic_search_no_result"
            android:drawablePadding="20dp"
            tools:text="@string/search_record_empty"
            android:textColor="@color/color_9B9B9B"
            android:layout_centerHorizontal="true"
            android:textSize="15sp"/>

    <TextView
            android:id="@+id/tv_feed_back"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="20sp"
            android:layout_marginEnd="20sp"
            android:gravity="center"
            android:text="@string/search_record_empty_reply_feedback"
            android:layout_marginTop="32dp"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:layout_below="@id/tv_empty"
            android:background="@drawable/chat_tv_bg_search_record_no_result" />

</RelativeLayout>