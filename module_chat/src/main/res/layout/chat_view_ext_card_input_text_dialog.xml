<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="callback"
            type="com.twl.hi.chat.messagecard.element.creator.ElementForTextInputViewCreator.TextInputDialogCallback" />

        <variable
            name="dialogViewModel"
            type="com.twl.hi.chat.messagecard.element.creator.ElementForTextInputViewCreator.InputTextDialogViewModel" />
    </data>

    <com.twl.hi.basic.views.HiShadowFrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_white"
        android:paddingTop="8dp"
        app:lyt_topLeftRadius="20dp"
        app:lyt_topRightRadius="20dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:onClick="@{v->callback.onInputCancel()}"
                android:src="@drawable/hd_icon_close"
                app:layout_constraintBottom_toBottomOf="@id/inputTextTitleTv"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/inputTextTitleTv" />

            <TextView
                android:id="@+id/inputTextTitleTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingVertical="8dp"
                android:text="请输入"
                android:textColor="@color/color_15181D"
                android:textSize="17sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:enabled="@{!dialogViewModel.inputTextFlow.empty}"
                android:onClick="@{v->callback.onInputFinish(dialogViewModel.inputTextFlow)}"
                android:text="确定"
                android:textColor="@{dialogViewModel.inputTextFlow.empty ? @color/color_primary_4 : @color/color_text_primary}"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="@id/inputTextTitleTv"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/inputTextTitleTv" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/color_F0F2F5"
                android:padding="20dp"
                app:layout_constraintTop_toBottomOf="@id/inputTextTitleTv">

                <com.twl.hi.basic.views.ClearEditText
                    android:id="@+id/textInputEt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/chat_bg_shape_color_white_radius_6"
                    android:drawablePadding="4dp"
                    android:hint="@string/please_input"
                    android:maxLines="3"
                    android:paddingVertical="9dp"
                    android:paddingStart="12dp"
                    android:paddingEnd="2dp"
                    android:scrollbars="vertical"
                    android:text="@={dialogViewModel.inputTextFlow}"
                    android:textColor="#000000"
                    android:textColorHint="#9B9B9B"
                    android:textCursorDrawable="@drawable/color_4a90e2_text_cursor"
                    android:textSize="14sp"
                    app:allowBlank="true"
                    app:icon_clear="@drawable/ic_icon_del_padding"
                    app:layout_constraintTop_toBottomOf="@id/inputTextTitleTv"
                    app:maxLength="2000" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.twl.hi.basic.views.HiShadowFrameLayout>
</layout>