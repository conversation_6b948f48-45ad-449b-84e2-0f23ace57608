<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.MessageForSticker" />

        <variable
            name="quote"
            type="com.twl.hi.foundation.model.message.ChatMessage" />

        <variable
            name="listener"
            type="com.twl.hi.basic.callback.ChatItemListener" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.ChatBaseViewModel" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:paddingLeft="20dp"
        android:paddingTop="16dp">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="@dimen/chat_reply_message_item_right_width"
            android:layout_marginBottom="2dp"
            android:singleLine="true"
            android:textColor="#9B9B9B"
            app:scaledTextSize="@{11}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:senderName="@{viewModel.getChatName(msg)}"
            app:showReply="@{msg.messageReply.replyId == msg.messageReply.topId ? false : true}"
            app:withReply="@{quote.sender.senderId}"
            tools:text="回复详情发送者名称回复详情发送者名称回复详情发送者名称回复详情发送者名称回复详情发送者名称回复详情发送者名称回复详情发送者名称回复详情发送者名称回复详情发送者名称回复详情发送者名称" />

        <include
            android:id="@+id/sdv_from_avatar"
            layout="@layout/item_avatar2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            app:avatarContact="@{viewModel.getContact(msg)}"
            app:avatarLongClick="@{(v)->listener.onLongAvatarClick(v,msg)}"
            app:groupRobot="@{viewModel.getGroupRobot(msg)}"
            app:layout_constraintEnd_toStartOf="@+id/tv_name"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginLeft="12dp" />

        <include
            android:id="@+id/view_message_reply"
            layout="@layout/chat_view_message_sticker_receive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="43dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="@+id/tv_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_name"
            app:listener="@{listener}"
            app:max_width_percent="@{msg.getMaxWidthPercent()}"
            app:msg="@{msg}"
            app:showDynamic="@{false}" />

        <include
            layout="@layout/chat_layout_reply_detail_item_more_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/view_message_reply"
            app:listener="@{listener}"
            app:msg="@{msg}" />

        <include
            android:id="@+id/layout_message_time"
            layout="@layout/chat_layout_message_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:count="@{msg.messageReply.replyId == msg.messageReply.topId ? msg.replyCount : 0}"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tv_name"
            app:layout_constraintTop_toBottomOf="@+id/view_message_reply"
            app:time="@{msg.time}" />


        <include
            android:id="@+id/layout_emoji"
            layout="@layout/chat_layout_message_emoji_reply"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            app:layout_constraintStart_toStartOf="@+id/tv_name"
            app:layout_constraintTop_toBottomOf="@+id/layout_message_time"
            app:listener="@{listener}"
            app:msg="@{msg}"
            app:showReply="@{true}" />

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/sdv_from_avatar"
            app:layout_constraintTop_toBottomOf="@+id/layout_emoji" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>