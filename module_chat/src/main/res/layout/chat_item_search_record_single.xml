<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="item"
            type="lib.twl.common.views.adapter.entity.MultiItemEntity" />
    </data>


    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_white"
        android:gravity="center"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/vg_avatar"
            android:layout_width="45dp"
            android:layout_height="45dp"
            app:avatarMultiItemEntity="@{item}">

            <com.facebook.drawee.view.SimpleDraweeView
                android:id="@+id/sdv_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:placeholderImage="@drawable/bg_text_avatar"
                app:roundAsCircle="true" />

            <TextView
                android:id="@+id/tv_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_text_avatar"
                android:gravity="center"
                android:textColor="@color/app_white"
                android:textSize="9sp"
                tools:text="Sa" />

            <TextView
                android:id="@+id/tv_bg_circle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_avatar_circle" />
        </FrameLayout>

        <TextView
            android:id="@+id/tv_username"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/color_1d2026"
            android:textSize="12sp"
            android:layout_marginBottom="12dp"
            app:textMultiItemEntity="@{item}"
            tools:text="1234567891011121314" />

    </LinearLayout>


</layout>
