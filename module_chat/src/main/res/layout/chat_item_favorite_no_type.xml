<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="bean"
            type="com.twl.hi.foundation.api.response.bean.FavoriteInterface" />

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.ChatMessage" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.message.viewmodel.FavoriteListBaseViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.message.callback.FavoriteListCallback" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:clickable="true"
        android:focusable="true"
        android:onLongClick="@{()->callback.onLongClick(bean)}"
        android:orientation="vertical"
        android:paddingLeft="20dp"
        android:paddingTop="20dp"
        android:paddingRight="20dp"
        app:callback="@{callback}"
        app:favorId="@{bean.favorId}"
        app:showFavoriteHighLight="@{viewModel.showFavoriteHighLight}">

        <TextView
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/chat_bg_message_from_text"
            android:text="@string/chat_un_support_message_type"
            app:scaledTextSize="@{14}" />

        <include
            layout="@layout/chat_item_favorite_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:bean="@{bean}"
            app:callback="@{callback}"
            app:viewModel="@{viewModel}" />
    </LinearLayout>
</layout>