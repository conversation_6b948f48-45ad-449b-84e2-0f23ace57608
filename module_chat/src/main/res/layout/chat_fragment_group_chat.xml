<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <variable
            name="callback"
            type="com.twl.hi.chat.callback.GroupChatPageCallback" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.GroupChatFragmentViewModel" />

    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <lib.twl.common.kpswitch.widget.KPSwitchRootLinearLayout
            android:id="@+id/mRootView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <include
                        android:id="@+id/title_bar"
                        layout="@layout/chat_title_bar_group"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        app:callback="@{callback}"
                        app:viewModel="@{viewModel}" />

                    <include
                        android:id="@+id/multiple_title_bar"
                        layout="@layout/chat_title_bar_chat"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        android:visibility="gone"
                        app:noBackground="@{true}" />

                    <com.twl.hi.chat.feature.chattab.entrance.ChatTabEntranceView
                        android:id="@+id/chatTabEntranceView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingStart="12dp"
                        android:paddingEnd="2dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        android:visibility="visible" />
                </LinearLayout>

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1">

                    <androidx.coordinatorlayout.widget.CoordinatorLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <include
                                android:id="@+id/message_container"
                                layout="@layout/chat_layout_message_container"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent" />

                            <!-- @表情雨 -->
                            <lib.twl.common.views.emojerain.RainViewGroup
                                android:id="@+id/rain_view"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <include
                            android:id="@+id/at_contacts"
                            layout="@layout/chat_view_at"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:behavior_hideable="true"
                            app:behavior_peekHeight="250dp"
                            app:layout_behavior="@string/bottom_sheet_behavior" />
                    </androidx.coordinatorlayout.widget.CoordinatorLayout>
                </FrameLayout>
            </LinearLayout>

            <include
                android:id="@+id/bottom_input_container"
                layout="@layout/chat_input_bottom_view"
                app:callback="@{callback}"
                app:viewModel="@{viewModel}" />

            <lib.twl.common.kpswitch.widget.KPSwitchPanelLinearLayout
                android:id="@+id/panel_root"
                android:layout_width="match_parent"
                android:layout_height="255dp"
                android:orientation="vertical"
                android:visibility="gone">

                <com.twl.hi.emotion.EmotionPanelView
                    android:id="@+id/sub_panel_emotion"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <com.twl.hi.chat.widget.funcview.ChatFuncView
                    android:id="@+id/sub_panel_func"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/app_white"
                    android:orientation="vertical" />

                <include
                    android:id="@+id/sub_panel_common_words"
                    layout="@layout/chat_view_chat_common_words" />

            </lib.twl.common.kpswitch.widget.KPSwitchPanelLinearLayout>

        </lib.twl.common.kpswitch.widget.KPSwitchRootLinearLayout>

        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:translationZ="3dp"
            tools:visibility="gone" />

        <include
            android:id="@+id/layout_forward_message_success"
            layout="@layout/view_forward_message_success"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|center"
            android:layout_marginTop="5dp"
            tools:visibility="gone" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>