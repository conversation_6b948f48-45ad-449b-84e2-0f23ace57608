<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.twl.hi.foundation.model.message.MessageConstants" />

        <import type="android.text.TextUtils" />

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.MessageForSystemCard" />

        <variable
            name="listener"
            type="com.twl.hi.basic.callback.ChatItemListener" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.ChatBaseViewModel" />

    </data>

    <com.twl.hi.basic.views.InterceptConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <include
            android:id="@+id/checkbox"
            layout="@layout/chat_item_msg_checkbox_top"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:msg="@{msg}"
            app:viewModel="@{viewModel}" />

        <include
            android:id="@+id/sdv_from_avatar"
            layout="@layout/item_avatar3"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginLeft="@dimen/message_item_content_margin_avatar"
            app:avatarContact="@{viewModel.getContact(msg)}"
            app:groupRobot="@{viewModel.getGroupRobot(msg)}"
            app:itemAvatarClick="@{(view)->listener.onAvatarClick(view,msg)}"
            app:itemAvatarDoubleClick="@{(view)->listener.onDoubleAvatarClick(view,msg)}"
            app:itemAvatarLongClick="@{(view)->listener.onLongAvatarClick(view,msg)}"
            app:message="@{msg}"
            app:showPendant="@{true}"
            app:layout_constraintStart_toEndOf="@+id/checkbox"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginLeft="@dimen/message_item_avatar_margin_container" />

        <TextView
            android:id="@+id/tv_sender"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="@dimen/chat_input_message_item_right_width"
            android:paddingBottom="@dimen/message_item_name_padding_content"
            android:singleLine="true"
            android:text="@{viewModel.getChatName(msg)}"
            android:textColor="@color/color_9999A3"
            app:scaledTextSize="@{12}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/sdv_from_avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:max_width_percent="@{msg.getMaxWidthPercent()}"
            app:visibleGone="@{msg.type==2}"
            tools:text="特别长的发送人姓名特别长的发送人姓名特别长的发送人姓名特别长的发送人姓名" />

        <LinearLayout
            android:id="@+id/layout_system"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/message_item_content_margin_avatar"
            android:layout_marginTop="@dimen/message_item_content_margin_name"
            android:layout_marginEnd="@dimen/chat_input_message_item_right_width"
            android:orientation="vertical"
            app:choseSystemCard="@{msg}"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/sdv_from_avatar"
            app:layout_constraintTop_toBottomOf="@+id/tv_sender"
            app:max_width_percent="@{msg.getMaxWidthPercent()}">

            <include
                layout="@layout/chat_view_message_schedule_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:listener="@{listener}"
                app:msg="@{msg}" />

            <include
                layout="@layout/chat_view_message_system_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:listener="@{listener}"
                app:msg="@{msg}"
                tools:visibility="gone" />
        </LinearLayout>

        <include
            layout="@layout/chat_view_message_shining_pic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="@+id/layout_system"
            app:layout_constraintTop_toTopOf="@+id/layout_system"
            app:showShining="@{msg.urgent}" />

        <include
            android:id="@+id/layout_message_bottom"
            layout="@layout/chat_layout_message_bottom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            app:chatType="@{viewModel.getType()}"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="@+id/layout_system"
            app:layout_constraintTop_toBottomOf="@+id/layout_system"
            app:msg="@{msg}" />

        <include
            layout="@layout/chat_item_message_time_annotation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:bindMsgId="@{msg.mid}"
            app:layout_constraintStart_toStartOf="@id/layout_message_bottom"
            app:layout_constraintTop_toBottomOf="@id/layout_message_bottom"
            app:timeAnnotationMsg="@{viewModel.messageNeedTimeAnnotation}" />
    </com.twl.hi.basic.views.InterceptConstraintLayout>

</layout>
