<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto">

	<data>

		<variable
			name="listener"
			type="com.twl.hi.basic.callback.ChatItemListener" />

		<variable
			name="msg"
			type="com.twl.hi.foundation.model.message.ChatMessage" />
	</data>

	<ImageView
		android:id="@+id/view_more_action"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:paddingLeft="10dp"
		android:paddingRight="10dp"
		android:paddingBottom="20dp"
		app:showAction="@{msg}"
		android:onClick="@{(view)->listener.onMoreAction(view, msg)}" />
</layout>