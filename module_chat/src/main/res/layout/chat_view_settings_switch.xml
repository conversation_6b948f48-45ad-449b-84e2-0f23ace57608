<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="enable"
            type="androidx.databinding.ObservableBoolean" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.ChatSettingSwitchViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.callback.ChatSettingBaseCallback" />

        <variable
            name="show"
            type="Boolean" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <FrameLayout
            visibleGone="@{show}"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/bg_corner_top_10_color_white"
            android:paddingHorizontal="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/chat_top_chat"
                android:textColor="@color/color_0D0D1A"
                android:textSize="16sp" />

            <Switch
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center_vertical"
                android:checked="@={viewModel.isTop}"
                android:enabled="@{enable}"
                android:thumb="@drawable/switch_no_enable_thumb"
                android:track="@drawable/switch_no_enable_track" />

            <View
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:layout_gravity="right|center_vertical"
                android:background="@color/color_transparent"
                android:onClick="@{()->callback.top()}" />
        </FrameLayout>

        <include
            layout="@layout/chat_view_divider_1"
            app:visibleGone="@{show}" />

        <FrameLayout
            visibleGone="@{show}"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@color/colorWhite"
            android:paddingHorizontal="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/chat_conversation_mark"
                android:textColor="@color/color_0D0D1A"
                android:textSize="16sp" />

            <Switch
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center_vertical"
                android:checked="@={viewModel.isMarked}"
                android:enabled="@{enable}"
                android:thumb="@drawable/switch_no_enable_thumb"
                android:track="@drawable/switch_no_enable_track" />
        </FrameLayout>

        <include
            layout="@layout/chat_view_divider_1"
            app:visibleGone="@{show}" />

        <FrameLayout
            visibleGone="@{show}"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@color/colorWhite"
            android:paddingHorizontal="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/chat_no_message_disturb"
                android:textColor="@color/color_0D0D1A"
                android:textSize="16sp" />

            <Switch
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center_vertical"
                android:checked="@={viewModel.isSilence}"
                android:enabled="@{enable}"
                android:thumb="@drawable/switch_no_enable_thumb"
                android:track="@drawable/switch_no_enable_track" />

            <View
                android:layout_width="56dp"
                android:layout_height="match_parent"
                android:layout_gravity="right|center_vertical"
                android:onClick="@{()->callback.showMuteDisableTips()}"
                android:visibility="@{viewModel.isArchive ? View.VISIBLE : View.GONE}" />

        </FrameLayout>

        <include
            layout="@layout/chat_view_divider_1"
            app:visibleGone="@{show}" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/bg_corner_bottom_10_color_white"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp"
            app:visibleGone="@{show}">

            <View
                android:id="@+id/collapse_chat_divider_view"
                android:layout_width="8dp"
                android:layout_height="2dp"
                android:background="@color/color_9999A3"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:text="@string/chat_collapse_chat"
                android:textColor="@color/color_0D0D1A"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/collapse_chat_divider_view"
                app:layout_constraintTop_toTopOf="parent" />

            <Switch
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="@={viewModel.isArchive}"
                android:enabled="@{enable}"
                android:thumb="@drawable/switch_no_enable_thumb"
                android:track="@drawable/switch_no_enable_track"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            visibleGone="@{show}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="top"
            android:paddingHorizontal="16dp"
            android:paddingVertical="8dp"
            android:text="@string/chat_collapse_conversation_tips"
            android:textColor="@color/color_9999A3"
            android:textSize="13sp" />
    </LinearLayout>
</layout>