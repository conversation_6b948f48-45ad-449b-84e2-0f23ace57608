<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.twl.hi.basic.util.ThemeUtils" />

        <variable
            name="callback"
            type="com.twl.hi.chat.feature.chattab.optdialog.ChatTabGuideStartClickCallback" />
    </data>

    <com.twl.hi.basic.views.HiShadowFrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_white"
        app:lyt_topLeftRadius="20dp"
        app:lyt_topRightRadius="20dp">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <View
                android:layout_width="32dp"
                android:layout_height="4dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/chat_tab_shape_color_dedee3_radius_4" />

            <ImageView
                android:layout_width="315dp"
                android:layout_height="134dp"
                android:scaleType="fitXY"
                android:src="@mipmap/chat_pic_tab_guide_feature" />


            <com.twl.hi.basic.views.CommonTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="16dp"
                android:background="@color/color_F9F9FA"
                android:padding="10dp"
                android:text="@string/chat_tab_guide_feature"
                android:textColor="@color/color_656773"
                android:textSize="14sp"
                app:btn_roundRadius="4dp" />

            <ImageView

                android:layout_width="315dp"
                android:layout_height="153dp"
                android:scaleType="fitXY"
                android:src="@mipmap/chat_pic_tab_guide_demos" />

            <com.twl.hi.basic.views.CommonTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="16dp"
                android:background="@color/color_F9F9FA"
                android:padding="10dp"
                android:text="@string/chat_tab_guide_demos"
                android:textColor="@color/color_656773"
                android:textSize="14sp"
                app:btn_roundRadius="4dp" />

            <com.twl.hi.basic.views.CommonTextView
                android:id="@+id/tv_cancel"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="4dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="12dp"
                android:background="@{ThemeUtils.useNewTheme?@drawable/bg_selector_common_button_primary:@drawable/chat_tab_shape_color_5d68e8_radius_6}"
                android:gravity="center"
                android:onClick="@{v->callback.onTabGuideStartClick()}"
                android:text="开始体验"
                android:textColor="@color/app_white"
                android:textSize="17sp"
                tools:background="@drawable/bg_selector_common_button_primary" />
        </LinearLayout>
    </com.twl.hi.basic.views.HiShadowFrameLayout>
</layout>