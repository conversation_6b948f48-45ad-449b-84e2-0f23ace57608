<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".chat.ChatRecordAllForRecordActivity">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.ChatRecordAllForTaskViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.callback.ChatRecordAllForScheduleCallback" />
    </data>


    <LinearLayout
        android:id="@+id/activityMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white"
        android:orientation="vertical">

        <include
            android:id="@+id/vg_search"
            layout="@layout/view_chat_record_search_input"
            app:searchInput="@{callback}" />

        <include
            android:id="@+id/ic_reset"
            layout="@layout/chat_item_header_search_chat_record_for_schedule"
            app:callback="@{callback}" />

        <FrameLayout
            android:id="@+id/fl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.scwang.smartrefresh.layout.SmartRefreshLayout
                android:id="@+id/smart_refresh_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:srlEnableAutoLoadMore="false">

                <lib.twl.common.views.CloseKeyboardRecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </com.scwang.smartrefresh.layout.SmartRefreshLayout>

            <TextView
                android:id="@+id/tv_no_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                android:layout_marginTop="40dp"
                android:visibility="gone"
                android:text="@string/no_content" />
        </FrameLayout>
    </LinearLayout>


</layout>