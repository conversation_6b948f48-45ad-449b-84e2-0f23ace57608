<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="item"
            type="lib.twl.common.views.adapter.entity.MultiItemEntity" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:background="@drawable/chat_shape_rect_radius_4_solid_f5f5f7"
        android:orientation="horizontal"
        android:paddingStart="10dp"
        android:paddingTop="6dp"
        android:paddingEnd="10dp"
        android:paddingBottom="6dp">

        <FrameLayout
            android:id="@+id/vg_avatar"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            app:avatarMultiItemEntity="@{item}">

            <com.facebook.drawee.view.SimpleDraweeView
                android:id="@+id/sdv_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:placeholderImage="@drawable/bg_text_avatar"
                app:roundAsCircle="true" />

            <TextView
                android:id="@+id/tv_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_text_avatar"
                android:gravity="center"
                android:textColor="@color/app_white"
                android:textSize="9sp"
                tools:text="Sa" />

            <TextView
                android:id="@+id/tv_bg_circle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_avatar_circle" />
        </FrameLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_1D2026"
            android:textSize="13sp"
            app:textMultiItemEntity="@{item}"
            tools:text="1234567891011121314" />

    </LinearLayout>

</layout>
