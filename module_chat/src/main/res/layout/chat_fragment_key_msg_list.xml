<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>

        <import type="android.view.View" />

        <import type="hi.kernel.Constants" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.MarkListViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.callback.MarkListCallback" />
    </data>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/smart_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/transparent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

        <TextView
            android:layout_marginTop="196dp"
            android:id="@+id/tv_empty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawableTop="@mipmap/chat_icon_shining_empty"
            android:drawablePadding="20dp"
            android:gravity="center_horizontal"
            android:text="@{viewModel.markEmptyTips}"
            android:textColor="@color/color_B1B1B8"
            android:textSize="14sp"
            android:visibility="gone" />
    </LinearLayout>
</layout>
