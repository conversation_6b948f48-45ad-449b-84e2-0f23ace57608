<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical"
    tools:padding="12dp">

    <TextView
        android:id="@+id/input_text_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:textColor="@color/color_0A1B33"
        android:maxLength="100"
        android:textSize="14sp"
        android:visibility="gone"
        tools:text="输入框标题"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/input_text_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/chat_bg_shape_color_white_radius_6"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="12dp"
        android:paddingTop="8dp"
        android:paddingEnd="8dp"
        android:paddingBottom="8dp">

        <lib.twl.common.util.widget.MaxHeightScrollView
            android:id="@+id/input_text_scroll_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="24dp"
            android:scrollbarStyle="insideInset"
            android:layout_weight="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:viewMaxHeight="62dp">

            <TextView
                android:id="@+id/input_text_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                tools:text="默认文字默认文字默认文字默字默认文字默认文字默认文字默认文字默认文字默认文字默认文字默认文字默认文字默字默认文字默认文字默认文字默认文字默认文字默认文字默认文字默认文字默认文字默字默认文字默认文字默认文字默认文字默认文字默认文字" />
        </lib.twl.common.util.widget.MaxHeightScrollView>

        <ImageView
            android:id="@+id/input_text_send_btn"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginHorizontal="4dp"
            android:layout_marginBottom="2dp"
            android:src="@drawable/chat_ic_input_text_send_disabled"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>