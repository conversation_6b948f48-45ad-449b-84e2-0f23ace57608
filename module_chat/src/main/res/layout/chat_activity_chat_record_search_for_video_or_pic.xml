<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.ChatRecordSearchForVideoOrPicViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.chat.callback.ChatRecordSearchForVideoOrPicCallback" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/app_white"
        tools:context=".chat.ChatRecordSearchForVideoOrPicActivity">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            app:callback="@{callback}"
            app:hasDivider="@{true}"
            app:right="@{@string/chat_select}"
            app:rightEnabled="@{true}"
            app:title="@{@string/chat_record_video_or_pic}" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <com.scwang.smartrefresh.layout.SmartRefreshLayout
                android:id="@+id/smart_refresh_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:srlEnableAutoLoadMore="false">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_video_pic"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </com.scwang.smartrefresh.layout.SmartRefreshLayout>

            <TextView
                android:id="@+id/tv_no_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                android:layout_marginTop="40dp"
                android:visibility="gone"
                android:text="@string/no_content" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/ll_select_container"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageButton
                android:layout_width="40dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="114dp"
                android:background="@null"
                android:onClick="@{(view)->callback.onDownloadClick(view)}"
                android:src="@drawable/chat_ic_icon_record_download" />

            <ImageButton
                android:layout_width="40dp"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="114dp"
                android:background="@null"
                android:onClick="@{(view)->callback.onForwardMessageClick(view)}"
                android:src="@drawable/chat_ic_icon_record_forward" />
        </FrameLayout>

    </LinearLayout>
</layout>