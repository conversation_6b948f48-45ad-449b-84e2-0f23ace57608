<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.MessageForVideo" />

        <variable
            name="listener"
            type="com.twl.hi.chat.callback.PicAndVideoGroupListCallback" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.callback.PictureAndVideoRecordCheckContract" />

        <variable
            name="group"
            type="com.twl.hi.foundation.model.message.MessageGroup" />
        
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="93dp"
        android:layout_margin="@dimen/divider_height"
        android:background="#cfcfcf"
        android:onClick="@{(view)->listener.onVideoClick(msg, group, view)}">

        <com.facebook.drawee.view.SimpleDraweeView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:imageUrl="@{msg.videoInfo.thumbnail.url}"
            app:roundingBorderColor="@color/color_D5D5D9"
            app:roundingBorderWidth="0.5dp" />

        <com.facebook.drawee.view.SimpleDraweeView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right"
            android:layout_marginRight="39dp"
            android:layout_marginBottom="7dp"
            android:background="@drawable/chat_ic_audio_icon" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right"
            android:layout_marginRight="4dp"
            android:layout_marginBottom="5dp"
            android:textColor="#FFFFFF"
            android:textSize="13sp"
            app:setDuration="@{msg.videoInfo.duration}" />

        <include
            android:id="@+id/checkbox"
            layout="@layout/chat_item_pic_video_checkbox"
            app:msg="@{msg}"
            app:viewModel="@{viewModel}" />
    </FrameLayout>
</layout>