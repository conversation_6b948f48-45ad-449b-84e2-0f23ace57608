<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="msg"
            type="com.twl.hi.foundation.model.message.ChatMessage" />

        <variable
            name="listener"
            type="com.twl.hi.basic.callback.ChatItemListener" />

        <variable
            name="viewModel"
            type="com.twl.hi.chat.viewmodel.ChatBaseViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <CheckBox
            android:id="@+id/checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="12dp"
            android:background="@drawable/selector_contact"
            android:button="@null"
            android:checked="false"
            android:enabled="false"
            app:layout_constraintBottom_toBottomOf="@id/sdv_from_avatar"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/sdv_from_avatar"
            app:visibleGone="@{viewModel.shouldShowCheckBox(msg)}" />

        <include
            android:id="@+id/sdv_from_avatar"
            layout="@layout/item_avatar3"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="@dimen/message_item_content_margin_avatar"
            app:avatarContact="@{viewModel.getContact(msg)}"
            app:groupRobot="@{viewModel.getGroupRobot(msg)}"
            app:itemAvatarClick="@{(view)->listener.onAvatarClick(view,msg)}"
            app:itemAvatarDoubleClick="@{(view)->listener.onDoubleAvatarClick(view,msg)}"
            app:itemAvatarLongClick="@{(view)->listener.onLongAvatarClick(view,msg)}"
            app:layout_constraintLeft_toRightOf="@id/checkbox"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginLeft="12dp"
            app:message="@{msg}"
            app:showPendant="@{true}" />

        <TextView
            android:id="@+id/hint"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/message_item_content_margin_avatar"
            android:layout_marginEnd="@dimen/chat_input_message_item_right_width"
            android:background="@drawable/chat_bg_message_from_text"
            android:paddingHorizontal="@dimen/chat_message_item_padding_horizontal"
            android:paddingVertical="@dimen/chat_message_item_padding_vertical"
            android:text="@string/chat_un_support_message_type"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@id/sdv_from_avatar"
            app:layout_constraintTop_toTopOf="@id/sdv_from_avatar"
            app:layout_constraintWidth_default="wrap"
            app:scaledTextSize="@{14}" />

        <include
            layout="@layout/chat_item_message_time_annotation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:bindMsgId="@{msg.mid}"
            app:layout_constraintStart_toStartOf="@id/hint"
            app:layout_constraintTop_toBottomOf="@id/hint"
            app:timeAnnotationMsg="@{viewModel.messageNeedTimeAnnotation}" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>