package com.twl.hi.chat.feed

import android.app.Activity
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Point
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.util.SparseArray
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import androidx.annotation.CallSuper
import androidx.annotation.DrawableRes
import androidx.core.graphics.withClip
import androidx.core.view.GestureDetectorCompat
import androidx.core.view.get
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.get
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.techwolf.lib.tlog.TLog
import com.twl.hi.chat.R
import com.twl.hi.chat.databinding.ChatFragmentConversationMainTabBinding
import com.twl.hi.chat.databinding.ChatLayoutEmptyConversationListBinding
import com.twl.hi.chat.feed.scene.AIAssistantScene
import com.twl.hi.chat.feed.scene.AtMeScene
import com.twl.hi.chat.feed.scene.ComprehensiveScene
import com.twl.hi.chat.feed.scene.GroupScene
import com.twl.hi.chat.feed.scene.HiddenScene
import com.twl.hi.chat.feed.scene.MarkedScene
import com.twl.hi.chat.feed.scene.SingleScene
import com.twl.hi.chat.feed.scene.TaggedScene
import com.twl.hi.chat.feed.scene.UnreadScene
import com.twl.hi.chat.message.callback.ConversationListCallback
import com.twl.hi.foundation.model.ChatGroupWrapper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.cancelChildren
import kotlinx.coroutines.job
import lib.twl.common.ext.dp
import lib.twl.common.ext.sp
import kotlin.math.max
import kotlin.math.min

private const val TAG = "F1Scene"

/**
 * F1 界面内容的转换控制器
 */
class F1SceneSwitcher(
    private val activity: Activity,
    private val lifecycleOwner: LifecycleOwner,
    private val viewModelProvider: ViewModelProvider,
    private val dataBinding: ChatFragmentConversationMainTabBinding
) {

    /**
     * 标签会话的列表视图，标签视图是不定数量的
     */
    private val taggedScenes = hashMapOf<String, TaggedScene>()

    /**
     * 全部会话的列表视图
     */
    private val comprehensiveScene = ComprehensiveScene(activity, lifecycleOwner, viewModelProvider.get(), dataBinding)

    /**
     * @ 我的消息列表视图
     */
    private val atMeScene by lazy { AtMeScene(activity, lifecycleOwner, viewModelProvider.get(), dataBinding) }

    /**
     * 未读的会话列表视图
     */
    private val unreadScene by lazy { UnreadScene(activity, lifecycleOwner, viewModelProvider.get(), dataBinding) }

    /**
     * 单聊的会话列表视图
     */
    private val singleScene by lazy { SingleScene(activity, lifecycleOwner, viewModelProvider.get(), dataBinding) }

    /**
     * 群聊的会话列表视图
     */
    private val groupScene by lazy { GroupScene(activity, lifecycleOwner, viewModelProvider.get(), dataBinding) }

    /**
     * 标记的会话的列表视图
     */
    private val markedScene by lazy { MarkedScene(activity, lifecycleOwner, viewModelProvider.get(), dataBinding) }

    /**
     * 不显示的会话的列表视图
     */
    private val hiddenScene by lazy { HiddenScene(activity, lifecycleOwner, viewModelProvider.get(), dataBinding) }

    /**
     * AI助聊的会话列表视图
     */
    private val aiAssistantScene by lazy { AIAssistantScene(activity, lifecycleOwner, viewModelProvider.get(), dataBinding) }

    /**
     * 当前会话列表的视图
     */
    private var currentScene: F1Scene = comprehensiveScene

    init {
        currentScene.onEnterScene()
        dataBinding.chatContent.doOnInterceptTouchEvent {
            (currentScene as? ConversationListCallback)?.onTouchItem(it)
            false
        }
    }

    /**
     * 会话标签切换事件
     */
    fun onChatGroupSelected(group: ChatGroupWrapper?, tabType: Int?) {
        val nextScene = if (group == null) {
            comprehensiveScene
        } else if (group.index == 0) {
            getF1SceneByType(tabType)
        } else {
            taggedScenes.getOrPut(group.id) {
                TaggedScene(activity, group, lifecycleOwner, viewModelProvider.get(), dataBinding)
            }
        }
        switchScene(nextScene)
    }

    /**
     * 会话标签的变更事件，在标签变更时需要清除已失效的视图
     */
    fun onChatGroupChanged(groups: List<ChatGroupWrapper>) {
        val remains = groups.map { it.id }.toSet()
        (taggedScenes.keys - remains).forEach {
            taggedScenes.remove(it)?.onClear()
        }
    }

    /**
     * 会话列表feed tab的切换事件
     */
    fun onChatTabSelected(tabType: Int) {
        val nextScene = getF1SceneByType(tabType)
        switchScene(nextScene)
    }

    /**
     * 根据指定的tabType匹配对应的Scene
     */
    private fun getF1SceneByType(tabType: Int?) = when (tabType) {
        FeedTab.TAB_UNREAD.tabType -> unreadScene
        FeedTab.TAB_AT_ME.tabType -> atMeScene
        FeedTab.TAB_MARK.tabType -> markedScene
        FeedTab.TAB_SINGLE.tabType -> singleScene
        FeedTab.TAB_GROUP.tabType -> groupScene
        FeedTab.TAB_HIDDEN.tabType -> hiddenScene
        FeedTab.TAB_AI.tabType -> aiAssistantScene
        else -> comprehensiveScene
    }

    /**
     * 滚动到下一个未读位置的事件
     */
    fun onScrollToNextUnread() {
        currentScene.scrollToNextUnread()
    }

    fun refreshLayout() {
        currentScene.onExitScene()
        currentScene.onEnterScene()

        dataBinding.emptyContentStub.binding?.invalidateAll()
        dataBinding.emptyContentStub.root?.requestLayout()
    }

    private fun switchScene(nextScene: F1Scene) {
        if (currentScene != nextScene) {
            TLog.info(TAG, "切换界面：$currentScene => $nextScene")
            currentScene.onExitScene()
            currentScene = nextScene
            currentScene.onEnterScene()
        } else {
            TLog.info(TAG, "$currentScene == $nextScene, 不切换显示")
        }
    }
}

abstract class F1Scene(lifecycleOwner: LifecycleOwner) {

    val sceneScope by lazy {
        CoroutineScope(
            SupervisorJob(lifecycleOwner.lifecycleScope.coroutineContext.job) + Dispatchers.Main.immediate
        )
    }

    /**
     * 当页面进入该场景时回调，通知场景下的元素开始在界面展示
     */
    abstract fun onEnterScene()

    /**
     * 当页面离开该场景时回调，该场景的元素不可见，需在此及时清理页面展示的资源
     */
    @CallSuper
    open fun onExitScene() {
        sceneScope.coroutineContext.cancelChildren()
    }

    /**
     * 当页面不再被需要，丢弃数据的回调
     */
    @CallSuper
    open fun onClear() {
        sceneScope.cancel()
    }

    /**
     * 处理滚动到下一个未读项
     */
    open fun scrollToNextUnread() {}

    companion object {
        /**
         * 空列表页面显示的中介函数，根据传入参数确定显示内容，当使用空参数时则表示隐藏页面
         */
        fun ChatFragmentConversationMainTabBinding.changeEmptyScreen(
            @DrawableRes placeholder: Int = R.drawable.icon_content_empty,
            title: CharSequence? = "",
            content: CharSequence? = "",
            actionText: CharSequence? = "",
            actionBg: Drawable? = root.context.getDrawable(R.drawable.bg_selector_common_button_primary_old),
            action: (View) -> Unit = {},
            colorActionText: CharSequence? = "",
            colorAction:(View)-> Unit = {}
        ) {
            val emptyView = emptyContentStub
            if (title.isNullOrEmpty() && content.isNullOrEmpty() && actionText.isNullOrEmpty() && colorActionText.isNullOrEmpty()) {
                if (emptyView.isInflated && emptyView.root.isVisible) {
                    emptyView.root.isVisible = false
                }
                return
            }
            if (!emptyView.isInflated) {
                emptyView.viewStub?.inflate()
            }
            emptyView.root.isVisible = true
            (emptyView.binding as? ChatLayoutEmptyConversationListBinding)?.let {
                it.emptyPlaceholder.setImageResource(placeholder)
                it.title.text = title
                it.title.isVisible = !title.isNullOrEmpty()
                it.content.text = content
                it.content.isVisible = !content.isNullOrEmpty()
                it.action.text = actionText
                it.action.isVisible = !actionText.isNullOrEmpty()
                it.action.background = actionBg
                it.action.setOnClickListener { v -> action(v) }
                it.colorAction.text = colorActionText
                it.colorAction.isVisible = !colorActionText.isNullOrEmpty()
                it.colorAction.setOnClickListener { v -> colorAction(v) }
            }
        }
    }

    /**
     * 触发滚动到指定位置的中介函数，滚动时保证 app bar 收起
     */
    fun ChatFragmentConversationMainTabBinding.scrollToPosition(position: () -> Int) {
        if (chatContent.childCount <= 0) {
            return
        }
        val targetPosition = position()
        if (targetPosition in 0 until (chatContent.adapter?.itemCount ?: 0)) {
            appbar.setExpanded(false)
            (chatContent.layoutManager as? LinearLayoutManager)?.let {
                val first = it.findFirstVisibleItemPosition()
                val last = it.findLastVisibleItemPosition()
                if (targetPosition in first..last) {
                    val view = it.findViewByPosition(targetPosition)
                    chatContent.smoothScrollBy(0, view?.top ?: 0)
                } else {
                    it.scrollToPositionWithOffset(targetPosition, 0)
                }
            }
        }
    }
}

class AtMeDecorationTouchListener(context: Context, private val atMeDecoration: AtMeDecoration, private val listener: ItemDecorationClickListener? = null) : RecyclerView.SimpleOnItemTouchListener() {

    private val gestureDetector = GestureDetectorCompat(context, object : GestureDetector.SimpleOnGestureListener() {
        override fun onSingleTapUp(e: MotionEvent): Boolean {
            return true
        }
    })

    override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
        if (atMeDecoration.clickArea().contains(e.x.toInt(), e.y.toInt()) && listener != null && gestureDetector.onTouchEvent(e)) {
            listener.onClickAtMeDecoration()
            return true
        }
        return false
    }

}

interface ItemDecorationClickListener {
    fun onClickAtMeDecoration()
}

class AtMeDecoration(private val text: String, private val textSizeSp: Float, private val textColor: Int, private val topPaddingDp: Float) : RecyclerView.ItemDecoration() {

    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = textColor
        textSize = textSizeSp.sp
        textAlign = Paint.Align.CENTER
    }

    private val textBounds = Rect()
    private val clickArea = Rect()

    fun clickArea(): Rect {
        return clickArea
    }

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        super.getItemOffsets(outRect, view, parent, state)
        val position = (view.layoutParams as RecyclerView.LayoutParams).viewLayoutPosition
        textPaint.getTextBounds(text, 0, text.length, textBounds)
        val height = 56f.dp
        if (position == state.itemCount - 1) {
            outRect.set(0, 0, 0, height.toInt()) // 设置底部偏移
        }
    }

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(c, parent, state)
        val layoutManager = parent.layoutManager
        if (layoutManager is LinearLayoutManager) {
            val itemCount = parent.adapter!!.itemCount
            val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()
            if (itemCount == 0) return
            val lastVisibleView = layoutManager.findViewByPosition(lastVisibleItemPosition)
            val lastCompletelyVisibleItemPosition = layoutManager.findLastCompletelyVisibleItemPosition()
            val left = 0
            val right = parent.width
            val top: Int
            val bottom: Int
            if (lastVisibleView != null && (parent.height > parent.computeVerticalScrollRange() || lastCompletelyVisibleItemPosition == itemCount - 1)) {
                val textBounds = Rect()
                textPaint.getTextBounds(text, 0, text.length, textBounds)
                // 最后一个 item 完全可见或者数据量不超过一个屏幕，显示在最后一个 item 的下面
                top = lastVisibleView.bottom
                bottom = (top + 40f.dp).toInt()
                // 绘制居中文字
                val centerX = (left + right) / 2.0f
                val textHeight = textBounds.height().toFloat()
                c.drawText(text, centerX, top + topPaddingDp + textHeight / 2, textPaint)
                clickArea.set(0, top, lastVisibleView.right, bottom)
            }
        }
    }

}


class TopChatDecoration(
    private val collapseAtBottomListener: (Boolean) -> Unit
) : RecyclerView.ItemDecoration() {

    var bottomOffset = 0
    var collapseEnabled = false
    var lastTopItemPosition = Int.MAX_VALUE

    private var recyclerView: RecyclerView? = null
    private val collapseButtonBottom = Point()
    private val validChildren = SparseArray<View>(20)

    fun attachToRecyclerView(recyclerView: RecyclerView) {
        if (this.recyclerView === recyclerView) {
            return
        }
        recyclerView.addItemDecoration(this)
        this.recyclerView = recyclerView
    }

    fun detachFromRecyclerView() {
        recyclerView?.removeItemDecoration(this)
        recyclerView = null
    }

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        if (!collapseEnabled || parent.childCount <= 0) {
            collapseAtBottomListener(false)
            return
        }
        collapseButtonBottom.set(0, 0)
        validChildren.clear()
        var start = Int.MAX_VALUE
        var end = Int.MIN_VALUE
        for (i in 0 until parent.childCount) {
            val child = parent[i]
            val position = parent.getChildAdapterPosition(child)
            if (position >= 0) {
                validChildren.put(position, child)
                start = min(start, position)
                end = max(end, position)
            }
        }
        if (validChildren.size() == 0 || start > end) {
            collapseAtBottomListener(false)
            return
        }

        // 检查真正的折叠按钮是否在可见范围内
        val collapseButtonChild = validChildren.get(lastTopItemPosition)
        val isCollapseButtonVisible = lastTopItemPosition in start..end && collapseButtonChild != null

        if (isCollapseButtonVisible) {
            // 折叠按钮在可见范围内，检查它的位置
            val buttonTop = collapseButtonChild!!.y
            val screenBottom = parent.height - bottomOffset

            // 只有当折叠按钮完全滚出屏幕底部时才显示悬停按钮
            // 从屏幕顶部滑出时不需要显示悬停按钮
            val isCompletelyBelowScreen = buttonTop >= screenBottom

            collapseAtBottomListener(isCompletelyBelowScreen)
        } else {
            // 折叠按钮不在可见范围内，需要判断是在顶部还是底部
            // 如果lastTopItemPosition大于end，说明在底部，显示悬停按钮
            // 如果lastTopItemPosition小于start，说明在顶部，不显示悬停按钮
            val isInBottomArea = lastTopItemPosition > end
            collapseAtBottomListener(isInBottomArea)
        }

        val child = when {
            lastTopItemPosition in start..end -> validChildren.get(lastTopItemPosition)
            lastTopItemPosition > end -> validChildren.get(end)
            else -> null
        }
        if (child != null) {
            collapseButtonBottom.set(0, child.y.toInt() + child.height)
            c.withClip(0, 0, child.right, child.y.toInt() + child.height) {
                drawColor(0xFFF0F1F2.toInt())
            }
        }
    }

    private fun clampButtonAtBottom(): Boolean {
        return recyclerView?.let {
            val bottomBarrier = it.height - bottomOffset
            if (bottomBarrier > 0 && collapseButtonBottom.y > bottomBarrier) {
                collapseButtonBottom.y = bottomBarrier
            }
            collapseButtonBottom.y == bottomBarrier
        } ?: false
    }
}

private fun RecyclerView.doOnInterceptTouchEvent(
    onTouch: (e: MotionEvent) -> Boolean
): RecyclerView.OnItemTouchListener {
    return object : RecyclerView.OnItemTouchListener {
        override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent) = onTouch(e)
        override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) = Unit
        override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) = Unit
    }.also { addOnItemTouchListener(it) }
}
