package com.twl.hi.chat.adapter

import androidx.databinding.library.baseAdapters.BR
import com.twl.hi.chat.R
import com.twl.hi.chat.databinding.ChatItemSearchMatchConditionBinding
import com.twl.hi.chat.model.MatchCondition
import lib.twl.common.adapter.BaseDataBindingAdapter
import lib.twl.common.adapter.BaseDataBindingViewHolder

/**
 * Author : Xuweixiang .
 * Date   : On 2024/5/11
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

class ChatSearchMatchConditionAdapter() :
    BaseDataBindingAdapter<MatchCondition, ChatItemSearchMatchConditionBinding>(
        R.layout.chat_item_search_match_condition
    ) {
    override fun bind(
        helper: BaseDataBindingViewHolder<ChatItemSearchMatchConditionBinding>?,
        binding: ChatItemSearchMatchConditionBinding?,
        item: MatchCondition?
    ) {
        binding?.setVariable(BR.item, item)
    }
}