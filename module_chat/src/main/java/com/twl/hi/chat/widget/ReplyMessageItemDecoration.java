package com.twl.hi.chat.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

import lib.twl.common.util.QMUIDisplayHelper;

public class ReplyMessageItemDecoration extends RecyclerView.ItemDecoration {

    private Paint mPaint;
    private int mDivider = 1;
    private int mLeftPadding;

    public ReplyMessageItemDecoration(Context context) {
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setColor(0xffcfcfcf);
        mPaint.setStrokeWidth(1);
        mLeftPadding = QMUIDisplayHelper.dp2px(context, 20);
    }

    @Override
    public void onDraw(Canvas c, RecyclerView parent, RecyclerView.State state) {
        super.onDraw(c, parent, state);

    }

    @Override
    public void onDrawOver(Canvas c, RecyclerView parent, RecyclerView.State state) {
        super.onDrawOver(c, parent, state);
        int childCount = parent.getChildCount();
        // 遍历每个Item，分别获取它们的位置信息，然后再绘制对应的分割线
        for (int i = 0; i < childCount; i++) {
            // 获取每个Item的位置
            final View child = parent.getChildAt(i);
            // 设置矩形(分割线)的宽度为1px
            // 矩形左上顶点 = (ItemView的左边界,ItemView的下边界)
            final int left = mLeftPadding;
            final int top = child.getBottom();
            // 矩形右下顶点 = (ItemView的右边界,矩形的下边界)
            final int right = child.getRight();
            final int bottom = top + mDivider;
            // 通过Canvas绘制矩形（分割线）
            c.drawRect(left, top, right, bottom, mPaint);
        }
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
    }
}
