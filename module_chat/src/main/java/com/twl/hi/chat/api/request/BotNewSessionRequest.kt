package com.twl.hi.chat.api.request

import com.google.gson.annotations.Expose
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.http.HttpUtils
import com.twl.http.client.BaseApiRequest
import com.twl.http.client.HttpResponse
import com.twl.http.config.RequestMethod
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaType

/**
 * 开启新会话
 */
class BotNewSessionRequest(callback: BaseApiRequestCallback<HttpResponse>) :
    BaseApiRequest< HttpResponse>(callback) {
    @Expose
    @JvmField
    var botId: String? = null


    override fun getUrl() = URLConfig.URL_BOT_NEW_CONVERSATION

    override fun getMethod() = RequestMethod.POST

    override fun getMediaType(): MediaType? {
        return HttpUtils.MEDIA_TYPE_JSON.toMediaType()
    }

}