package com.twl.hi.chat.message.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

/**
 * Created by Cha<PERSON>J<PERSON>gpeng on 2020-03-26
 * Describe:消息归档接口
 */
public class ArchiveAddChatRequest extends BaseApiRequest<HttpResponse> {

    @Expose
    public String chatId;
    @Expose
    public int chatType;
    @Expose
    public int source;//1-消息列表   2-聊天设置页

    public ArchiveAddChatRequest(BaseApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_CHAT_ARCHIVE_ADD;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
