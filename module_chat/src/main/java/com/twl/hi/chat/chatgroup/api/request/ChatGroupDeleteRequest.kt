package com.twl.hi.chat.chatgroup.api.request

import com.google.gson.annotations.Expose
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.http.client.BaseApiRequest
import com.twl.http.client.HttpResponse
import com.twl.http.config.RequestMethod

/**
 * <AUTHOR>
 * @date 2022/6/7
 * description:
 */
class ChatGroupDeleteRequest
@JvmOverloads constructor(callback: BaseApiRequestCallback<HttpResponse>? = null) :
    BaseApiRequest<HttpResponse>(callback) {
    @Expose
    @JvmField
    var chatGroupId: String? = null

    override fun getUrl() = URLConfig.URL_CHAT_GROUP_DELETE

    override fun getMethod() = RequestMethod.POST
}