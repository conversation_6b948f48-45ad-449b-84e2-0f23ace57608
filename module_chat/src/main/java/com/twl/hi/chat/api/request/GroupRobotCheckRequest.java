package com.twl.hi.chat.api.request;

import com.twl.hi.chat.api.response.GroupRobotCheckResponse;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

/**
 * 添加机器人前的检查请求
 */
public class GroupRobotCheckRequest extends BaseApiRequest<GroupRobotCheckResponse> {

    public GroupRobotCheckRequest(AbsRequestCallback<GroupRobotCheckResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_GROUP_ROBOT_CHECK;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
