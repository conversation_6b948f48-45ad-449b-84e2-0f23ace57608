package com.twl.hi.chat.api.request

import com.google.gson.annotations.Expose
import com.twl.hi.chat.api.response.ChatSearchEmailResponse
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.http.client.BaseApiRequest
import com.twl.http.client.HttpResponse
import com.twl.http.config.RequestMethod

/**
 * Author : Xuweixiang .
 * Date   : On 2023/5/5
 * Email  : Contact <EMAIL>
 * Desc   : 邮箱搜索
 *
 */

class ChatSearchEmailRequest(callback: BaseApiRequestCallback<ChatSearchEmailResponse>) :
    BaseApiRequest<ChatSearchEmailResponse>(callback) {

    // 搜索词
    @Expose
    @JvmField
    var content: String? = ""

    // 当前用户邮箱账号
    @Expose
    @JvmField
    var mailAddress: String? = ""

    // 本次搜索开始位置
    @Expose
    @JvmField
    var startMailId: String? = ""

    @Expose
    @JvmField
    var size: Int? = 10


    override fun getUrl(): String {
        return URLConfig.URL_SEARCH_EMAIL
    }

    override fun getMethod(): RequestMethod {
        return RequestMethod.POST
    }
}