package com.twl.hi.chat.chatrecord.history.viewmodel

import android.app.Application
import android.text.TextUtils
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import com.techwolf.lib.tlog.TLog
import com.twl.hi.chat.R
import com.twl.hi.chat.callback.QueryContactInfoContract
import com.twl.hi.chat.chatrecord.history.AllTypeRecordFragment
import com.twl.hi.chat.chatrecord.history.FileTypeRecordFragment
import com.twl.hi.chat.chatrecord.history.LinkRecordFragment
import com.twl.hi.chat.chatrecord.history.PictureAndVideoRecordFragment
import com.twl.hi.chat.chatrecord.history.bean.state.SearchState
import com.twl.hi.foundation.MessageFactory
import com.twl.hi.foundation.api.base.SearchMessageContentRequestCallback
import com.twl.hi.foundation.api.request.SearchChatContentRecordRequest
import com.twl.hi.foundation.api.response.SearchChatContentRecordResponse
import com.twl.hi.foundation.base.FoundationViewModel
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.Contact
import com.twl.hi.foundation.model.message.*
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.error.ErrorReason
import lib.twl.common.ext.getResourceString
import lib.twl.common.util.LDate
import lib.twl.common.util.LList
import lib.twl.common.views.calendar.Calendar
import lib.twl.common.views.imagesview.Image
import lib.twl.common.views.imagesview.MultiViewerBean
import lib.twl.common.views.imagesview.Video
import java.text.MessageFormat
import java.util.*
import com.twl.hi.foundation.model.GroupRobotEntity
import com.twl.hi.foundation.utils.point.GlobalSearchPointRecord
import com.twl.hi.foundation.utils.point.SEARCH_CHAT_RECORD
import com.twl.hi.foundation.utils.point.SearchStateBuilderCollection
import com.twl.hi.foundation.utils.point.initialSearchState
import com.twl.hi.foundation.utils.point.uploadSearchResult

/**
 *@author: musa on 2022/8/24
 *@e-mail: <EMAIL>
 *@desc: 搜索聊天记录页 viewModel
 */
private const val TAG = "ChatRecordHistorySearch"

class ChatRecordHistorySearchViewModel(application: Application?) :
    FoundationViewModel(application), SearchState.OnStateChangeListener,
    QueryContactInfoContract {

    private val groupRobotCache = hashMapOf<String, GroupRobotEntity>()

    var isFirstPage: Boolean = true
    val messagesLiveData = MutableLiveData<List<ChatMessage>>()
    val messagesForVideoAndPicture = MutableLiveData<List<MessageGroup>>()
    val isNoSearchResult = MutableLiveData(false)

    /**筛选器 发送人*/
    val sender = MutableLiveData(R.string.chat_send_people.getResourceString())
    val isSenderFill = MutableLiveData(false)

    /**筛选器 时间段*/
    val period = MutableLiveData(R.string.chat_send_time.getResourceString())
    val isPeriodFill = MutableLiveData(false)

    /**不同搜索页*/
    val pages by lazy {
        listOf<Triple<String, String, Class<out Fragment>>>(
            Triple(
                R.string.chat_message.getResourceString(),
                "",
                AllTypeRecordFragment::class.java
            ),
            Triple(
                R.string.file.getResourceString(),
                MessageConstants.MSG_FILE.toString(),
                FileTypeRecordFragment::class.java
            ),
            Triple(
                R.string.chat_record_video_or_pic.getResourceString(),
                "${MessageConstants.MSG_PIC},${MessageConstants.MSG_VIDEO}",
                PictureAndVideoRecordFragment::class.java
            ),
            Triple(
                R.string.chat_search_records_link.getResourceString(),
                MessageConstants.MSG_LINK.toString(),
                LinkRecordFragment::class.java
            )
        )
    }

    /**搜索状态收束在这里*/
    val searchState: SearchState = SearchState(
        "",
        pages.foldRight(mutableListOf()) { triple, acc ->
            acc.add(triple.second)
            acc
        }
    ).apply {
        listener = this@ChatRecordHistorySearchViewModel
    }

    /**聊天记录中的图片*/
    val images = mutableListOf<MultiViewerBean>()

    /**重置是否可用*/
    val resetEnable = MutableLiveData(false)

    /**搜索埋点状态集*/
    val searchPointStates = SearchStateBuilderCollection()

    /**当前搜索Id*/
    val currentSearchPointId
        get() = GlobalSearchPointRecord.globalSearchId

    fun searchChatRecordHistory(loadMore: Boolean) {
        if (searchShouldIntercept()) {
            messagesLiveData.postValue(emptyList())
            return
        }
        isFirstPage = !loadMore
        if (isFirstPage) {
            searchState.offsetMsgId = ""
        }
        val uniqueId = with(searchState) {
            searchPointStates.initialSearchState(
                SEARCH_CHAT_RECORD,
                content,
                GlobalSearchPointRecord.globalSearchId
            ).setTab(
                when (mediaTypes) {
                    pages[0].second -> "消息"
                    pages[1].second -> "文件"
                    pages[2].second -> "图片及视频"
                    else -> "链接"
                }
            ).uniqueId
        }

        /*这两个字段主要是用来记录此次请求的 搜索关键字 和 搜索类型
        * 因为可能短时间发起好几个请求，不能确保结果的返回顺序和请求结果一致
        * 因此需要这两个字段来判断返回结果是否还有效*/
        val theSearchKey = searchState.content
        val theMediaTypes = searchState.mediaTypes
        SearchChatContentRecordRequest(object :
            SearchMessageContentRequestCallback<SearchChatContentRecordResponse>() {

            override fun handleInChildThread(data: ApiData<SearchChatContentRecordResponse>?) {
                data?.resp?.run {
                    //如果返回结果搜索的关键词已经过期，则抛弃
                    searchState.hasMore = hasMore == 1
                    searchState.offsetMsgId = offsetMsgId.toString()
                    handleDataInChildThread(messages, theSearchKey, theMediaTypes, uniqueId)
                }
            }

            override fun onSuccess(data: ApiData<SearchChatContentRecordResponse>?) {
                //nothing
            }

            override fun onComplete() {
                //nothing
            }

            override fun onFailed(reason: ErrorReason?) {
                //nothing
            }

            override fun handleErrorInChildThread(reason: ErrorReason?) {
                handleDataInChildThread(emptyList(), theSearchKey, theMediaTypes, uniqueId)
                TLog.error(TAG, reason?.errReason)
            }
        }).run {
            highlight = true
            if (searchState.chatId.isNotEmpty()) {
                chatId = searchState.chatId
            }
            if (searchState.chatType > 0) {
                chatType = searchState.chatType.toString()
            }
            this.content = theSearchKey
            if (!TextUtils.isEmpty(searchState.offsetMsgId)) {
                msgId = searchState.offsetMsgId
            }
            if (!TextUtils.isEmpty(searchState.mediaTypes)) {
                mediaTypes = searchState.mediaTypes
                if (TextUtils.equals(mediaTypes,  "${MessageConstants.MSG_PIC},${MessageConstants.MSG_VIDEO}")) {
                    // 图片和视频的搜索保持旧逻辑，highlight传false，否则服务端会把富文本里的图片标签给替换成[图片]，客户端无法解析出url，搜索结果里无法预览
                    highlight = false
                }
            }
            searchState.getBeginDate()?.let {
                beginDate = it.toString()
            }
            searchState.getEndDate()?.let {
                endDate = it.toString()
            }
            if (searchState.getUserId().isNotEmpty()) {
                userIds = assembleSenderIds(searchState.getUserId())
            }
            order = searchState.order
            HttpExecutor.execute(this)
        }
    }


    fun handleDataInChildThread(
        messages: List<ChatMessage>,
        searchKey: String,
        mediaTypes: String,
        uniqueId: String
    ) {
        //过期数据直接抛弃
        if (searchKey != searchState.content
            || mediaTypes != searchState.mediaTypes
        ) {
            searchPointStates.uploadSearchResult(uniqueId, false, isValid = false)
            return
        }
        val absentRobots = messages.filter {
            it.sender.senderType == ChatMessage.SENDER_TYPE_ROBOT && !groupRobotCache.containsKey(it.sender.senderId)
        }.map {
            it.sender.senderId
        }
        ServiceManager.getInstance()
            .groupService
            .getAllGroupRobotsById(absentRobots.toSet())
            .associateByTo(groupRobotCache) { it.robotId }
        //图片和视频的tab的数据要特殊处理
        if (searchState.mediaTypes == pages[2].second) {
            messagesForVideoAndPicture.postValue(convert2MessageGroup(messages))
        }
        if (LList.isEmpty(messages)) {
            searchPointStates.uploadSearchResult(uniqueId, true, isValid = false)
            messagesLiveData.postValue(emptyList())
        } else {
            searchPointStates.uploadSearchResult(uniqueId, true, isValid = true)
            messagesLiveData.postValue(messages)
        }
    }

    /**修改内容发起搜索*/
    fun queryContent(content: String) {
        searchState.content = content
    }

    override fun onDataChange(state: SearchState) {
        searchChatRecordHistory(false)
    }

    override fun onSenderChange(userIds: List<String>) {
        setResetSate()
        if (userIds.isEmpty()) {
            sender.postValue(R.string.chat_send_people.getResourceString())
            isSenderFill.postValue(false)
            return
        }

        if (userIds.size == 1) {
            getContactAvatar(userIds[0])?.let {
                sender.postValue(it.getShowName(Contact.SHOW_NAME_SCENE_NAME))
                isSenderFill.postValue(true)
            } ?: run {
                TLog.error(TAG, "找不到联系人的id为:${userIds[0]}")
                sender.postValue(R.string.chat_send_people.getResourceString())
                isSenderFill.postValue(false)
            }
        } else if (userIds.size < 100) {
            sender.postValue(
                MessageFormat.format(
                    R.string.chat_send_people_number.getResourceString(),
                    userIds.size
                )
            )
            isSenderFill.postValue(true)
        } else {
            sender.postValue(
                MessageFormat.format(
                    R.string.chat_send_people_number.getResourceString(),
                    "99+"
                )
            )
            isSenderFill.postValue(true)
        }
    }

    override fun onPeriodChange(beginDate: Calendar?, endDate: Calendar?) {
        setResetSate()
        beginDate?.run {
            endDate?.run {
                period.postValue("${beginDate.hiFormateStr}-${endDate.hiFormateStr}")
                isPeriodFill.postValue(true)
            }
        } ?: run {
            period.postValue(R.string.chat_send_time.getResourceString())
            isPeriodFill.postValue(false)
        }
    }

    fun onSelectingPage(position: Int) {
        //切换页面先清除缓存，防止错误分发到下一个页面
        messagesLiveData.value = null
        searchState.mediaTypes = pages[position].second
    }

    override fun getContactAvatar(uid: String): Contact? {
        return ServiceManager.getInstance().contactService.getContactById(uid)
    }

    override fun getContactName(uid: String): String {
        val contact = getContactAvatar(uid)
        return if (contact != null) {
            contact.showName
        } else ""
    }

    override fun getGroupRobot(chatMessage: ChatMessage?): GroupRobotEntity? {
        return chatMessage?.let { groupRobotCache[it.sender.senderId] }
    }

    private fun convert2MessageGroup(msgs: List<ChatMessage>): List<MessageGroup> {
        var lastMessage: ChatMessage? = null
        val chatGroups: Deque<MessageGroup> = LinkedList()
        // 将新到达消息按照月份进行分组
        for (i in msgs.indices) {
            val chatMessage = msgs[i]
            var messageOfGroup: MutableList<ChatMessage>

            // 若无前置消息或者跨月，则新建分组，插入时间消息以及多媒体消息
            if (lastMessage == null
                || !LDate.isInTheSameMonth(lastMessage.time, chatMessage.time)
            ) {
                messageOfGroup = mutableListOf()
                addTextMessage(messageOfGroup, chatMessage)
                messageOfGroup.addAll(getMessagesToResult(chatMessage))

                chatGroups.offer(MessageGroup(messageOfGroup))
            } else {
                val messageGroup = chatGroups.peekLast()
                messageGroup?.messages?.addAll(getMessagesToResult(chatMessage))
            }
            lastMessage = chatMessage
        }
        return ArrayList(chatGroups)
    }

    /**
     * chatMessage 如果是富文本的话可能有多张图片，需要拆出来，用MessageForPic来保存他的图片；mid用临时变量存储
     *
     * @param chatMessage
     * @return
     */
    private fun getMessagesToResult(chatMessage: ChatMessage): List<ChatMessage> {
        val messages: MutableList<ChatMessage> = ArrayList()
        if (chatMessage is MessageForRichText) {
            val picInfos = chatMessage.picInfoList
            for (info in picInfos) {
                val messageForPic = MessageForPic()
                messageForPic.chatId = chatMessage.chatId
                MessageFactory.copeBaseInfo(messageForPic, chatMessage)
                messageForPic.setImage(info.original, info.tiny)
                setImages(messageForPic)
                messages.add(messageForPic)
            }
        } else if (chatMessage is MessageForMarkdownText) {
            val picInfos = chatMessage.picInfoList
            for (info in picInfos) {
                val messageForPic = MessageForPic()
                messageForPic.chatId = chatMessage.chatId
                MessageFactory.copeBaseInfo(messageForPic, chatMessage)
                messageForPic.setImage(info.original, info.tiny)
                setImages(messageForPic)
                messages.add(messageForPic)
            }
        } else {
            setImages(chatMessage)
            messages.add(chatMessage)
        }
        return messages
    }

    /**
     * 填充Images列表
     */
    private fun setImages(message: ChatMessage) {
        if (message.mediaType == MessageConstants.MSG_PIC) { //填充images列表
            val imageInfo = (message as MessageForPic).tinyReal
            if (null != imageInfo) {
                val image = Image(
                    imageInfo.getUrl(),
                    message.originReal?.getUrl(),
                    message.tinyReal?.getUrl(),
                    imageInfo.getWidth(),
                    imageInfo.getHeight(),
                    message.mid,
                    message.seq,
                    message.type,
                    message.chatId
                )
                val multiViewerBean = MultiViewerBean(
                    MultiViewerBean.TYPE_IMAGE,
                    message.mid,
                    message.seq,
                    message.chatId,
                    message.type
                )
                multiViewerBean.image = image
                images.add(multiViewerBean)
            }
        } else if (message.mediaType == MessageConstants.MSG_VIDEO) {
            message as MessageForVideo
            val video = Video(message.mid, message.seq, message.chatId, message.type)
            video.url = message.url
            video.duration = message.duration
            video.localPath = message.localPath
            video.size = message.size
            video.thumbnail = message.thumbnail
            video.urlSource = message.videoInfo?.urlSource?:0
            video.vurl = message.videoInfo?.vurl?:""
            val videoMultiViewerBean = MultiViewerBean(
                MultiViewerBean.TYPE_VIDEO,
                message.mid,
                message.seq,
                message.chatId,
                message.type
            )
            videoMultiViewerBean.video = video
            images.add(videoMultiViewerBean)
        }
    }


    private fun addTextMessage(messages: MutableList<ChatMessage>, chatMessage: ChatMessage) {
        val messageForText = MessageForText()
        messageForText.content = LDate.getSearchRecordFileMoth(chatMessage.time)
        messages.add(messageForText)
    }

    fun getPosition(message: ChatMessage): Int {
        var position = 0
        var type = 0
        if (message.mediaType == MessageConstants.MSG_PIC) {
            type = MultiViewerBean.TYPE_IMAGE
        } else if (message.mediaType == MessageConstants.MSG_VIDEO) {
            type = MultiViewerBean.TYPE_VIDEO
        }
        if (type == 0) {
            return position
        }
        for (i in images.indices) {
            val multiViewerBean: MultiViewerBean = images[i]
            if (type == multiViewerBean.type) {
                if (type == MultiViewerBean.TYPE_IMAGE) {
                    val messageForPic = message as MessageForPic
                    if (TextUtils.equals(messageForPic.tinyReal?.getUrl(), multiViewerBean.image.url)) {
                        position = i
                        break
                    }
                } else {
                    if (message.mid == multiViewerBean.mid) {
                        position = i
                        break
                    }
                }
            }
        }
        return position
    }

    fun resetFilter() = searchState.resetFilter()

    /**设置是否有搜索结果，来展示无搜索结果图标*/
    fun setSearchResult(isNoResult: Boolean) {
        isNoSearchResult.postValue(isNoResult)
    }

    private fun assembleSenderIds(ids: List<String>) =
        if (ids.isNotEmpty()) { //拼接Id
            ids.foldRightIndexed(StringBuilder(ids[0]))
            { index: Int, element: String, acc: java.lang.StringBuilder ->
                if (index > 0) {
                    acc.append(",$element")
                } else {
                    acc
                }
            }.toString()
        } else {
            ""
        }

    /**
     * 是否应该拦截搜索操作
     * 消息页 搜索内容和筛选条件是空的时候 不搜索
     */
    fun searchShouldIntercept() = with(searchState) {
        searchState.mediaTypes == pages[0].second && content.isEmpty() && getUserId().isEmpty() && getBeginDate() == null
    }

    /**设置 重置 可用状态*/
    private fun setResetSate() {
        resetEnable.postValue(searchState.hasFiltered())
    }

}