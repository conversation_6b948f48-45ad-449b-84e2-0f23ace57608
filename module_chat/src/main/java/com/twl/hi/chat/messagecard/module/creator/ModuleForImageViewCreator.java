package com.twl.hi.chat.messagecard.module.creator;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.twl.hi.chat.messagecard.config.CardViewConfig;
import com.twl.hi.chat.messagecard.element.ElementViewFactory;
import com.twl.hi.foundation.model.message.extensioncard.data.element.Element;
import com.twl.hi.foundation.model.message.extensioncard.data.element.ElementForImage;
import com.twl.hi.foundation.model.message.extensioncard.data.modules.ModuleForImage;
import com.twl.hi.chat.messagecard.module.IModuleView;
import com.twl.hi.foundation.model.message.extensioncard.data.modules.Module;


import lib.twl.common.util.QMUIDisplayHelper;

/**
 * 图片模块视图构造器
 * <p>
 * Created by tanshicheng on 2023/5/9
 */
public class ModuleForImageViewCreator extends ModuleViewCreator<ModuleForImage> {

    public ModuleForImageViewCreator(ModuleForImage module, CardViewConfig config) {
        super(module, config);
    }

    @Override
    public View createView() {
        Context context = config.getContext();
        ImageModuleView imageModuleViewContainer = new ImageModuleView(context, config);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = QMUIDisplayHelper.dp2px(context, 16);
        imageModuleViewContainer.setLayoutParams(layoutParams);
        imageModuleViewContainer.updateData(module);
        return imageModuleViewContainer;
    }

    public static class ImageModuleView extends LinearLayout implements IModuleView {
        private CardViewConfig config;
        public ImageModuleView(Context context, CardViewConfig config) {
            super(context);
            this.config = config;
            setOrientation(VERTICAL);
        }
        @Override
        public void updateData(Module module) {
            if (!(module instanceof ModuleForImage)) return;
            removeAllViews();
            ModuleForImage imageModule = (ModuleForImage) module;
            View imageTitleView = ElementViewFactory.createElementView(imageModule.getImgTextElement(), config);
            if (imageTitleView != null) {
                ((ViewGroup.MarginLayoutParams) imageTitleView.getLayoutParams()).bottomMargin = QMUIDisplayHelper.dpToPx(8);
                addView(imageTitleView);
            }
            if (imageModule.getImgImageData() != null) {
                ElementForImage imageElement = new ElementForImage();
                imageElement.setImage(imageModule.getImgImageData());
                imageElement.setElementTag(Element.ELEMENT_TAG_IMAGE);
                View imageContentView = ElementViewFactory.createElementView(imageElement, config);
                if (imageContentView != null) {
                    addView(imageContentView);
                }
            }
        }
    }
}
