package com.twl.hi.chat.message;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import com.twl.hi.basic.BottomListDialog;
import com.twl.hi.basic.PageConstantsKt;
import com.twl.hi.basic.SubPageTransferActivity;
import com.twl.hi.basic.model.SelectBottomBean;
import com.twl.hi.basic.model.WebViewBean;
import com.twl.hi.basic.model.select.ForwardMessageBean;
import com.twl.hi.basic.util.FilePreviewUtil;
import com.twl.hi.chat.ChatRecordFragment;
import com.twl.hi.chat.GroupCardDetailActivity;
import com.twl.hi.chat.GroupChatForSearchActivity;
import com.twl.hi.chat.R;
import com.twl.hi.chat.SingleChatForSearchActivity;
import com.twl.hi.chat.callback.MsgContentClickDispatcher;
import com.twl.hi.chat.callback.MsgContentClickListener;
import com.twl.hi.chat.message.callback.FavoriteListCallback;
import com.twl.hi.chat.message.viewmodel.FavoriteListBaseViewModel;
import com.twl.hi.chat.message.viewmodel.FavoriteViewModel;
import com.twl.hi.chat.messagecard.utils.ExtensionCardHelper;
import com.twl.hi.chat.router.handler.custom.ImagePreviewHandler;
import com.twl.hi.chat.util.AudioPlayWrapper;
import com.twl.hi.chat.util.ChatRobotHelper;
import com.twl.hi.chat.util.F2AudioManager;
import com.twl.hi.export.organization.router.OrganizationPageRouter;
import com.twl.hi.export.select.bean.SelectBaseParams;
import com.twl.hi.export.select.bean.SelectConversationParams;
import com.twl.hi.export.select.router.SelectPageRouter;
import com.twl.hi.export.webview.WebViewPageRouter;
import com.twl.hi.foundation.SendMessageContent;
import com.twl.hi.foundation.api.base.BasePbApiRequestCallback;
import com.twl.hi.foundation.api.callback.ChatMessgaePbApiRequestCallback;
import com.twl.hi.foundation.api.request.GetMessageFromIdsV2;
import com.twl.hi.foundation.api.response.bean.FavoriteInterface;
import com.twl.hi.foundation.base.fragment.FoundationVMShareFragment;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.message.ChatMessage;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.model.message.MessageForAppCard;
import com.twl.hi.foundation.model.message.MessageForAudio;
import com.twl.hi.foundation.model.message.MessageForChatShare;
import com.twl.hi.foundation.model.message.MessageForFile;
import com.twl.hi.foundation.model.message.MessageForGroupCard;
import com.twl.hi.foundation.model.message.MessageForImageCard;
import com.twl.hi.foundation.model.message.MessageForLink;
import com.twl.hi.foundation.model.message.MessageForLinkCall;
import com.twl.hi.foundation.model.message.MessageForMarkdownText;
import com.twl.hi.foundation.model.message.MessageForOnlineFile;
import com.twl.hi.foundation.model.message.MessageForPic;
import com.twl.hi.foundation.model.message.MessageForRedEnvelope;
import com.twl.hi.foundation.model.message.MessageForRichText;
import com.twl.hi.foundation.model.message.MessageForSticker;
import com.twl.hi.foundation.model.message.MessageForSystemCard;
import com.twl.hi.foundation.model.message.MessageForTaskCommentCard;
import com.twl.hi.foundation.model.message.MessageForText;
import com.twl.hi.foundation.model.message.MessageForUserCard;
import com.twl.hi.foundation.model.message.MessageForVideo;
import com.twl.hi.foundation.model.message.extensioncard.MessageForExtensionCard;
import com.twl.hi.foundation.utils.ContactUtils;
import com.twl.hi.foundation.utils.HiMessageDBObservable;
import com.twl.hi.foundation.utils.HiMessageDBObserver;
import com.twl.hi.foundation.utils.MessageUtils;
import com.twl.hi.router.base.RouterBaseConstant;
import com.twl.hi.viewer.DraggableImageViewerHelper;
import com.twl.hi.viewer.bean.ImageExtraBean;
import com.twl.hi.viewer.bean.ImageExtraIndexBean;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.utils.SettingBuilder;

import java.util.ArrayList;
import java.util.List;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import hi.kernel.RequestCodeConstants;
import lib.twl.common.callback.OuterCallback;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.LList;
import lib.twl.common.util.ToastUtils;

/**
 * <AUTHOR>
 * @date 2021/12/31.
 */
public abstract class FavoriteBaseFragment<D extends ViewDataBinding, M extends FavoriteListBaseViewModel, AM extends FavoriteViewModel> extends FoundationVMShareFragment<D, M, AM>
        implements FavoriteListCallback, MsgContentClickListener, HiMessageDBObserver {

    protected AudioPlayWrapper mAudioPlayWrapper = new AudioPlayWrapper();

    @Override
    protected void initFragment() {
        super.initFragment();
        ServiceManager.getInstance().getAppLifecycleService().getHiMessageDBObservable().addObserver(this);
        getViewModel().getImageLiveData().observe(this, new Observer<ImageExtraBean>() {
            @Override
            public void onChanged(ImageExtraBean imageExtraBean) {
                if (imageExtraBean == null || LList.isEmpty(imageExtraBean.getMultiViewerBeans())) {
                    return;
                }
                DraggableImageViewerHelper.showImagesDraggableParamsInfo(activity,
                        imageExtraBean.getDraggableParamsInfo(),
                        imageExtraBean.getMultiViewerBeans(),
                        imageExtraBean.getParams().initialIndex,
                        "",
                        MessageConstants.MSG_SINGLE_CHAT,
                        imageExtraBean.isHideAllMedia(),
                        Constants.ORDER_ASC,
                        imageExtraBean.isAutoLoadMore()
                );
            }
        });
        getViewModel().getLoadFinishLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean finish) {
                finishRefreshLayout();
            }
        });
    }

    public abstract void finishRefreshLayout();

    @Override
    public void onPause() {
        super.onPause();
        F2AudioManager.getInstance().pause();
        mAudioPlayWrapper.setPlay(false);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        F2AudioManager.getInstance().release();
        ServiceManager.getInstance().getAppLifecycleService().getHiMessageDBObservable().deleteObserver(this);
    }


    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(activity);
    }

    @Override
    public void clickRight(View view) {
    }

    @Override
    public void onInlineLinkClick(View view, ChatMessage message, String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        AppUtil.getDefaultUriRequest(activity, url)
                .putField(ImagePreviewHandler.IMAGE_PREVIEW_CALLBACK, new OuterCallback<String>(){ //图片预览回调
                    @Override
                    public void onSuccess(String destination) {
                        ImageExtraIndexBean imageExtraIndexBean = new ImageExtraIndexBean(destination, message);
                        getViewModel().getContactImages(imageExtraIndexBean, DraggableImageViewerHelper.createImageDraggableParamsWithWHRadio(view));
                    }
                })
                .putField(RouterBaseConstant.WEB_STYLE, Constants.WEB_STYLE_SHARE_URL)
                .start();
    }

    @Override
    public void onChatClick(View view, FavoriteInterface bean) {
        ChatMessage chatMessage = bean.getMessage();
        if (chatMessage == null) {
            return;
        }
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                ChatMessage message = ServiceManager.getInstance().getMessageService().getChatMessage(chatMessage.getMid());
                if (message == null) {
                    if (chatMessage instanceof MessageForChatShare) {
                        // 分享合并消息不做插入，因为多选收藏的分享合并消息，来源可能是界面上的多条消息
                        // 如果插入，那边会造成本地展示时多出一条本地消息
                        dealClick(chatMessage, view, bean.getFavorId());
                    } else {
                        GetMessageFromIdsV2 messageFromIds = new GetMessageFromIdsV2(new ChatMessgaePbApiRequestCallback<BasePbApiRequestCallback.MessageResponse>() {
                            @Override
                            public void handleInChildThread(ApiData<MessageResponse> data) {
                                super.handleInChildThread(data);
                                if (data != null && data.resp != null && !LList.isEmpty(data.resp.messages)) {
                                    ServiceManager.getInstance().getMessageService().insertMessages(data.resp.messages);
                                    dealClick(data.resp.messages.get(0), view, bean.getFavorId());
                                }
                            }
                        });
                        messageFromIds.setIds(String.valueOf(chatMessage.getMid()));
                        HttpExecutor.execute(messageFromIds);
                    }
                } else {
                    dealClick(message, view, bean.getFavorId());
                }
            }
        });
    }

    @Override
    public void onSearchClick() {
    }

    public void dealClick(ChatMessage message, View view, String favorId) {
        ExecutorFactory.execMainTask(new Runnable() {
            @Override
            public void run() {
                if (!message.isDeleted() && message.isShow()) {
                    if (message.getMediaType() == MessageConstants.MSG_RICH) {
                        AppUtil.startActivityForResult(activity, RichDetailActivity.createIntent(activity, message.getMid(), favorId), RequestCodeConstants.REQUEST_CODE_RICH_DETAIL);
                    } else {
                        MsgContentClickDispatcher.dispatch(FavoriteBaseFragment.this, view, message);
                    }
                } else {
                    ToastUtils.ss(R.string.message_not_visiable);
                }
            }
        });
    }

    @Override
    public boolean onLongClick(FavoriteInterface bean) {
        ChatMessage chatMessage = bean.getMessage();
        if (chatMessage == null) {
            return false;
        }
        List<SelectBottomBean> list = new ArrayList<>();
        if (!bean.hasWithdraw()) {
            if (bean.isEnableLocateContext()) {
                list.add(new SelectBottomBean(Constants.CHECK_CONTENT, getContext().getResources().getString(R.string.check_content), R.drawable.chat_ic_check_content));
            }
            if (chatMessage.getMediaType() != MessageConstants.MSG_MESSAGE_EXTENSION_CARD
                    || ((MessageForExtensionCard) chatMessage).canForward()) {
                list.add(new SelectBottomBean(Constants.SEND_FILE, getContext().getResources().getString(R.string.forward), R.drawable.chat_ic_forward));
            }
        }
        list.add(new SelectBottomBean(Constants.DELETE, getContext().getResources().getString(R.string.delete), R.drawable.chat_ic_delete_black));
        new BottomListDialog.Builder(activity)
                .setData(list)
                .setCanceledOnTouchOutside(true)
                .setSpanCount(list.size())
                .setItemLayout(R.layout.item_bottom_new)
                .setOnBottomItemClickListener(new BottomListDialog.OnBottomItemClickListener() {
                    @Override
                    public void onBottomItemClick(View view, int pos, SelectBottomBean bottomBean) {
                        switch (bottomBean.type) {
                            case Constants.CHECK_CONTENT:
                                ExecutorFactory.execLocalTask(new Runnable() {
                                    @Override
                                    public void run() {
                                        ChatMessage message = ServiceManager.getInstance().getMessageService().getChatMessage(bean.getMessage().getMid());
                                        if (message == null) {
                                            GetMessageFromIdsV2 messageFromIds = new GetMessageFromIdsV2(new ChatMessgaePbApiRequestCallback<BasePbApiRequestCallback.MessageResponse>() {
                                                @Override
                                                public void handleInChildThread(ApiData<MessageResponse> data) {
                                                    super.handleInChildThread(data);
                                                    if (data != null && data.resp != null && !LList.isEmpty(data.resp.messages)) {
                                                        ServiceManager.getInstance().getMessageService().insertMessages(data.resp.messages);
                                                        ChatMessage chatMessage = data.resp.messages.get(0);
                                                        if (!chatMessage.isDeleted()) {
                                                            if (chatMessage.getType() == MessageConstants.MSG_GROUP_CHAT) {
                                                                GroupChatForSearchActivity.start(activity, chatMessage.getChatId(), chatMessage.getMid(), chatMessage.getSeq());
                                                            } else {
                                                                SingleChatForSearchActivity.start(activity, chatMessage.getChatId(), chatMessage.getMid(), chatMessage.getSeq());
                                                            }
                                                        } else {
                                                            ToastUtils.ss(R.string.message_not_visiable);
                                                        }
                                                    }
                                                }
                                            });
                                            messageFromIds.setIds(String.valueOf(bean.getMessage().getMid()));
                                            HttpExecutor.execute(messageFromIds);
                                        } else {
                                            if (!message.isDeleted() && message.isShow()) {
                                                if (message.getType() == MessageConstants.MSG_GROUP_CHAT) {
                                                    GroupChatForSearchActivity.start(activity, message.getChatId(), message.getMid(), message.getSeq());
                                                } else {
                                                    SingleChatForSearchActivity.start(activity, message.getChatId(), message.getMid(), message.getSeq());
                                                }
                                            } else {
                                                ToastUtils.ss(R.string.message_not_visiable);
                                            }
                                        }
                                    }
                                });
                                break;
                            case Constants.SEND_FILE:
                                ExecutorFactory.execLocalTask(new Runnable() {
                                    @Override
                                    public void run() {
                                        ChatMessage chatMessage = bean.getMessage();
                                        if (chatMessage != null) {
                                            // 分享合并消息不做插入，因为多选收藏的分享合并消息，来源可能是界面上的多条消息
                                            // 如果插入，那边会造成本地展示时多出一条本地消息
                                            if (!(chatMessage instanceof MessageForChatShare)) {
                                                ServiceManager.getInstance().getMessageService().insertMessage(chatMessage);
                                            }
                                            forwardFavorite(bean);
                                        }
                                    }
                                });
                                break;
                            case Constants.DELETE:
                                getActivityViewModel().cancelFavorite(bean.getFavorId());
                                break;
                            default:
                                break;
                        }
                    }
                })
                .create().show();
        return true;
    }

    @Override
    public void onTextClick(View view, MessageForText messageForText) {

    }

    @Override
    public void onPicClick(View view, MessageForPic messageForPic) {
        ImageExtraIndexBean imageExtraIndexBean = new ImageExtraIndexBean("", messageForPic);
        getViewModel().getContactImages(imageExtraIndexBean, DraggableImageViewerHelper.createImageDraggableParamsWithWHRadio(view));
    }

    @Override
    public void onFileClick(View view, MessageForFile messageForFile) {
        FilePreviewUtil.jumpToMsgFilePreviewPage(activity, messageForFile, PageConstantsKt.FAVORITE_LIST_PAGE);
    }

    @Override
    public void onStickerClick(View view, MessageForSticker messageForSticker) {

    }

    @Override
    public void onAudioClick(View view, MessageForAudio messageForAudio) {
        getViewModel().onPlayAudio(messageForAudio).observe(this, b -> {
            if (b) {
                View animationView = view.findViewById(R.id.animation_view);
                if (animationView != null && animationView instanceof ImageView) {
                    mAudioPlayWrapper.setMessageForAudio(messageForAudio, true, (ImageView) animationView, false);
                }
            } else {
                mAudioPlayWrapper.setPlay(false);
            }
        });
    }

    @Override
    public void onAppCardClick(MessageForAppCard chatMessage) {

    }

    @Override
    public void onUserCardClick(View view, MessageForUserCard messageForUserCard) {
        if (messageForUserCard == null) return;
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(messageForUserCard.getUserId());
        if (ContactUtils.isRobot(contact)) {
            ChatRobotHelper.handleRobotCardClick(activity, messageForUserCard.getUserId());
//            ChatPageRouter.jumpToRobotProfilePage(activity, messageForUserCard.getUserCard().getUid());
        } else {
            OrganizationPageRouter.jumpToUserInfoActivity(activity, messageForUserCard.getUserId());
        }
    }

    @Override
    public void onMsgShareCardClick(View view, MessageForChatShare chatShare) {
        String title = chatShare.getChatShareInfo().getTitle();
        long msgId = chatShare.getMid();
        SubPageTransferActivity.jump(activity, ChatRecordFragment.class, ChatRecordFragment.getBundle(title, msgId, "", chatShare.getSender().getSenderId()));
    }

    @Override
    public void onVideoClick(View view, MessageForVideo messageForVideo) {
        ImageExtraIndexBean imageExtraIndexBean = new ImageExtraIndexBean("", messageForVideo);
        getViewModel().getContactImages(imageExtraIndexBean, DraggableImageViewerHelper.createImageDraggableParamsWithWHRadio(view));
    }

    @Override
    public void onRedEnvelopClick(View view, MessageForRedEnvelope messageForRedEnvelope) {

    }

    @Override
    public void onSystemCardClick(View view, MessageForSystemCard messageForSystemCard) {

    }

    @Override
    public void onTaskCommentCardClick(View view, MessageForTaskCommentCard messageForTaskCommentCard) {

    }

    @Override
    public void onLinkCallClick(View view, MessageForLinkCall messageForLinkCall) {

    }

    @Override
    public void onMessageLinkClick(MessageForLink messageForLink) {
        String url = messageForLink.getUrl();
        urlClick(url);
    }

    @Override
    public void onImageCardClick(MessageForImageCard messageForImageCard) {

    }

    private void urlClick(String url) {
        AppUtil.getDefaultUriRequest(getContext(), url)
                .start();
    }

    public void forwardFavorite(FavoriteInterface favoriteBean) {
        ChatMessage chatMessage = favoriteBean.getMessage();
        String msgContent = MessageUtils.messageContentSummary(chatMessage);
        String msgRichTitle = "";
        if (chatMessage instanceof MessageForRichText) {
            String title = ((MessageForRichText) chatMessage).getTitle();
            if (!TextUtils.isEmpty(title)) {
                msgRichTitle = title;
            }
        } else if (chatMessage instanceof MessageForMarkdownText) {
            String title = ((MessageForMarkdownText) chatMessage).getTitle();
            if (!TextUtils.isEmpty(title)) {
                msgRichTitle = title;
            }
        }

        if (chatMessage instanceof MessageForUserCard) {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(((MessageForUserCard) chatMessage).getUserId());
            msgContent = SendMessageContent.getCardString(contact);
        }

        ForwardMessageBean forwardMessageBean = new ForwardMessageBean();
        forwardMessageBean.msgId = chatMessage.getMid();
        forwardMessageBean.mediaType = chatMessage.getMediaType();
        SelectConversationParams params = new SelectConversationParams()
                .setSendMessageType(SendMessageContent.TYPE_FORWARD_SINGLE)
                .<SelectConversationParams>setTitle(getResources().getString(R.string.select_contact))
                .setMaxSelectCount(50)
                .<SelectConversationParams>setOrgVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setSearchVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setOtherVisible(SelectBaseParams.VISIBLE);
        params.sendMessageContent.content = forwardMessageBean;
        params.sendMessageContent.msgRichTitle = msgRichTitle;
        params.sendMessageContent.msgShowContent = msgContent;

        if (chatMessage instanceof MessageForChatShare) {
            MessageForChatShare chatShare = (MessageForChatShare) chatMessage;
            forwardMessageBean.chatShareInfo = chatShare.getChatShareInfo();
        }

        Bundle bundleSelect = new Bundle();
        bundleSelect.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        AppUtil.startUri(activity, SelectPageRouter.SELECT_CONVERSATION_ACTIVITY, bundleSelect, ActivityAnimType.UP_GLIDE);
    }

    @Override
    public boolean update(HiMessageDBObservable o, long mid) {
        List<ChatMessage> dataSource = getViewModel().getImageChatMessages();
        if (!LList.isEmpty(dataSource)) {
            for (ChatMessage chatMessage :
                    dataSource) {
                if (mid == chatMessage.getMid()) {
                    ServiceManager.getInstance().getMessageService().insertMessage(chatMessage);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void onOnlineFileClick(MessageForOnlineFile chatMessage) {
        WebViewBean webViewBean = new WebViewBean();
        StringBuilder stringBuilder = new StringBuilder(SettingBuilder.getInstance().getOnlineFileMidUrl());
        stringBuilder.append("?redirectUrl=");
        stringBuilder.append(chatMessage.getFileInfo().getFileUrl());
        webViewBean.setUrl(stringBuilder.toString());
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constants.DATA_WEB_BEAN, webViewBean);
        AppUtil.startUri(activity, WebViewPageRouter.WEB_VIEW_ACTIVITY, bundle);
    }

    @Override
    public void highLightOver() {
        getViewModel().setShowFavoriteHighLight("");
    }


    @Override
    public void onGroupCardClick(View view, MessageForGroupCard messageForGroupCard) {
        GroupCardDetailActivity.intentStart(view.getContext(),
                messageForGroupCard.getGroupCard().getGroupId(),
                messageForGroupCard.getMid(),
                messageForGroupCard.getSender().getSenderId(),
                messageForGroupCard.getGroupCard().getGroupName(),
                messageForGroupCard.getGroupCard().getAvatar());
    }

    @Override
    public void onMessageExtensionCardClick(View view, MessageForExtensionCard messageForExtensionCard) {
        ExtensionCardHelper.handleMessageExtensionCardClick(getContext(),messageForExtensionCard);
    }

    @Override
    public void onExtensionCardImageClick(View view, String imgUrl, MessageForExtensionCard messageForExtensionCard) {
        ImageExtraIndexBean imageExtraIndexBean = new ImageExtraIndexBean(imgUrl, messageForExtensionCard);
        getViewModel().getContactImages(imageExtraIndexBean, DraggableImageViewerHelper.createImageDraggableParamsWithWHRadio(view));
    }
}
