package com.twl.hi.chat.widget.emojireply;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.twl.hi.chat.R;

/**
 * 消息item里展示表情回复列表的自定义LayoutManager
 * 流式排布
 */
public class EmojiReplyFlexLayoutManager extends RecyclerView.LayoutManager {

    private final int mMaxWidth;

    public EmojiReplyFlexLayoutManager(Context context) {
        mMaxWidth = context.getResources().getDimensionPixelSize(R.dimen.chat_message_item_emoji_reply_max_width);
    }

    @Override
    public RecyclerView.LayoutParams generateDefaultLayoutParams() {
        return new RecyclerView.LayoutParams(RecyclerView.LayoutParams.WRAP_CONTENT,
                RecyclerView.LayoutParams.WRAP_CONTENT);
    }

    @Override
    public boolean isAutoMeasureEnabled() {
        return true;
    }

    @Override
    public void onMeasure(@NonNull RecyclerView.Recycler recycler, @NonNull RecyclerView.State state, int widthSpec, int heightSpec) {
        super.onMeasure(recycler, state, widthSpec, heightSpec);
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        super.onLayoutChildren(recycler, state);
        detachAndScrapAttachedViews(recycler);
        int curLineWidth = 0, curLineTop = 0;//curLineWidth 累加item布局时的x轴偏移curLineTop 累加item布局时的x轴偏移
        int lastLineMaxHeight = 0;
        for (int i = 0; i < getItemCount(); i++) {

            View view = recycler.getViewForPosition(i);

            //获取每个item的布局参数，计算每个item的占用位置时需要加上margin
            RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) view.getLayoutParams();

            addView(view);

            measureChildWithMargins(view, 0, 0);

            int width = getDecoratedMeasuredWidth(view) + params.leftMargin + params.rightMargin;
            int height = getDecoratedMeasuredHeight(view) + params.topMargin + params.bottomMargin;

            curLineWidth += width;//累加当前行已有item的宽度

            if (curLineWidth <= mMaxWidth + params.rightMargin) {//如果累加的宽度小于等于RecyclerView的宽度，不需要换行,最后一列的右侧的margin不需要计算在内
                layoutDecorated(view, curLineWidth - width + params.leftMargin, curLineTop + params.topMargin, curLineWidth - params.rightMargin, curLineTop + height - params.bottomMargin);//布局item的真实位置
                //比较当前行多有item的最大高度，用于换行后计算item在y轴上的偏移量
                lastLineMaxHeight = Math.max(lastLineMaxHeight, height);
            } else {//换行
                curLineWidth = width;

//              修复历史版本存在的问题,如果第一项就比较长,此处代码会导致curLineTop值错误，后续布局有问题
//              if (lastLineMaxHeight == 0) {
//                  lastLineMaxHeight = height;
//              }

                //记录当前行top
                curLineTop += lastLineMaxHeight;

                layoutDecorated(view, params.leftMargin, curLineTop + params.topMargin, width - params.rightMargin, curLineTop + height - params.bottomMargin);
                lastLineMaxHeight = height;
            }
        }
    }
}
