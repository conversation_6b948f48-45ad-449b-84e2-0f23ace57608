package com.twl.hi.chat.api.response.bean;

import lib.twl.common.util.SearchResultInterface;

/**
 * <AUTHOR>
 * @date 2021/12/23.
 */
public class PluginSearchBean implements SearchResultInterface {
    public String appId; // 插件唯一Id
    public String name;// 插件名称
    public String icon;// 插件图标地址
    public String description;// 插件简介
    public boolean enableApp; // app是否支持  1可用，-1不支持
    public boolean enablePc; // pc是否支持 1可用，-1不支持
    public boolean isRobot; //是否是机器人
    public boolean aiRobot; // AI助理

    @Override
    public String getPointId() {
        return appId;
    }

    @Override
    public String getPointLevel() {
        return "应用";
    }
}
