package com.twl.hi.chat.widget.funcview;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;


import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.PagerAdapter;

import com.twl.hi.chat.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lib.twl.common.BR;
import lib.twl.common.adapter.CommonAdapter;

/**
 * <AUTHOR>
 * @date 2020/12/2.
 */
public class FuncViewPageAdapter extends PagerAdapter {
    private final Map<Integer, View> mPageMap = new HashMap<>();
    private List<List<ItemFunc>> mData = new ArrayList<>();
    private ChatFuncView.FuncItemClickListener funcItemClickListener;

    public void setFunItemClickListener(ChatFuncView.FuncItemClickListener funcItemClickListener) {
        this.funcItemClickListener = funcItemClickListener;
    }

    @Override
    public int getCount() {
        return mData.size();
    }

    public void notifyData(List<List<ItemFunc>> mData) {
        this.mData.clear();
        this.mData = mData;
        notifyDataSetChanged();
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }

    @NonNull
    @Override
    public Object instantiateItem(final ViewGroup container, int position) {
        View view = LayoutInflater.from(container.getContext()).inflate(R.layout.lib_common_fun_page_item, container, false);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        container.addView(view, params);
        initPageData(view.findViewById(R.id.rv_func), position);
        mPageMap.put(position, view);
        return view;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, @NonNull Object object) {
        View view = mPageMap.remove(position);
        container.removeView(view);
    }

    /**
     * 默认的notifyDataSetChanged 不好使，getItemPosition需返回POSITION_NONE才好使
     */
    @Override
    public int getItemPosition(@NonNull Object object) {
        return POSITION_NONE;
    }

    private void initPageData(RecyclerView recyclerView, int position) {
        CommonAdapter<ItemFunc> adapter = new CommonAdapter<>(R.layout.chat_view_func_item, BR.bean);
        adapter.addCallback(BR.callback, funcItemClickListener);
        recyclerView.setAdapter(adapter);
        adapter.submitList(mData.get(position));
    }
}
