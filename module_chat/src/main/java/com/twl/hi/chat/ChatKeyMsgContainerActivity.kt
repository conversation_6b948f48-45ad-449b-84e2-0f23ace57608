package com.twl.hi.chat

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.twl.hi.basic.activity.FoundationVMActivity
import com.twl.hi.basic.callback.TitleBarCallback
import com.twl.hi.export.schedule.router.SchedulePageRouter
import com.twl.hi.chat.databinding.ChatActivityKeyMsgContainerBinding
import com.twl.hi.chat.util.ChatPointUtils
import com.twl.hi.chat.viewmodel.KeyMsgActivityViewModel
import com.twl.hi.foundation.model.message.MessageConstants
import hi.kernel.Constants
import lib.twl.common.util.AppUtil

/**
 *
 * 重要消息容器页面
 *
 * 从单聊设置进入只有重要消息
 * 从群聊设置进入有重要消息&日程，可通过Tab切换
 *
 * Created by tanshicheng on 2022/5/30
 */
class ChatKeyMsgContainerActivity : FoundationVMActivity<ChatActivityKeyMsgContainerBinding, KeyMsgActivityViewModel>(), TitleBarCallback {

    private val mKeyMsgFragment by lazy {
        MarkListFragment.create(viewModel.chatId, viewModel.chatType, viewModel.isGroupManager)
    }

    private val mGroupScheduleFragment by lazy {
        SchedulePageRouter.getScheduleListFragment(viewModel.chatId)
    }

    companion object {

        const val TAG = "KeyMsg"

        @JvmStatic
        fun intentStart(context: Context) {
            val intent = Intent(context, ChatKeyMsgContainerActivity::class.java)
            AppUtil.startActivity(context, intent)
        }

        @JvmStatic
        fun jumpToActivity(context: Context?, chatId: String, chatType: Int, groupManager: Boolean) {
            context ?: return
            val intent = Intent()
            intent.putExtra(Constants.CHAT_ITEM_ID, chatId)
            intent.putExtra(Constants.CHAT_ITEM_TYPE, chatType)
            intent.putExtra(Constants.GROUP_MANAGER, groupManager)
            intent.setClass(context, ChatKeyMsgContainerActivity::class.java)
            AppUtil.startActivity(context, intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parseArgs()
        initView()
    }

    private fun parseArgs() {
        viewModel.chatId = intent.getStringExtra(Constants.CHAT_ITEM_ID)
        viewModel.chatType = intent.getIntExtra(Constants.CHAT_ITEM_TYPE, MessageConstants.MSG_SINGLE_CHAT)
        viewModel.isGroupManager = intent.getBooleanExtra(Constants.GROUP_MANAGER, false)
    }

    private fun initView() {
        setupKeyMsgTabs()
        dataBinding.keyMsgViewpager.adapter = object : FragmentStateAdapter(this) {
            override fun getItemCount() = viewModel.mTabsTitleList.size

            override fun createFragment(position: Int) = generateFragment(position)
        }
        TabLayoutMediator(
            dataBinding.keyMsgTabs,
            dataBinding.keyMsgViewpager,
            true,
            false
        ) { tab, position ->
            tab.text = viewModel.mTabsTitleList[position]
        }.attach()
    }

    private fun generateFragment(position: Int): Fragment {
        return if (position == 0) {
            mKeyMsgFragment
        } else {
            mGroupScheduleFragment
        }
    }

    private fun isFromGroupChat(): Boolean {
        return viewModel.chatType == MessageConstants.MSG_GROUP_CHAT
    }

    private fun setupKeyMsgTabs() {
        if (isFromGroupChat()) {
            viewModel.mTabsTitleList.add(getString(R.string.chat_card_schedule))
        } else {
            dataBinding.keyMsgTabs.isVisible = false
        }
        dataBinding.keyMsgTabs.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                ChatPointUtils.point(
                    ChatPointUtils.POINT_MARKED_CLICK,
                    mutableMapOf<String, String>().apply {
                        put(ChatPointUtils.PARAM_CHAT_TYPE, if (tab?.position == 0) "1" else "2")
                    }
                )
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }
        })
    }

    override fun getContentLayoutId() = R.layout.chat_activity_key_msg_container

    override fun getCallbackVariable() = BR.callback

    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel

    override fun clickLeft(view: View?) {
        AppUtil.finishActivity(this)
    }

    override fun clickRight(view: View?) {
    }
}