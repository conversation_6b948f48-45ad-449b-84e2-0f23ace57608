package com.twl.hi.chat.message.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.lifecycle.FlowLiveDataConversions;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.twl.hi.chat.message.model.FavoriteLocationBean;
import com.twl.hi.foundation.api.callback.FavoriteCallback;
import com.twl.hi.foundation.api.request.FavoriteListRequest;
import com.twl.hi.foundation.api.response.FavoriteListResponse;
import com.twl.hi.foundation.api.response.bean.FavoriteBean;
import com.twl.hi.foundation.api.response.bean.FavoriteInterface;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.message.ChatMessage;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;

import java.util.ArrayList;
import java.util.List;

import hi.kernel.Constants;
import kotlin.Unit;
import lib.twl.common.util.LList;

/**
 * <AUTHOR>
 * @date 2021/12/31.
 */
public class FavoriteListFragmentViewModel extends FavoriteListBaseViewModel {

    private final MutableLiveData<FavoriteLocationBean> locationPositionLiveData = new MutableLiveData<>();

    public FavoriteListFragmentViewModel(Application application) {
        super(application);
    }

    public MutableLiveData<FavoriteLocationBean> getLocationPositionLiveData() {
        return locationPositionLiveData;
    }

    public LiveData<Unit> getContactUpdateEvent() {
        return FlowLiveDataConversions.asLiveData(ServiceManager.getInstance().getContactService().contactUpdateFlow());
    }

    /**
     * 加载数据
     *
     * @param pageOrderType   加载顺序，0，加载更多 1，刷新更多
     * @param locationMessage 是否定位消息
     */
    public void getMessages(final int pageOrderType, final boolean locationMessage) {
        final String favorId;
        final int order;
        if (pageOrderType == Constants.PAGE_ORDER_TYPE_MORE) {
            favorId = morePageId;
            order = 2;
        } else {
            favorId = freshPageId;
            order = 1;
        }
        FavoriteListRequest request = new FavoriteListRequest(new FavoriteCallback<FavoriteListResponse>() {

            @Override
            public void handleInChildThread(ApiData<FavoriteListResponse> data) {
                super.handleInChildThread(data);
                FavoriteListResponse response = data.resp;
                List<FavoriteInterface> laterDealBeans = new ArrayList<>();
                List<FavoriteInterface> cache = FavoriteListFragmentViewModel.this.favoritesLiveData.getValue();
                if (!LList.isEmpty(cache)) {
                    laterDealBeans.addAll(cache);
                }
                boolean moreOrder = pageOrderType == Constants.PAGE_ORDER_TYPE_MORE;
                List<FavoriteBean> beans = response.favoriteBeans;
                if (!LList.isEmpty(beans)) {
                    List<ChatMessage> messages = new ArrayList<>();
                    for (FavoriteBean bean : beans) {
                        if (moreOrder) {
                            laterDealBeans.add(bean);
                        } else {
                            laterDealBeans.add(0, bean);
                        }
                        ChatMessage chatMessage = bean.message;
                        if (chatMessage != null) {
                            messages.add(chatMessage);
                            if (chatMessage.getMediaType() == MessageConstants.MSG_PIC ||
                                    chatMessage.getMediaType() == MessageConstants.MSG_RICH ||
                                    chatMessage.getMediaType() == MessageConstants.MSG_MARKDOWN_TEXT ||
                                    chatMessage.getMediaType() == MessageConstants.MSG_MESSAGE_EXTENSION_CARD
                            ) {
                                imageChatMessages.add(chatMessage);
                            }
                        }
                    }
                    updateGroupCache(messages);
                }
                if (laterDealBeans.size() > 0) {
                    setMorePageId(laterDealBeans.get(laterDealBeans.size() - 1).getFavorId());
                    setFreshPageId(laterDealBeans.get(0).getFavorId());
                }
                if (moreOrder) {
                    setHasMore(data.resp.hasMore == 1);
                } else {
                    setHasFresh(data.resp.hasMore == 1);
                }
                FavoriteListFragmentViewModel.this.favoritesLiveData.postValue(laterDealBeans);
                if (locationMessage && !LList.isEmpty(laterDealBeans)) {
                    for (int i = 0; i < laterDealBeans.size(); i++) {
                        if (TextUtils.equals(favorId, laterDealBeans.get(i).getFavorId())) {
                            locationPositionLiveData.postValue(new FavoriteLocationBean(i, favorId));
                            break;
                        }
                    }
                }
            }

            @Override
            public void onSuccess(ApiData<FavoriteListResponse> data) {
                super.onSuccess(data);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                loadFinishLiveData.postValue(true);
            }
        });

        request.favorId = favorId;
        request.order = order;
        request.include = locationMessage ? 1 : 0;
        HttpExecutor.execute(request);
    }
}
