package com.twl.hi.chat.message.callback;

import android.view.View;

import com.twl.hi.basic.callback.HighLightCallback;
import com.twl.hi.basic.callback.MessageWithInlineLinkCallback;
import com.twl.hi.basic.callback.TitleBarCallback;
import com.twl.hi.foundation.api.response.bean.FavoriteInterface;

public interface FavoriteListCallback extends TitleBar<PERSON>allback, HighLightCallback, MessageWithInlineLinkCallback {
    void onChatClick(View view, FavoriteInterface bean);

    boolean onLongClick(FavoriteInterface favoriteBean);

    void onSearchClick();
}