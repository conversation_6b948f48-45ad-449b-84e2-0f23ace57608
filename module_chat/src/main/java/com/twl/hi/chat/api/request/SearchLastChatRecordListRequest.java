package com.twl.hi.chat.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.chat.api.callback.ChatRecordRecentCallback;
import com.twl.hi.chat.api.response.SearchChatRecordRecentResponse;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class SearchLastChatRecordListRequest extends BaseApiRequest<SearchChatRecordRecentResponse> {

    @Expose
    public String version;

    public SearchLastChatRecordListRequest(ChatRecordRecentCallback<SearchChatRecordRecentResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_RECENT_RECORDS;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
