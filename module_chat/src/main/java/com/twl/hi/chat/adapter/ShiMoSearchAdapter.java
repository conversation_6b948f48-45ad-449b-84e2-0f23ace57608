package com.twl.hi.chat.adapter;

import com.twl.hi.chat.R;
import com.twl.hi.chat.api.response.bean.ShiMoSearchBean;
import com.twl.hi.chat.databinding.ChatItemShiMoSearchBinding;

import lib.twl.common.adapter.BaseDataBindingViewHolder;

/**
 * <AUTHOR>
 * @date 2022/1/4.
 */
public class ShiMoSearchAdapter extends SearchDefaultAdapter<ShiMoSearchBean, ChatItemShiMoSearchBinding> {

    public ShiMoSearchAdapter() {
        super(R.layout.chat_item_shi_mo_search, null);
    }

    @Override
    protected void bind(BaseDataBindingViewHolder<ChatItemShiMoSearchBinding> helper, ChatItemShiMoSearchBinding binding, ShiMoSearchBean item) {
        binding.setItem(item);
        binding.setSearchContent(searchContent);
    }
}
