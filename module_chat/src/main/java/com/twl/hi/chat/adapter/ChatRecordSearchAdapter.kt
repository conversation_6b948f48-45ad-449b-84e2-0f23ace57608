package com.twl.hi.chat.adapter

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.twl.hi.chat.R
import com.twl.hi.chat.callback.ChatRecordSearchAdapterCallback
import com.twl.hi.chat.callback.QueryContactInfoContract
import com.twl.hi.chat.databinding.ChatItemChatRecordSearchAdapterBinding
import com.twl.hi.chat.util.EnhanceHypertextProxy
import com.twl.hi.chat.util.buildMarkdownMessageTitle
import com.twl.hi.chat.util.buildRichMessageTitle
import com.twl.hi.chat.util.buildTextMessageTitle
import com.twl.hi.chat.util.clipAlignHighlight
import com.twl.hi.chat.util.highlightSpan
import com.twl.hi.foundation.model.message.ChatMessage
import com.twl.hi.foundation.model.message.MessageForMarkdownText
import com.twl.hi.foundation.model.message.MessageForRichText
import com.twl.hi.foundation.model.message.MessageForText
import com.twl.hi.foundation.utils.MessageUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import lib.twl.common.adapter.BaseDataBindingAdapter
import lib.twl.common.adapter.BaseDataBindingViewHolder

/**
 * Created by ChaiJiangpeng on 2020-01-21
 * Describe:
 */
class ChatRecordSearchAdapter(
    private val mCallback: ChatRecordSearchAdapterCallback,
) :
    BaseDataBindingAdapter<ChatMessage?, ChatItemChatRecordSearchAdapterBinding?>(
        R.layout.chat_item_chat_record_search_adapter, null
    ) {
    private var mViewModel: QueryContactInfoContract? = null
    private var mSearchContent: String? = null
    private val enhanceProxy = EnhanceHypertextProxy()
    fun setViewModel(viewModel: QueryContactInfoContract?) {
        mViewModel = viewModel
    }

    fun setSearchContent(searchContent: String?) {
        mSearchContent = searchContent
    }

    override fun bind(
        helper: BaseDataBindingViewHolder<ChatItemChatRecordSearchAdapterBinding?>?,
        binding: ChatItemChatRecordSearchAdapterBinding?, item: ChatMessage?
    ) {
        binding ?: return
        binding.viewModel = mViewModel
        binding.searchContent = mSearchContent
        binding.msg = item
        binding.position = helper?.adapterPosition
        binding.callback = mCallback

        (mViewModel as? ViewModel)?.viewModelScope?.launch {
            val title = withContext(Dispatchers.IO) {
                when (item) {
                    is MessageForText -> {
                        item.buildTextMessageTitle(mContext, enhanceProxy)
                    }
                    is MessageForMarkdownText -> {
                        item.buildMarkdownMessageTitle(mContext, enhanceProxy)
                    }
                    is MessageForRichText -> {
                        item.buildRichMessageTitle(mContext, enhanceProxy)
                    }
                    else -> {
                        MessageUtils.messageSearchText(item, mSearchContent)
                    }
                }
            }
            binding.tvMsg.clipAlignHighlight(1, title) {
                binding.tvMsg.text = it.highlightSpan()
            }
        }
    }

    override fun getPointLevel(): String {
        return "消息"
    }
}