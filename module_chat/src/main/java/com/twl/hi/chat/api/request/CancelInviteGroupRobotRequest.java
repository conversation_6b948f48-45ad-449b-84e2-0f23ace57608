package com.twl.hi.chat.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

public class CancelInviteGroupRobotRequest extends BaseApiRequest<HttpResponse> {

    @Expose
    public String groupId;

    @Expose
    public String robotId;

    @Expose
    public Long mid;

    public CancelInviteGroupRobotRequest(AbsRequestCallback<HttpResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_GROUP_ROBOT_CANCEL_INVITE;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
