package com.twl.hi.chat.message.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.DiffUtil;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.adapter.DataBoundListAdapter;
import com.twl.hi.chat.R;
import com.twl.hi.chat.databinding.ChatItemConversationBinding;
import com.twl.hi.chat.message.callback.ConversationListCallback;
import com.twl.hi.chat.message.viewmodel.ConversationListViewModel;
import com.twl.hi.foundation.model.Conversation;
import com.twl.hi.foundation.model.ConversationWithMessage;
import com.twl.utils.StringUtils;
import lib.twl.common.activity.AsyncInflateManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Author: <PERSON>
 * Date: 2019/03/19.
 */
public class ConversationAdapter extends DataBoundListAdapter<ConversationWithMessage, ChatItemConversationBinding> {
    private static final String TAG = "ConversationAdapter";
    private LifecycleOwner mLifecycleOwner;
    private ConversationListCallback mCallback;
    private ConversationListViewModel mViewModel;
    /**
     * 用于控制在标记tab中复用时忽略部分布局元素
     */
    private final boolean mSimpleLayout;

    public ConversationAdapter(LifecycleOwner lifecycleOwner, ConversationListCallback callback, ConversationListViewModel viewModel) {
        this(lifecycleOwner, callback, viewModel, false);
    }

    public ConversationAdapter(LifecycleOwner lifecycleOwner, ConversationListCallback callback, ConversationListViewModel viewModel, boolean simpleLayout) {
        super(new DiffUtil.ItemCallback<ConversationWithMessage>() {
            @Override
            public boolean areItemsTheSame(@NonNull ConversationWithMessage oldItem1, @NonNull ConversationWithMessage newItem1) {
                Conversation conversation1 = oldItem1.getConversation();
                Conversation conversation2 = newItem1.getConversation();
                return StringUtils.isEquals(conversation1.getChatId(), conversation2.getChatId())
                        && conversation1.getType() == conversation2.getType()
                        && conversation1.isTop() == conversation2.isTop();
            }

            @Override
            public boolean areContentsTheSame(@NonNull ConversationWithMessage oldItem1, @NonNull ConversationWithMessage newItem1) {
                return Objects.equals(oldItem1.getConversation(), newItem1.getConversation())
                        && Objects.equals(oldItem1.getMarkdownDraft(), newItem1.getMarkdownDraft())
                        && oldItem1.getHasExtraFile() == newItem1.getHasExtraFile();
            }
        });
        mLifecycleOwner = lifecycleOwner;
        mCallback = callback;
        mViewModel = viewModel;
        mSimpleLayout = simpleLayout;
    }

    @Override
    protected ChatItemConversationBinding createBinding(ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = AsyncInflateManager.getInstance().getRepeatInflateView(R.layout.chat_item_conversation);
        if (view != null) {
            return DataBindingUtil.bind(view);
        }
        return ChatItemConversationBinding.inflate(inflater, parent, false);
    }

    @Override
    protected void bind(ChatItemConversationBinding vdb, ConversationWithMessage item, int position) {
        //由于第一条添加了占位会话，所以position=0时，item的高度为0。
        ViewGroup.LayoutParams layoutParams = vdb.getRoot().getLayoutParams();
        if (position == 0) {
            layoutParams.height = 0;
            return;
        }
        TLog.debug(TAG, "绑定会话数据：" + item.getConversation().getChatId());
        layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        MainConversationAdapterKt.adaptToScaledSize(vdb);
        vdb.setCwm(item);
        vdb.setSimpleLayout(mSimpleLayout);
        vdb.setConversationContent(mViewModel.getConversationSummary(item));
        vdb.getRoot().setOnClickListener(v -> mCallback.onItemClick(v, item.getConversation()));
        vdb.getRoot().setOnLongClickListener(v -> mCallback.onItemLongClick(v, item.getConversation(), position));
        vdb.setLifecycleOwner(mLifecycleOwner);
    }

    @Override
    public void submitList(@Nullable List<ConversationWithMessage> list) {
        submitList(list, null);
    }

    @Override
    public void submitList(@Nullable List<ConversationWithMessage> list, @Nullable Runnable commitCallback) {
        if (list != null) {
            //添加空数据，使有数据的item下标从1开始，解决notifyItemInsert(0)动画不生效。
            list = new ArrayList<>(list);
            list.add(0, null);
        }
        super.submitList(list, commitCallback);
    }
}
