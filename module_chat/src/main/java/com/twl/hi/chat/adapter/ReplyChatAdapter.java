package com.twl.hi.chat.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.DiffUtil;

import com.twl.hi.basic.adapter.DataBoundListAdapter;
import com.twl.hi.basic.callback.ChatItemListener;
import com.twl.hi.chat.BR;
import com.twl.hi.chat.databinding.ChatItemMessageGoneBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyAppCardBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyAudioBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyChatShareBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyDeletedBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyEnvelopBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyFileBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyGroupCardBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyImageCardBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyLinkBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyMarkdownTextBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyMessageExtesionCardBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyNoTypeBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyOnlineFileBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyPicBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyRichTextBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyStickerBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplySystemCardBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyTaskCommentCardBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyTextBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyUserCardBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyVideoBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyVideoMeetingCardBinding;
import com.twl.hi.chat.databinding.ChatItemMessageReplyWithDrawBinding;
import com.twl.hi.chat.viewmodel.ChatBaseViewModel;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.message.ChatMessage;
import com.twl.hi.foundation.model.message.MessageAndQuote;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.model.message.MessageForVideoMeetingCard;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 消息回复的消息列表适配器
 */
public class ReplyChatAdapter extends DataBoundListAdapter<MessageAndQuote, ViewDataBinding> {
    private final LifecycleOwner mLifecycleOwner;
    private final ChatItemListener mListener;
    private final ChatBaseViewModel mViewModel;
    private final List<MessageAndQuote> mList = new ArrayList<>();
    private final float mFontScale = ServiceManager.getInstance().getSettingService().getFontScale();

    public ReplyChatAdapter(LifecycleOwner lifecycleOwner, ChatBaseViewModel viewModel, ChatItemListener listener) {
        super(new DiffUtil.ItemCallback<MessageAndQuote>() {
            @Override
            public boolean areItemsTheSame(@NonNull MessageAndQuote oldItem, @NonNull MessageAndQuote newItem) {
                return oldItem.getMessage().getMediaType() == newItem.getMessage().getMediaType()
                        && oldItem.getMessage().getMid() == newItem.getMessage().getMid();
            }

            @Override
            public boolean areContentsTheSame(@NonNull MessageAndQuote oldItem, @NonNull MessageAndQuote newItem) {
                return Objects.equals(oldItem, newItem);
            }
        });
        mLifecycleOwner = lifecycleOwner;
        mListener = listener;
        mViewModel = viewModel;
    }

    @Override
    public void submitList(List<MessageAndQuote> list) {
        mList.clear();
        mList.addAll(list);
        super.submitList(list);
    }

    public List<MessageAndQuote> getData() {
        return mList;
    }

    @Override
    public int getItemViewType(int position) {
        ChatMessage message = getItem(position).getMessage();
//        if (message.isDeleted()) {
//            return Integer.MAX_VALUE;
//        }
        if (message.isWithdrawn()) {
            return -message.getMediaType();
        }
        return message.getMediaType();
    }

    @Override
    protected ViewDataBinding createBinding(ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        if (viewType < 0) {
            return ChatItemMessageReplyWithDrawBinding.inflate(inflater, parent, false);
        }
        switch (viewType) {
            case MessageConstants.MSG_EMPTY:
            case MessageConstants.MSG_HINT:
                return ChatItemMessageGoneBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_TEXT:
                ChatItemMessageReplyTextBinding text = ChatItemMessageReplyTextBinding.inflate(inflater, parent, false);
                text.viewMessageReplyText.setFontScale(mFontScale);
                return text;
            case MessageConstants.MSG_STICKER:
                return ChatItemMessageReplyStickerBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_USER_CARD:
                return ChatItemMessageReplyUserCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_FILE:
                return ChatItemMessageReplyFileBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_AUDIO:
                return ChatItemMessageReplyAudioBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_VIDEO:
                return ChatItemMessageReplyVideoBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_CHAT_SHARE:
                return ChatItemMessageReplyChatShareBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_PIC:
                return ChatItemMessageReplyPicBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_RED_ENVELOPE:
                return ChatItemMessageReplyEnvelopBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_SYSTEM_CARD:
                return ChatItemMessageReplySystemCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_LINK:
                return ChatItemMessageReplyLinkBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_RICH:
                ChatItemMessageReplyRichTextBinding richText = ChatItemMessageReplyRichTextBinding.inflate(inflater, parent, false);
                richText.viewMessageReplyText.setFontScale(mFontScale);
                return richText;
            case MessageConstants.MSG_MARKDOWN_TEXT:
                ChatItemMessageReplyMarkdownTextBinding mdText = ChatItemMessageReplyMarkdownTextBinding.inflate(inflater, parent, false);
                mdText.viewMessageReplyText.setFontScale(mFontScale);
                return mdText;
            case MessageConstants.MSG_IMAGE_CARD:
                return ChatItemMessageReplyImageCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_FILE_ONLINE:
                return ChatItemMessageReplyOnlineFileBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_TASK_COMMENT_CARD:
                return ChatItemMessageReplyTaskCommentCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_VIDEO_MEETING_CARD:
                return ChatItemMessageReplyVideoMeetingCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_APP_CARD:
                return ChatItemMessageReplyAppCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_GROUP_CARD:
                return ChatItemMessageReplyGroupCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_MESSAGE_EXTENSION_CARD:
                return ChatItemMessageReplyMessageExtesionCardBinding.inflate(inflater, parent, false);
            case Integer.MAX_VALUE:
                return ChatItemMessageReplyDeletedBinding.inflate(inflater, parent, false);
            default:
                return ChatItemMessageReplyNoTypeBinding.inflate(inflater, parent, false);
        }
    }

    @Override
    protected void bind(ViewDataBinding vdb, MessageAndQuote item, int position) {
        vdb.setVariable(BR.msg, item.getMessage());
        vdb.setVariable(BR.quote, item.getQuote());
        vdb.setVariable(BR.listener, mListener);
        if (mViewModel != null) {
            vdb.setVariable(BR.viewModel, mViewModel);
        }
        vdb.setLifecycleOwner(mLifecycleOwner);
        if (vdb instanceof ChatItemMessageReplyVideoMeetingCardBinding) {
            //监听当前参加的会议的会议号来标记当前的会议卡片
            ChatMessageAdapterKt.observeButtonStr(((ChatItemMessageReplyVideoMeetingCardBinding) vdb).layoutVideoMeeting, (MessageForVideoMeetingCard) item.getMessage());
        }
    }
}
