package com.twl.hi.chat.chatgroup

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.twl.hi.basic.activity.FoundationVMActivity
import com.twl.hi.basic.helpers.AppPageRouterHelper
import com.twl.hi.chat.BR
import com.twl.hi.chat.R
import com.twl.hi.chat.chatgroup.callback.ChatGroupEditActivityCallback
import com.twl.hi.chat.chatgroup.viewmodel.ChatGroupEditActivityViewModel
import com.twl.hi.chat.databinding.ChatActivityChatGroupEditBinding
import com.twl.hi.chat.util.ChatGroupPointUtils
import com.twl.hi.export.select.bean.SelectBaseParams
import com.twl.hi.export.select.bean.SelectConversationParams
import com.twl.hi.export.select.router.SelectPageRouter
import com.twl.hi.foundation.model.ChatGroupWrapper
import com.twl.hi.foundation.model.ConversationSelectBean
import com.twl.hi.foundation.utils.GroupStatusCheckCallback
import com.twl.utils.StringUtils
import hi.kernel.BundleConstants
import hi.kernel.Constants
import hi.kernel.RequestCodeConstants
import lib.twl.common.adapter.CommonAdapter
import lib.twl.common.ext.dp
import lib.twl.common.ext.getResourceString
import lib.twl.common.util.ActivityAnimType
import lib.twl.common.util.AppUtil
import lib.twl.common.util.ProcessHelper
import lib.twl.common.util.ToastUtils

/**
 * 会话标签编辑页面
 * <AUTHOR>
 * @date 2022/05/30 10:34
 */
class ChatGroupEditActivity : FoundationVMActivity<ChatActivityChatGroupEditBinding,
        ChatGroupEditActivityViewModel>(), ChatGroupEditActivityCallback {

    companion object {
        @JvmStatic
        @JvmOverloads
        fun intentStart(
            context: Context,
            source: String,
            chatGroupWrapper: ChatGroupWrapper? = null,
            selectChatId: String? = null,
            selectChatType: Int? = null
        ) {
            val intent = Intent(context, ChatGroupEditActivity::class.java)
            intent.putExtra(Constants.SOURCE, source)
            if (StringUtils.isNotEmpty(selectChatId) && selectChatType != 0) {
                intent.putExtra(Constants.CHAT_ID, selectChatId)
                intent.putExtra(Constants.CHAT_TYPE, selectChatType)
            }
            val hasGuide = ProcessHelper.getUserPreferences().getBoolean(Constants.CHAT_GROUP_GUIDE, false)
            if (chatGroupWrapper == null && !hasGuide) {
                ChatGroupGuideActivity.intentStart(context, source, selectChatId, selectChatType)
            } else {
                if (chatGroupWrapper != null) {
                    intent.putExtra(Constants.GROUP, chatGroupWrapper)
                }
                AppUtil.startActivity(context, intent)
            }
        }
    }

    private val mAdapter =
        CommonAdapter(
            R.layout.chat_item_chat_in_group,
            BR.bean,
            object : DiffUtil.ItemCallback<ConversationSelectBean>() {
                override fun areItemsTheSame(
                    oldItem: ConversationSelectBean,
                    newItem: ConversationSelectBean
                ) = oldItem.chatId == newItem.chatId

                override fun areContentsTheSame(
                    oldItem: ConversationSelectBean,
                    newItem: ConversationSelectBean
                ) = oldItem.chatId == newItem.chatId
            }
        ).apply {
            addCallback(BR.callback, this@ChatGroupEditActivity)
            addVariable(BR.lifeCycleOwner, this@ChatGroupEditActivity)
        }

    override fun getContentLayoutId() = R.layout.chat_activity_chat_group_edit

    override fun getCallbackVariable() = BR.callback

    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel

    override fun clickLeft(view: View?) {
        viewModel.checkGroupStatus(object : GroupStatusCheckCallback {
            override fun onStatusNormal() {
                finish()
            }

            override fun onStatusAbnormal() {
                AppPageRouterHelper.backToMainTabActivity(this@ChatGroupEditActivity)
            }
        })
    }

    override fun clickRight(view: View?) {
        viewModel.checkGroupStatus(object : GroupStatusCheckCallback {
            override fun onStatusNormal() {
                showProgressDialog(null, false)
                handleChatTagUpdate()
            }

            override fun onStatusAbnormal() {
                ToastUtils.failure(R.string.tips_not_in_group)
            }
        })
    }

    private fun handleChatTagUpdate() {
        viewModel.requestUpdateTag({
            dismissProgressDialog()
            ToastUtils.failure("消息标签名称不能为空")
        }, {
            dismissProgressDialog()
            pointSave()
            finish()
        }) {
            dismissProgressDialog()
        }
    }

    private fun pointSave() {
        val source = intent.getStringExtra(Constants.SOURCE)
        if (source in arrayListOf(
                ChatGroupPointUtils.SOURCE_ADD_EDIT_LIST_SELECT,
                ChatGroupPointUtils.SOURCE_ADD_EDIT_SETTING_SELECT
            )
        ) {
            ChatGroupPointUtils.pointChatGroupChangeSave(
                source ?: "",
                intent.getStringExtra(Constants.CHAT_ID).orEmpty(),
                intent.getIntExtra(Constants.CHAT_TYPE, 0),
                ChatGroupPointUtils.TYPE_CHAT_GROUP_ADD
            )
        }

    }

    override fun deleteConversation(conversationSelectBean: ConversationSelectBean) {
        viewModel.deleteConversation(conversationSelectBean)
    }

    override fun addConversation() {
        val params = SelectConversationParams()
            .setTitle<SelectConversationParams>(resources.getString(R.string.select_conversation))
            .setCreateGroupVisible(SelectBaseParams.INVISIBLE)
            .setOrgVisible<SelectConversationParams>(
                SelectBaseParams.INVISIBLE)
            .setSearchVisible<SelectConversationParams>(
                SelectBaseParams.VISIBLE)
            .setOtherVisible<SelectConversationParams>(
                SelectBaseParams.VISIBLE)
            .setMultiSelect<SelectConversationParams>(true)
            .setShowFileHelper(SelectBaseParams.INVISIBLE)
            .setAddIds<SelectConversationParams>(viewModel.existIds)
            .setJumpSource(SelectConversationParams.SOURCE_JUMP_SELECT_CONVERSATION)
            .setIncludeDeleteConversation(true)
        val bundle = Bundle()
        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params)
        AppUtil.startUriForResult(
            this,
            SelectPageRouter.SELECT_CONVERSATION_ACTIVITY,
            RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION,
            bundle,
            ActivityAnimType.DEFAULT
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initView()
        initObserver()
        initData()
    }

    private fun initData() {
        val chatId = intent.getStringExtra(Constants.CHAT_ID).orEmpty()
        val chatType = intent.getIntExtra(Constants.CHAT_TYPE, 0)
        if (StringUtils.isNotEmpty(chatId) && chatType != 0) {
            viewModel.addSelectConversation(chatId, chatType)
        }
    }

    private fun initObserver() {
        viewModel.getConversations().observe(this) {
            viewModel.saveExistIds(it)
            1.dp
            dataBinding.tvGroupCount.text = R.string.chat_chat_group_count.getResourceString(
                if (it.size > 99) {
                    "99+"
                } else {
                    "${it.size}"
                }
            )
            mAdapter.submitList(it)
            viewModel.changeSaveEnable()
        }

        viewModel.saveEnable().observe(this) {
            dataBinding.titleBar.rightEnabled = it
        }
    }

    private fun initView() {
        viewModel.source = intent.getStringExtra(Constants.SOURCE)
        (intent.getSerializableExtra(Constants.GROUP) as ChatGroupWrapper?)?.run {
            viewModel.chatGroup = this
            dataBinding.title = getString(R.string.chat_edit_chat_group)
            viewModel.changeSaveEnable()
        } ?: run {
            dataBinding.title = getString(R.string.chat_add_chat_group)
        }

        dataBinding.rvConversations.layoutManager = LinearLayoutManager(this)
        dataBinding.rvConversations.adapter = mAdapter

        dataBinding.etGroupName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                s?.run {
                    viewModel.changeSaveEnable()
                }
            }
        })
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION &&
            resultCode == RESULT_OK
        ) {
            data?.let {
                it.getSerializableExtra(Constants.LIST)?.let { list ->
                    viewModel.addConversations(list as List<ConversationSelectBean>, true)
                }
            }
        }
    }

}