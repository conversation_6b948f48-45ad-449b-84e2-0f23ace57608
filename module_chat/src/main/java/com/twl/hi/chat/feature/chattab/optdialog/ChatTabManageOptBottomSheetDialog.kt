package com.twl.hi.chat.feature.chattab.optdialog

import android.os.Bundle
import com.twl.hi.chat.R
import com.twl.hi.chat.databinding.ChatTabFragmentOptBottomSheetDialogBinding
import com.twl.hi.chat.feature.chattab.ChatTabPointHelper
import com.twl.hi.chat.feature.chattab.core.ChatTabBaseViewModel
import com.twl.hi.chat.feature.chattab.edit.ChatTabEditActivity
import com.twl.hi.chat.feature.chattab.manage.ChatTabManageActivity
import lib.twl.common.util.ToastUtils

/**
 *
 * 聊天标签页 添加/管理 底部弹窗
 *
 * Created by tanshicheng on 2022/11/10
 */
open class ChatTabManageOptBottomSheetDialog : ChatTabOptBaseBottomSheetDialog<ChatTabFragmentOptBottomSheetDialogBinding, ChatTabBaseViewModel>() {

    companion object {
        const val TAB_OPT_ADD = "tab_opt_add"
        const val TAB_OPT_MANAGE = "tab_opt_manage"

        @JvmStatic
        fun newInstance(chatId: String?, chatType: Int?, tabCount: Int) = ChatTabManageOptBottomSheetDialog().apply {
            arguments = Bundle().apply {
                putString("chatId", chatId)
                putInt("chatType", chatType ?: 0)
                putInt("tabCount", tabCount)
            }
        }
    }

    override fun getOptItemList(): List<ChatTabOptMenuItem> {
        return arrayListOf<ChatTabOptMenuItem>().apply {
            add(ChatTabOptMenuItem(TAB_OPT_ADD, getString(R.string.chat_tab_title_add_tab)))
            add(ChatTabOptMenuItem(TAB_OPT_MANAGE, getString(R.string.chat_tab_title_manage_tab)))
        }
    }

    override fun onOptItemClick(optItemModel: ChatTabOptMenuItem?) {
        super.onOptItemClick(optItemModel)
        val chatId = arguments?.getString("chatId")
        val chatType = arguments?.getInt("chatType")
        val tabCount = arguments?.getInt("tabCount") ?: 0
        if (optItemModel?.type == TAB_OPT_ADD) {
            ChatTabPointHelper.pointTabOptClick("add", chatType, chatId)
            if (tabCount >= 20) {
                ToastUtils.ss(activity?.getString(R.string.chat_tab_tips_count_limit) ?: "")
                return
            }
            ChatTabEditActivity.start(activity, chatId = chatId, chatType = chatType)
        } else if (optItemModel?.type == TAB_OPT_MANAGE) {
            ChatTabPointHelper.pointTabOptClick("manage", chatType, chatId)
            ChatTabManageActivity.start(activity, chatId = chatId, chatType = chatType)
        }
        dismiss()
    }
}