package com.twl.hi.chat.widget

import kotlin.math.max
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.View.MeasureSpec.EXACTLY
import android.widget.FrameLayout
import android.widget.TextView
import com.twl.hi.chat.R
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.Hypertext
import com.twl.hi.foundation.model.NotedContact
import com.twl.hi.foundation.model.message.ChatMessage
import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.hi.foundation.model.message.MessageForMarkdownText
import com.twl.hi.foundation.model.message.MessageForRichText
import com.twl.hi.foundation.model.message.MessageForText
import com.twl.hi.foundation.utils.RichTextEscapes
import com.twl.hi.richtext.render.RichTextView
import lib.twl.common.ext.dp

/**
 * 文本消息的通用布局类型
 *
 * 纯文本 [MessageForText]
 * 富文本 [MessageForRichText]
 * MD 富文本 [MessageForMarkdownText]
 */
class TextMessageLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val collapseHeight = 360.dp
    private val expandButton: TextView
    private val collapseMask: View
    val textView: RichTextView

    private var overflow: Boolean = false
    var expandListener: OnExpandListener? = null
    var expanded: Boolean = false
        set(value) {
            if (field != value) {
                field = value
                requestLayout()
            }
        }
    var useLinkify: Boolean = false
    var fontScale: Float = 1.0F
        set(value) {
            if (field != value) {
                field = value
                textView.fontScale = value
                textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textView.textSize * value)
                expandButton.setTextSize(TypedValue.COMPLEX_UNIT_PX, expandButton.textSize * value)
                requestLayout()
            }
        }

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.TextMessageLayout)
        val maskColor = typedArray.getColor(R.styleable.TextMessageLayout_collapseMaskColor, Color.WHITE)
        val buttonColor = typedArray.getColor(R.styleable.TextMessageLayout_expandButtonColor, Color.WHITE)
        typedArray.recycle()
        LayoutInflater.from(context).inflate(R.layout.chat_layout_text_message, this)

        textView = findViewById(R.id.message_content)
        expandButton = findViewById(R.id.expend_button)
        collapseMask = findViewById(R.id.collapse_mask)

        collapseMask.background = GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            intArrayOf(
                Color.argb(0, 255, 255, 255),
                Color.argb(127, 255, 255, 255),
                Color.argb(220, 255, 255, 255),
                Color.argb(255, 255, 255, 255),
                Color.argb(255, 255, 255, 255),
            )
        )
        collapseMask.backgroundTintList = ColorStateList.valueOf(maskColor)
        expandButton.backgroundTintList = ColorStateList.valueOf(buttonColor)
        expandButton.setOnClickListener { expandListener?.onExpand() }
    }

    fun renderMessage(message: MessageForText, dynamicHypertext: List<Hypertext>?, onLinkClickListener: OnMessageClickListener?) {
        textView.imageAsBlock = false
        textView.useLinkify = useLinkify
        textView.renderMarkdown(
            markdownText = RichTextEscapes.escapeKeywords(message.content),
            sender = message.sender.senderId,
            agreements = message.agreements,
            contacts = getNotedContacts(message.atIds, message.atReads),
            hypertext = dynamicHypertext ?: message.hypertexts
        )
        textView.onLinkClickListener = RichTextView.OnLinkClickListener { view, url ->
            onLinkClickListener?.onLinkClick(view, message, url)
        }
    }

    fun renderTranslatedMessage(message: MessageForText, dynamicHypertext: List<Hypertext>?, onLinkClickListener: OnMessageClickListener?) {
        textView.imageAsBlock = false
        textView.useLinkify = useLinkify
        textView.renderMarkdown(
            markdownText = RichTextEscapes.escapeKeywords(message.translateContent),
            sender = message.sender.senderId,
            agreements = message.agreements,
            contacts = getNotedContacts(message.atIds, message.atReads),
            hypertext = dynamicHypertext ?: message.hypertexts
        )
        textView.onLinkClickListener = RichTextView.OnLinkClickListener { view, url ->
            onLinkClickListener?.onLinkClick(view, message, url)
        }
    }

    fun renderMessage(message: MessageForRichText, dynamicHypertext: List<Hypertext>?, onLinkClickListener: OnMessageClickListener?) {
        textView.imageAsBlock = true
        textView.useLinkify = useLinkify
        textView.renderMarkdown(
            title = message.title,
            markdownText = message.content,
            sender = message.sender.senderId,
            contacts = getNotedContacts(message.atIds, message.atReads),
            hypertext = dynamicHypertext ?: message.hypertexts
        )
        textView.onLinkClickListener = RichTextView.OnLinkClickListener { view, url ->
            onLinkClickListener?.onLinkClick(view, message, url)
        }
    }

    fun renderMessage(message: MessageForMarkdownText, dynamicHypertext: List<Hypertext>?, onLinkClickListener: OnMessageClickListener?) {
        textView.imageAsBlock = false
        textView.useLinkify = false
        textView.renderMarkdown(
            title = message.title,
            markdownText = message.content,
            sender = message.sender.senderId,
            contacts = getNotedContacts(message.atIds, message.atReads),
            hypertext = dynamicHypertext ?: message.hypertexts
        )
        textView.onLinkClickListener = RichTextView.OnLinkClickListener { view, url ->
            onLinkClickListener?.onLinkClick(view, message, url)
        }
    }

    fun renderTranslatedMessage(message: MessageForMarkdownText, dynamicHypertext: List<Hypertext>?, onLinkClickListener: OnMessageClickListener?) {
        textView.imageAsBlock = false
        textView.useLinkify = false
        textView.renderMarkdown(
            title = message.translateTitle,
            markdownText = message.translateContent,
            sender = message.sender.senderId,
            contacts = getNotedContacts(message.atIds, message.atReads),
            hypertext = dynamicHypertext ?: message.hypertexts
        )
        textView.onLinkClickListener = RichTextView.OnLinkClickListener { view, url ->
            onLinkClickListener?.onLinkClick(view, message, url)
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, 0) // 不限制子 View 的可用高度，让子 View 自由测量
        overflow = textView.measuredHeight > collapseHeight
        val measuredWidth = resolveWidth(widthMeasureSpec)
        val measuredHeight = resolveHeight(heightMeasureSpec)
        setMeasuredDimension(measuredWidth, measuredHeight)
        updateExpandState()
    }

    private fun resolveWidth(widthMeasureSpec: Int): Int {
        val mode = MeasureSpec.getMode(widthMeasureSpec)
        return when {
            EXACTLY == mode -> MeasureSpec.getSize(widthMeasureSpec)
            overflow && !expanded -> max(textView.measuredWidth, expandButton.measuredWidth)
            else -> textView.measuredWidth
        }
    }

    private fun resolveHeight(heightMeasureSpec: Int): Int {
        val mode = MeasureSpec.getMode(heightMeasureSpec)
        return when {
            EXACTLY == mode -> MeasureSpec.getSize(heightMeasureSpec)
            overflow && !expanded -> collapseHeight.toInt()
            else -> textView.measuredHeight
        }
    }

    private fun updateExpandState() {
        if (textView.measuredHeight <= 0) {
            return
        }
        val collapsed = overflow && !expanded
        val visibility = if (collapsed) VISIBLE else GONE
        if (expandButton.visibility != visibility) {
            expandButton.visibility = visibility
            collapseMask.visibility = visibility
        }
    }

    private fun getNotedContacts(atIds: List<String>, atRead: List<Boolean>): List<NotedContact> {
        val contactService = ServiceManager.getInstance().contactService
        return atIds.mapIndexedNotNull { index, userId ->
            if (MessageConstants.MSG_AT_ALL == userId) {
                NotedContact(userId, "", "", 0, true)
            } else {
                contactService.getContactById(userId)?.let {
                    val alreadyRead = if (atRead.size > index) atRead[index] else false
                    NotedContact(userId, it.userName, it.chatShowName, it.userType, alreadyRead)
                }
            }
        }
    }

    fun interface OnExpandListener {
        fun onExpand()
    }

    fun interface OnMessageClickListener {
        fun onLinkClick(view: View, message: ChatMessage, url: String)
    }
}
