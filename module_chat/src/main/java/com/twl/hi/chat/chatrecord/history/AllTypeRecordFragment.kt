package com.twl.hi.chat.chatrecord.history

import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.twl.hi.chat.GroupChatForSearchActivity
import com.twl.hi.chat.R
import com.twl.hi.chat.SingleChatForSearchActivity
import com.twl.hi.chat.adapter.ChatRecordSearchMessageAdapter
import com.twl.hi.chat.callback.ChatRecordSearchAdapterCallback
import com.twl.hi.chat.chatrecord.history.base.BaseChatRecordFragment
import com.twl.hi.chat.chatrecord.history.viewmodel.ChatRecordHistorySearchViewModel
import com.twl.hi.chat.chatrecord.history.viewmodel.ChatRecordHistoryTabPageViewModel
import com.twl.hi.chat.databinding.ChatFragmentChatRecordHistoryBinding
import com.twl.hi.foundation.model.message.ChatMessage
import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.hi.foundation.utils.PointUtils
import com.twl.hi.foundation.utils.point.SearchPointUtil
import lib.twl.common.util.ExecutorFactory
import lib.twl.common.util.TimeDifferenceUtil
import lib.twl.common.util.TimeTag

/**
 *@author: musa on 2022/8/25
 *@e-mail: <EMAIL>
 *@desc: 所有类型聊天记录搜索结果的fragment
 */
class AllTypeRecordFragment :
    BaseChatRecordFragment<ChatFragmentChatRecordHistoryBinding, ChatRecordHistoryTabPageViewModel, ChatRecordHistorySearchViewModel>(),
    View.OnClickListener,
    ChatRecordSearchAdapterCallback {

    private val mSearchPointUtil by lazy {
        SearchPointUtil()
    }
    private val adapter by lazy {
        ChatRecordSearchMessageAdapter(this)
    }

    override fun initRV() {
        adapter.setViewModel(activityViewModel)
        dataBinding.imageNoResult.setImageDrawable(context?.getDrawable(R.drawable.chat_ic_history_chat_mg))
        dataBinding.textNoResult.text = context?.getString(R.string.chat_no_search_result)
        dataBinding.rvChatRecordList.adapter = adapter
        dataBinding.rvChatRecordList.layoutManager =
            LinearLayoutManager(context, RecyclerView.VERTICAL, false)
        activityViewModel.messagesLiveData.observe(this, Observer { messages: List<ChatMessage>? ->
            messages?.let {
                if (shouldReceiveData()) {
                    mSearchPointUtil.updateSearchId(activityViewModel.currentSearchPointId)
                    if (activityViewModel.isFirstPage) {
                        //消息tab下 没有筛选条件的情况 展示空白提示
                        if (activityViewModel.searchShouldIntercept()) {
                            activityViewModel.setSearchResult(false)
                            setNoSearchState(true)
                        } else {
                            setNoSearchState(false)
                            activityViewModel.setSearchResult(it.isEmpty())
                        }
                        adapter.setNewData(messages)
                    } else {
                        adapter.addData(messages)
                    }
                    adapter.setSearchContent(activityViewModel.searchState.content)
                    if (activityViewModel.searchState.hasMore) {
                        dataBinding.smartRefreshLayout.setNoMoreData(false)
                        dataBinding.smartRefreshLayout.finishLoadMore(true)
                        dataBinding.smartRefreshLayout.setEnableLoadMore(true)
                    } else {
                        dataBinding.smartRefreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
            }
        })
        mSearchPointUtil.setExposeListener(dataBinding.rvChatRecordList, adapter)
    }

    override fun getMediaType() = activityViewModel.pages[0].second

    override fun onItemClick(bean: ChatMessage, position: Int) {
        ExecutorFactory.execLocalTask {
            TimeDifferenceUtil.getInstance().start(TimeTag.LOCATE_MESSAGE)
            if (activityViewModel.searchState.chatType == MessageConstants.MSG_GROUP_CHAT) {
                GroupChatForSearchActivity.start(
                    context,
                    activityViewModel.searchState.chatId,
                    bean.mid,
                    bean.seq
                )
            } else {
                SingleChatForSearchActivity.start(
                    context,
                    activityViewModel.searchState.chatId,
                    bean.mid,
                    bean.seq
                )
            }

            PointUtils.BuilderV4()
                .params("search_id", activityViewModel.currentSearchPointId)
                .params("info", bean.mid)
                .params("level", "消息")
                .name("search-result-click")
                .point()
        }
    }

    private fun setNoSearchState(noSearch: Boolean) {
        dataBinding.llNoSearch.visibility = if (noSearch) View.VISIBLE else View.GONE
    }

}