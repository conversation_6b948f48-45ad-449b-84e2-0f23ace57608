package com.twl.hi.chat.message.viewmodel;

import android.app.Application;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.chat.R;
import com.twl.hi.chat.message.api.request.GroupRemarkUpdateRequest;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;

import lib.twl.common.util.ToastUtils;

public class GroupRemarkChangeViewModel extends FoundationViewModel  {
    private static final String TAG = "GroupRemarkChangeViewModel";
    public static final int GROUP_REMARK_UPDATE_FAIL = 0;
    public static final int GROUP_REMARK_UPDATE_SUCCESE = 1;
    private String mGroupID;
    private ObservableField<String> mChangedGroupRemark = new ObservableField<>();
    private ObservableField<String> mGroupRemark = new ObservableField<>();
    private MutableLiveData<Integer> status = new MutableLiveData<>();
    private ObservableBoolean showEditBtn = new ObservableBoolean(true);

    public GroupRemarkChangeViewModel(Application application) {
        super(application);
    }

    public ObservableBoolean getShowEditBtn() {
        return showEditBtn;
    }

    public ObservableField<String> getChangedGroupRemark() {
        return mChangedGroupRemark;
    }

    public ObservableField<String> getGroupRemark() {
        return mGroupRemark;
    }

    public MutableLiveData<Integer> getStatus() {
        return status;
    }

    public void setGroupID(String groupID) {
        this.mGroupID = groupID;
        initInBackground();
    }

    @Override
    protected void initData() {
        super.initData();
    }

    /**
     * 修改群名称
     */
    public void changeGroupRemark() {
        GroupRemarkUpdateRequest request = new GroupRemarkUpdateRequest(new BaseApiRequestCallback<HttpResponse>() {
            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);
                ServiceManager.getInstance().getGroupService().updateGroupRemark(mGroupID, mChangedGroupRemark.get().trim());
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                status.postValue(GROUP_REMARK_UPDATE_SUCCESE);
                ToastUtils.success(R.string.save_success);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                status.postValue(GROUP_REMARK_UPDATE_FAIL);
            }
        });
        try {
            request.groupId = mGroupID;
            request.groupRemark = mChangedGroupRemark.get().trim();
            HttpExecutor.execute(request);
        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
        }
    }

    public void init(String groupId, String groupName) {
        setGroupID(groupId);
        mGroupRemark.set(groupName);
    }
}
