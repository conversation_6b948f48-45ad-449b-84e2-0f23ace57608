package com.twl.hi.chat.callback;

import android.view.View;
import android.widget.TextView;

import com.twl.hi.basic.callback.ChatCommonWordsCallback;
import com.twl.hi.basic.callback.TitleBarCallback;
import com.twl.hi.basic.feature.scheduledmsg.OnScheduledMsgSetCallback;
import com.twl.hi.chat.model.ChatClickOptionBean;
import com.twl.hi.richtext.editor.RichEditDialog;

/**
 * 单聊&群聊通用回调
 * Author: <PERSON>
 * Date: 2019/03/14.
 */
public interface InputChatPageCallback extends TitleBarCallback, TextView.OnEditorActionListener, View.OnFocusChangeListener, ChatCommonWordsCallback,
        OnEmojiReplyCallback, MsgMultipleOptCallback, OnScheduledMsgSetCallback, RichEditDialog.OnRichEditActionListener  {

    /**
     * 消息发送
     */
    void onSendClick();

    /**
     * 长按消息发送
     */
    boolean onSendLongClick();

    /**
     * 定时发送
     */
    void onScheduleSendClick();

    /**
     * 富文本编辑点击
     */
    void onOpenRichEditClick();

    /**
     * 消息长按选项点击
     */
    void onMsgOptionItemClick(View view, ChatClickOptionBean chatClickOptionBean);

    /**
     * 语音消息切换点击
     */
    void onSwitchVoiceClick();

    /**
     * 取消消息回复
     */
    void clickCloseReply();
}
