package com.twl.hi.chat.feed.scene

import android.content.Context
import android.os.Bundle
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import com.twl.hi.basic.model.WebViewBean
import com.twl.hi.basic.views.menu.MenuItem
import com.twl.hi.chat.R
import com.twl.hi.chat.databinding.ChatFragmentConversationMainTabBinding
import com.twl.hi.chat.feed.F1Scene
import com.twl.hi.chat.message.adapter.ConversationAdapter
import com.twl.hi.chat.message.callback.ChatItemClickDelegation
import com.twl.hi.chat.message.callback.ConversationListCallback
import com.twl.hi.chat.message.viewmodel.ConversationListViewModel
import com.twl.hi.export.webview.WebViewPageRouter
import hi.kernel.Constants
import kotlinx.coroutines.launch
import lib.twl.common.util.AppUtil

/**
 * f1-单聊的会话列表
 */
class AIAssistantScene(
    private val context: Context,
    lifecycleOwner: LifecycleOwner,
    viewModel: ConversationListViewModel,
    dataBinding: ChatFragmentConversationMainTabBinding
) : F1Scene(lifecycleOwner),
    ConversationListCallback by ChatItemClickDelegation(context, viewModel, menuBuilder = {
        add(MenuItem.TYPE_TOP)
        add(MenuItem.TYPE_READ_LABEL)
        add(MenuItem.TYPE_MARK)
        add(MenuItem.TYPE_CHAT_GROUP)
        add(MenuItem.TYPE_SILENCE)
        add(MenuItem.TYPE_ARCHIVE)
        add(MenuItem.TYPE_HIDE)
        add(MenuItem.TYPE_DELETE)
    }) {

    private val conversationsFlow = viewModel.getAIAssistantConversation()
    private val adapter = ConversationAdapter(lifecycleOwner, this, viewModel, false)
    private val recyclerView = dataBinding.chatContent
    private val emptyOrNot: (Boolean) -> Unit = {
        dataBinding.changeEmptyScreen(title = if (it) context.getString(R.string.no_conversation) else null, colorActionText = "查看AI助理使用说明", colorAction = {
            //跳转直书链接
            val bean = WebViewBean()
            bean.url = "https://zhishu.zhipin.com/wiki/rKaqAiHTs3N"
            val bundle = Bundle()
            bundle.putSerializable(Constants.DATA_WEB_BEAN, bean)
            AppUtil.startUri(context, WebViewPageRouter.WEB_VIEW_ACTIVITY, bundle)
        })
    }

    override fun onEnterScene() {
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = adapter

        sceneScope.launch {
            conversationsFlow.collect {
                emptyOrNot(it.isEmpty())
                adapter.submitList(it.toMutableList())
            }
        }
    }
}