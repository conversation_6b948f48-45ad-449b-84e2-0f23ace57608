package com.twl.hi.chat.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.DiffUtil;

import com.twl.hi.basic.adapter.DataBoundListAdapter;
import com.twl.hi.basic.callback.ChatItemListener;
import com.twl.hi.chat.BR;
import com.twl.hi.chat.R;
import com.twl.hi.chat.databinding.ChatItemChatRecordAppCardBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordAudioBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordChatShareBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordDeletedBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordFileBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordGroupCardBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordHintBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordImageCardBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordLinkBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordLinkCallBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordMarkdownTextBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordMessageExtensionCardBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordOnlineFileBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordPicBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordRedEnvelopBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordRichTextBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordStickerBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordTextBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordUserCardBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordVideoBinding;
import com.twl.hi.chat.databinding.ChatItemChatRecordVideoMeetingCardBinding;
import com.twl.hi.chat.databinding.ChatItemMessageGoneBinding;
import com.twl.hi.chat.databinding.ChatItemMessageRecordNoTypeBinding;
import com.twl.hi.chat.viewmodel.ChatRecordViewModel;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.message.ChatMessage;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.model.message.MessageForVideoMeetingCard;
import com.twl.utils.StringUtils;

/**
 * 合并转发消息中的消息详情列表适配器
 */
public class ChatRecordAdapter extends DataBoundListAdapter<ChatMessage, ViewDataBinding> {
    private final LifecycleOwner mLifecycleOwner;
    private final ChatItemListener mChatItemListener;
    private ChatRecordViewModel chatRecordViewModel;
    private final float mFontScale = ServiceManager.getInstance().getSettingService().getFontScale();

    public ChatRecordAdapter(LifecycleOwner lifecycleOwner, ChatItemListener listener) {
        super(new DiffUtil.ItemCallback<ChatMessage>() {
            @Override
            public boolean areItemsTheSame(@NonNull ChatMessage oldItem, @NonNull ChatMessage newItem) {
                return false;
            }

            @Override
            public boolean areContentsTheSame(@NonNull ChatMessage oldItem, @NonNull ChatMessage newItem) {
                return false;
            }
        });
        mLifecycleOwner = lifecycleOwner;
        mChatItemListener = listener;
    }

    public void setViewModel(ChatRecordViewModel viewModel) {
        chatRecordViewModel = viewModel;
    }

    @Override
    public int getItemViewType(int position) {
        ChatMessage message = getItem(position);
        if (message.isDeleted()) {
            return -message.getMediaType();
        }
        return message.getMediaType();
    }

    @Override
    protected ViewDataBinding createBinding(ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        return createDataBinding(inflater, parent, viewType);
    }

    public ViewDataBinding createDataBinding(LayoutInflater inflater, ViewGroup parent, int viewType) {
        if (viewType < 0) {
            return ChatItemChatRecordDeletedBinding.inflate(inflater, parent, false);
        }
        switch (viewType) {
            case MessageConstants.MSG_TEXT:
                ChatItemChatRecordTextBinding binding = ChatItemChatRecordTextBinding.inflate(inflater, parent, false);
                binding.content.setFontScale(mFontScale);
                return binding;
            case MessageConstants.MSG_STICKER:
                return ChatItemChatRecordStickerBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_AUDIO:
                return ChatItemChatRecordAudioBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_CHAT_SHARE:
                ChatItemChatRecordChatShareBinding chatShareBinding = ChatItemChatRecordChatShareBinding.inflate(inflater, parent, false);
                chatShareBinding.content.getRoot().setBackgroundResource(R.color.color_f8f8f8);
                return chatShareBinding;
            case MessageConstants.MSG_FILE:
                return ChatItemChatRecordFileBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_PIC:
                return ChatItemChatRecordPicBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_USER_CARD:
                ChatItemChatRecordUserCardBinding userCardBinding = ChatItemChatRecordUserCardBinding.inflate(inflater, parent, false);
                userCardBinding.content.getRoot().setBackgroundResource(R.color.color_f8f8f8);
                return userCardBinding;
            case MessageConstants.MSG_VIDEO:
                return ChatItemChatRecordVideoBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_RED_ENVELOPE:
                return ChatItemChatRecordRedEnvelopBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_LINK_CALL:
                return ChatItemChatRecordLinkCallBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_LINK:
                return ChatItemChatRecordLinkBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_HINT:
                return ChatItemChatRecordHintBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_RICH:
                ChatItemChatRecordRichTextBinding richTextBinding = ChatItemChatRecordRichTextBinding.inflate(inflater, parent, false);
                richTextBinding.content.setFontScale(mFontScale);
                return richTextBinding;
            case MessageConstants.MSG_MARKDOWN_TEXT:
                ChatItemChatRecordMarkdownTextBinding mdText = ChatItemChatRecordMarkdownTextBinding.inflate(inflater, parent, false);
                mdText.content.setFontScale(mFontScale);
                return mdText;
            case MessageConstants.MSG_IMAGE_CARD:
                return ChatItemChatRecordImageCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_FILE_ONLINE:
                return ChatItemChatRecordOnlineFileBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_APP_CARD:
                return ChatItemChatRecordAppCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_GROUP_CARD:
                return ChatItemChatRecordGroupCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_VIDEO_MEETING_CARD:
                return ChatItemChatRecordVideoMeetingCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_MESSAGE_EXTENSION_CARD:
                return ChatItemChatRecordMessageExtensionCardBinding.inflate(inflater, parent, false);
            case MessageConstants.MSG_EMPTY:
            case MessageConstants.MSG_SYSTEM_CARD:
                return ChatItemMessageGoneBinding.inflate(inflater, parent, false);
            default:
                return ChatItemMessageRecordNoTypeBinding.inflate(inflater, parent, false);
        }
    }

    @Override
    protected void bind(ViewDataBinding vdb, ChatMessage item, int position) {
        vdb.setVariable(BR.msg, item);
        vdb.setVariable(BR.listener, mChatItemListener);
        if (chatRecordViewModel != null) {
            vdb.setVariable(BR.viewModel, chatRecordViewModel);
        }
        vdb.setLifecycleOwner(mLifecycleOwner);

        if (vdb instanceof ChatItemChatRecordVideoMeetingCardBinding) {
            //监听当前参加的会议的会议号来标记当前的会议卡片
            ChatMessageAdapterKt.observeButtonStr(((ChatItemChatRecordVideoMeetingCardBinding) vdb).layoutVideoMeeting, (MessageForVideoMeetingCard) item);
        }
    }

    @Override
    public long getItemId(int position) {
        return getItemCount() > 0 ? StringUtils.getHashCode(getItem(position).getChatId()) : -1;
    }
}
