package com.twl.hi.chat.message.callback

import com.twl.hi.chat.chatgroup.callback.ChatTagItemClickListener

/**
 * <AUTHOR>
 * @date 2022/04/25 15:31
 */
interface ConversationMainTabFragmentCallback : ChatTagItemClickListener {
    /**
     * 打开左边侧边栏
     */
    fun openLeftDraw()

    /**
     * 打开搜索页
     */
    fun onSearchClick()

    /**
     * 打开"+"菜单
     */
    fun onMoreClick()

    /**
     * 顶部pc登录状态点击
     */
    fun onPCStatusClick()

    /**
     * 点击会话顶部日程
     */
    fun onScheduleClick(position: Int)

    /**
     * 扫一扫
     */
    fun onQRScanClick()

    /**
     * 创建群聊
     */
    fun onGroupChatCreateClick()

    /**
     * 创建部门群
     */
    fun onDepartmentGroupChatClick()

    /**
     * 邀请
     */
    fun onInviteClick()

    /**
     * feed列表Tab排序点击
     */
    fun onFeedTabSortClick();

    /**
     * 开启视频会议
     */
    fun initiateMeeting()

    /**
     * 加入视频会议
     */
    fun joinMeeting()

    /**
     * 重新入会
     */
    fun reenterMeeting()

    /**
     * 关闭重新入会提示
     */
    fun closeReenterMeetingTip()

    /**
     * 开启AI速记
     */
    fun audioAiShortHand()
}