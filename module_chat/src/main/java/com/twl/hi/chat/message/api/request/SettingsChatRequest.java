package com.twl.hi.chat.message.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.chat.message.api.response.SettingsChatResponse;
import com.twl.http.callback.ApiRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

/**
 * Author: <PERSON>
 */
public class SettingsChatRequest extends BaseApiRequest<SettingsChatResponse> {

    /**
     * chatId	T文本	是
     * 100001
     * 设置的用户id或群id      类型：id
     * chatType	T文本	是
     * 1
     * 聊天类型；1-单聊，2-群聊
     * top	T文本	否
     * 1
     * 是否免打扰；0-不免打扰，1-免打扰
     * silence	T文本	否
     * 1
     * 是否置顶；0-不置顶，1-置顶；
     */
    @Expose
    public String chatId;
    @Expose
    public int chatType;
    @Expose
    public Integer top;
    @Expose
    public Integer silence;
    @Expose
    public int source;

    public SettingsChatRequest(ApiRequestCallback<SettingsChatResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_SETTING_CHAT;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
