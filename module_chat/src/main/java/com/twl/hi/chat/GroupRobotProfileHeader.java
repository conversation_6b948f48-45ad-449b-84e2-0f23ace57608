package com.twl.hi.chat;

import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.view.View;

import com.twl.hi.chat.databinding.ChatLayoutGroupRobotProfileHeadBinding;

/**
 * 通用的机器人名片头部，展示基本的机器人属性
 */
public class GroupRobotProfileHeader {

    private final ChatLayoutGroupRobotProfileHeadBinding mBinding;

    public GroupRobotProfileHeader(ChatLayoutGroupRobotProfileHeadBinding binding) {
        mBinding = binding;
    }

    public void bindData(Model detail) {
        if (TextUtils.isEmpty(detail.icon)) {
            mBinding.robotIcon.setImageResource(R.drawable.chat_ic_default_group_robot);
        } else {
            mBinding.robotIcon.setImageURI(detail.icon);
        }
        int startColor = TextUtils.isEmpty(detail.color) ? Color.parseColor("#5D68E8") : Color.parseColor(detail.color);
        int[] gradient = {startColor, Color.WHITE, Color.TRANSPARENT};
        GradientDrawable drawable = new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, gradient);
        drawable.setAlpha(100);
        mBinding.getRoot().setBackground(drawable);
        mBinding.robotName.setText(detail.name);
        mBinding.markDeprecated.setVisibility(detail.deprecated ? View.VISIBLE : View.GONE);
        if (TextUtils.isEmpty(detail.description)) {
            mBinding.robotDescription.setVisibility(View.GONE);
        } else {
            mBinding.robotDescription.setVisibility(View.VISIBLE);
            mBinding.robotDescription.setText(detail.description);
        }
        mBinding.actionSendMessage.getRoot().setVisibility(detail.sendMessageVisible ? View.VISIBLE : View.GONE);
        mBinding.actionEnterApp.getRoot().setVisibility(detail.enterAppVisible ? View.VISIBLE : View.GONE);
    }

    /**
     * 基础头部的视图模型数据
     */
    public static class Model {
        public final String color;
        public final String icon;
        public final String name;
        public final String description;
        public final boolean deprecated;
        public final boolean sendMessageVisible;
        public final boolean enterAppVisible;

        public Model(
                String color,
                String icon,
                String name,
                String description,
                boolean deprecated,
                boolean sendMessageVisible,
                boolean enterAppVisible
        ) {
            this.color = color;
            this.icon = icon;
            this.name = name;
            this.description = description;
            this.deprecated = deprecated;
            this.sendMessageVisible = sendMessageVisible;
            this.enterAppVisible = enterAppVisible;
        }
    }
}
