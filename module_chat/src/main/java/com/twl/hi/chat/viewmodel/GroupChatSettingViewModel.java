package com.twl.hi.chat.viewmodel;

import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.export.select.bean.SelectBaseParams;
import com.twl.hi.export.select.bean.SelectConversationParams;
import com.twl.hi.chat.GroupShareActivity;
import com.twl.hi.chat.R;
import com.twl.hi.chat.api.request.GroupMemberCountRequest;
import com.twl.hi.chat.api.request.GroupQuitRequest;
import com.twl.hi.chat.api.request.GroupShareCheckRequest;
import com.twl.hi.chat.api.request.SingleGroupSyncRequest;
import com.twl.hi.chat.api.response.GroupMemberCountResponse;
import com.twl.hi.chat.api.response.GroupShareCheckResponse;
import com.twl.hi.chat.api.response.SingleGroupSyncResponse;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.Group;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.model.message.MessageForGroupCard;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.callback.ApiRequestCallback;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;
import com.twl.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import hi.kernel.Constants;
import hi.kernel.HiKernel;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.LList;
import lib.twl.common.util.ToastUtils;

public class GroupChatSettingViewModel extends ChatSettingSwitchViewModel {
    private static final int TOP_MEMBER_COUNT_LIMIT = 7;
    private int mGroupTopMemberCount = TOP_MEMBER_COUNT_LIMIT;
    private static final String TAG = "GroupChatSettingViewModel";
    public MutableLiveData<List<Contact>> topMembers = new MutableLiveData<>();
    private ObservableField<Group> mGroup = new ObservableField<>();
    private ObservableField<String> mTotal = new ObservableField<String>();
    private MutableLiveData<Boolean> mQuitChat = new MutableLiveData<>();
    private ArrayList<String> allIds = new ArrayList<>();
    public ObservableBoolean canQuitBySelf = new ObservableBoolean(true);
    public ObservableBoolean mIsGroupManager = new ObservableBoolean(false);
    private String deptId;
    private int outDeptMemberCount = 0; //非本部门的数量

    public GroupChatSettingViewModel(Application application) {
        super(application);
    }

    @Override
    public int getChatType() {
        return MessageConstants.MSG_GROUP_CHAT;
    }

    public String getDeptId() {
        return deptId;
    }

    public void updateGroup(Group group) {
        mGroup.set(group);
        setGroupTopMemberCount();
        ExecutorFactory.execLocalTask(() -> {
            updateGroupSetting(group);
            getTopMembers(group);
        });
        if (group != null && group.getGroupMembers() != null) {
            GroupMemberCountRequest request = new GroupMemberCountRequest(new ApiRequestCallback<GroupMemberCountResponse>() {
                @Override
                public void onSuccess(ApiData<GroupMemberCountResponse> groupMemberCountResponseApiData) {
                    if (groupMemberCountResponseApiData != null && groupMemberCountResponseApiData.resp != null && groupMemberCountResponseApiData.resp.userCount != group.getGroupMembers().size()) {
                        SingleGroupSyncRequest singleGroupSyncRequest = new SingleGroupSyncRequest(new ApiRequestCallback<SingleGroupSyncResponse>() {
                            @Override
                            public void handleInChildThread(ApiData<SingleGroupSyncResponse> singleGroupSyncResponseApiData) {
                                super.handleInChildThread(singleGroupSyncResponseApiData);
                                if (singleGroupSyncResponseApiData.resp.group == null) {
                                    TLog.error(TAG, "singleGroupSyncResponseApiData group is null.");
                                    return;
                                }
                                ServiceManager.getInstance().getGroupService().addGroup(new Group(singleGroupSyncResponseApiData.resp.group));
                            }

                            @Override
                            public void onSuccess(ApiData<SingleGroupSyncResponse> data) {

                            }

                            @Override
                            public void onComplete() {

                            }

                            @Override
                            public void onFailed(ErrorReason reason) {

                            }
                        });
                        singleGroupSyncRequest.groupId = group.getGroupId();
                        HttpExecutor.execute(singleGroupSyncRequest);
                    }
                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            request.groupId = group.getGroupId();
            HttpExecutor.execute(request);
        }
    }

    /**
     * 实际展示的群成员数量
     * UI优化后一行最多展示8个item，同时要根据权限考虑是否展示添加&删除item，所以会有好几种情况
     */
    private void setGroupTopMemberCount() {
        if (isAllDepartment()) { //全员群，展示8个
            mGroupTopMemberCount = TOP_MEMBER_COUNT_LIMIT;
        } else if (isOwner()) { //群管理/群主，展示6个
            mGroupTopMemberCount = TOP_MEMBER_COUNT_LIMIT - 2;
        } else if (addMemberOpen()) { //普通成员,且仅群管理和群主可添加成员，展示8个
            mGroupTopMemberCount = TOP_MEMBER_COUNT_LIMIT;
        } else { //普通成员也可添加
            mGroupTopMemberCount = TOP_MEMBER_COUNT_LIMIT - 1;
        }
    }

    private void updateGroupSetting(Group group) {
        mIsGroupManager.set(isGroupManager());
        int size;
        if (showAddMember()) {
            List<String> allIds = ServiceManager.getInstance().getContactService().getGroupMembersIds(chatId);
            this.allIds.clear();
            if (!LList.isEmpty(allIds)) {
                this.allIds.addAll(allIds);
            }
            size = this.allIds.size();
        } else {
            size = ServiceManager.getInstance().getContactService().getGroupMemberCount(chatId);
        }
        mTotal.set(size > Constants.GROUP_SHOW_MAX_COUNT ? Constants.GROUP_SHOW_MAX_COUNT_STRING : String.valueOf(size));
        deptId = group.getDeptId();
        if (StringUtils.isNotEmpty(deptId)) {
            outDeptMemberCount = ServiceManager.getInstance().getContactService().getMemberCountOutDepartment(chatId);
        }
        canQuitBySelf.set(showGroupDelete(deptId));
    }

    /**
     * @param deptId
     * @return
     */
    private boolean showGroupDelete(String deptId) {

        if (TextUtils.equals(deptId, Constants.All_STAFF_GROUP)) { //全员群不可以退出
            return false;
        } else if (!TextUtils.equals(deptId, Constants.NOT_DEPARTMENT_GROUP)) { //部门群
            Contact me = ServiceManager.getInstance().getContactService().queryContactFromDb(HiKernel.getHikernel().getAccount().getUserId());
            return Optional.ofNullable(me).map(Contact::getAboveDeptIds).map(it -> !it.contains(deptId)).orElse(true);
        }
        return true;
    }

    public void getTopMembers(Group group) {
        List<Contact> result = new ArrayList<>(mGroupTopMemberCount);
        String ownerId = group.getOwnerId();
        Contact owner = ServiceManager.getInstance().getContactService().getContactById(ownerId);
        if (owner != null) {
            result.add(owner);
        }
        List<String> managerIds = group.getManagerIds();
        if (!LList.isEmpty(managerIds)) {
            List<Contact> managers = ServiceManager.getInstance().getContactService().getGroupContactInIds(chatId, managerIds);
            if (!LList.isEmpty(managers)) {
                int remainCount = mGroupTopMemberCount - 1;
                if (managers.size() <= remainCount) {
                    result.addAll(managers);
                } else {
                    for (int i = 0; i < remainCount; i++) {
                        result.add(managers.get(i));
                    }
                }
            }
        }
        int limit = mGroupTopMemberCount - result.size();
        if (limit > 0) {
            List<String> ids = new ArrayList<>();
            ids.add(ownerId);
            if (!LList.isEmpty(managerIds)) {
                ids.addAll(managerIds);
            }
            List<Contact> normal = ServiceManager.getInstance().getContactService().getGroupContactNotInIds(chatId, ids, limit);
            if (!LList.isEmpty(normal)) {
                result.addAll(normal);
            }
        }
        this.topMembers.postValue(result);
    }

    public ArrayList<String> convertUserIds() {
        return allIds;
    }

    public ObservableField<Group> getGroup() {
        return mGroup;
    }

    public ObservableField<String> getTotal() {
        return mTotal;
    }

    public void deleteGroup() {
        GroupQuitRequest request = new GroupQuitRequest(new BaseApiRequestCallback<HttpResponse>() {
            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);
                ServiceManager.getInstance().getConversationService().delete(chatId, MessageConstants.MSG_GROUP_CHAT);
                ServiceManager.getInstance().getGroupService().deleteGroup(chatId, MessageConstants.GROUP_CHAT_STATUS_QUIT);
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                ToastUtils.success("退群成功");
                mQuitChat.setValue(true);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                mQuitChat.setValue(false);
            }
        });
        request.groupId = getGroup().get().getGroupId();
        HttpExecutor.execute(request);
    }

    public MutableLiveData<Boolean> getQuitChat() {
        return mQuitChat;
    }

    /**
     * 是否是全员群
     *
     * @return
     */
    public boolean isAllDepartment() {
        if (mGroup == null || mGroup.get() == null) {
            return false;
        }
        return TextUtils.equals(mGroup.get().getDeptId(), Constants.All_STAFF_GROUP);
    }

    public boolean isOwner() {
        if (mGroup == null || mGroup.get() == null) {
            return false;
        }
        return mGroup.get().getAdminRole() != Constants.TYPE_GROUP_NORMAL;
    }

    public int getAdminRole() {
        if (mGroup == null || mGroup.get() == null) {
            return Constants.TYPE_GROUP_NORMAL;
        }
        return mGroup.get().getAdminRole();
    }

    public boolean addMemberOpen() {
        if (mGroup == null || mGroup.get() == null) {
            return false;
        }
        return mGroup.get().getOwnerAddUser() == Constants.GROUP_OWNER_SETTING_OPEN;
    }

    public boolean showAddMember() {
        if (mGroup == null || mGroup.get() == null) {
            return false;
        }
        if (TextUtils.equals(mGroup.get().getDeptId(), Constants.All_STAFF_GROUP)) {
            return false;
        }
        if (mGroup.get().getAdminRole() != Constants.TYPE_GROUP_NORMAL) {
            return true;
        }
        return mGroup.get().getOwnerAddUser() != Constants.GROUP_OWNER_SETTING_OPEN;
    }

    /**
     * 是否显示成员删除操作
     *
     * @return
     */
    public boolean showReduceMember() {
        if (mGroup == null || mGroup.get() == null) {
            return false;
        }
        if (TextUtils.equals(mGroup.get().getDeptId(), Constants.All_STAFF_GROUP)) { //全员群
            return false;
        }
        return mGroup.get().getAdminRole() != Constants.TYPE_GROUP_NORMAL
                && (StringUtils.isNotEmpty(mGroup.get().getDeptId())
                && StringUtils.isNotEquals(mGroup.get().getGroupId(), Constants.NOT_DEPARTMENT_GROUP)
                && outDeptMemberCount > 0 || StringUtils.isNotEmpty(mGroup.get().getDeptId()))
                && topMembers.getValue().size() > 1;
    }

    public boolean isGroupManager() {
        if (getGroup() != null && getGroup().get() != null) {
            String userId = HiKernel.getHikernel().getAccount().getUserId();
            if (TextUtils.equals(userId, getGroup().get().getOwnerId()) || getGroup().get().getManagerIds().contains(userId)) {
                return true;
            }
        }
        return false;
    }

    public void checkForGroupShare(Context context) {
        final Group group = getGroup().get();
        GroupShareCheckRequest request = new GroupShareCheckRequest(new BaseApiRequestCallback<GroupShareCheckResponse>() {
            @Override
            public void onSuccess(ApiData<GroupShareCheckResponse> data) {
                SelectConversationParams params = new SelectConversationParams()
                        .setTitleVisible(SelectBaseParams.INVISIBLE)
                        .setGroupCardSource(2)
                        .setSendMessageType(MessageConstants.MSG_GROUP_CARD)
                        .setTitle(getApplication().getResources().getString(R.string.select_contact))
                        .setOrgVisible(SelectBaseParams.VISIBLE)
                        .setSearchVisible(SelectBaseParams.VISIBLE)
                        .setOtherVisible(SelectBaseParams.VISIBLE)
                        .setMaxSelectCount(50);
                params.sendMessageContent.content = new MessageForGroupCard.GroupCard(
                        group.getGroupId(),
                        group.getAvatar(),
                        group.getTinyAvatar(),
                        group.getGroupName(),
                        group.getMembers().size(),
                        group.getBulletin()
                );
                params.sendMessageContent.msgShowContent = "[群名片]";
                GroupShareActivity.intentStart(context, group, params);
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }

        });
        if (group != null) {
            request.groupId = group.getGroupId();
            HttpExecutor.execute(request);
        }
    }

}