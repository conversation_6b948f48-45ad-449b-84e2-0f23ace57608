package com.twl.hi.chat.widget;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.facebook.cache.common.CacheKey;
import com.facebook.cache.common.SimpleCacheKey;
import com.facebook.common.references.CloseableReference;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.bitmaps.PlatformBitmapFactory;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.facebook.imagepipeline.request.Postprocessor;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.BottomListDialog;
import com.twl.hi.basic.model.SelectBottomBean;
import com.twl.hi.basic.util.ThemeUtils;
import com.twl.hi.chat.R;
import com.twl.hi.chat.model.SelectBottomHighlightBean;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.request.ZhishuPermissionRequest;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.message.ChatMessage;
import com.twl.hi.foundation.model.security.BusinessModuleEnum;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.List;

import hi.kernel.HiKernel;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.util.ToastUtils;

public class ZhishuPreviewLayout extends ConstraintLayout {

    private static final int PERMISSION_NO_PERMISSION = -1;
    private static final int PERMISSION_READ_PERMISSION = 1;
    private static final int PERMISSION_WRITE_PERMISSION = 2;

    private static final String TAG = "ZhishuPreviewLayout";
    public static final int MAX_WIDTH_IN_DP = 260;
    public static final int MAX_HEIGHT_IN_DP = 130;

    private final int mRoundRadius = QMUIDisplayHelper.dpToPx(4);
    private final int mRequiredWidth = QMUIDisplayHelper.dpToPx(MAX_WIDTH_IN_DP);
    private final Path mRoundRect = new Path();
    private final SimpleDraweeView mPreviewImage;
    private final TextView mDescription;
    private final TextView mAccessControl;
    private final View mDivider;

    public ZhishuPreviewLayout(Context context) {
        this(context, null);
    }

    public ZhishuPreviewLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ZhishuPreviewLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        LayoutInflater.from(getContext()).inflate(R.layout.chat_layout_zhishu_preview, this);

        mPreviewImage = findViewById(R.id.preview_image);
        mDescription = findViewById(R.id.description);
        mAccessControl = findViewById(R.id.access_control);
        mDivider = findViewById(R.id.divider);

        float fontScale = ServiceManager.getInstance().getSettingService().getFontScale();
        mDescription.setTextSize(TypedValue.COMPLEX_UNIT_PX, mDescription.getTextSize() * fontScale);
        mAccessControl.setTextColor(ThemeUtils.getThemeTextColorInt());
        mAccessControl.setTextSize(TypedValue.COMPLEX_UNIT_PX, mAccessControl.getTextSize() * fontScale);
        mAccessControl.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, ThemeUtils.useNewTheme ? R.drawable.chat_ic_zhishu_access_control : R.drawable.chat_ic_zhishu_access_control_old, 0);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int mode = MeasureSpec.getMode(widthMeasureSpec);
        switch (mode) {
            case MeasureSpec.AT_MOST:
                int maxWidth = MeasureSpec.getSize(widthMeasureSpec);
                int maxWidthMeasureSpec = MeasureSpec.makeMeasureSpec(maxWidth, MeasureSpec.EXACTLY);
                super.onMeasure(maxWidthMeasureSpec, heightMeasureSpec);
                break;
            case MeasureSpec.EXACTLY:
                super.onMeasure(widthMeasureSpec, heightMeasureSpec);
                break;
            case MeasureSpec.UNSPECIFIED:
                int requiredWidthMeasureSpec = MeasureSpec.makeMeasureSpec(mRequiredWidth, MeasureSpec.EXACTLY);
                super.onMeasure(requiredWidthMeasureSpec, heightMeasureSpec);
                break;
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mRoundRect.reset();
        mRoundRect.addRoundRect(0, 0, getWidth(), getHeight(), mRoundRadius, mRoundRadius, Path.Direction.CCW);
    }

    @Override
    protected void dispatchDraw(Canvas canvas) {
        int count = canvas.save();
        canvas.clipPath(mRoundRect);
        canvas.drawColor(Color.WHITE);
        super.dispatchDraw(canvas);
        canvas.restoreToCount(count);
    }

    public void setPreview(SenderPreviewModel document) {
        if (document == null) {
            setVisibility(GONE);
        } else {
            setVisibility(VISIBLE);
            setZhishuData(document);
        }
    }

    public void setPreview(ReceiverPreviewModel document) {
        if (document == null) {
            setVisibility(GONE);
        } else {
            setVisibility(VISIBLE);
            setZhishuData(document);
        }
    }

    private void setZhishuData(ReceiverPreviewModel document) {
        placePreviewImage(document.previewUrl);
        mDescription.setText(document.displayUser);
        mAccessControl.setVisibility(GONE);
        switch (document.permission) {
            case PERMISSION_READ_PERMISSION:
                mDescription.append(getContext().getString(R.string.chat_text_message_zhishu_permission_read));
                mDescription.setVisibility(VISIBLE);
                mDivider.setVisibility(VISIBLE);
                break;
            case PERMISSION_WRITE_PERMISSION:
                mDescription.append(getContext().getString(R.string.chat_text_message_zhishu_permission_write));
                mDescription.setVisibility(VISIBLE);
                mDivider.setVisibility(VISIBLE);
                break;
            case PERMISSION_NO_PERMISSION:
                mDescription.append(getContext().getString(R.string.chat_text_message_zhishu_permission_denied));
                mDescription.setVisibility(VISIBLE);
                mDivider.setVisibility(VISIBLE);
                break;
            default:
                TLog.info(TAG, "我接收的直书文档 %s 权限为：%s", document.documentId, document.permission);
                mDescription.setVisibility(GONE);
                mDivider.setVisibility(GONE);
                break;
        }
    }

    private void setZhishuData(SenderPreviewModel document) {
        placePreviewImage(document.previewUrl);
        mDescription.setText(document.displayUser);
        boolean selfChat = TextUtils.equals(document.origin.getChatId(), HiKernel.getHikernel().getAccount().getUserId());
        if (document.assignablePermission == null || selfChat) {
            mAccessControl.setVisibility(GONE);
            if ((selfChat && document.assignedPermission == -1) || document.assignedPermission == 0) {
                mDescription.setVisibility(GONE);
                mDivider.setVisibility(GONE);
            } else {
                mDescription.setVisibility(VISIBLE);
                mDivider.setVisibility(VISIBLE);
            }
            switch (document.assignedPermission) {
                case PERMISSION_NO_PERMISSION:
                    mDescription.append("  ");
                    mDescription.append(getContext().getString(R.string.chat_text_message_zhishu_permission_denied));
                    break;
                case PERMISSION_READ_PERMISSION:
                    mDescription.append("  ");
                    mDescription.append(getContext().getString(R.string.chat_text_message_zhishu_permission_read));
                    break;
                case PERMISSION_WRITE_PERMISSION:
                    mDescription.append("  ");
                    mDescription.append(getContext().getString(R.string.chat_text_message_zhishu_permission_write));
                    break;
            }
        } else {
            mDescription.setVisibility(VISIBLE);
            mAccessControl.setVisibility(VISIBLE);
            mDivider.setVisibility(VISIBLE);
            switch (document.assignedPermission) {
                case PERMISSION_NO_PERMISSION:
                    mAccessControl.setText(R.string.chat_text_message_zhishu_permission_denied);
                    break;
                case PERMISSION_READ_PERMISSION:
                    mAccessControl.setText(R.string.chat_text_message_zhishu_permission_read);
                    break;
                case PERMISSION_WRITE_PERMISSION:
                    mAccessControl.setText(R.string.chat_text_message_zhishu_permission_write);
                    break;
            }
        }
        mAccessControl.setOnClickListener(v -> {
            if (ServiceManager.getInstance().getSecurityService().isForbidden(BusinessModuleEnum.ZHI_SHU)) {
                ToastUtils.failure(R.string.no_permission_access);
                return;
            }
            List<SelectBottomBean> selectBeans = new ArrayList<>();
            if (document.assignablePermission != null) {
                for (Integer permission : document.assignablePermission) {
                    String desc = "";
                    switch (permission) {
                        case PERMISSION_NO_PERMISSION:
                            desc = getContext().getString(R.string.chat_text_message_zhishu_permission_denied);
                            break;
                        case PERMISSION_READ_PERMISSION:
                            desc = getContext().getString(R.string.chat_text_message_zhishu_permission_read);
                            break;
                        case PERMISSION_WRITE_PERMISSION:
                            desc = getContext().getString(R.string.chat_text_message_zhishu_permission_write);
                            break;
                    }
                    if (!TextUtils.isEmpty(desc)) {
                        selectBeans.add(new SelectBottomHighlightBean(desc, document.assignedPermission == permission, permission));
                    }
                }
            }
            mAccessControl.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, ThemeUtils.useNewTheme ? R.drawable.chat_ic_zhishu_access_control_active : R.drawable.chat_ic_zhishu_access_control_active_old, 0);
            BottomListDialog dialog = new BottomListDialog.Builder(getContext())
                    .setData(selectBeans)
                    .setItemLayout(R.layout.chat_item_view_bottom_select)
                    .setOnBottomItemClickListener((view, pos, bottomBean) -> document.accessPermit(bottomBean.type))
                    .createDialog();
            dialog.setOnDismissListener(d -> mAccessControl.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, ThemeUtils.useNewTheme ? R.drawable.chat_ic_zhishu_access_control : R.drawable.chat_ic_zhishu_access_control_old, 0));
            dialog.show();
        });
    }

    private void placePreviewImage(String previewUrl) {
        if (previewUrl == null) {
            mPreviewImage.setImageURI((String) null);
        } else {
            ImageRequest imageRequest = ImageRequestBuilder.newBuilderWithSource(Uri.parse(previewUrl))
                    .setPostprocessor(new ScaleCropPostprocessor())
                    .build();
            mPreviewImage.setImageRequest(imageRequest);
        }
    }

    public static class ReceiverPreviewModel {

        public final String documentId;
        public final String previewUrl;
        public final String displayUser;
        public final int permission;

        public ReceiverPreviewModel(String documentId, String previewUrl, String displayUser, int permission) {
            this.documentId = documentId;
            this.previewUrl = previewUrl;
            this.displayUser = displayUser;
            this.permission = permission;
        }
    }

    public static class SenderPreviewModel {

        public final String documentId;
        public final String previewUrl;
        public final String displayUser;
        public final int[] assignablePermission;
        public final int assignedPermission;
        public final ChatMessage origin;

        public SenderPreviewModel(String documentId, String previewUrl, String displayUser, int[] assignablePermission, int assignedPermission, ChatMessage origin) {
            this.documentId = documentId;
            this.previewUrl = previewUrl;
            this.displayUser = displayUser;
            this.assignablePermission = assignablePermission;
            this.assignedPermission = assignedPermission;
            this.origin = origin;
        }

        public void accessPermit(int permission) {
            ZhishuPermissionRequest request = new ZhishuPermissionRequest(new BaseApiRequestCallback<HttpResponse>() {

                @Override
                public void handleInChildThread(ApiData<HttpResponse> data) {
                    super.handleInChildThread(data);
                    ServiceManager.getInstance().getHyperlinkPreviewService().updatePermission(origin.getChatId(), origin.getType(), documentId, permission);
                    switch (permission) {
                        case PERMISSION_READ_PERMISSION:
                            ToastUtils.success("当前权限已修改为 可阅读");
                            break;
                        case PERMISSION_WRITE_PERMISSION:
                            ToastUtils.success("当前权限已修改为 可编辑");
                            break;
                        case PERMISSION_NO_PERMISSION:
                            ToastUtils.success("当前权限已修改为 无权限");
                            break;
                    }
                }

                @Override
                public void onSuccess(ApiData<HttpResponse> data) {
                    new PointUtils.BuilderV4()
                            .name("zhishu-auth-update")
                            .params("chat_type", origin.getType() == 1 ? "single" : "group")
                            .params("chat_id", origin.getChatId())
                            .params("zhishu_id", documentId)
                            .params("auth_type", permission)
                            .point();
                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            request.visitCode = documentId;
            request.entityId = origin.getChatId();
            request.entityType = origin.getType();
            request.permission = permission;
            HttpExecutor.execute(request);
        }
    }

    private static class ScaleCropPostprocessor implements Postprocessor {

        private final int mHeight = QMUIDisplayHelper.dpToPx(MAX_HEIGHT_IN_DP);
        private final int mWidth = QMUIDisplayHelper.dpToPx(MAX_WIDTH_IN_DP);

        @Override
        public String getName() {
            return "PreviewCropPostprocessor";
        }

        @Override
        public CloseableReference<Bitmap> process(Bitmap sourceBitmap, PlatformBitmapFactory bitmapFactory) {
            float scale = mWidth * 1.0f / sourceBitmap.getWidth();
            Bitmap bitmap = Bitmap.createBitmap(mWidth, mHeight, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
            Matrix matrix = new Matrix();
            matrix.setScale(scale, scale);
            canvas.drawBitmap(sourceBitmap, matrix, paint);
            return bitmapFactory.createBitmap(bitmap);
        }

        @Override
        public CacheKey getPostprocessorCacheKey() {
            return new SimpleCacheKey(getName());
        }
    }
}
