package com.twl.hi.chat.adapter;

import androidx.lifecycle.LifecycleOwner;

import com.twl.hi.chat.R;
import com.twl.hi.chat.databinding.ChatItemChatRecordAllSummaryBinding;
import com.twl.hi.foundation.api.response.bean.ChatRecordSummaryBean;

import lib.twl.common.adapter.BaseDataBindingAdapter;
import lib.twl.common.adapter.BaseDataBindingViewHolder;

/**
 * Created by ChaiJiangpeng on 2020-01-21
 * Describe:
 */
public class ChatRecordSummarySearchAdapter extends BaseDataBindingAdapter<ChatRecordSummaryBean, ChatItemChatRecordAllSummaryBinding> {

    private final LifecycleOwner mLifecycleOwner;

    public ChatRecordSummarySearchAdapter(LifecycleOwner lifecycleOwner) {
        super(R.layout.chat_item_chat_record_all_summary, null);
        mLifecycleOwner = lifecycleOwner;
    }

    @Override
    protected void bind(BaseDataBindingViewHolder<ChatItemChatRecordAllSummaryBinding> helper,
                        ChatItemChatRecordAllSummaryBinding binding, ChatRecordSummaryBean item) {
        binding.setItem(item);
        binding.setLifeCycleOwner(mLifecycleOwner);
    }

}
