package com.twl.hi.chat.feature.msgstickytop;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

/**
 * 聊天消息置顶请求
 * <p>
 * Created by tanshicheng on 2023/02/09
 */
public class ChatStickyTopMsgAddRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String topMessageStr = "";

    public ChatStickyTopMsgAddRequest(BaseApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_CHAT_STICKY_TOP_MSG_CREATE;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}

