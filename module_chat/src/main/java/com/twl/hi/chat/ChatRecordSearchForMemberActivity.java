package com.twl.hi.chat;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.sankuai.waimai.router.annotation.RouterUri;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.chat.adapter.ChatRecordSearchAdapter;
import com.twl.hi.chat.callback.ChatRecordSearchForMemberCallback;
import com.twl.hi.chat.databinding.ChatActivityChatRecordSearchForMemberBinding;
import com.twl.hi.chat.viewmodel.ChatRecordSearchForMemberViewModel;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.message.ChatMessage;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.utils.point.SearchPointUtil;

import hi.kernel.BundleConstants;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.LList;

/**
 * 按照群成员搜索聊天记录
 */
@RouterUri(path = ChatPageRouter.CHAT_RECORD_SEARCH_FOR_MEMBER_ACTIVITY)
public class ChatRecordSearchForMemberActivity extends FoundationVMActivity<ChatActivityChatRecordSearchForMemberBinding, ChatRecordSearchForMemberViewModel>
        implements ChatRecordSearchForMemberCallback {

    private String mChatId;
    private int mChatType;
    private String mUserId;
    private ChatRecordSearchAdapter mAdapter;
    private SearchPointUtil mSearchPointUtil = new SearchPointUtil();

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_activity_chat_record_search_for_member;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }


    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        mUserId = intent.getStringExtra(BundleConstants.BUNDLE_USER_ID);
        mChatId = intent.getStringExtra(BundleConstants.BUNDLE_CHAT_ID);
        mChatType = intent.getIntExtra(BundleConstants.BUNDLE_CHAT_TYPE, 0);
        getDataBinding().smartRefreshLayout.setEnableRefresh(false);
        getDataBinding().smartRefreshLayout.setEnableLoadMore(false);
        getDataBinding().smartRefreshLayout.setOnLoadMoreListener(this);
        getViewModel().defaultData(mChatId, mChatType);
        getViewModel().setUserId(mUserId);
        Contact contact = getViewModel().getContact();
        if (contact != null) {
            getDataBinding().titleBar.setTitle(contact.getShowName() + "的消息记录");
        }
        mAdapter = new ChatRecordSearchAdapter(this);
        mSearchPointUtil.setExposeListener(getDataBinding().recyclerView, mAdapter);
        mAdapter.setViewModel(getViewModel());
        getDataBinding().recyclerView.setLayoutManager(new LinearLayoutManager(this));
        getDataBinding().recyclerView.setAdapter(mAdapter);
        showProgressDialog(getResources().getString(R.string.loading));
        getViewModel().searchContent("");
        getViewModel().getMessagesLiveData().observe(this, chatMessages -> {
            dismissProgressDialog();
            mSearchPointUtil.updateSearchId(getViewModel().getCurrentSearchId());
            if (getViewModel().isFirstPage()) {
                //第一页
                mAdapter.setNewData(chatMessages);
                if (!LList.isEmpty(chatMessages)) {
                    getDataBinding().recyclerView.scrollToPosition(0);
                    getDataBinding().tvNoContent.setVisibility(View.GONE);
                } else {
                    getDataBinding().tvNoContent.setVisibility(View.VISIBLE);
                }
                if (getViewModel().isHasMore()) {
                    getDataBinding().smartRefreshLayout.setNoMoreData(false);
                    getDataBinding().smartRefreshLayout.setEnableLoadMore(true);
                } else {
                    getDataBinding().smartRefreshLayout.finishLoadMoreWithNoMoreData();
                }
            } else {
                if (!LList.isEmpty(chatMessages)) {
                    mAdapter.addData(chatMessages);
                }
                if (getViewModel().isHasMore()) {
                    getDataBinding().smartRefreshLayout.finishLoadMore(true);
                    getDataBinding().smartRefreshLayout.setEnableLoadMore(true);
                } else {
                    getDataBinding().smartRefreshLayout.finishLoadMoreWithNoMoreData();
                }
            }
        });
    }

    @Override
    public void clickLeft(View view) {
        finish();
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
        getViewModel().loadMoreContent();
    }

    @Override
    public void onItemClick(ChatMessage bean, int position) {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                if (mChatType == MessageConstants.MSG_GROUP_CHAT) {
                    GroupChatForSearchActivity.start(ChatRecordSearchForMemberActivity.this, mChatId, bean.getMid(), bean.getSeq());
                } else {
                    SingleChatForSearchActivity.start(ChatRecordSearchForMemberActivity.this, mChatId, bean.getMid(), bean.getSeq());
                }
            }
        });

    }
}

