package com.twl.hi.chat.api.response.bean;

import lib.twl.common.util.SearchResultInterface;

/**
 * Author : Xuweixiang .
 * Date   : On 2023/5/5
 * Email  : Contact <EMAIL>
 * Desc   : 全局搜索 - 全部 tab 下的搜索结果
 */

public class MailSearchBean implements SearchResultInterface {

    public boolean isDraft;
    public String mailId;
    public String folderName;
    // 所属文件夹 - 对应 tag
    public String folderId;
    // 标题 - 【含高亮】
    public String subject;
    // 正文概要
    public String textSummary;
    // 发件人邮箱地址【不含高亮】
    public String senderRealAddress;
    // 发件人邮箱地址【包含高亮】
    public String senderAddress;
    // 发件人名称
    public String senderName;
    // 仅草稿状态下返回 - 收件人名字拼接：111；222
    public String recipientNames;
    // 仅草稿状态下返回 - 第一个收件人邮箱地址
    public String firstRecipientAddress;
    // 发件时间
    public long mailTime;

    @Override
    public String getPointId() {
        return mailId;
    }

    @Override
    public String getPointLevel() {
        return "邮箱";
    }
}
