package com.twl.hi.chat.viewmodel;

import android.app.Application;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.twl.hi.chat.R;
import com.twl.hi.chat.api.request.ReplyListRequest;
import com.twl.hi.chat.api.response.ReplyListResponse;
import com.twl.hi.chat.util.ChatUtils;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.message.ChatMessage;
import com.twl.hi.foundation.model.message.MessageAndQuote;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import lib.twl.common.util.LList;
import lib.twl.common.util.QMUIDisplayHelper;


public class GroupChatReplyDetailFragmentViewModel extends GroupChatBaseViewModel {
    private long mOffsetMsgId;
    private boolean mHasMore = true;
    private final MutableLiveData<List<Long>> mMsgIds = new MutableLiveData<>();
    private LiveData<List<MessageAndQuote>> mMessageLiveData;

    public GroupChatReplyDetailFragmentViewModel(Application application) {
        super(application);
    }

    @Override
    public int getType() {
        return MessageConstants.MSG_GROUP_CHAT;
    }

    public LiveData<List<MessageAndQuote>> getMessages(long rootMsgId) {
        if (mMessageLiveData == null) {
            mMessageLiveData = Transformations.switchMap(
                    getGroupRobotCount(),
                    o -> Transformations.switchMap(mMsgIds, msgs -> {
                        if (LList.isEmpty(msgs)) {
                            return new MutableLiveData<>(Collections.emptyList());
                        }
                        return ServiceManager.getInstance().getMessageService().getReplyMessageOf(rootMsgId, msgs);
                    }));
        }
        return mMessageLiveData;
    }

    public void requestReplyList(long msgId) {
        ReplyListRequest request = new ReplyListRequest(new BaseApiRequestCallback<ReplyListResponse>() {
            @Override
            public void handleInChildThread(ApiData<ReplyListResponse> data) {
                super.handleInChildThread(data);
                mHasMore = data.resp.hasMore > 0;
                List<Long> ids = data.resp.msgIds;
                if (ids != null && ids.size() > 0) {
                    Collections.sort(ids);
                    List<Long> value = mMsgIds.getValue();
                    List<Long> msgIds = new ArrayList<>(ids);
                    if (value != null) {
                        msgIds.addAll(0, value);
                    }
                    mMsgIds.postValue(msgIds);
                    mOffsetMsgId = ids.get(ids.size() - 1);
                }
            }

            @Override
            public void onSuccess(ApiData<ReplyListResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });

        request.msgId = msgId;
        if (mOffsetMsgId > 0) {
            request.offsetMsgId = mOffsetMsgId;
        }
        HttpExecutor.execute(request);
    }

    public boolean isHasMore() {
        return mHasMore;
    }

    public void setHasMore(boolean hasMore) {
        this.mHasMore = hasMore;
    }

    @Override
    public List<ChatMessage> getChatMessagesSupportViewer(long anchorSeq) {
        List<ChatMessage> list = new ArrayList<>();
        List<MessageAndQuote> messages = mMessageLiveData.getValue();
        if (!LList.isEmpty(messages)) {
            for (MessageAndQuote message : messages) {
                if (ChatUtils.isSupportMediaViewer(message.getMessage())) {
                    list.add(message.getMessage());
                }
            }
        }
        return list;
    }

    @Override
    public int getMsgRightMargin() {
        return QMUIDisplayHelper.getDimenInPixel(R.dimen.chat_reply_message_item_right_width);
    }
}
