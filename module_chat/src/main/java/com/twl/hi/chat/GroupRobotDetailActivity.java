package com.twl.hi.chat;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.basic.dialog.DialogUtils;
import com.twl.hi.chat.dialog.RobotAlertDialog;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.export.organization.router.OrganizationPageRouter;
import com.twl.hi.chat.databinding.ChatActivityGroupRobotDetailBinding;
import com.twl.hi.chat.viewmodel.GroupRobotDetailViewModel;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.utils.StringUtils;

import hi.kernel.BundleConstants;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.QMUIStatusBarHelper;
import lib.twl.common.util.ToastUtils;

/**
 * 群机器人的详情页
 *
 * 根据是否已加入群区分状态，请求不同的接口获取数据，展示不同的内容
 *
 * 与机器人名片页共用头部布局 {@link GroupRobotProfileHeader}
 */
public class GroupRobotDetailActivity extends FoundationVMActivity<ChatActivityGroupRobotDetailBinding, GroupRobotDetailViewModel> {

    private static final String TAG = "GroupRobotDetailActivity";
    private GroupRobotProfileHeader mHeader;
    private Contact mDeveloper;
    private Contact mIntroducer;

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_activity_group_robot_detail;
    }

    @Override
    public int getCallbackVariable() {
        return -1;
    }

    @Override
    public Object getCallback() {
        return null;
    }

    @Override
    public int getBindingVariable() {
        return -1;
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    protected ViewModelProvider.Factory getViewModelFactory() {
        return new ViewModelProvider.Factory() {
            @NonNull
            @Override
            public <T extends ViewModel> T create(@NonNull Class<T> modelClass) {
                //noinspection unchecked
                return (T) new GroupRobotDetailViewModel(
                        getApplication(),
                        getIntent().getStringExtra(BundleConstants.BUNDLE_GROUP_ID),
                        getIntent().getStringExtra(BundleConstants.BUNDLE_ID)
                );
            }
        };
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        QMUIStatusBarHelper.setStatusBarLightMode(this);

        initHeaderView();
        initItemView();
        initActionAdd();
        initActionRemove();
    }

    private void initHeaderView() {
        mHeader = new GroupRobotProfileHeader(getDataBinding().header);
        showProgressDialog(R.string.brvah_loading);
        getViewModel().uiState().observe(this, detail -> {
            dismissProgressDialog();
            if (detail == null) {
                return;
            }

            switch (detail.type) {
                case 0:
                    inflateShipRobot(detail);
                    break;
                case 1:
                    inflateAppRobot(detail);
                    break;
                case 2:
                    inflateCustomizedRobot(detail);
                    break;
                default:
                    TLog.error(TAG, "未识别的机器人类型：%s", detail.type);
                    inflateAppRobot(detail);
            }
            getDataBinding().header.actionAdd.setVisibility(detail.added ? View.GONE : View.VISIBLE);
            getDataBinding().setDetail(detail);
        });

        getDataBinding().header.actionBack.setOnClickListener(v -> onBackPressed());
        getDataBinding().header.actionSendMessage.getRoot().setOnClickListener(v -> {
            GroupRobotDetailViewModel.RobotDetail value = getViewModel().uiState().getValue();
            if (value == null || value.chatId == null) {
                return;
            }
            Bundle bundle = new Bundle();
            bundle.putString(BundleConstants.BUNDLE_USER_ID, value.chatId);
            AppUtil.startUri(this, ChatPageRouter.SINGLE_CHAT_ACTIVITY, bundle);
            new PointUtils.BuilderV4()
                    .name("chatbot-detail-page-click")
                    .params("type", "message")
                    .params("robot_id", getViewModel().getRobotId())
                    .point();
        });
        getDataBinding().header.actionEnterApp.getRoot().setOnClickListener(v -> {
            /* 目前无用 */
            new PointUtils.BuilderV4()
                    .name("chatbot-detail-page-click")
                    .params("type", "app")
                    .params("robot_id", getViewModel().getRobotId())
                    .point();
        });
    }

    private void initItemView() {
        getDataBinding().developer.getRoot().setOnClickListener(v -> {
            if (mDeveloper != null) {
                Bundle bundle = new Bundle();
                bundle.putString(BundleConstants.BUNDLE_DATA_LONG, mDeveloper.getUserId());
                AppUtil.startUri(this, OrganizationPageRouter.USER_INFO_ACTIVITY, bundle);
            } else {
                TLog.info(TAG, "无开发者信息");
            }
        });
        getDataBinding().introducer.getRoot().setOnClickListener(v -> {
            if (mIntroducer != null) {
                Bundle bundle = new Bundle();
                bundle.putString(BundleConstants.BUNDLE_DATA_LONG, mIntroducer.getUserId());
                AppUtil.startUri(this, OrganizationPageRouter.USER_INFO_ACTIVITY, bundle);
            } else {
                TLog.info(TAG, "无添加者信息");
            }
        });
        getDataBinding().switchConfig.setOnClickListener(v -> {
            boolean checked = getDataBinding().switchConfig.isChecked();
            getViewModel().update(checked);
            new PointUtils.BuilderV4()
                    .name("chatbot-detail-page-click")
                    .params("type", "checkbox")
                    .params("icon_type", checked ? 1 : 0)
                    .params("robot_id", getViewModel().getRobotId())
                    .point();
        });
    }

    private void initActionAdd() {
        getDataBinding().header.actionAdd.setOnClickListener(v -> {
            showProgressDialog(R.string.chat_group_robot_request_add);
            getViewModel().checkThenAdd(getDataBinding().switchConfig.isChecked());
            new PointUtils.BuilderV4()
                    .name("chatbot-add-click")
                    .params("robot_id", getViewModel().getRobotId())
                    .point();
        });
        getViewModel().getAddAlert().observe(this, content -> {
            dismissProgressDialog();
            new RobotAlertDialog(this)
                    .show(getDataBinding().header.robotName.getText().toString(), content, close -> {
                        if (close) {
                            getViewModel().requestCloseRemind();
                        }
                        showProgressDialog(R.string.chat_group_robot_request_add);
                        getViewModel().add(getDataBinding().switchConfig.isChecked());
                        return null;
                    });
        });
        getViewModel().getAddResult().observe(this, result -> {
            if (result) {
                ToastUtils.success(R.string.chat_group_robot_add_success);
            }
            dismissProgressDialog();
        });
    }

    private void initActionRemove() {
        getDataBinding().actionRemove.setOnClickListener(v -> {
            new DialogUtils.Builder(this)
                    .setCancelable(true)
                    .setTitle(R.string.chat_group_robot_remove_alert)
                    .setTitleSize(16)
                    .setPositive(R.string.remove)
                    .setAutoCloseAfterClick(true)
                    .setPositiveListener(p -> {
                        showProgressDialog(R.string.chat_group_robot_request_remove);
                        getViewModel().remove();
                    })
                    .setNegative(R.string.cancel)
                    .build();
            new PointUtils.BuilderV4()
                    .name("chatbot-detail-page-click")
                    .params("type", "delete")
                    .params("robot_id", getViewModel().getRobotId())
                    .point();
        });
        getViewModel().canBeRemoved().observe(this, removable ->
                getDataBinding().actionRemove.setVisibility(removable ? View.VISIBLE : View.GONE)
        );
        getViewModel().getRemoveResult().observe(this, result -> {
            if (result) {
                ToastUtils.success(R.string.chat_group_robot_remove_success);
            }
            dismissProgressDialog();
            finish();
        });
    }

    private void inflateShipRobot(GroupRobotDetailViewModel.RobotDetail detail) {
        mHeader.bindData(new GroupRobotProfileHeader.Model(
                detail.color,
                detail.icon,
                detail.name,
                detail.description,
                detail.deprecated,
                false,
                false
        ));
        bindDeveloper(null, null);
        bindIntroducer(null);
        bindUserManual(detail.helpDoc);
    }

    private void inflateAppRobot(GroupRobotDetailViewModel.RobotDetail detail) {
        mHeader.bindData(new GroupRobotProfileHeader.Model(
                detail.color,
                detail.icon,
                detail.name,
                detail.description,
                detail.deprecated,
                !detail.deprecated && detail.visible && detail.chatId != null,
                !detail.deprecated && detail.visible && detail.appId != null
        ));
        bindDeveloper(detail.developer, detail.developerId);
        bindUserManual(detail.helpDoc);
        bindIntroducer(detail.introducer);
    }

    private void inflateCustomizedRobot(GroupRobotDetailViewModel.RobotDetail detail) {
        mHeader.bindData(new GroupRobotProfileHeader.Model(
                detail.color,
                detail.icon,
                detail.name,
                detail.description,
                detail.deprecated,
                false,
                false
        ));
        bindDeveloper(null, null);
        bindUserManual(detail.helpDoc);
        bindIntroducer(detail.introducer);
    }

    private void bindDeveloper(String developerName, String developerId) {
        mDeveloper = (developerId == null || StringUtils.isEmpty(developerId)) ? null : getViewModel().getContact(developerId);
        getDataBinding().developer.getRoot().setVisibility(TextUtils.isEmpty(developerName) ? View.GONE : View.VISIBLE);
    }

    private void bindUserManual(String manualUrl) {
        getDataBinding().manual.getRoot().setVisibility(TextUtils.isEmpty(manualUrl) ? View.GONE : View.VISIBLE);
    }

    private void bindIntroducer(Contact introducer) {
        mIntroducer = introducer;
        getDataBinding().introducer.getRoot().setVisibility(introducer == null ? View.GONE : View.VISIBLE);
    }
}
