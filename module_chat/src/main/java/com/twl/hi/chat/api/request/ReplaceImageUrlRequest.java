package com.twl.hi.chat.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.chat.api.response.ReplaceImageUrlResponse;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class ReplaceImageUrlRequest extends BaseApiRequest<ReplaceImageUrlResponse> {

    @Expose
    public String urls; // 旧的原图url，多个使用,分隔

    public ReplaceImageUrlRequest(AbsRequestCallback<ReplaceImageUrlResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_REPLACE_IMAGE_URL;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
