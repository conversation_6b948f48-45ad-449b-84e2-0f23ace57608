package com.twl.hi.chat

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentPagerAdapter
import androidx.viewpager.widget.ViewPager
import com.google.android.material.tabs.TabLayout
import com.sankuai.waimai.router.annotation.RouterUri
import com.twl.hi.basic.activity.FoundationVMActivity
import com.twl.hi.basic.callback.TitleBarCallback
import com.twl.hi.basic.helpers.AppPageRouterHelper
import com.twl.hi.export.select.bean.SelectBaseParams
import com.twl.hi.export.select.bean.SelectConversationParams
import com.twl.hi.chat.databinding.ChatActivityGroupShareBinding
import com.twl.hi.export.chat.router.ChatPageRouter
import com.twl.hi.foundation.model.BigBinder
import com.twl.hi.foundation.model.Group
import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.hi.foundation.utils.GroupStatusCheckCallback
import com.twl.hi.select.conversation.SelectConversationWithConfirmFragment
import com.twl.hi.select.conversation.viewmodel.SelectConversationViewModel
import hi.kernel.BundleConstants
import hi.kernel.Constants
import lib.twl.common.util.AppUtil

/**
 * Author: DingDong
 * Date: 2022/3/10
 * Description:分享群页面
 */
@RouterUri(path = [ChatPageRouter.GROUP_CHAT_SHARE_ACTIVITY])
class GroupShareActivity : FoundationVMActivity<ChatActivityGroupShareBinding, SelectConversationViewModel>(), TitleBarCallback {

    companion object {
        @JvmStatic
        fun intentStart(context: Context, group: Group?, params: SelectBaseParams) {
            group?.run {
                AppUtil.startActivity(context,
                    Intent(context, GroupShareActivity::class.java).apply {
                        val bundle = Bundle()
                        val bigData = BigBinder(group)
                        bundle.putBinder(Constants.GROUP, bigData)
                        putExtra("bundle", bundle)
                        putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params)
                    })
            }
        }
    }

    override fun getContentLayoutId() = R.layout.chat_activity_group_share

    override fun getCallbackVariable() = BR.callback

    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel


    private var mGroup: Group? = null
    private val fragments = arrayListOf<Fragment>()
    private val titles = arrayListOf("群名片", "群二维码")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val bigBinder = intent.getBundleExtra("bundle")?.getBinder(Constants.GROUP) as BigBinder?
        mGroup = bigBinder?.data as Group?
        initTabAndViewpager()
    }

    private fun initTabAndViewpager() {
        val tabLayout = dataBinding.tlTab
        val vpContent = dataBinding.vpContent
        tabLayout.tabMode = TabLayout.MODE_SCROLLABLE
        dataBinding.tlTab.setupWithViewPager(vpContent)
        val params = intent.getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE) as SelectConversationParams?
        fragments.add(SelectConversationWithConfirmFragment.newInstance(params, Constants.TYPE_ALL))
        fragments.add(GroupQRCodeFragment.newInstance(mGroup))
        vpContent.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                dataBinding.titleBar.tvRight.visibility = if (position == 0) {
                    View.VISIBLE
                } else {
                    View.INVISIBLE
                }
            }

            override fun onPageScrollStateChanged(state: Int) {
            }
        })
        vpContent.adapter = object :
            FragmentPagerAdapter(supportFragmentManager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {
            override fun getCount() = fragments.size

            override fun getItem(position: Int) = fragments[position]

            override fun getPageTitle(position: Int) = titles[position]
        }
        viewModel.showSelectType.observe(this) {
            dataBinding.titleBar.tvRight.visibility = if (it) {
                View.VISIBLE
            } else {
                View.INVISIBLE
            }
        }
    }

    override fun clickLeft(view: View?) {
        viewModel.checkGroupStatus(mGroup?.groupId, MessageConstants.MSG_GROUP_CHAT, object : GroupStatusCheckCallback {
            override fun onStatusNormal() {
                finish()
            }

            override fun onStatusAbnormal() {
                AppPageRouterHelper.backToMainTabActivity(this@GroupShareActivity)
            }
        })
    }

    override fun clickRight(view: View?) {
        dataBinding.titleBar.right = if (!viewModel.multiSelectStatus.get()) {
            getString(R.string.select_single_check)
        } else {
            getString(R.string.multiple_check)
        }
        (fragments[0] as SelectConversationWithConfirmFragment).switchSelectType()
    }

    override fun onBackPressed() {
        super.onBackPressed()
        val fragments = supportFragmentManager.fragments
        viewModel.showSelectType.postValue(fragments.size == 2)
    }
}