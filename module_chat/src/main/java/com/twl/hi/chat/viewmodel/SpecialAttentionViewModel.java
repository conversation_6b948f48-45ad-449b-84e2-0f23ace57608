package com.twl.hi.chat.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;

import com.twl.hi.chat.api.request.GroupFocusAddRequest;
import com.twl.hi.chat.api.request.GroupFocusDelRequest;
import com.twl.hi.chat.api.request.GroupFocusQueryRequest;
import com.twl.hi.chat.api.response.GroupFocusQueryResponse;
import com.twl.hi.chat.model.SpecialAttendanceBean;
import com.twl.hi.chat.util.ChatPointUtils;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.Group;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.utils.GroupInfoHelper;
import com.twl.hi.foundation.utils.GroupStatusCheckCallback;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import hi.kernel.Constants;
import lib.twl.common.util.LList;

/**
 * <AUTHOR>
 * @date 2021/11/2.
 */
public class SpecialAttentionViewModel extends FoundationViewModel {
    private String mChatId;
    private final ArrayList<String> selectedIds = new ArrayList<>();
    private final MutableLiveData<List<SpecialAttendanceBean>> specialAttentionLiveData = new MutableLiveData<>();

    public SpecialAttentionViewModel(Application application) {
        super(application);
    }

    public List<String> getSelectedIds() {
        return selectedIds;
    }

    public MutableLiveData<List<SpecialAttendanceBean>> getSpecialAttentionLiveData() {
        return specialAttentionLiveData;
    }

    public void initAndLoadData(String chatId) {
        this.mChatId = chatId;
        loadSpecialAttentions(chatId, "");
    }

    public void loadSpecialAttentions(String groupId, String formatUserIds) {

        GroupFocusQueryRequest request = new GroupFocusQueryRequest(new BaseApiRequestCallback<GroupFocusQueryResponse>() {
            @Override
            public void handleInChildThread(ApiData<GroupFocusQueryResponse> data) {
                super.handleInChildThread(data);
                GroupFocusQueryResponse response = data.resp;
                if (response == null) {
                    return;
                }
                selectedIds.clear();
                if (!LList.isEmpty(response.focusUserIds)) {
                    selectedIds.addAll(response.focusUserIds);
                }
                List<SpecialAttendanceBean> results = getContacts(selectedIds);
                specialAttentionLiveData.postValue(results);
                pointGroupFollowUpdate(formatUserIds, selectedIds.size());
            }

            @Override
            public void onSuccess(ApiData<GroupFocusQueryResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.groupId = groupId;
        HttpExecutor.execute(request);
    }

    public void delSpecialAttention(SpecialAttendanceBean contact) {
        GroupFocusDelRequest request = new GroupFocusDelRequest(new BaseApiRequestCallback<HttpResponse>() {
            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);
                selectedIds.remove(contact.getUserId());
                List<SpecialAttendanceBean> results = getContacts(selectedIds);
                specialAttentionLiveData.postValue(results);
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.focusUserIds = "" + contact.getUserId();
        request.groupId = this.mChatId;
        HttpExecutor.execute(request);
    }

    @NotNull
    private List<SpecialAttendanceBean> getContacts(ArrayList<String> selectedIds) {
        Group group = ServiceManager.getInstance().getGroupService().getGroupById(mChatId);
        String ownerId = "";
        List<String> managerIds = null;
        if (group != null) {
            ownerId = group.getOwnerId();
            managerIds = group.getManagerIds();
        }
        List<SpecialAttendanceBean> results = new ArrayList<>(selectedIds.size());
        for (String id : selectedIds) {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(id);
            if (contact != null) {
                int adminRole = Constants.TYPE_GROUP_NORMAL;
                if (TextUtils.equals(id, ownerId)) {
                    adminRole = Constants.TYPE_GROUP_CREATE;
                } else if (!LList.isEmpty(managerIds) && managerIds.contains(id)) {
                    adminRole = Constants.TYPE_GROUP_ADMIN;
                }
                results.add(new SpecialAttendanceBean(adminRole, contact));
            }
        }
        return results;
    }

    public void addSpecialAttentions(ArrayList<Long> ids) {

        StringBuilder focusUserIds = new StringBuilder();
        if (!LList.isEmpty(ids)) {
            int size = ids.size();
            for (int i = 0; i < size; i++) {
                focusUserIds.append(ids.get(i));
                if (i < size - 1) {
                    focusUserIds.append(",");
                }
            }
        }

        GroupFocusAddRequest request = new GroupFocusAddRequest(new BaseApiRequestCallback<HttpResponse>() {
            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);
                loadSpecialAttentions(mChatId, focusUserIds.toString());
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });

        request.groupId = mChatId;
        request.focusUserIds = focusUserIds.toString();
        HttpExecutor.execute(request);
    }

    private void pointGroupFollowUpdate(String formatIds, int total) {
        if (TextUtils.isEmpty(formatIds)) {
            return;
        }
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put(ChatPointUtils.PARAM_CHAT_ID, this.mChatId + "");
        paramsMap.put(ChatPointUtils.PARAM_FOLLOW_ID, formatIds);
        paramsMap.put(ChatPointUtils.PARAM_FOLLOW_NUM, total);
        ChatPointUtils.point(ChatPointUtils.POINT_GROUP_FOLLOW_MEMBER, paramsMap);
    }

    public void checkGroupStatus(GroupStatusCheckCallback callback) {
        GroupInfoHelper.optWithGroupStatusCheck(mChatId, MessageConstants.MSG_GROUP_CHAT, callback);
    }
}
