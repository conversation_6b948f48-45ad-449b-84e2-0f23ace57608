package com.twl.hi.emotion.common;

import android.content.Context;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.twl.hi.emotion.EmotionPanelView;

/**
 * Author: <PERSON>
 * Date: 2018/11/24.
 */
public class OnEmotionItemTouchImpl implements RecyclerView.OnItemTouchListener {

    private final GestureDetector gestureDetector;
    private RecyclerView recyclerView;

    public OnEmotionItemTouchImpl(Context context, EmotionPanelView.OnEmotionItemInnerClickListener itemInnnerClickListener) {

        gestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onSingleTapUp(MotionEvent e) {
                View child = recyclerView.findChildViewUnder(e.getX(), e.getY());
                if (child != null && itemInnnerClickListener != null) {
                    child.setPressed(true);
                    return itemInnnerClickListener.onItemClick(child, recyclerView.getChildAdapterPosition(child));
                }
                return false;
            }

            @Override
            public void onLongPress(MotionEvent e) {
                View child = recyclerView.findChildViewUnder(e.getX(), e.getY());
                if (child != null && itemInnnerClickListener != null) {
                    itemInnnerClickListener.onItemLongClick(child, recyclerView.getChildAdapterPosition(child));
                }
            }
        });
    }

    @Override
    public boolean onInterceptTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
        recyclerView = rv;
        return gestureDetector.onTouchEvent(e);
    }

    @Override
    public void onTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {

    }

    @Override
    public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {

    }
}
