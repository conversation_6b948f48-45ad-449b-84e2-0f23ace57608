<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_title"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@color/app_white"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <lib.twl.common.views.MTextView
        android:id="@+id/tv_btn_action_left"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="12dp"
        android:gravity="center"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:textColor="@color/color_484848"
        android:textSize="18dip"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="取消" />

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_gravity="center_vertical|left"
        android:layout_marginStart="12dp"
        android:paddingLeft="12dp"
        android:paddingTop="8dp"
        android:paddingRight="12dp"
        android:paddingBottom="8dp"
        android:src="@drawable/ic_action_back_black"
        android:visibility="gone"
        tools:visibility="visible" />

    <lib.twl.common.views.MTextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="100dip"
        android:layout_marginRight="100dip"
        android:ellipsize="middle"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/color_484848"
        android:textSize="18dip"
        tools:text="郭峰郭峰郭峰郭峰郭峰郭峰郭峰郭峰" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical|right"
        android:layout_marginEnd="12dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_action_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="12dp"
            android:paddingTop="8dp"
            android:paddingRight="12dp"
            android:paddingBottom="8dp"
            android:visibility="gone"
            tools:visibility="gone" />

        <ImageView
            android:id="@+id/iv_action_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="12dp"
            android:paddingTop="8dp"
            android:paddingRight="12dp"
            android:paddingBottom="8dp"
            android:visibility="gone"
            tools:visibility="gone" />

        <lib.twl.common.views.MTextView
            android:id="@+id/tv_btn_action"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:textColor="@color/color_484848"
            android:textSize="18dip"
            android:textStyle="bold"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

</FrameLayout>