package com.twl.hi.export.audio.shorthand

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData

interface IAudioShortHandService {
    /**开始音频速记
     * */
    fun startAudioShortHand(context: Context)

    /**停止音频速记
     * */
    fun stopHistoryAudioShortHand(): MutableLiveData<Boolean>

    /**获取音频速记状态
     * */
    fun getShortHanding(): Boolean

    fun killShortHandActivity()
    fun showShortHandTipDialog(lifecycleOwner: LifecycleOwner): MutableLiveData<Boolean>
}