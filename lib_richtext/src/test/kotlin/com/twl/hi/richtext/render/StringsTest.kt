package com.twl.hi.richtext.render

import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test

class StringsTest {

    private val testing =
        "www.baidu.com\nbaidu.com\nhttps://baidu.com\nhttps://www.baidu.com\nhttp://baidu.com\nhttp://www.baidu.com\nhttp://m.baidu.com\n"

    @Test
    fun test_no_mapping() {
        assertEquals(testing, testing.replace(emptyMap(), mutableMapOf()))
    }

    @Test
    fun test_no_match() {
        val valueMapping = mapOf(
            "taobao.com" to "淘宝",
            "www.taobao.com" to "淘宝",
            "www.bai.*.com" to "百度",
            "baidu.co.|baidu.c.m" to "百度",
        )
        assertEquals(testing, testing.replace(valueMapping, mutableMapOf()))
    }

    @Test
    fun test_single_match() {
        val valueMapping = mapOf(
            "https://www.baidu.com" to "百度",
        )
        val indices = mutableMapOf<IntRange, String>()

        indices.clear()
        assertEquals("百度", "https://www.baidu.com".replace(valueMapping, indices))
        assertEquals(1, indices.size)
        assertTrue(indices.contains(0 until "百度".length))

        val match =
            "www.baidu.com\nbaidu.com\nhttps://baidu.com\n百度\nhttp://baidu.com\nhttp://www.baidu.com\nhttp://m.baidu.com\n"
        indices.clear()
        assertEquals(match, testing.replace(valueMapping, indices))
        assertEquals(1, indices.size)
        val range = indices.keys.first()
        assertEquals(42..43, range)
        assertEquals("百度", match.subSequence(range))
    }

    @Test
    fun test_multi_match() {
        val valueMapping = mapOf(
            "baidu.com" to "百度",
        )
        val indices = mutableMapOf<IntRange, String>()

        val match =
            "www.百度\n百度\nhttps://百度\nhttps://www.百度\nhttp://百度\nhttp://www.百度\nhttp://m.百度\n"
        indices.clear()
        assertEquals(match, testing.replace(valueMapping, indices))
        assertEquals(7, indices.size)
        assertTrue(indices.contains(4..5))
        assertTrue(indices.contains(7..8))
        assertTrue(indices.contains(18..19))
        assertTrue(indices.contains(33..34))
        assertTrue(indices.contains(43..44))
        assertTrue(indices.contains(57..58))
        assertTrue(indices.contains(69..70))
    }

    @Test
    fun test_overlap_match() {
        val valueMapping = mapOf(
            "baidu.com" to "百度",
            "www.baidu.com" to "百度一下",
        )
        val indices = mutableMapOf<IntRange, String>()

        val match =
            "百度一下\n百度\nhttps://百度\nhttps://百度一下\nhttp://百度\nhttp://百度一下\nhttp://m.百度\n"
        indices.clear()
        assertEquals(match, testing.replace(valueMapping, indices))

        val url = "https://zhishu-qa.weizhipin.com/docs/N5ew2Juc2uV https://zhishu-qa.weizhipin.com/docs/N5ew2Juc2uV?position=xjbtgUmRPvm9FNAtaFqxFZ"
        val matching1 = "https://zhishu-qa.weizhipin.com/docs/N5ew2Juc2uV"
        val matching2 = "https://zhishu-qa.weizhipin.com/docs/N5ew2Juc2uV?position=xjbtgUmRPvm9FNAtaFqxFZ"

        val mapping = mapOf(
            matching1 to "直书1",
            matching2 to "直书2",
        )
        indices.clear()
        assertEquals("直书1\u00a0直书2", url.replace(mapping, indices))
    }
}