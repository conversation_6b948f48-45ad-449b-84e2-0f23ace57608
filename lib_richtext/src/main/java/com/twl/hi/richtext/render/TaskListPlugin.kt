package com.twl.hi.richtext.render

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.twl.hi.richtext.R
import com.twl.hi.richtext.parser.item.task.TaskListItem
import com.twl.hi.richtext.parser.item.task.TaskListProps
import com.twl.hi.richtext.span.TaskListItemSpan
import io.noties.markwon.AbstractMarkwonPlugin
import io.noties.markwon.MarkwonSpansFactory
import io.noties.markwon.MarkwonVisitor
import lib.twl.common.ext.dp
import org.commonmark.node.ListItem
import org.commonmark.node.Node
import org.commonmark.node.Paragraph
import org.commonmark.node.Text
import org.commonmark.parser.Parser
import java.util.regex.Pattern

/**
 * 筛选框解析插件
 */
class TaskListPlugin(private val context: Context) : AbstractMarkwonPlugin() {

    companion object {

        @JvmStatic
        fun create(context: Context): TaskListPlugin {
            return TaskListPlugin(context)
        }
    }

    override fun configureParser(builder: Parser.Builder) {
        super.configureParser(builder)
        // 实现自定义的任务列表解析器，将有筛选框标签的行，从任务列表节点转为筛选框节点
        builder.postProcessor { document ->
            processListItems(document)
            return@postProcessor document
        }
    }

    private fun processListItems(node: Node?) {
        var funNode = node
        while (funNode != null) {
            val next = funNode.next
            // 递归处理所有子节点
            if (funNode.firstChild != null) {
                processListItems(funNode.firstChild)
            }
            if (funNode is ListItem) {
                processTaskListItem(funNode)
            }
            funNode = next
        }
    }

    private fun processTaskListItem(listItem: ListItem) {
        val firstChild = listItem.firstChild
        // 确保列表项有内容
        if (firstChild is Paragraph) {
            // 获取段落的第一个子节点
            val paragraphChild = firstChild.getFirstChild()
            if (paragraphChild is Text) {
                val content = paragraphChild.literal

                // 直接使用正则表达式匹配任务列表格式
                val matcher = Pattern.compile("^\\[([ xX])]\\s+(.+)$").matcher(content)
                if (matcher.find()) {
                    val checked = matcher.group(1)!! == "x" || matcher.group(1)!! == "X"
                    val newContent = matcher.group(2);
                    // 更新文本内容，不改变节点结构
                    paragraphChild.literal = newContent

                    val taskListItem = TaskListItem(checked)
                    // 复制原始 ListItem 的所有子节点到新的 TaskListItem
                    while (listItem.firstChild != null) {
                        val child = listItem.firstChild
                        child.unlink()
                        taskListItem.appendChild(child)
                    }

                    // 替换节点
                    listItem.insertAfter(taskListItem)
                    listItem.unlink()
                }
            }
        }
    }

    override fun configureSpansFactory(builder: MarkwonSpansFactory.Builder) {
        super.configureSpansFactory(builder)
        builder.setFactory(TaskListItem::class.java) { _, props ->
            val bitmap = getCheckedBitmap(TaskListProps.CHECKED.get(props))
            return@setFactory if (bitmap != null) {
                TaskListItemSpan(bitmap)
            } else {
                null
            }
        }
    }

    /**
     * 获取选中/非选中状态的 bitmap
     */
    private fun getCheckedBitmap(checked: Boolean?): Bitmap? {
        try {
            val resId = if (checked == true) {
                R.drawable.rich_ic_on_selected
            } else {
                R.drawable.rich_ic_on_normal
            }
            val options = BitmapFactory.Options()
            options.inPreferredConfig = Bitmap.Config.ARGB_8888
            val sourceBitmap = BitmapFactory.decodeResource(context.resources, resId, options)
            val imgBitmap =
                Bitmap.createScaledBitmap(sourceBitmap, 16.dp.toInt(), 16.dp.toInt(), true)
            sourceBitmap.recycle()
            return imgBitmap
        } catch (e: Exception) {
            return null
        }
    }

    override fun configureVisitor(builder: MarkwonVisitor.Builder) {
        super.configureVisitor(builder)
        builder.on(
            Text::class.java
        ) { p0, p1 ->
            p0.builder().append(p1.literal)
        }
        // 注册任务列表项访问者
        builder.on(
            TaskListItem::class.java
        ) { p0, p1 ->
            p0.blockStart(p1)
            val length = p0.length()
            p0.visitChildren(p1)
            TaskListProps.run { CHECKED.set(p0.renderProps(), p1.checked) }
            p0.setSpansForNodeOptional(p1, length)
            p0.blockEnd(p1)
        }
    }
}