package com.twl.hi.richtext.utils

import com.techwolf.lib.tlog.TLog

object MDCoreUtils {

    @JvmStatic
    fun checkFirstCodeText(codeContent: String?): String? {
        return if (codeContent.isNullOrEmpty()) {
            null
        } else {
            codeContent.trim().split("\n").first()
        }
    }

    @JvmStatic
    fun checkLastCodeText(codeContent: String?): String? {
        return if (codeContent.isNullOrEmpty()) {
            null
        } else {
            codeContent.trim().split("\n").last()
        }
    }

    @JvmStatic
    fun checkIsFirstCodeText(
        codeText: String?,
        text: CharSequence?,
        start: Int,
        end: Int
    ): Bo<PERSON>an {
        if (codeText == null || text == null) {
            return false
        }
        return try {
            val onCodeText = text.substring(start, end)
            codeText.startsWith(onCodeText)
        } catch (e: Exception) {
            TLog.error("checkIsLastCodeText", e.message)
            false
        }
    }

    @JvmStatic
    fun checkIsLastCodeText(codeText: String?, text: CharSequence?, start: Int, end: Int): Boolean {
        if (codeText == null || text == null) {
            return false
        }
        return try {
            val onCodeText = text.substring(start, end)
            if (onCodeText.endsWith("\n")) {
                codeText.endsWith(onCodeText.substring(0, onCodeText.length - 1))
            } else {
                codeText.endsWith(onCodeText)
            }
        } catch (e: Exception) {
            TLog.error("checkIsLastCodeText", e.message)
            false
        }
    }
}