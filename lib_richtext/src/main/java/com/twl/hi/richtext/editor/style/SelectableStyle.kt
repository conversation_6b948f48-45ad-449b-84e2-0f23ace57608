package com.twl.hi.richtext.editor.style

import android.text.Editable
import android.widget.EditText
import com.techwolf.lib.tlog.TLog
import com.twl.hi.richtext.editor.RichEditView

/**
 * <AUTHOR>
 * @date 2023/2/28
 * description:可选择style
 */
abstract class SelectableStyle(var selected: Boolean = false, editView: RichEditView) :
    Style(editView) {

    companion object {
        const val TAG = "rich--->SelectableStyle"
    }

    var onStyleSelectChangedListener: OnStyleSelectChangedListener? = null

    /**
     * 选中文案时再选择开启或者关闭菜单自动应用样式
     */
    abstract fun applyStyleWhenSelectChanged(
        editable: Editable,
        start: Int,
        end: Int,
        selected: Boolean
    ): Boolean

    /**
     * 监听光标变化
     */
    abstract fun onSelectionChanged(editable: Editable, start: Int, end: Int)

    /**
     * 设置是否选中
     */
    fun selectStyle(selected: <PERSON>ole<PERSON>) {
        TLog.debug(TAG, "${this.javaClass.simpleName} selectStyle selected : $selected")
        this.selected = selected
        onStyleSelectChangedListener?.onStyleSelectChanged(selected)
    }

    /**
     * 是否选中
     */
    fun isStyleSelected() = selected


    /**
     * 选中状态监听
     */
    interface OnStyleSelectChangedListener {
        /**
         * 选中状态变化回调
         */
        fun onStyleSelectChanged(selected: Boolean)
    }


}