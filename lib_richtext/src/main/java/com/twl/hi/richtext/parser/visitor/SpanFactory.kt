package com.twl.hi.richtext.parser.visitor

import android.text.style.ImageSpan
import android.text.style.QuoteSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import com.twl.hi.richtext.span.CustomBlockQuoteSpan
import com.twl.hi.richtext.span.CustomUnderLineSpan
import com.twl.hi.richtext.span.OrderedListItemSpanWrapper
import io.noties.markwon.core.spans.*
import io.noties.markwon.image.AsyncDrawableSpan
import lib.twl.common.views.richeditor.span.AtReplacementSpan
import lib.twl.common.views.richeditor.span.CenterImageSpan
import com.twl.hi.richtext.span.HyperlinkDisplaySpan
import lib.twl.common.views.richeditor.span.SlashCommandBgColorSpan

/**
 * Author : Xuweixiang .
 * Date   : On 2023/3/21
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

@JvmOverloads
fun getVisitor(span: Any?): SpanVisitor? {
    return when (span) {
        is CodeSpan -> {
            CodeSpanVisitor(span)
        }
        is EmphasisSpan -> {
            EmphasisSpanVisitor(span)
        }
        is StrongEmphasisSpan -> {
            StrongEmphasisSpanVisitor(span)
        }
        is StrikethroughSpan -> {
            StrikethroughSpanVisitor(span)
        }
        is LinkSpan -> {
            LinkSpanVisitor(span)
        }
        is ImageSpan -> {
            if (span is CenterImageSpan) {
                CenterImageSpanVisitor(span)
            } else {
                ImageSpanVisitor(span)
            }
        }
        is BulletListItemSpan -> {
            BulletListItemSpanVisitor(span)
        }
        is HeadingSpan -> {
            HeadingSpanVisitor(span)
        }
        is QuoteSpan -> {
            QuoteSpanVisitor(span)
        }
        is BlockQuoteSpan -> {
            BlockQuoteSpanVisitor(span)
        }
        is ThematicBreakSpan -> {
            ThematicBreakSpanVisitor(span)
        }
        is OrderedListItemSpan -> {
            if (span is OrderedListItemSpanWrapper) {
                OrderedListItemSpanWrapperVisitor(span)
            } else {
                OrderedListItemSpanVisitor(span)
            }
        }
        is StyleSpan -> {
            StyleSpanVisitor(span)
        }
        is CustomUnderLineSpan -> {
            CustomUnderlineSpanVisitor(span)
        }
        is AsyncDrawableSpan -> {
            AsyncDrawableSpanVisitor(span)
        }
        is AtReplacementSpan -> {
            AtReplacementSpanVisitor(span)
        }
        is HyperlinkDisplaySpan -> {
            HyperlinkDisplaySpanVisitor(span)
        }
        is CustomBlockQuoteSpan -> {
            CustomBlockQuoteSpanVisitor(span)
        }
        is SlashCommandBgColorSpan -> {
            SlashCmdReplacementSpanVisitor(span)
        }
        else -> null
    }
}