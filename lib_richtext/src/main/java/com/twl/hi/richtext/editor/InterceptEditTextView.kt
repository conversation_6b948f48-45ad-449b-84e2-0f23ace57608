package com.twl.hi.richtext.editor

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.R
import androidx.appcompat.widget.AppCompatEditText
import com.twl.hi.richtext.clipboard.retrieveRichText
import com.twl.hi.richtext.editor.interceptor.PasteInterceptListener

/**
 * <AUTHOR>
 * @date 2023/7/28
 * description:
 */
class InterceptEditTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = R.attr.editTextStyle
) : AppCompatEditText(context, attrs, defStyleAttr) {

    var pasteInterceptListener: PasteInterceptListener? = null


    override fun onTextContextMenuItem(id: Int): Boolean {
        return when (id) {
            android.R.id.paste -> {
                val intercept =
                    pasteInterceptListener?.onPasteIntercept(retrieveRichText(context).first.toString())
                        ?: false
                if (intercept) {
                    return true
                }
                super.onTextContextMenuItem(android.R.id.pasteAsPlainText)
            }

            else -> super.onTextContextMenuItem(id)
        }
    }

}