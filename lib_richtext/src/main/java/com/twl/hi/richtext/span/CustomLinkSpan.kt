package com.twl.hi.richtext.span

import android.text.TextPaint
import io.noties.markwon.LinkResolver
import io.noties.markwon.core.MarkwonTheme
import io.noties.markwon.core.spans.LinkSpan

open class CustomLinkSpan(
    theme: MarkwonTheme,
    link: String,
    resolver: LinkResolver,
    val title: String? = null
) : LinkSpan(theme, link, resolver) {
    override fun updateDrawState(ds: TextPaint) {
        // 保持下划线为 TextPaint 的原始状态，不根据 theme 的配置进行更改
        val underlineStyle = ds.isUnderlineText
        super.updateDrawState(ds)
        ds.isUnderlineText = underlineStyle
    }
}

class InlineLinkSpan(
    theme: MarkwonTheme,
    link: String,
    resolver: LinkResolver
) : CustomLinkSpan(theme, link, resolver) {
    var namePair: Pair<String, String>? = null
}

class HyperlinkSpan(
    theme: MarkwonTheme,
    val ref: String,
    link: String,
    resolver: LinkResolver
) : CustomLinkSpan(theme, link, resolver)
