package com.twl.hi.richtext.span;

import android.text.TextPaint;
import android.text.style.BackgroundColorSpan;

import androidx.annotation.NonNull;

/**
 * 指令字体样式span，字体颜色+背景色
 * <p>
 * Created by <PERSON><PERSON><PERSON> on 2023/6/26
 */
public class SlashCommandColorSpan extends BackgroundColorSpan {

    private final int mTextColor;

    public SlashCommandColorSpan(int color, int textColor) {
        super(color);
        mTextColor = textColor;
    }

    @Override
    public void updateDrawState(@NonNull TextPaint textPaint) {
        super.updateDrawState(textPaint);
        textPaint.setColor(mTextColor);
    }
}
