package com.twl.hi.richtext.parser.visitor

import com.twl.hi.richtext.span.CustomLinkSpan
import io.noties.markwon.core.spans.LinkSpan

/**
 * Author : Xuweixiang .
 * Date   : On 2023/3/21
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

class LinkSpanVisitor(private val span: LinkSpan) : SpanVisitor {
    override fun visitPrefix(): String {
        return "["
    }

    override fun visitSuffix(): String {
        val title = if (span is CustomLinkSpan) {
            span.title
        } else null
        return if (title.isNullOrBlank()) {
            "](${span.link})"
        } else {
            "](${span.link} \"${title.replace("\"", "\\\"")}\")"
        }
    }
}