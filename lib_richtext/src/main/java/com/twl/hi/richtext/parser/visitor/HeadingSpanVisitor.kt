package com.twl.hi.richtext.parser.visitor

import io.noties.markwon.core.spans.HeadingSpan

/**
 * Author : <PERSON><PERSON><PERSON><PERSON> .
 * Date   : On 2023/3/21
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

class HeadingSpanVisitor(private val span: HeadingSpan) : SpanVisitor {
    override fun visitPrefix(): String {
        val sb = StringBuilder("\n")
        for (i in 0 until span.level) {
            sb.append("#")
        }
        sb.append(" ")
        return sb.toString()
    }

    override fun visitSuffix(): String {
        return "\n"
    }
}