package com.twl.hi.richtext.movement

import android.text.Spannable
import android.text.style.ClickableSpan
import android.view.MotionEvent
import android.widget.TextView
import me.saket.bettermovementmethod.BetterLinkMovementMethod

class MarkdownMovementMethod : BetterLinkMovementMethod() {

    private var startTime: Long = 0

    override fun onTouchEvent(widget: TextView, buffer: Spannable, event: MotionEvent): Boolean {
        val action = event.action
        if (action == MotionEvent.ACTION_DOWN) {
            startTime = System.currentTimeMillis()
        }
        if (action == MotionEvent.ACTION_UP) {
            if (System.currentTimeMillis() - startTime > 300) {
                return false
            }
            var x = event.x.toInt()
            var y = event.y.toInt()
            x -= widget.totalPaddingLeft
            y -= widget.totalPaddingTop
            x += widget.scrollX
            y += widget.scrollY
            val layout = widget.layout
            val line = layout.getLineForVertical(y)
            val off = layout.getOffsetForHorizontal(line, x.toFloat())
            val links = buffer.getSpans(off, off, ClickableSpan::class.java)
            if (links.isNotEmpty()) {
                val link = links[0]
                dispatchUrlClick(widget, link)
                return true
            }
        }
        return false
    }

    override fun highlightUrl(textView: TextView?, clickableSpan: ClickableSpan?, text: Spannable?) {
    }

    override fun removeUrlHighlightColor(textView: TextView?) {
    }
}