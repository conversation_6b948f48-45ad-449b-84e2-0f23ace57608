package com.twl.hi.richtext.span

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Paint.Align
import android.graphics.Path
import android.graphics.RectF
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.text.style.LeadingMarginSpan
import android.text.style.MetricAffectingSpan
import com.techwolf.lib.tlog.TLog
import com.twl.hi.richtext.parser.item.table.MDTableItem
import com.twl.hi.richtext.parser.item.table.MDTableAlign
import com.twl.hi.richtext.parser.item.table.MDTableRowType
import com.twl.hi.richtext.utils.MDConstants
import com.twl.hi.richtext.utils.MDTableUtils
import lib.twl.common.ext.dp

class MDTableSpan(
    private val tableItem: MDTableItem,
    private val textPaint: Paint = Paint(),
    private val rect: RectF = RectF(),
    private val borderPaint: Paint = Paint()
) : MetricAffectingSpan(), LeadingMarginSpan {

    companion object {

        class TextDrawerValue(
            val x: Float,
            val y: Float,
            val width: Int,
            val align: Align,
            val isFakeBold: Boolean
        )

        @JvmStatic
        fun buildTextDrawerValue(
            x: Int,
            y: Int,
            cellWidth: Int,
            arraySize: Int,
            @MDTableAlign tableAlign: Int,
            @MDTableRowType rowType: Int
        ): TextDrawerValue {
            val paddingValue = MDTableUtils.tableCellPadding(arraySize)
            val textX = when (tableAlign) {
                MDTableAlign.RIGHT -> x + cellWidth - paddingValue
                MDTableAlign.CENTER -> x + cellWidth / 2
                else -> x + paddingValue
            }
            val align = when (tableAlign) {
                MDTableAlign.RIGHT -> Align.RIGHT
                MDTableAlign.CENTER -> Align.CENTER
                else -> Align.LEFT
            }
            return TextDrawerValue(
                textX * 1.0f,
                y * 1.0f,
                cellWidth - paddingValue * 2,
                align,
                rowType == MDTableRowType.HEADER
            )
        }

        class BorderDrawerValue(
            val x: Float,
            val y: Float,
            val width: Int,
            val height: Int,
            val cornerFlag: Int
        )

        @JvmStatic
        fun buildBorderDrawerValue(
            x: Int,
            y: Int,
            cellWidth: Int,
            cellHeight: Int,
            index: Int,
            arraySize: Int,
            @MDTableRowType rowType: Int
        ): BorderDrawerValue {
            val cornerFlag = when {
                index == 0 && rowType == MDTableRowType.HEADER -> 1
                index == arraySize - 1 && rowType == MDTableRowType.HEADER -> 2
                index == arraySize - 1 && rowType == MDTableRowType.FOOTER -> 3
                index == 0 && rowType == MDTableRowType.FOOTER -> 4
                else -> 0
            }
            var drawX = x
            var drawY = y
            var drawWidth = cellWidth
            var drawHeight = cellHeight
            if (index == 0) {
                drawX = x + 2
                drawWidth = cellWidth - 2
            }
            if (rowType == MDTableRowType.HEADER) {
                drawY = y + 2
                drawHeight = cellHeight - 2
            }
            if (index == arraySize - 1) {
                drawWidth = cellWidth - 2
            }
            if (rowType == MDTableRowType.FOOTER) {
                drawHeight = cellHeight - 2
            }
            return BorderDrawerValue(
                drawX * 1.0f,
                drawY * 1.0f,
                drawWidth,
                drawHeight,
                cornerFlag
            )
        }
    }

    init {
        textPaint.isAntiAlias = true
        textPaint.textSize = MDConstants.TABLE_TEXT_SIZE
        textPaint.color = Color.parseColor(MDConstants.TABLE_TEXT_COLOR)

        borderPaint.isAntiAlias = true
        borderPaint.style = Paint.Style.STROKE
        borderPaint.strokeWidth = MDConstants.TABLE_BORDER_WIDTH.dp
        borderPaint.color = Color.parseColor(MDConstants.TABLE_BORDER_COLOR)
    }

    override fun updateDrawState(tp: TextPaint?) {
        // do noting
    }

    override fun updateMeasureState(tp: TextPaint) {
        val maxCount = MDTableUtils.checkMDTableSpanLineCount(tp, tableItem.contentArray)
        tp.textSize = MDConstants.TABLE_PADDING.dp + MDConstants.TABLE_PADDING.dp.toInt() * maxCount
    }

    override fun getLeadingMargin(first: Boolean): Int {
        return 0
    }

    override fun drawLeadingMargin(
        canvas: Canvas?,
        p: Paint?,
        x: Int,
        dir: Int,
        top: Int,
        baseline: Int,
        bottom: Int,
        text: CharSequence?,
        start: Int,
        end: Int,
        first: Boolean,
        layout: Layout?
    ) {
        if (canvas == null) {
            return
        }
        val funTop = MDTableUtils.tableTopValue(top, tableItem.rowType)
        val funBottom = MDTableUtils.tableBottomValue(bottom)

        val cellWidth = (canvas.width - MDConstants.TABLE_RIGHT_MARGIN.dp.toInt()) / tableItem.contentArray.size
        textPaint.set(p)
        tableItem.contentArray.forEachIndexed { index, s ->
            val textDrawerValue = buildTextDrawerValue(
                x + index * cellWidth,
                funTop + (funBottom - funTop) / 2,
                cellWidth,
                tableItem.contentArray.size,
                tableItem.alignArray[index],
                tableItem.rowType
            )
            drawCell(canvas, s, textDrawerValue)

            val borderDrawerValue = buildBorderDrawerValue(
                x + index * cellWidth,
                funTop,
                cellWidth,
                funBottom - funTop,
                index,
                tableItem.contentArray.size,
                tableItem.rowType
            )
            drawBorder(canvas, borderDrawerValue)
        }
    }

    private fun drawCell(canvas: Canvas, content: String, drawerValue: TextDrawerValue) {
        try {
            canvas.save()
            // 设置文本对齐
            textPaint.textAlign = drawerValue.align
            textPaint.isFakeBoldText = drawerValue.isFakeBold

            val staticLayout = StaticLayout.Builder.obtain(
                content,
                0,
                content.length,
                TextPaint(textPaint),
                drawerValue.width
            )
                .setIncludePad(true)
                .setLineSpacing(0f, 1.2f)
                .build()
            canvas.translate(drawerValue.x, drawerValue.y - staticLayout.height / 2)
            staticLayout.draw(canvas)
        } catch (e: Exception) {
            TLog.error("drawCell()", e.message)
        } finally {
            canvas.restore()
        }
    }

    private fun drawBorder(
        canvas: Canvas,
        drawerValue: BorderDrawerValue
    ) {

        rect.top = drawerValue.y
        rect.left = drawerValue.x
        rect.right = drawerValue.x + drawerValue.width
        rect.bottom = drawerValue.y + drawerValue.height

        val cornerRadius = MDConstants.TABLE_BORDER_RADIUS.dp
        val radii = FloatArray(8).apply {
            when (drawerValue.cornerFlag) {
                1 -> {
                    this[0] = cornerRadius
                    this[1] = cornerRadius
                }

                2 -> {
                    this[2] = cornerRadius
                    this[3] = cornerRadius
                }

                3 -> {
                    this[4] = cornerRadius
                    this[5] = cornerRadius
                }

                4 -> {
                    this[6] = cornerRadius
                    this[7] = cornerRadius
                }
            }
        }
        val path = Path().apply {
            addRoundRect(rect, radii, Path.Direction.CW)
        }
        canvas.drawPath(path, borderPaint)
    }
}