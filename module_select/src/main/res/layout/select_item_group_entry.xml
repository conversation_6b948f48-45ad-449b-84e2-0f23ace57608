<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="callback"
            type="Runnable" />

    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/vg_orga"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/sel_item_user"
        android:onClick="@{()->callback.run()}"
        android:paddingLeft="20dp"
        android:paddingTop="15dp"
        android:paddingBottom="15dp">

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/select_ic_group" />

        <TextView
            android:id="@+id/tv_group_chat_option"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="12dp"
            android:text="@string/group_select"
            android:textColor="#ff212121"
            android:textSize="17sp" />

    </LinearLayout>

</layout>
