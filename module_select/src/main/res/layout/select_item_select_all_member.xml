<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="selectAllBean"
            type="com.twl.hi.basic.model.SelectAllBean" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="20dp">

        <CheckBox
            android:id="@+id/cb_user"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:background="@drawable/selector_contact"
            android:checked="@{selectAllBean.isChecked}"
            android:button="@null"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/select_all_member"
            android:textColor="@color/image_color_black"
            android:textSize="17sp"
            android:layout_marginHorizontal="10dp"/>

    </LinearLayout>
</layout>