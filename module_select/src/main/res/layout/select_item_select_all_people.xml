<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="callback"
            type="com.twl.hi.select.callback.SelectAllCallback" />

        <variable
            name="selectAll"
            type="com.twl.hi.select.bean.SelectAll" />

    </data>

    <FrameLayout
        android:id="@+id/rl_all_xuan"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_white"
        android:clickable="true"
        android:focusable="true"
        android:onClick="@{(view)->callback.onSelectAllClick(view,selectAll)}"
        android:paddingLeft="20dp"
        android:paddingRight="20dp">

        <CheckBox
            android:id="@+id/cb_users"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left|center_vertical"
            android:background="@drawable/selector_contact"
            android:button="@null"
            android:checked="@={selectAll.isCheck}"
            android:clickable="false"
            android:enabled="@{selectAll.isEnable}"
            android:text="" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:text="@string/select_all_people"
            android:textColor="@color/color_0D0D1A"
            android:textSize="17sp" />

    </FrameLayout>

</layout>
