<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".contacts.SelectContactsByDepartmentFragment">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.select.multi.viewmodel.SelectMultiByDepartmentViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.basic.callback.TitleBarCallback" />
    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white"
        android:clickable="true"
        android:focusable="true"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            app:callback="@{callback}"
            app:left='@{" "}'
            app:title="@{@string/select_members}" />

        <include
            android:id="@+id/contacts_search"
            layout="@layout/item_contacts_search" />

        <include
            android:id="@+id/view_path_container"
            layout="@layout/view_path_container" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_user_search" />

    </LinearLayout>


</layout>