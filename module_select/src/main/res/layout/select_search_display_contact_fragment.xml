<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>


        <variable
            name="viewModel"
            type="com.twl.hi.select.contact.viewmodel.SearchDisplayGroupContactViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.select.contact.callback.SearchContactCallback" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:clickable="true"
        android:focusable="true"
        android:orientation="vertical"
        tools:context=".contact.select.contact.SearchContactFragment">

        <include
            android:id="@+id/vg_search"
            layout="@layout/view_search_input"
            app:searchInput="@{callback}"
            app:hint="@{@string/select_search_group_member}"/>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <lib.twl.common.views.CloseKeyboardRecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <include
                android:id="@+id/vg_empty"
                layout="@layout/view_search_empty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="150dp" />

        </FrameLayout>

    </LinearLayout>
</layout>