<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".contact.select.contact.SelectContactFragment">

    <data>

        <import type="android.view.View" />

        <variable
            name="rightEditOpen"
            type="Boolean" />

        <variable
            name="viewModel"
            type="com.twl.hi.select.contact.viewmodel.DisplayGroupContactsViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.basic.callback.TitleBarCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/app_white">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:background="@color/app_white"
            app:callback="@{callback}"
            app:right="@{viewModel.rightStr}"
            app:title="@{viewModel.title}" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintTop_toBottomOf="@+id/title_bar"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <FrameLayout
            android:id="@+id/selected_fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>