package com.twl.hi.select.bindadapter;

import static com.twl.hi.basic.bindadapter.BasicBindingAdapters.setConversationGroupType;

import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.TextView;

import androidx.databinding.BindingAdapter;
import androidx.lifecycle.LifecycleOwner;

import com.twl.hi.basic.bindadapter.BasicBindingAdapters;
import com.twl.hi.foundation.logic.ContactService;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.ConversationSelectBean;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.select.R;

import lib.twl.common.base.BaseApplication;

/**
 * <AUTHOR>
 * @date 2021/7/2.
 */
public class SelectBindingAdapter {
    @BindingAdapter("avatarConversationSelect")
    public static void avatarConversationSelect(View view, ConversationSelectBean conversation) {
        BasicBindingAdapters.setAvatar(view, conversation.getAvatar(), conversation.getStrAvatar(), 0);
    }

    @BindingAdapter("setConservationSelectDept")
    public static void setConservationSelectDept(TextView textView, ConversationSelectBean con) {
        String name = !TextUtils.isEmpty(con.getRemark()) ? con.getRemark() : con.getUserName();

        String dept = "";
        if (con.getType() == MessageConstants.MSG_SINGLE_CHAT) {
            ContactService service = ServiceManager.getInstance().getContactService();
            Contact contact = service.getContactById(con.getChatId());
            if (contact != null && !TextUtils.isEmpty(contact.getShowDeptNames())) {
                dept = "  -  " + contact.getShowDeptNames();
            }
        } else if (con.getType() == MessageConstants.MSG_GROUP_CHAT) {
            // 如果是群聊，优先显示群备注名称
            name = !TextUtils.isEmpty(con.getGroupRemark()) ? con.getGroupRemark() : con.getUserName();
        }
        setDepartmentName(textView, name, dept);
    }

    private static void setDepartmentName(TextView textView, String name, String dept) {
        SpannableString ss = new SpannableString(name + dept);
        if (!TextUtils.isEmpty(name)) {
            ss.setSpan(new ForegroundColorSpan(BaseApplication.getApplication().getResources().getColor(R.color.color_212121)),
                    0, name.length(), SpannableString.SPAN_INCLUSIVE_EXCLUSIVE);
        } else {
            name = "";
        }
        if (!TextUtils.isEmpty(dept)) {
            ss.setSpan(new ForegroundColorSpan(BaseApplication.getApplication().getResources().getColor(R.color.color_9B9B9B)), name.length(),
                    name.length() + dept.length(), SpannableString.SPAN_INCLUSIVE_EXCLUSIVE);
        }

        textView.setText(ss);
    }

    @BindingAdapter({"conversationSelectGroupType", "lifeCycleOwner"})
    public static void conversationSelectGroupType(TextView textView, ConversationSelectBean conversation, LifecycleOwner lifeCycleOwner) {
        setConversationGroupType(textView, ServiceManager.getInstance().getConversationService().queryConversationProfile(conversation.getChatId(), conversation.getType()), lifeCycleOwner);
    }

    @BindingAdapter({"selectedCount", "maxCount", "isMultiSelected", "showMultiConfirm"})
    public static void setSelectCountText(TextView textView, int selectedCount, int maxCount, boolean isMultiSelected, boolean showMultiConfirm) {
        if (!showMultiConfirm) {
            if (selectedCount > 0) {
                textView.setText(String.format(textView.getResources().getString(R.string.select_sure_select_count), "(" + (selectedCount > 99 ? "99+" : selectedCount + "") + ")"));
            } else {
                textView.setText(R.string.select_sure);
            }
            return;
        }
        if (isMultiSelected && maxCount > 0) {
            textView.setText(String.format(textView.getResources().getString(R.string.select_sure_select_count), " " + selectedCount + "/" + maxCount));
        } else {
            textView.setText(R.string.select_sure);
        }
    }
}
