package com.twl.hi.select.contact;

import static com.twl.hi.foundation.model.message.MessageConstants.MSG_AT_ALL;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.MotionEvent;
import android.view.View;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;

import com.twl.hi.basic.adapter.MyMultiTypeAdapter;
import com.twl.hi.basic.api.response.bean.GroupMatchBean;
import com.twl.hi.basic.databinding.ItemContactsSearchBinding;
import com.twl.hi.basic.databinding.TitleBarBinding;
import com.twl.hi.basic.dialog.DialogUtils;
import com.twl.hi.basic.helpers.AppPageRouterHelper;
import com.twl.hi.basic.model.SearchBean;
import com.twl.hi.export.select.bean.SelectContactsParams;
import com.twl.hi.export.select.bean.SelectOption;
import com.twl.hi.foundation.SendMessageContent;
import com.twl.hi.basic.views.QuickIndexView;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.export.main.router.AppPageRouter;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.ConversationSelectBean;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.utils.GroupInfoHelper;
import com.twl.hi.foundation.utils.GroupStatusCheckCallback;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.select.BR;
import com.twl.hi.select.R;
import com.twl.hi.select.SendMessageConfirmDialog;
import com.twl.hi.select.SendMessageParams;
import com.twl.hi.select.bean.AtAllBean;
import com.twl.hi.select.bean.GroupChatOptionBean;
import com.twl.hi.select.bean.GroupMatchHint;
import com.twl.hi.select.bean.OrgBean;
import com.twl.hi.select.bean.SelectContactBean;
import com.twl.hi.select.bean.SelectGroupOptionBean;
import com.twl.hi.select.bean.TypeTitleBean;
import com.twl.hi.select.bean.UrgentTypeBean;
import com.twl.hi.select.contact.callback.SelectContactCallback;
import com.twl.hi.select.contact.viewmodel.SelectContactsFragmentViewModel;
import com.twl.hi.select.contact.viewmodel.SelectContactsViewModel;
import com.twl.hi.select.databinding.SelectFragmentSelectOnlyContactBinding;
import com.twl.hi.select.databinding.SelectItemContactsAtAllBinding;
import com.twl.hi.select.databinding.SelectItemContactsOrgaBinding;
import com.twl.hi.select.databinding.SelectItemContactsUrgentTypeBinding;
import com.twl.hi.select.databinding.SelectItemFromGroupChatBinding;
import com.twl.hi.select.databinding.SelectItemGroupEntryBinding;
import com.twl.hi.select.databinding.SelectItemGroupMatchBinding;
import com.twl.hi.select.databinding.SelectItemTypeTitleTvBinding;
import com.twl.hi.select.databinding.SelectItemUserSelectBinding;
import com.twl.hi.select.util.SelectedPointUtil;
import com.twl.utils.SettingBuilder;
import com.twl.utils.StringUtils;
import com.twl.utils.jurisdiction.JurisdictionUtils;

import java.util.ArrayList;
import java.util.List;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.LList;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.util.ToastUtils;

/**
 * 选择联系人的fragment 包含一些按其它方法选择联系人的入口
 * 比如：按组织架构查找，
 */
public class SelectContactFragment extends SelectContactWithIndexAndTitleFragment<SelectFragmentSelectOnlyContactBinding, SelectContactsFragmentViewModel, SelectContactsViewModel> implements SelectContactCallback {
    private DialogUtils mTransferDialog;

    private final int GROUP_MATCH_WHAT = 1;
    Handler handler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            if (msg.what == GROUP_MATCH_WHAT) {
                getViewModel().getGroupMatchList();
            }
            return false;
        }
    });

    public static Fragment create(SelectContactsParams params) {
        SelectContactFragment fragment = new SelectContactFragment();
        Bundle args = new Bundle();
        args.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return 0;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.select_fragment_select_only_contact;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }


    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        // 搜索item
        adapter.register(SearchBean.class, R.layout.item_contacts_search, new MyMultiTypeAdapter.ItemViewBinder<ItemContactsSearchBinding, SearchBean>() {
            @Override
            public void bind(ItemContactsSearchBinding viewDataBinding, SearchBean item, int linkIndex) {
                viewDataBinding.setClick(SelectContactFragment.this);
            }
        });
        // 加急item
        adapter.register(UrgentTypeBean.class, R.layout.select_item_contacts_urgent_type, new MyMultiTypeAdapter.ItemViewBinder<SelectItemContactsUrgentTypeBinding, UrgentTypeBean>() {
            @Override
            public void bind(SelectItemContactsUrgentTypeBinding viewDataBinding, UrgentTypeBean item, int linkIndex) {
                viewDataBinding.setShowSMS(SettingBuilder.getInstance().isJurisdictionVisible(JurisdictionUtils.URGENT_SMS_IDENTITY));
                viewDataBinding.setShowPhone(SettingBuilder.getInstance().isJurisdictionVisible(JurisdictionUtils.URGENT_PHONE_IDENTITY));
                viewDataBinding.rg.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                    @Override
                    public void onCheckedChanged(RadioGroup group, int checkedId) {
                        if (checkedId == R.id.rd_app) {
                            getActivityViewModel().setUrgentType(Constants.SHINING_APP);
                        } else if (checkedId == R.id.rd_sms) {
                            getActivityViewModel().setUrgentType(Constants.SHINING_SMS);
                        } else if (checkedId == R.id.rd_phone) {
                            getActivityViewModel().setUrgentType(Constants.SHINING_PHONE);
                        }
                    }
                });
            }
        });
        // 组织架构
        adapter.register(OrgBean.class, R.layout.select_item_contacts_orga,
                new MyMultiTypeAdapter.ItemViewBinder<SelectItemContactsOrgaBinding, OrgBean>() {
                    @Override
                    public void bind(SelectItemContactsOrgaBinding vdb, OrgBean item, int linkIndex) {
                        vdb.setCallback(SelectContactFragment.this);
                    }
                });
        // @所有人
        adapter.register(AtAllBean.class, R.layout.select_item_contacts_at_all, new MyMultiTypeAdapter.ItemViewBinder<SelectItemContactsAtAllBinding, AtAllBean>() {
            @Override
            public void bind(SelectItemContactsAtAllBinding viewDataBinding, AtAllBean item, int linkIndex) {
                viewDataBinding.setCallback(SelectContactFragment.this);
            }
        });
        adapter.register(GroupMatchHint.class, R.layout.select_item_group_match_hint, null);
        // 群组item
        adapter.register(GroupMatchBean.class, R.layout.select_item_group_match, new MyMultiTypeAdapter.ItemViewBinder<SelectItemGroupMatchBinding, GroupMatchBean>() {
            @Override
            public void bind(SelectItemGroupMatchBinding viewDataBinding, GroupMatchBean item, int linkIndex) {
                viewDataBinding.setGroup(item);
                viewDataBinding.setCallback(SelectContactFragment.this);
            }
        });

        // 近期联系人tip
        adapter.register(TypeTitleBean.class, R.layout.select_item_type_title_tv, new MyMultiTypeAdapter.ItemViewBinder<SelectItemTypeTitleTvBinding, TypeTitleBean>() {
            @Override
            public void bind(SelectItemTypeTitleTvBinding viewDataBinding, TypeTitleBean item, int linkIndex) {
                viewDataBinding.tvTypeTitle.setText(item.getTitle());
            }
        });
        // 选择用户item
        adapter.register(SelectContactBean.class, R.layout.select_item_user_select,
                (MyMultiTypeAdapter.ItemViewBinder<SelectItemUserSelectBinding, SelectContactBean>) (vdb, item, linkIndex) -> {
                    vdb.setCallback(SelectContactFragment.this);
                    vdb.setUser(item);
                    String showName = item.getShowName();
                    Contact contact = item.getContact();
                    if (contact != null) {
                        if (TextUtils.isEmpty(showName)) {
                            showName = contact.getShowName();
                            SpannableStringBuilder style = new SpannableStringBuilder(showName);
                            String deptNames = contact.getShowDeptNames();
                            if (!TextUtils.isEmpty(deptNames)) {
                                style.append(" | ");
                                style.append(deptNames);
                                style.setSpan(new ForegroundColorSpan(activity.getResources().getColor(com.twl.hi.basic.R.color.color_9B9B9B)), showName.length(), style.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                            }
                            vdb.tvUsername.setText(style);
                        } else {
                            SpannableStringBuilder style = new SpannableStringBuilder(showName);
                            String deptNames = contact.getShowDeptNames();
                            if (!TextUtils.isEmpty(deptNames)) {
                                style.append(" | ");
                                style.append(deptNames);
                                style.setSpan(new ForegroundColorSpan(activity.getResources().getColor(com.twl.hi.basic.R.color.color_9B9B9B)), showName.length(), style.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                            }
                            vdb.tvUsername.setServerContent(style.toString());
                        }
                    }
                    vdb.setSelectOnlyOne(selectOnlyOne);
                    vdb.setShowCheckBox(params.isMultiSelect);
                    vdb.setShowBot(item.getContact().getUserType() == MessageConstants.MSG_CONTACT_TYPE_SYS);
                    Context context = vdb.getRoot().getContext();
                    vdb.getRoot().setPadding(QMUIDisplayHelper.dp2px(context, 20), 0, QMUIDisplayHelper.dp2px(context, 28), 0);
                    if (item.getAdminRole() == Constants.TYPE_GROUP_NORMAL) {
                        vdb.tvLabel.setVisibility(View.GONE);
                    } else {
                        vdb.tvLabel.setVisibility(View.VISIBLE);
                        if (item.getAdminRole() == Constants.TYPE_GROUP_CREATE) {
                            vdb.tvLabel.setText(R.string.select_group_creator);
                            vdb.tvLabel.setDefaultBgColor(Color.parseColor("#FD8C43"));
                        } else {
                            vdb.tvLabel.setText(R.string.select_group_admin);
                            vdb.tvLabel.setDefaultBgColor(Color.parseColor("#6176E7"));
                        }
                    }
                });
        adapter.register(GroupChatOptionBean.class, R.layout.select_item_from_group_chat, new MyMultiTypeAdapter.ItemViewBinder<SelectItemFromGroupChatBinding, GroupChatOptionBean>() {
            @Override
            public void bind(SelectItemFromGroupChatBinding selectItemFromGroupChatBinding, GroupChatOptionBean item, int linkIndex) {
                selectItemFromGroupChatBinding.setCallback(() -> {
                    jumpToSelectProjectGroup(true);
                });
            }
        });
        adapter.register(SelectGroupOptionBean.class, R.layout.select_item_group_entry, new MyMultiTypeAdapter.ItemViewBinder<SelectItemGroupEntryBinding, SelectGroupOptionBean>() {
            @Override
            public void bind(SelectItemGroupEntryBinding binding, SelectGroupOptionBean item, int linkIndex) {
                binding.setCallback(() -> gotoSelectGroups());
            }
        });

        getViewModel().init(getActivityViewModel(), params);
        // 分页加载会话
        getDataBinding().smartRefresh.setEnableRefresh(false);
        getDataBinding().smartRefresh.setEnableLoadMore(true);
        getDataBinding().smartRefresh.setOnLoadMoreListener(refreshLayout -> getViewModel().loadMorePage());
        getViewModel().mNextPageConversationLiveData.observe(this, list -> {
            getDataBinding().smartRefresh.finishLoadMore();
            if (LList.isEmpty(list)) {
                return;
            }
            adapter.addData(list);
            adapter.notifyDataSetChanged();
            if (isShowIndex()) {
                getQuickIndexView().setIndexer(getViewModel().getIndex());
            }
        });
        getDataBinding().quickIndexView.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    getDataBinding().smartRefresh.setEnableLoadMore(false);
                    break;
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    getDataBinding().smartRefresh.setEnableLoadMore(true);
                    break;
            }
            return false;
        });
        getViewModel().getListMutableLiveData().observe(this, new Observer<List<Object>>() {
            @Override
            public void onChanged(List<Object> selectContactBeans) {
                if (LList.isEmpty(selectContactBeans)) {
                    return;
                }
                adapter.setData(selectContactBeans);
                adapter.notifyDataSetChanged();
                if (isShowIndex()) {
                    getQuickIndexView().setIndexer(getViewModel().getIndex());
                }
            }
        });
        if (StringUtils.isNotEmpty(params.getGroupId())) {
            //群数据有更新监听变化
            ServiceManager.getInstance().getGroupService().getRefreshGroupId().observe(this, refreshGroupId -> {
                if (null == refreshGroupId) {
                    return;
                }
                if (StringUtils.isEquals(params.getGroupId(), refreshGroupId)) {
                    return;
                }
                getViewModel().load(params);
            });
        }
        if (params.isMultiSelect()) {
            getActivityViewModel().getSelectedContactsMutableModel().mSelectCount.observe(this, new Observer<Integer>() {
                @Override
                public void onChanged(Integer count) {
                    if (count == null) {
                        return;
                    }
                    int num = count - params.getExistIds().size();
                    if (params.isShowGroupMatch()) {
                        handler.removeMessages(GROUP_MATCH_WHAT);
                        if (num >= 2) {
                            Message msg = Message.obtain();
                            msg.what = GROUP_MATCH_WHAT;
                            handler.sendMessageDelayed(msg, 500); //隔一小段时间发送msg
                        } else {
                            if (getViewModel().hasMatchList()) {
                                getViewModel().refreshContactOriginData();
                            }
                        }
                    }
                }
            });
            getActivityViewModel().getNotifyRefreshMainLiveData().observe(this, selectNotifyBean -> getViewModel().refreshData(selectNotifyBean));
            getActivityViewModel().getNotifyCleanOtherLiveData().observe(this, selectNotifyBean -> getViewModel().refreshData(selectNotifyBean));
            getActivityViewModel().getNotifyDeleteSelectedLiveData().observe(this, selectNotifyBean -> getViewModel().refreshData(selectNotifyBean));
        }
        getViewModel().load(params);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
            handler = null;
        }
    }

    @Override
    protected TitleBarBinding getTitleBarBinding() {
        return getDataBinding().titleBar;
    }

    @Override
    protected RecyclerView getContactRecyclerView() {
        return getDataBinding().vgContactListIndex.recyclerView;
    }

    @Override
    protected QuickIndexView getQuickIndexView() {
        return getDataBinding().quickIndexView;
    }

    @Override
    protected TextView getOverlayView() {
        return getDataBinding().vgContactListIndex.overlayView;
    }

    @Override
    protected int parseIndexPosition(List list, String indexText) {
        int ind = -1;
        if (StringUtils.isNotEmpty(params.getGroupId())) { //范围为群
            for (int i = 0; i < list.size(); i++) {
                Object o = list.get(i);
                if (o instanceof SelectContactBean) {
                    SelectContactBean userBean = (SelectContactBean) o;
                    if (params.isAdminTop() && getViewModel().isSkipIndex(userBean)) {
                        continue;
                    }
                    if (userBean.getContact().getFirstChar().equalsIgnoreCase(indexText)) {
                        ind = i;
                        break;
                    }
                }
            }
        } else { //范围为所有
            for (int i = 0; i < list.size(); i++) {
                Object o = list.get(i);
                if (o instanceof SelectContactBean) {
                    Contact userBean = ((SelectContactBean) o).getContact();
                    if (userBean.getFirstChar().equalsIgnoreCase(indexText)) {
                        ind = i;
                        break;
                    }
                }
            }
        }
        return ind;
    }

    @Override
    protected String parseDecoratorText(Object bean) {
        if (StringUtils.isNotEmpty(params.getGroupId())) {
            if (!(bean instanceof SelectContactBean)) {
                return "";
            }
            SelectContactBean user = (SelectContactBean) bean;
            if (params.isAdminTop() && getViewModel().isSkipIndex(user)) {
                return "";
            }
            return user.getContact().getFirstChar().toUpperCase();
        } else {
            if (bean instanceof SelectContactBean) {
                Contact user = ((SelectContactBean) bean).getContact();
                return user.getFirstChar().toUpperCase();
            }
            return "";
        }
    }

    @Override
    public void clickLeft(View view) {
        if (needCheckGroupStatus()) {
            GroupInfoHelper.optWithGroupStatusCheck(params.getGroupId(), MessageConstants.MSG_GROUP_CHAT, new GroupStatusCheckCallback() {
                @Override
                public void onStatusNormal() {
                    activity.onBackPressed();
                }

                @Override
                public void onStatusAbnormal() {
                    AppPageRouterHelper.backToMainTabActivity(activity);
                }
            });
        } else {
            activity.onBackPressed();
        }
    }

    private boolean needCheckGroupStatus() {
        return params.getOptionId() == SelectOption.OPTION_ADD_GROUP_MEMBER ||
                params.getOptionId() == SelectOption.OPTION_REDUCE_GROUP_MEMBER ||
                params.getOptionId() == SelectOption.OPTION_ADD_DEPARTMENT_GROUP_MEMBER ||
                params.getOptionId() == SelectOption.OPTION_GROUP_ADMIN_ADD ||
                params.getOptionId() == SelectOption.OPTION_GROUP_SPECIAL_ATTENTION ||
                params.getOptionId() == SelectOption.OPTION_GROUP_TRANSFER;
    }

    @Override
    public void clickRight(View view) {
    }

    @Override
    public void clickSearch() {

    }

    @Override
    public void onSelectContactItemClick(SelectContactBean contactBean) {

        if (params.isMultiSelect) {
            if (contactBean.isAlreadySelected) {
                return;
            }
            boolean oldSelect = contactBean.isSelected();
            int maxSelectCount = params.getMaxSelectCount();
            if (maxSelectCount > 0) {
                if (maxSelectCount == 1) {
                    ArrayList<String> selects = getActivityViewModel().getSelectedContactsMutableModel().mSelects;
                    if (!LList.isEmpty(selects)) {
                        String firstSelectId = selects.get(0);
                        if (!TextUtils.equals(contactBean.getId(), firstSelectId)) {
                            selects.remove(0);
                            getActivityViewModel().cleanSelectedContact(false, firstSelectId);
                        }
                    }
                } else if (getActivityViewModel().getSelectedContactsMutableModel().mSelects.size() >= maxSelectCount && !oldSelect) {
                    ToastUtils.failure(String.format(getResources().getString(R.string.select_max_count), maxSelectCount));
                    return;
                }
            }
            getActivityViewModel().getSelectedContactsMutableModel().mConversationSource.setContact(1, params.isMultiSelect);
            contactBean.setSelected(!oldSelect);
        } else {
            getActivityViewModel().getSelectedContactsMutableModel().mConversationSource.setContact(1, params.isMultiSelect);
            Intent data = new Intent();
            switch (params.getOptionId()) {
                case SelectOption.OPTION_GROUP_TRANSFER:
                    showTransferDialog(contactBean.getContact());
                    break;
                case SelectOption.OPTION_H5_SELECT:
                    ArrayList<String> allIds = new ArrayList<>();
                    String userId = contactBean.getContact().getUserId();
                    Contact h5Contact = ServiceManager.getInstance().getContactService().getContactById(userId);
                    allIds.add(userId);
                    data.putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE, allIds);
                    data.putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE_3, h5Contact);
                    SelectedPointUtil.sendForwardPoint(getActivityViewModel().getSelectedContactsMutableModel().mConversationSource, 1);
                    activity.setResult(Activity.RESULT_OK, data);
                    activity.finish();
                    break;
                case SelectOption.OPTION_GROUP_SEARCH_CHAT_RECORD:
                    SelectedPointUtil.sendForwardPoint(getActivityViewModel().getSelectedContactsMutableModel().mConversationSource, 1);
                    Bundle bundle = new Bundle();
                    bundle.putString(BundleConstants.BUNDLE_USER_ID, contactBean.getContact().getUserId());
                    bundle.putString(BundleConstants.BUNDLE_CHAT_ID, params.getGroupId());
                    bundle.putInt(BundleConstants.BUNDLE_CHAT_TYPE, MessageConstants.MSG_GROUP_CHAT);
                    AppUtil.startUri(activity, ChatPageRouter.CHAT_RECORD_SEARCH_FOR_MEMBER_ACTIVITY, bundle);
                    break;
                case SelectOption.OPTION_SEND_USER_CARD:
                    //发送用户名片
                    SendMessageContent sendMessageContent = new SendMessageContent(SendMessageContent.TYPE_SHARE_USER_CARD);
                    sendMessageContent.content = contactBean.getContact();
                    sendMessageContent.msgShowContent = SendMessageContent.getCardString(contactBean.getContact());
                    SendMessageParams sendMessageParams = new SendMessageParams();
                    if (params.getSelectBean().getType() == MessageConstants.MSG_SINGLE_CHAT) {
                        Contact contact = ServiceManager.getInstance().getContactService().getContactById(params.getSelectBean().getChatId());
                        if (contact != null) {
                            ConversationSelectBean conversationSelectBean = new ConversationSelectBean();
                            conversationSelectBean.setChatId(contact.getUserId());
                            conversationSelectBean.setType(MessageConstants.MSG_SINGLE_CHAT);
                            conversationSelectBean.setAvatar(contact.getAvatar());
                            conversationSelectBean.setNickName(contact.getNickName());
                            conversationSelectBean.setUserName(contact.getShowName());
                            sendMessageParams.selectedConversationList.add(conversationSelectBean);
                        } else {
                            ToastUtils.failure("用户名片不存在");
                        }
                    } else {
                        sendMessageParams.selectedConversationList.add(params.getSelectBean());
                    }
                    sendMessageParams.sendMessageContent = sendMessageContent;
                    SendMessageConfirmDialog fragment = SendMessageConfirmDialog.newInstance(sendMessageParams);
                    fragment.setSendMessageCallback(() ->
                            SelectedPointUtil.sendForwardPoint(
                                    getActivityViewModel().getSelectedContactsMutableModel().mConversationSource, sendMessageParams.selectedConversationList
                            )
                    );
                    fragment.show(((FragmentActivity) activity).getSupportFragmentManager(), SendMessageConfirmDialog.TAG);
                    break;
                default:
                    SelectedPointUtil.sendForwardPoint(getActivityViewModel().getSelectedContactsMutableModel().mConversationSource, 1);
                    data.putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE, contactBean.getContact());
                    activity.setResult(Activity.RESULT_OK, data);
                    activity.finish();
            }

        }
    }

    @Override
    public void onGroupMatchItemClick(GroupMatchBean groupMatchBean) {
        if (params.getOptionId() == SelectOption.OPTION_CREATE_GROUP_AND_FORWARD) {
            Intent data = new Intent();
            data.putExtra(BundleConstants.BUNDLE_CHAT_ID, groupMatchBean.groupId);
            activity.setResult(Activity.RESULT_OK, data);
            activity.finish();
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_CHAT_ID, groupMatchBean.groupId);
        AppUtil.startUri(activity, ChatPageRouter.GROUP_CHAT_ACTIVITY, bundle);

    }

    @Override
    public void onOrgItemClick() {
        ((FragmentActivity) activity)
                .getSupportFragmentManager()
                .beginTransaction()
                .setCustomAnimations(R.anim.activity_new_enter_up_glide,
                        0,
                        0,
                        R.anim.activity_new_exit_up_glide)
                .add(R.id.fragment_container, SelectContactsByDepartmentFragment.create(params))
                .addToBackStack(null)
                .commitAllowingStateLoss();
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.vg_search) {
            SearchContactFragment fragment = SearchContactFragment.create(params);
            ((FragmentActivity) activity)
                    .getSupportFragmentManager()
                    .beginTransaction()
                    .setCustomAnimations(R.anim.activity_new_enter_up_glide,
                            0,
                            0,
                            R.anim.activity_new_exit_up_glide)
                    .add(R.id.fragment_container, fragment, "SearchContactFragment")
                    .addToBackStack(null)
                    .commitAllowingStateLoss();
        }
    }

    @Override
    public void onAtAllClick() {
        Contact contact = new Contact();
        contact.setUserId(MSG_AT_ALL);
        contact.setUserName(getResources().getString(R.string.all_users));
        Intent intent = new Intent();
        intent.putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE, contact);
        activity.setResult(Activity.RESULT_OK, intent);
        activity.finish();
    }

    private void showTransferDialog(Contact contact) {
        mTransferDialog = new DialogUtils.Builder(activity)
                .setCancelable(true)
                .setCanceledOnTouchOutside(true)
                .setContent(getResources().getString(R.string.select_group_transfer_desc, getString(R.string.group), contact.getShowName()))
                .setPositive(R.string.sure)
                .setNegative(R.string.cancel)
                .setPositiveListener(v ->
                        getActivityViewModel().checkGroupStatus(params.getGroupId(), new GroupStatusCheckCallback() {
                            @Override
                            public void onStatusNormal() {
                                mTransferDialog.dismiss();
                                showProgressDialog(getString(R.string.select_transfer_group), false);
                                SelectedPointUtil.sendForwardPoint(getActivityViewModel().getSelectedContactsMutableModel().mConversationSource, 1);
                                getActivityViewModel().requestChangeGroupOwner(params.getGroupId(), contact.getUserId()).observe(SelectContactFragment.this, apiResponse -> {
                                    dismissProgressDialog();
                                    if (apiResponse != null && apiResponse.isSuccess()) {
                                        new PointUtils.BuilderV4()
                                                .name("chat_settings_transfer_ownership_save")
                                                .params("chat_id", params.getGroupId() + "")
                                                .point();
                                        ToastUtils.success(R.string.select_transfer_success);
                                        AppUtil.getDefaultUriRequest(activity, AppPageRouter.MAIN_TAB_ACTIVITY).setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).start();
                                        activity.finish();
                                    }
                                });
                            }

                            @Override
                            public void onStatusAbnormal() {
                                ToastUtils.failure(R.string.tips_not_in_group);
                            }
                        }))
                .setNegativeListener(v ->
                        getActivityViewModel().checkGroupStatus(params.getGroupId(), new GroupStatusCheckCallback() {
                            @Override
                            public void onStatusNormal() {
                                mTransferDialog.dismiss();
                            }

                            @Override
                            public void onStatusAbnormal() {
                                AppPageRouterHelper.backToMainTabActivity(activity);
                            }
                        }))
                .build();
        mTransferDialog.show();
    }

    /**
     * 跳转到选择群聊组界面
     */
    public void jumpToSelectProjectGroup(boolean selectPartMember) {
        FragmentTransaction fragmentTransaction = ((FragmentActivity) activity)
                .getSupportFragmentManager()
                .beginTransaction()
                .setCustomAnimations(R.anim.activity_new_enter_up_glide,
                        0,
                        0,
                        R.anim.activity_new_exit_up_glide)
                .addToBackStack(null);
        if (selectPartMember) {
            fragmentTransaction
                    .add(R.id.fragment_container,
                            SelectGroupFragment.newInstance(true, params));
            new PointUtils.BuilderV3().name("add-bygroup-click").point();
        }else {
            //清除选择数据
            getViewModel().cleanSelected(params);
            fragmentTransaction
                    .add(R.id.selected_fragment_container,
                            SelectGroupFragment.newInstance(false, params));
        }
        fragmentTransaction.commitAllowingStateLoss();
    }

    public void gotoSelectGroups() {
        ((FragmentActivity) activity)
                .getSupportFragmentManager()
                .beginTransaction()
                .setCustomAnimations(R.anim.activity_new_enter_up_glide,
                        0,
                        0,
                        R.anim.activity_new_exit_up_glide)
                .addToBackStack(null)
                .add(R.id.fragment_container, new SelectMultiGroupFragment())
                .commitAllowingStateLoss();
    }

    @Override
    protected RecyclerView.ItemDecoration makeItemDecorator(Context context) {
        if (isShowIndex()) {
            return super.makeItemDecorator(context);
        } else {
            return null;
        }
    }

    /**是否展示首字母index*/
    private boolean isShowIndex() {
        return !params.isOnlyConversationContacts()
                && params.getOptionId() != SelectOption.OPTION_REDUCE_GROUP_MEMBER;
    }

    @Override
    public RecyclerView getRecyclerView() {
        return getDataBinding().vgContactListIndex.recyclerView;
    }

    @Override
    public boolean forceUpdateVisibleItem() {
        return true;
    }

    /**
     * 获取被选中的联系人的位置
     * @param uids 被选中的用户ids
     * @return
     */
    public ArrayList<Integer> getSelectedContactPos(ArrayList<String> uids) {
        ArrayList<Integer> selectedItemPos = new ArrayList<>();
        if (adapter == null || uids == null || uids.size() == 0) {
            return selectedItemPos;
        }
        ArrayList<String> selectedItems = new ArrayList<>();
        List<Object> datas = adapter.getData();
        for (String chatId : uids) {
            int j = 1;
            for (Object o : datas) {
                if (o instanceof SelectContactBean) {
                    SelectContactBean bean = (SelectContactBean) o;
                    if (TextUtils.equals(chatId, bean.getId())) {
                        selectedItemPos.add(j);
                        selectedItems.add(chatId);
                        break;
                    }
                    j++;
                }
            }
        }
        uids.clear();
        uids.addAll(selectedItems);
        return selectedItemPos;
    }
}

