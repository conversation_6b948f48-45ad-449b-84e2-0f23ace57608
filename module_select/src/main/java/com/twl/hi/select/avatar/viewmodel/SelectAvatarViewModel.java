package com.twl.hi.select.avatar.viewmodel;

import android.app.Application;
import android.graphics.drawable.Drawable;

import androidx.databinding.ObservableField;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.twl.hi.export.select.bean.SelectAvatarParam;
import com.twl.hi.foundation.api.response.bean.AvatarBean;
import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.select.R;
import com.twl.hi.select.avatar.model.contact.SelectAvatarStrategy;
import com.twl.hi.select.avatar.model.contact.SubmitAvatarParam;
import com.twl.hi.select.avatar.model.strategy.SelectCalendarAvatarStrategy;
import com.twl.hi.select.avatar.model.strategy.SelectGroupAvatarStrategy;
import com.twl.hi.select.avatar.model.strategy.SelectInvalidateStrategy;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lib.twl.common.util.ToastUtils;
import lib.twl.common.util.lifecycle.NoStickLiveData;


public class SelectAvatarViewModel extends FoundationViewModel {
    private static final String TAG = "ProjectTeamSelectAvatarViewModel";
    private ObservableField<String> mTinyAvatar = new ObservableField<>();
    private ObservableField<String> mAvatarUrl = new ObservableField<>();
    private String groupId;
    private SelectAvatarStrategy mStrategy = null;
    /**颜色配置*/
    private final MutableLiveData<Drawable> topBackground = new MutableLiveData<>();
    private final MutableLiveData<Drawable> centerBackground = new MutableLiveData<>();
    private final MutableLiveData<Drawable> bottomBackground = new MutableLiveData<>();
    private final MutableLiveData<Integer> topTextColor = new MutableLiveData<>(0);
    private final MutableLiveData<Integer> submitColor = new MutableLiveData<>(0);

    /**退出Activity事件*/
    private final NoStickLiveData<Boolean> mQuiteObservable = new NoStickLiveData<>();
    /**返回上个页面结果事件*/
    private final NoStickLiveData<AvatarBean> mBackResultObservable = new NoStickLiveData<>();

    public SelectAvatarViewModel(Application application) {
        super(application);
    }

    public ObservableField<String> getTinyAvatar() {
        return mTinyAvatar;
    }

    public ObservableField<String> getAvatarUrl() {
        return mAvatarUrl;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public LiveData<List<AvatarBean>> getAllHeaders() {
        return mStrategy.getDefaultAvatar();
    }

    public SelectAvatarStrategy getStrategy() {
        return mStrategy;
    }

    public LiveData<Boolean> updateGroupAvatar() {
        return mStrategy.submitAvatar(generaSubmitParam());
    }

    private SubmitAvatarParam generaSubmitParam() {
        if (mStrategy instanceof SelectGroupAvatarStrategy) {
            return new SelectGroupAvatarStrategy.SubmitParam(
                    groupId,
                    mAvatarUrl.get(),
                    mTinyAvatar.get()
            );
        } else if (mStrategy instanceof SelectCalendarAvatarStrategy) {
            return null;
        } else {
            return null;
        }
    }

    public String getGroupId() {
        return groupId;
    }

    public void initialScene(String scene) {
        if (scene == null) {
            mStrategy = new SelectInvalidateStrategy();
        } else {
            switch (scene) {
                case SelectAvatarParam.SCENE_SELECT_GROUP_AVATAR:
                    mStrategy = new SelectGroupAvatarStrategy();
                    break;
                case SelectAvatarParam.SCENE_SELECT_CALENDAR_AVATAR:
                    mStrategy = new SelectCalendarAvatarStrategy();
                    break;
                default:
                    mStrategy = new SelectInvalidateStrategy();
            }
        }
        configBackground(mStrategy);
    }

    private void configBackground(SelectAvatarStrategy strategy) {
        topBackground.setValue(strategy.getTopBackground());
        centerBackground.setValue(strategy.getCenterBackground());
        bottomBackground.setValue(strategy.getBottomBackground());
        topTextColor.setValue(strategy.getTopTextColor());
        submitColor.setValue(strategy.getSubmitColor());
        submitColor.setValue(strategy.getSubmitColor());
    }

    public void updateSuccess() {
        if (mStrategy instanceof SelectGroupAvatarStrategy) {
            mQuiteObservable.setValue(true);
            pointAvatarUpdate();
            ToastUtils.success(R.string.operate_success);
        } else {
            AvatarBean avatarBean = new AvatarBean();
            avatarBean.setTinyAvatar(mTinyAvatar.get());
            avatarBean.setAvatar(mAvatarUrl.get());
            mBackResultObservable.setValue(avatarBean);
        }
    }

    private void pointAvatarUpdate() {
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("icon_type", "1");
        paramsMap.put("chat_id", groupId + "");
        PointUtils.pointV4("chat_group_info_save", paramsMap);
    }

    public LiveData<Boolean> getQuiteObservable() {
        return mQuiteObservable;
    }

    public LiveData<AvatarBean> getBackResultObservable() {
        return mBackResultObservable;
    }

    public LiveData<Drawable> getTopBackground() {
        return topBackground;
    }

    public LiveData<Drawable> getCenterBackground() {
        return centerBackground;
    }

    public LiveData<Drawable> getBottomBackground() {
        return bottomBackground;
    }

    public LiveData<Integer> getTopTextColor() {
        return topTextColor;
    }

    public LiveData<Integer> getSubmitColor() {
        return submitColor;
    }
}