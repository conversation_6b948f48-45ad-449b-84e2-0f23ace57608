package com.twl.hi.select.avatar.model.strategy

import android.graphics.Color
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.techwolf.lib.tlog.TLog
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.api.response.bean.AvatarBean
import com.twl.hi.select.avatar.model.contact.SelectAvatarStrategy
import com.twl.hi.select.avatar.model.contact.SubmitAvatarParam
import com.twl.hi.select.avatar.model.remote.request.GroupAvatarsRequest
import com.twl.hi.select.avatar.model.remote.request.GroupInfoUpdateRequest
import com.twl.hi.foundation.api.response.AvatarsResponse
import com.twl.hi.select.R
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.client.HttpResponse
import com.twl.http.error.ErrorReason
import lib.twl.common.ext.getResourceDrawable
import lib.twl.common.ext.getResourceString

/**
 *@author: musa on 2022/12/9
 *@e-mail: <EMAIL>
 *@desc: 选择群成员头像的策略
 */
class SelectGroupAvatarStrategy : SelectAvatarStrategy {
    private val TAG = "SelectGroupAvatarStrategy"

    override fun getDefaultAvatar(): LiveData<List<AvatarBean>> {
        val avatars = MutableLiveData<List<AvatarBean>>()
        GroupAvatarsRequest(object : BaseApiRequestCallback<AvatarsResponse?>() {

            override fun onSuccess(data: ApiData<AvatarsResponse?>) {
                data.resp?.result?.let {
                    avatars.postValue(it)
                }
            }

            override fun onComplete() {}
            override fun onFailed(reason: ErrorReason) {}
        }).let {
            HttpExecutor.execute(it)
        }
        return avatars
    }

    override fun submitAvatar(param: SubmitAvatarParam?): LiveData<Boolean> {
        val updateLiveData = MutableLiveData<Boolean>()
        if (param !is SubmitParam) {
            TLog.error(TAG, "提交参数类型不符合")
            return updateLiveData;
        }
        GroupInfoUpdateRequest(object : BaseApiRequestCallback<HttpResponse?>() {
            override fun handleInChildThread(data: ApiData<HttpResponse?>) {
                ServiceManager.getInstance().groupService.updateGroupAvatar(
                    param.groupId,
                    param.avatarUrl,
                    param.tinyAvatar
                )
            }

            override fun onFailed(reason: ErrorReason) {
                //nothing
            }

            override fun onComplete() {
                //nothing
            }

            override fun onSuccess(data: ApiData<HttpResponse?>?) {
                updateLiveData.value = true
            }
        }).let {
            it.groupId = param.groupId
            it.avatar = param.avatarUrl
            it.tinyAvatar = param.tinyAvatar
            HttpExecutor.execute(it)
        }
        return updateLiveData
    }

    override fun getTopBackground() = R.color.color_2e2e2e.getResourceDrawable()

    override fun getCenterBackground() = R.color.color_2e2e2e.getResourceDrawable()

    override fun getBottomBackground() = R.color.color_333333.getResourceDrawable()

    override fun getTopTextColor(): Int = Color.WHITE

    override fun getSubmitColor(): Int = Color.WHITE

    override fun getTitle() = R.string.select_chat_group_avatar.getResourceString()

    override fun getTips() = R.string.select_chose_group_avatar.getResourceString()

    class SubmitParam(
        val groupId: String,
        val avatarUrl: String,
        val tinyAvatar: String
    ): SubmitAvatarParam
}

