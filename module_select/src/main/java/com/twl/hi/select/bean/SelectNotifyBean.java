package com.twl.hi.select.bean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/31.
 */
public class SelectNotifyBean {
    private boolean notifyCheck;//通知变化 true选中，false删除
    private List<String> changedIds;//通知变化的ID列表

    public SelectNotifyBean(boolean notifyCheck, List<String> changedContactIds) {
        this.notifyCheck = notifyCheck;
        this.changedIds = changedContactIds;
    }

    public boolean isNotifyCheck() {
        return notifyCheck;
    }

    public List<String> getChangedIds() {
        return changedIds;
    }
}
