package com.twl.hi.select.avatar.model.strategy

import android.graphics.Color
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.response.bean.AvatarBean
import com.twl.hi.select.avatar.model.contact.SelectAvatarStrategy
import com.twl.hi.select.avatar.model.contact.SubmitAvatarParam
import com.twl.hi.foundation.api.request.QueryCalendarDefaultAvatarRequest
import com.twl.hi.foundation.api.response.AvatarsResponse
import com.twl.hi.select.R
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.error.ErrorReason
import lib.twl.common.ext.getResourceDrawable
import lib.twl.common.ext.getResourceString

/**
 *@author: musa on 2022/12/9
 *@e-mail: <EMAIL>
 *@desc: 选择日历头像策略
 */
class SelectCalendarAvatarStrategy : SelectAvatarStrategy{

    override fun getDefaultAvatar(): LiveData<List<AvatarBean>> {
        val avatars = MutableLiveData<List<AvatarBean>>()
        QueryCalendarDefaultAvatarRequest(object : BaseApiRequestCallback<AvatarsResponse>(){
            override fun onSuccess(data: ApiData<AvatarsResponse>?) {
                data?.resp?.result?.let {
                    avatars.postValue(it)
                }
            }

            override fun onComplete() {

            }

            override fun onFailed(reason: ErrorReason?) {

            }
        }).let {
            HttpExecutor.execute(it)
        }
        return avatars
    }

    override fun submitAvatar(param: SubmitAvatarParam?): LiveData<Boolean>
        = MutableLiveData(true)

    override fun getTopBackground() = R.color.app_white.getResourceDrawable()

    override fun getCenterBackground() = R.color.color_F9F9FA.getResourceDrawable()

    override fun getBottomBackground() = R.color.app_white.getResourceDrawable()

    override fun getTopTextColor(): Int = Color.BLACK

    override fun getSubmitColor(): Int = 0xFF5D68E8.toInt()

    override fun getTitle() = R.string.select_calendar_avatar.getResourceString()

    override fun getTips() = R.string.select_chose_calendar_avatar.getResourceString()

}