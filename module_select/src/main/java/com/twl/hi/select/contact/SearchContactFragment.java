package com.twl.hi.select.contact;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.EditText;

import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;

import com.twl.hi.basic.adapter.MyMultiTypeAdapter;
import com.twl.hi.basic.dialog.DialogUtils;
import com.twl.hi.basic.helpers.AppPageRouterHelper;
import com.twl.hi.basic.views.multitext.ChatRecordSearchTextView;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.export.main.router.AppPageRouter;
import com.twl.hi.export.select.bean.SelectContactsParams;
import com.twl.hi.export.select.bean.SelectOption;
import com.twl.hi.foundation.SendMessageContent;
import com.twl.hi.foundation.base.fragment.FoundationVMShareFragment;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.ConversationSelectBean;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.utils.GroupStatusCheckCallback;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.foundation.utils.point.GlobalSearchPointUtilKt;
import com.twl.hi.foundation.utils.point.SearchPointUtil;
import com.twl.hi.select.BR;
import com.twl.hi.select.R;
import com.twl.hi.select.SendMessageConfirmDialog;
import com.twl.hi.select.SendMessageParams;
import com.twl.hi.select.bean.ContactHint;
import com.twl.hi.select.bean.SelectContactBean;
import com.twl.hi.select.bean.SelectNotifyBean;
import com.twl.hi.select.contact.callback.SearchContactCallback;
import com.twl.hi.select.contact.callback.SelectContactItemCallback;
import com.twl.hi.select.contact.viewmodel.SearchContactViewModel;
import com.twl.hi.select.contact.viewmodel.SelectContactsViewModel;
import com.twl.hi.select.databinding.SelectItemUserSelectBinding;
import com.twl.hi.select.databinding.SelectSearchContactFragmentBinding;
import com.twl.utils.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.LList;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.util.QMUIKeyboardHelper;
import lib.twl.common.util.ToastUtils;

public class SearchContactFragment extends FoundationVMShareFragment<SelectSearchContactFragmentBinding, SearchContactViewModel, SelectContactsViewModel> implements SearchContactCallback, SelectContactItemCallback {

    private DialogUtils mTransferDialog;
    private SelectContactsParams params;
    private MyMultiTypeAdapter mAdapter = new MyMultiTypeAdapter();
    boolean selectOnlyOne;
    private SearchPointUtil mSearchPointUtil = new SearchPointUtil();
    private int mSelectedCount = 0; // 会话被选中个数

    public static SearchContactFragment create(SelectContactsParams params) {
        SearchContactFragment fragment = new SearchContactFragment();
        Bundle args = new Bundle();
        args.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        fragment.setArguments(args);
        return fragment;
    }

    public static SearchContactFragment create(SelectContactsParams params, String defaultKey) {
        SearchContactFragment fragment = new SearchContactFragment();
        Bundle args = new Bundle();
        args.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        args.putString(BundleConstants.BUNDLE_DATA_STRING, defaultKey);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.select_search_contact_fragment;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return 0;
    }


    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }


    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        getDataBinding().vgSearch.setHint(new StringBuilder().append(" ").append(getResources().getString(R.string.search_contact)).toString());
        getViewModel().setActivityViewModel(getActivityViewModel());
        if (getArguments() != null) {
            params = (SelectContactsParams) getArguments().getSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE);
            String defaultKey = getArguments().getString(BundleConstants.BUNDLE_DATA_STRING);
            if (!TextUtils.isEmpty(defaultKey)) {
                EditText etInput = getDataBinding().vgSearch.etInput;
                etInput.setText(defaultKey);
                etInput.setSelection(defaultKey.length());
            }
            if (params != null) {
                selectOnlyOne = params.isMultiSelect && params.getMaxSelectCount() == 1;
                getDataBinding().vgSearch.etInput.setOnClickListener(v -> {
                    PointUtils.BuilderV4 builderV4 = new PointUtils.BuilderV4()
                            .name("search-box-click")
                            .params("source", getViewModel().getSource(params));
                    if (TextUtils.equals(getViewModel().getSource(params), GlobalSearchPointUtilKt.SEARCH_WITH_FILTER)) {
                        HashMap<String, String> map = new HashMap<>();
                        map.put("page", params.getFilterFromSate().getPage());
                        String type = "";
                        if (TextUtils.equals(params.getFilterFromSate().getPage(), "全局搜索")) {
                            type = "发送人";
                        } else {
                            type = "群成员";
                        }
                        map.put("type", type);
                        builderV4.params("icon_type", map);
                    }
                    builderV4.point();
                });
            }
        }
        getDataBinding().vgSearch.ivBack.setVisibility(View.VISIBLE);
        mAdapter.register(ContactHint.class, R.layout.select_item_contact_hint, null);
        mAdapter.register(SelectContactBean.class, R.layout.select_item_user_select,
                new MyMultiTypeAdapter.ItemViewBinder<SelectItemUserSelectBinding, SelectContactBean>() {
                    @Override
                    public void bind(SelectItemUserSelectBinding vdb, SelectContactBean item, int linkIndex) {
                        vdb.setCallback(SearchContactFragment.this);
                        vdb.setUser(item);
                        String searchKey = getDataBinding().vgSearch.etInput.getText().toString();
                        vdb.setSearchKey(searchKey);

                        String showName = item.getShowName();
                        Contact contact = item.getContact();
                        if (contact != null) {
                            if (TextUtils.isEmpty(showName)) {
                                showName = contact.getShowName();
                                SpannableStringBuilder style = new SpannableStringBuilder(showName);
                                String deptNames = contact.getShowDeptNames();
                                if (!TextUtils.isEmpty(deptNames)) {
                                    style.append(" | ");
                                    style.append(deptNames);
                                    style.setSpan(new ForegroundColorSpan(activity.getResources().getColor(com.twl.hi.basic.R.color.color_9B9B9B)), showName.length(), style.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                                }
                                vdb.tvUsername.setText(style);

                                if (!TextUtils.isEmpty(searchKey)) {
                                    // 需要做本地搜索高亮处理
                                    ChatRecordSearchTextView searchTextView = (ChatRecordSearchTextView) vdb.tvUsername;
                                    searchTextView.setAllContentText(style.toString(), searchKey);
                                }
                            } else {
                                SpannableStringBuilder style = new SpannableStringBuilder(showName);
                                String deptNames = contact.getShowDeptNames();
                                if (!TextUtils.isEmpty(deptNames)) {
                                    style.append(" | ");
                                    style.append(deptNames);
                                    style.setSpan(new ForegroundColorSpan(activity.getResources().getColor(com.twl.hi.basic.R.color.color_9B9B9B)), showName.length(), style.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                                }
                                vdb.tvUsername.setServerContent(style.toString());
                            }
                        }

                        vdb.setShowCheckBox(params.isMultiSelect);
                        vdb.setSelectOnlyOne(selectOnlyOne);
                        Context context = vdb.getRoot().getContext();
                        vdb.getRoot().setPadding(QMUIDisplayHelper.dp2px(context, 20), 0
                                , QMUIDisplayHelper.dp2px(context, 28), 0);
                        if (item.getAdminRole() == Constants.TYPE_GROUP_NORMAL) {
                            vdb.tvLabel.setVisibility(View.GONE);
                        } else {
                            vdb.tvLabel.setVisibility(View.VISIBLE);
                            if (item.getAdminRole() == Constants.TYPE_GROUP_CREATE) {
                                vdb.tvLabel.setText(R.string.select_group_creator);
                                vdb.tvLabel.setDefaultBgColor(Color.parseColor("#FD8C43"));
                            } else {
                                vdb.tvLabel.setText(R.string.select_group_admin);
                                vdb.tvLabel.setDefaultBgColor(Color.parseColor("#6176E7"));
                            }
                        }
                    }
                });
        getDataBinding().recyclerView.setAdapter(mAdapter);

        getViewModel().searchLiveData().observe(this, new Observer<List<Object>>() {
            @Override
            public void onChanged(List<Object> list) {
                if (list == null) {
                    return;
                }
                getDataBinding().recyclerView.setVisibility(list.size() > 0 ? View.VISIBLE : View.GONE);
                getDataBinding().vgEmpty.getRoot().setVisibility(list.size() > 0 ? View.GONE : View.VISIBLE);
                mAdapter.setData(list);
                mAdapter.notifyDataSetChanged();
            }

        });
        if (params != null && params.isMultiSelect) {
            getActivityViewModel().getNotifyDeleteSelectedLiveData().observe(this, new Observer<SelectNotifyBean>() {
                @Override
                public void onChanged(SelectNotifyBean selectNotifyBean) {
                    getViewModel().refreshData(selectNotifyBean);
                }
            });
            getActivityViewModel().getNotifyCleanOtherLiveData().observe(this, new Observer<SelectNotifyBean>() {
                @Override
                public void onChanged(SelectNotifyBean selectNotifyBean) {
                    getViewModel().refreshData(selectNotifyBean);
                }
            });
        }
        QMUIKeyboardHelper.showKeyboardDelay(activity);
        mSearchPointUtil.setExposeListener(getDataBinding().recyclerView, mAdapter);
    }

    @Override
    public void back() {
        QMUIKeyboardHelper.hideKeyboard(getDataBinding().vgSearch.etInput);
        ((FragmentActivity) activity).getSupportFragmentManager().popBackStack();
        if (mSelectedCount > 0) {
            // 只有当前页面被选中的会话数量大于1，才有必要去更新searchId，用于后续的埋点
            getActivityViewModel().updateSearchId(getViewModel().getCurrentSearchId());
        }
    }

    @Override
    public void afterTextChanged(Editable s) {
        getViewModel().search(s.toString(), params);
        mSearchPointUtil.updateSearchId(getViewModel().getCurrentSearchId());
    }

    @Override
    public void cancel() {
        // 这里需要主动关闭曝光，避免触发onGlobalLayout，曝光了不必要的item
        mSearchPointUtil.setExposeEnable(false);
        QMUIKeyboardHelper.hideKeyboard(getDataBinding().vgSearch.etInput);
        activity.finish();
        if (mSelectedCount > 0) {
            // 只有当前页面被选中的会话数量大于1，才有必要去更新searchId，用于后续的埋点
            getActivityViewModel().updateSearchId(getViewModel().getCurrentSearchId());
        }
    }

    @Override
    public void onSelectContactItemClick(SelectContactBean item) {
        if (params.isMultiSelect) {
            if (item.isAlreadySelected) {
                return;
            }
            boolean oldSelect = item.isSelected();
            if (oldSelect) {
                mSelectedCount--;
            } else {
                mSelectedCount++;
            }
            int maxSelectCount = params.getMaxSelectCount();
            if (maxSelectCount > 0) {
                if (maxSelectCount == 1) {
                    ArrayList<String> selects = getActivityViewModel().getSelectedContactsMutableModel().mSelects;
                    if (!LList.isEmpty(selects)) {
                        String firstSelectId = selects.get(0);
                        if (StringUtils.isNotEquals(item.getId(), firstSelectId)) {
                            selects.remove(0);
                            getActivityViewModel().cleanSelectedContact(false, firstSelectId);
                        }

                    }
                } else if (getActivityViewModel().getSelectedContactsMutableModel().mSelects.size() >= maxSelectCount && !oldSelect) {
                    ToastUtils.failure(String.format(getString(R.string.select_max_count), maxSelectCount));
                    return;
                }
            }
            item.setSelected(!oldSelect);
            getActivityViewModel().notifyRefreshMainSelectedContact(!oldSelect, item.getId());
        } else {
            Intent data = new Intent();
            switch (params.getOptionId()) {
                case SelectOption.OPTION_GROUP_TRANSFER:
                    showTransferDialog(item.getContact());
                    break;
                case SelectOption.OPTION_H5_SELECT:
                    ArrayList<String> allIds = new ArrayList<>();
                    String selectedUserId = item.getContact().getUserId();
                    allIds.add(selectedUserId);

                    Contact selectedContact = ServiceManager.getInstance().getContactService().getContactById(selectedUserId);

                    data.putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE, allIds);
                    data.putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE_3, selectedContact);
                    activity.setResult(Activity.RESULT_OK, data);
                    activity.finish();
                    break;
                case SelectOption.OPTION_GROUP_SEARCH_CHAT_RECORD:
                    Bundle bundle = new Bundle();
                    bundle.putString(BundleConstants.BUNDLE_USER_ID, item.getContact().getUserId());
                    bundle.putString(BundleConstants.BUNDLE_CHAT_ID, params.getGroupId());
                    bundle.putInt(BundleConstants.BUNDLE_CHAT_TYPE, MessageConstants.MSG_GROUP_CHAT);
                    AppUtil.startUri(activity, ChatPageRouter.CHAT_RECORD_SEARCH_FOR_MEMBER_ACTIVITY, bundle);
                    break;
                case SelectOption.OPTION_SEND_USER_CARD:
                    //发送用户名片
                    SendMessageContent sendMessageContent = new SendMessageContent(SendMessageContent.TYPE_SHARE_USER_CARD);
                    sendMessageContent.content = item.getContact();
                    sendMessageContent.msgShowContent = SendMessageContent.getCardString(item.getContact());
                    SendMessageParams sendMessageParams = new SendMessageParams();
                    if (params.getSelectBean().getType() == MessageConstants.MSG_SINGLE_CHAT) {
                        Contact contact = ServiceManager.getInstance().getContactService().getContactById(params.getSelectBean().getChatId());
                        if (contact != null) {
                            ConversationSelectBean conversationSelectBean = new ConversationSelectBean();
                            conversationSelectBean.setChatId(contact.getUserId());
                            conversationSelectBean.setType(MessageConstants.MSG_SINGLE_CHAT);
                            conversationSelectBean.setAvatar(contact.getAvatar());
                            conversationSelectBean.setNickName(contact.getNickName());
                            conversationSelectBean.setUserName(contact.getShowName());
                            sendMessageParams.selectedConversationList.add(conversationSelectBean);
                        } else {
                            ToastUtils.failure("用户名片不存在");
                        }
                    } else {
                        sendMessageParams.selectedConversationList.add(params.getSelectBean());
                    }
                    sendMessageParams.sendMessageContent = sendMessageContent;
                    SendMessageConfirmDialog fragment = SendMessageConfirmDialog.newInstance(sendMessageParams);
                    fragment.show(((FragmentActivity) activity).getSupportFragmentManager(), SendMessageConfirmDialog.TAG);
                    break;
                default:
                    QMUIKeyboardHelper.hideKeyboard(getDataBinding().vgSearch.etInput);
                    data.putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE, item.getContact());
                    activity.setResult(Activity.RESULT_OK, data);
                    activity.finish();
            }
        }
    }

    private void showTransferDialog(Contact contact) {
        mTransferDialog = new DialogUtils.Builder(activity)
                .setCancelable(true)
                .setCanceledOnTouchOutside(true)
                .setContent(getResources().getString(R.string.select_group_transfer_desc, getString(R.string.group), contact.getShowName()))
                .setPositive(R.string.sure)
                .setNegative(R.string.cancel)
                .setPositiveListener(v -> {
                    getActivityViewModel().checkGroupStatus(params.getGroupId(), new GroupStatusCheckCallback() {
                        @Override
                        public void onStatusNormal() {
                            mTransferDialog.dismiss();
                            showProgressDialog(getString(R.string.select_transfer_group), false);
                            getActivityViewModel().requestChangeGroupOwner(params.getGroupId(), contact.getUserId()).observe(SearchContactFragment.this, apiResponse -> {
                                dismissProgressDialog();
                                if (apiResponse != null && apiResponse.isSuccess()) {
                                    ToastUtils.success(R.string.select_transfer_success);
                                    AppUtil.getDefaultUriRequest(activity, AppPageRouter.MAIN_TAB_ACTIVITY).setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).start();
                                    activity.finish();
                                }
                            });
                        }

                        @Override
                        public void onStatusAbnormal() {
                            ToastUtils.failure(R.string.tips_not_in_group);
                        }
                    });
                })
                .setNegativeListener(v ->
                        getActivityViewModel().checkGroupStatus(params.getGroupId(), new GroupStatusCheckCallback() {
                            @Override
                            public void onStatusNormal() {
                                mTransferDialog.dismiss();
                            }

                            @Override
                            public void onStatusAbnormal() {
                                AppPageRouterHelper.backToMainTabActivity(activity);
                            }
                        }))
                .build();
        mTransferDialog.show();
    }

    /**
     * 获取被选中的联系人的位置
     *
     * @param uids 被选中的用户ids
     * @return
     */
    public ArrayList<Integer> getSelectedContactPos(ArrayList<String> uids) {
        ArrayList<Integer> selectedItemPos = new ArrayList<>();
        if (mAdapter == null || uids == null || uids.size() == 0) {
            return selectedItemPos;
        }
        ArrayList<String> selectedItems = new ArrayList<>();
        List<Object> datas = mAdapter.getData();
        for (String chatId : uids) {
            int j = 1;
            for (Object o : datas) {
                if (o instanceof SelectContactBean) {
                    SelectContactBean bean = (SelectContactBean) o;
                    if (TextUtils.equals(chatId, bean.getId())) {
                        selectedItemPos.add(j);
                        selectedItems.add(chatId);
                        break;
                    }
                    j++;
                }
            }
        }
        uids.clear();
        uids.addAll(selectedItems);
        return selectedItemPos;
    }
}
