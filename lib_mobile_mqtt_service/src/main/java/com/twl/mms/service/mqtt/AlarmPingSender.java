/*******************************************************************************
 * Copyright (c) 2014 IBM Corp.
 * <p>
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * and Eclipse Distribution License v1.0 which accompany this distribution.
 * <p>
 * The Eclipse Public License is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * and the Eclipse Distribution License is available at
 * http://www.eclipse.org/org/documents/edl-v10.php.
 */
package com.twl.mms.service.mqtt;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Message;
import android.os.PowerManager;
import android.os.PowerManager.WakeLock;
import android.os.SystemClock;

import androidx.annotation.RequiresApi;

import com.techwolf.lib.tlog.TLog;

import com.twl.mms.utils.ThreadManager;
import com.twl.mms.utils.ThreadUtil;

import org.eclipse.paho.client.mqttv3.IMqttActionListener;
import org.eclipse.paho.client.mqttv3.IMqttToken;
import org.eclipse.paho.client.mqttv3.MqttPingSender;
import org.eclipse.paho.client.mqttv3.internal.ClientComms;

import java.lang.ref.SoftReference;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Default ping sender implementation on Android. It is based on AlarmManager.
 * <p>
 * <p>This class implements the {@link MqttPingSender} pinger interface
 * allowing applications to send ping packet to server every keep alive interval.
 * </p>
 *
 * @see MqttPingSender
 */
public class AlarmPingSender implements MqttPingSender {
    private static final int HANDLE_WRITE_TIME_OUT = 101;
    private static final int HANDLE_PING = 102;
    public static final long MIN_WRITE_TIME_OUT = 5 * 1000;
    private static final long MAX_WRITE_TIME_OUT = 30 * 1000;
    // Identifier for Intents, log messages, etc..
    static final String TAG = "AlarmPingSender";

    private static TimeOutChecker gTimeOutChecker = new TimeOutChecker();
    private static SoftReference<AlarmPingSender> gPingSender;

    // TODO: Addlog.
    private ClientComms comms;
    private Context mContext;
    private BroadcastReceiver alarmReceiver;
    private AlarmPingSender that;
    private PendingIntent pendingIntent;
    private volatile boolean hasStarted = false;
    private PingHelper mPingHelper;
    private long mLastDelay;

    public AlarmPingSender(Context context, PingHelper pingHelper) {
        if (context == null) {
            throw new IllegalArgumentException(
                    "Neither nor client can be null.");
        }
        this.mContext = context;
        mPingHelper = pingHelper;
        that = this;
        gPingSender = new SoftReference(this);
    }

    @Override
    public void init(ClientComms comms) {
        this.comms = comms;
        this.alarmReceiver = new AlarmReceiver();
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void start() {
        try {
            String action = TAG
                    + comms.getClient().getClientId();
            mContext.registerReceiver(alarmReceiver, new IntentFilter(action));
            TLog.info(TAG, "Register alarmreceiver to MqttService action = [%s]", action);
            pendingIntent = PendingIntent.getBroadcast(mContext, 0, new Intent(
                    action), PendingIntent.FLAG_UPDATE_CURRENT| PendingIntent.FLAG_IMMUTABLE);
            mPingHelper.onConnect();
            long keepAlive = comms.getKeepAlive();
            schedule(keepAlive);
            hasStarted = true;
        } catch (Exception e) {
        }
    }

    @Override
    public void stop() {
        // Cancel Alarm.
        isCanPing.set(false);
        gTimeOutChecker.removeMessages(HANDLE_PING);
        AlarmManager alarmManager = (AlarmManager) mContext
                .getSystemService(Service.ALARM_SERVICE);

        if (alarmManager != null && pendingIntent != null) {
            try {
                alarmManager.cancel(pendingIntent);
            } catch (Exception e) {
            }
        }
        TLog.info(TAG, "Unregister alarmreceiver to MqttService %s:%b", comms.getClient().getClientId(), hasStarted);
        if (hasStarted) {
            hasStarted = false;
            try {
                mContext.unregisterReceiver(alarmReceiver);
            } catch (Exception e) {
                //Ignore unregister errors.
            }
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void schedule(long delayInMilliseconds) {
        long delay = mPingHelper.getDelayInMilliseconds(delayInMilliseconds);
        long nextAlarmInMilliseconds = SystemClock.elapsedRealtime()
                + delay;
        AlarmManager alarmManager = (AlarmManager) mContext
                .getSystemService(Service.ALARM_SERVICE);
        TLog.info(TAG, "Schedule next alarm at %d, delay = [%d], delayInMilliseconds = [%d]", nextAlarmInMilliseconds, delay, delayInMilliseconds);
        int type = mPingHelper.getRTCType();
        int sdk = Build.VERSION.SDK_INT;
        if (sdk >= Build.VERSION_CODES.KITKAT) {
            alarmManager.setExact(type, nextAlarmInMilliseconds,
                    pendingIntent);
        } else {
            alarmManager.set(type, nextAlarmInMilliseconds,
                    pendingIntent);
        }
        mLastDelay = delay;
        if (delay < PingHelper.DEFAULT_PING_TIME) {
            gTimeOutChecker.sendEmptyMessageDelayed(HANDLE_PING, delay + 10000);
        } else if (delay == PingHelper.DEFAULT_PING_TIME){
            gTimeOutChecker.sendEmptyMessageDelayed(HANDLE_PING, delay + 1000 * 30);
        }
        isCanPing.set(true);
    }

    /*
     * This class sends PingReq packet to MQTT broker
     */
    class AlarmReceiver extends BroadcastReceiver {
        private static final String TAG = "AlarmReceiver";

        @Override
        public void onReceive(Context context, Intent intent) {
            // According to the docs, "Alarm Manager holds a CPU wake lock as
            // long as the alarm receiver's onReceive() method is executing.
            // This guarantees that the phone will not sleep until you have
            // finished handling the broadcast.", but this class still get
            // a wake lock to wait for ping finished.
            ThreadUtil.sExecutor.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        int count;
                        try {
                            count = intent.getIntExtra(Intent.EXTRA_ALARM_COUNT, -1);
                        } catch (ClassCastException ex) {
                            // This is a Motorola Phone (Probably a Moto G or X)
                            // And so Intent.EXTRA_ALARM_COUNT is actually a Long!
                            Long longCount = intent.getLongExtra(Intent.EXTRA_ALARM_COUNT, -1);
                            count = longCount.intValue();
                        }
                        TLog.info(TAG, "Ping %d times.", count);
                        ping();
                    } catch (Exception e) {
                        TLog.error(TAG, e.getMessage());
                    }
                }
            });
        }
    }

    private WakeLock wakelock;
    private String wakeLockTag;
    private AtomicBoolean isCanPing = new AtomicBoolean(false);

    private void ping(){
        try {
            if (isCanPing.compareAndSet(true, false)) {
                gTimeOutChecker.removeMessages(HANDLE_PING);
                if (wakeLockTag == null) {
                    wakeLockTag = TAG
                            + that.comms.getClient().getClientId();
                }
                internalPing();
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private void internalPing(){
        PowerManager pm = (PowerManager) mContext
                .getSystemService(Service.POWER_SERVICE);
        wakelock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, wakeLockTag);
        wakelock.acquire();
        mPingHelper.onPingStart();
        // Assign new callback to token to execute code after PingResq
        // arrives. Get another wakelock even receiver already has one,
        // release it until ping response returns.
        IMqttToken token = comms.checkForActivity(new IMqttActionListener() {
            @Override
            public void onSuccess(IMqttToken asyncActionToken) {
                //Release wakelock when it is done.
                TLog.info(TAG, "Success. Release lock(%s):%d", wakeLockTag, SystemClock.elapsedRealtime());
                mPingHelper.onPingResult(true);
                wakelock.release();

            }

            @Override
            public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                //Release wakelock when it is done.
                TLog.info(TAG, "Failure. Release lock(%s):%d:s", wakeLockTag, SystemClock.elapsedRealtime(), exception.getMessage());
                mPingHelper.onPingResult(false);
                wakelock.release();
            }
        }, mLastDelay);

        if (token == null && wakelock.isHeld()) {
            TLog.info(TAG, "Failure. Release lock(%s) isHeld(%b):%d", wakeLockTag, wakelock.isHeld(), SystemClock.elapsedRealtime());
            wakelock.release();
        }
    }

    /**
     * 添加发送超时检查
     */
    public static void addTimeOutCheck(boolean isMaxType){
        Message message = gTimeOutChecker.obtainMessage(HANDLE_WRITE_TIME_OUT,  System.currentTimeMillis());
        gTimeOutChecker.sendMessageDelayed(message, isMaxType ? MAX_WRITE_TIME_OUT : MIN_WRITE_TIME_OUT);
    }

    /**
     * 删除发送超时检查
     */
    public static void removeTimeOutCheck(){
        gTimeOutChecker.removeMessages(HANDLE_WRITE_TIME_OUT);
    }

    /**
     * 执行ping
     */
    public static void executePing(){
        AlarmPingSender alarmPingSender = gPingSender != null ? gPingSender.get() : null;
        if (alarmPingSender != null) {
            alarmPingSender.ping();
        }
    }

    private static class TimeOutChecker extends ThreadManager.MMSHandler{
        public TimeOutChecker(){
            super(ThreadManager.getSubThreadLooper());
        }

        @Override
        public void handleMessage(Message msg) {
            AlarmPingSender alarmPingSender = gPingSender != null ? gPingSender.get() : null;
            switch (msg.what) {
                case HANDLE_WRITE_TIME_OUT:
                    TLog.info(TAG, "TimeOutChecker.handleMessage message = [%s]", msg);
                    if (alarmPingSender != null && alarmPingSender.comms != null) {
                        ClientComms comms = alarmPingSender.comms;
                        if (comms != null) {
                            long time = (long) msg.obj;
                            TLog.info(TAG, "handleMessage() called with: msg = [%d]", time);
                            comms.onlyCheckForActivity(time);
                        }
                    }
                    break;
                case HANDLE_PING:
                    if (alarmPingSender != null) {
                        TLog.info(TAG, "HANDLE_PING");
                        alarmPingSender.ping();
                    }
                    break;
                default:
                    break;
            }
        }
    }

}
