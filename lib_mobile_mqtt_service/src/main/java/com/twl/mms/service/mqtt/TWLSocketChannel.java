package com.twl.mms.service.mqtt;

import android.os.SystemClock;
import android.util.Log;

import com.techwolf.lib.tlog.TLog;
import com.twl.mms.service.AppStatus;

import com.twl.mms.utils.MqttUtil;
import com.twl.net.NetUtils;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.nio.channels.SocketChannel;

/**
 * Created by y<PERSON><PERSON>fei on 2017/4/26.
 */

class TWLSocketChannel {
    private static final String TAG = "TWLSocketChannel";
    private static final long DEFAULT_TIMEOUT = 10 * 1000;
    private SocketChannel mSocketChannel;
    private long mStartTime;
    private long mTimeout;
    private SocketAddress mSocketAddress;

    public TWLSocketChannel() {
        this(DEFAULT_TIMEOUT);
    }

    public TWLSocketChannel(long timeout) {
        mTimeout = timeout;
    }

    public boolean connect(SocketAddress endpoint) throws IOException {
        boolean ret = false;
        mSocketChannel = SocketChannel.open();
        mSocketChannel.configureBlocking(false);
        pretreatment(mSocketChannel);
        mStartTime = SystemClock.elapsedRealtime();
        try {
            mSocketAddress = endpoint;
            ret = mSocketChannel.connect(endpoint);
        } catch (IOException e) {
            e.printStackTrace();
            checkException(e);
        }
        return ret;
    }

    public boolean finishConnect() throws IOException {
        if (!isCanClose) {
            return false;
        }
        boolean ret = false;
        try {
            ret = mSocketChannel.finishConnect();
            if (!ret && SystemClock.elapsedRealtime() - mStartTime > mTimeout) {
                close();
            }
        } catch (IOException e) {
            close();
            checkException(e);
        }
        return ret;
    }

    public SocketChannel getSocketChannel() {
        return mSocketChannel;
    }

    private volatile boolean isCanClose = true;

    public void close() {
        try {
            if (isCanClose) {
                Log.d(TAG, "close() called");
                isCanClose = false;
                SocketChannel socketChannel = mSocketChannel;
                if (socketChannel != null) {
                    socketChannel.close();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 检查异常类型，如果是服务端异常，并且是在前台状态，删除该IP
     * @param e
     * @throws IOException
     */
    private void checkException(IOException e) throws IOException {
        if (MqttUtil.isServerException(e)){
            SocketAddress socketAddress = mSocketAddress;
            if (socketAddress != null && socketAddress instanceof InetSocketAddress && AppStatus.isForeground()) {
                InetSocketAddress inetSocketAddress = (InetSocketAddress) socketAddress;
                if (IPManager.getInstance().removeIP(inetSocketAddress)) {
//                    ExceptionUtils.postCatchedException(new TWLException(MMS_SERVER_NET_ERROR, e));
                }
            }
            isCanClose = false;
        } else {
            throw e;
        }
    }

    /**
     * 设置socket属性
     * @param socketChannel
     */
    private static void pretreatment(SocketChannel socketChannel) {
        try {
            socketChannel.socket().setTcpNoDelay(true);
            if (!AppStatus.gIsMobileNet) {
                if (!NetUtils.fixTcpMss(socketChannel)) {//fix mss
                    TLog.info(TAG, "fixTcpMss error");
                }
            }
        } catch (Throwable e) {
            TLog.error(TAG, e, "pretreatment");
        }
    }
}
