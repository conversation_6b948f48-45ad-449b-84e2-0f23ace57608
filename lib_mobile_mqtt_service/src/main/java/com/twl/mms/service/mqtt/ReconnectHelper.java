package com.twl.mms.service.mqtt;

import com.techwolf.lib.tlog.TLog;
import com.twl.mms.service.AppStatus;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/3/14.
 * 重连逻辑类
 */

public class ReconnectHelper {
    private static final String TAG = "ReconnectHelper";
    private static final int CONNECT_RETRY_INITIAL = 0;
    private static final int CONNECT_RETRY_LIMIT = 11;//最大连续重试次数
    private static final int CONNECT_TIMEOUT_LIMIT = 2;//超时情况下最大连续重试次数

    private static final int CONNECT_MIN_RETRY_TIME = 4 * 1000;//最小重试时间，单位毫秒
    private static final int CONNECT_BACKGROUND_MIN_RETRY_TIME = 10 * 1000;//后台情况最小重试时间
    private static final int CONNECT_MAX_RETRY_TIME = 1000 * 60 * 20;//最大重试时间，单位毫秒
    private static final int DIVISOR = 3;
    private static final int MAX_OFFSET = 5;

    private int mReconnectCount = CONNECT_RETRY_INITIAL;

    /**
     * 连接成功
     */
    public void onConnected() {
        mReconnectCount = CONNECT_RETRY_INITIAL;
    }

    /**
     * 重连
     */
    public void onReconnect(){
        mReconnectCount++;
    }

    /**
     * 客户端超时
     */
    public void onTimeout(){
        if (mReconnectCount >= CONNECT_TIMEOUT_LIMIT) {
            mReconnectCount = CONNECT_RETRY_LIMIT + 1;
        }
    }

    public int getReconnectCount() {
        return mReconnectCount;
    }

    /**
     * 主动重置等待时间
     */
    public void reset(){
        mReconnectCount = CONNECT_RETRY_INITIAL;
    }

    /**
     * 返回下一次重试时间
     * 1.如果大于最大重试次数，则返回最大重试时间
     * 2.如果是前台状态，返回4s,8s,8s,16s,16s,32s,32s,64s,64s
     * 3.如果是后台状态，返回10s,20s,20s,40s,40s,80s,80s,160s,160s
     *
     * @return
     */
    public long getDelayedTime() {
        long time;
        if (mReconnectCount > CONNECT_RETRY_LIMIT) {
            time = CONNECT_MAX_RETRY_TIME;
        } else {
            int offset = (mReconnectCount /DIVISOR);
            if (offset > MAX_OFFSET) {
                offset = MAX_OFFSET;
            }
            int base = 1 << offset;
            time = base * (AppStatus.isActive() ? CONNECT_MIN_RETRY_TIME : CONNECT_BACKGROUND_MIN_RETRY_TIME);
        }
        TLog.info(TAG, "delay time = [%d]", time);
        return time;
    }
}
