package com.twl.mms.utils;

import java.lang.reflect.Field;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/6/9.
 */

public class StringCacheHelper {

    private static final int CHAR_CACHE_SIZE = 512;

    private static boolean sIsSupport = true;
    private static Field sValueField;

    private static final ThreadLocal<char[]> sMemBuffer = new ThreadLocal<>();
    private static final ThreadLocal<Boolean> sIsOpen = new ThreadLocal<>();

    public static void openCache() {
        sIsOpen.set(true);
    }

    private static boolean isOpen() {
        Boolean bool = sIsOpen.get();
        if (bool == null) {
            return false;
        }
        return bool.booleanValue();
    }

    public static StringBuilder obtainStringBuilder() {
        return obtainStringBuilder(CHAR_CACHE_SIZE);
    }

    public static StringBuilder obtainStringBuilder(int size) {
        boolean isUseCache = sIsSupport && isOpen();
        StringBuilder stringBuilder = isUseCache ? new StringBuilder(0) : new StringBuilder();
        if (isUseCache) {
            setCache(stringBuilder, size);
        }
        return stringBuilder;
    }

    private static void setCache(StringBuilder sb, int size){
        try {
            if (sValueField == null) {
                try {
                    sValueField = StringBuilder.class.getSuperclass().getDeclaredField("value");
                    sValueField.setAccessible(true);
                } catch (NoSuchFieldException e) {
                    e.printStackTrace();
                    sIsSupport = false;
                }
            }
            if (sIsSupport) {
                char[] data = sMemBuffer.get();
                if (data == null) {
                    data = new char[size];
                    sMemBuffer.set(data);
                }
                sValueField.set(sb, data);
            }
        } catch (Throwable e) {
            e.printStackTrace();
            sIsSupport = false;
        }
    }

}
