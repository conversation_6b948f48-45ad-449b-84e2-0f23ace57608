package com.twl.mms.utils;

import android.os.MemoryFile;

import java.io.FileDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/25.
 */

public class MemoryFileUtil {
    private static final Method sMethodGetFileDescriptor;

    static {
        sMethodGetFileDescriptor = get("getFileDescriptor");
    }

    public static boolean isEffective(){
        return sMethodGetFileDescriptor != null;
    }

    public static FileDescriptor getFileDescriptor(MemoryFile file) {
        try {
            return (FileDescriptor) sMethodGetFileDescriptor.invoke(file);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }

    private static Method get(String name) {
        Method method = null;
        try {
            method =  MemoryFile.class.getDeclaredMethod(name);
            method.setAccessible(true);
        } catch (NoSuchMethodException e) {

        }
        return method;
    }
}
