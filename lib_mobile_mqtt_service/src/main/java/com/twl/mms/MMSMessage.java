package com.twl.mms;

import android.os.MemoryFile;
import android.os.Parcel;
import android.os.ParcelFileDescriptor;
import android.os.Parcelable;

import com.twl.mms.client.ISendCallback;
import com.twl.mms.utils.MemoryFileUtil;

import java.io.FileDescriptor;
import java.io.IOException;
import java.lang.ref.WeakReference;

/**
 * Created by yuch<PERSON><PERSON><PERSON> on 16/11/1.
 * aidl Client 与 Server 通信协议；比Intent效率更好，传递数据更小；不要往Bundle添加Serializable类型的数据,序列化反序列化效率更低。
 */

public final class MMSMessage implements Parcelable {
    private static final int DATA_TYPE_ASHMEM_SIZE = 1024 * 20;//200 * 1024;
    public static final byte DATA_TYPE_MEM = 0;//使用Binder内存
    public static final byte DATA_TYPE_ASHMEM = 1;//使用匿名共享内存

    private static final byte SEND_MAX_TRY_CONNECT = 3;//发送中重试次数
    /**
     * 是否能使用匿名共享内存
     */
    private static boolean sCanUseASM = true;//如果有异常不适用匿名共享内存

    /**
     * 类型，默认使用Binder内存
     */
    public byte state = DATA_TYPE_MEM;

    /**
     * 消息ID
     */
    private short id;

    /**
     * 消息数据
     */
    private byte[] data;

    /**
     * 重试次数
     */
    private byte mTryCount = SEND_MAX_TRY_CONNECT;

    /**
     * 额外数据有可能存储ISendCallback 或者 WeakReference，不参与序列化
     */
    private Object mExtra = null;

    public MMSMessage() {
    }

    public MMSMessage(short id) {
        this(id, null, null);
    }

    public MMSMessage(short id, byte[] data) {
       this(id, data, null);
    }

    public MMSMessage(short id, byte[] data, ISendCallback callback) {
        this.id = id;
        this.mExtra = callback;
        setData(data);
    }

    public MMSMessage(Parcel in) {
        id = (short) in.readInt();
        state = in.readByte();
        if (state == DATA_TYPE_MEM) {
            data = in.createByteArray();
        } else {
            readFromAshm(in);
        }
        mTryCount = in.readByte();
    }

    private void setState(byte state) {
        this.state = state;
    }

    public void setData(byte[] data) {
        this.data = data;
        changeState();
    }

    public byte[] getData() {
        return data;
    }

    public void setId(short id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public ISendCallback getSendCallback() {
        ISendCallback sendCallback = null;
        if (mExtra instanceof ISendCallback) {
            sendCallback = (ISendCallback) mExtra;
        }
        return sendCallback;
    }

    public static final Creator<MMSMessage> CREATOR = new Creator<MMSMessage>() {
        @Override
        public MMSMessage createFromParcel(Parcel in) {
            return new MMSMessage(in);
        }

        @Override
        public MMSMessage[] newArray(int size) {
            return new MMSMessage[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        if (state == DATA_TYPE_MEM) {
            dest.writeByte(state);
            dest.writeByteArray(data);
        } else {
            writeToAshm(dest);
        }
        dest.writeByte(mTryCount);
    }

    private void changeState() {
        if (data.length > DATA_TYPE_ASHMEM_SIZE && MemoryFileUtil.isEffective() && sCanUseASM) {
            setState(DATA_TYPE_ASHMEM);
        }
    }

    /**
     * 写数据到匿名共享内存
     *
     * @param parcel
     */
    private void writeToAshm(Parcel parcel) {
        MemoryFile memoryFile = null;
        FileDescriptor fileDescriptor = null;
        boolean isWriteData = false;
        try {
            memoryFile = new MemoryFile(String.valueOf(id), data.length);
            memoryFile.writeBytes(data, 0, 0, data.length);
            fileDescriptor = MemoryFileUtil.getFileDescriptor(memoryFile);
            if (fileDescriptor.valid()) {
                isWriteData = true;
                parcel.writeByte(state);
                parcel.writeInt(data.length);
                parcel.writeFileDescriptor(fileDescriptor);
                if (mExtra == null)
                    mExtra = new WeakReference(memoryFile);
            } else {//如果MemoryFile文件验证失败，走binder内存
                writeToMemory(parcel);
                if (memoryFile != null) {
                    try {
                        memoryFile.close();
                        fileDescriptor = null;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Throwable e) {
            /**处理异常逻辑**/
            StringBuilder stringBuilder = new StringBuilder("writeToAshm error:valid = [");
            if (fileDescriptor != null) {
                stringBuilder.append(fileDescriptor.valid());
            } else {
                stringBuilder.append("null");
            }
            stringBuilder.append("], iswriteData = [").append(isWriteData).append("], datalen = [");
            if (data != null) {
                stringBuilder.append(data.length).append("]");
            } else {
                stringBuilder.append("null]");
            }
            sCanUseASM = false;
            if (!isWriteData) {//失败且没有写数据使用内存传递；基本上不会走这
                writeToMemory(parcel);
            }
        }
    }

    /**
     * 从匿名共享内存读取数据
     *
     * @param parcel
     */
    private void readFromAshm(Parcel parcel) {
        ParcelFileDescriptor.AutoCloseInputStream in = null;
        ParcelFileDescriptor fileDescriptor = null;
        try {
            int len = parcel.readInt();
            fileDescriptor = parcel.readFileDescriptor();
            in = new ParcelFileDescriptor.AutoCloseInputStream(fileDescriptor);

            byte[] data = new byte[len];
            in.read(data);
            this.data = data;
        } catch (Throwable e) {
            sCanUseASM = false;
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fileDescriptor != null) {
                try {
                    fileDescriptor.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 释放匿名共享内存
     */
    private void releaseMemoryFile() {
        if (mExtra != null && mExtra instanceof WeakReference) {
            WeakReference<MemoryFile> memoryFileWeakReference = (WeakReference<MemoryFile>) mExtra;
            MemoryFile memoryFile = memoryFileWeakReference.get();
            if (memoryFile != null) {
                try {
                    memoryFile.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 设置为Binder内存数据
     *
     * @param parcel
     */
    private void writeToMemory(Parcel parcel) {
        setState(DATA_TYPE_MEM);
        parcel.writeByte(state);
        parcel.writeByteArray(data);
    }

    public int getTryCount() {
        return mTryCount;
    }

    public void setTryCount(byte tryCount) {
        mTryCount = tryCount;
    }

    /**
     * 释放消息中的匿名共享内存
     *
     * @param mmsMessage
     */
    public static void relaseMessage(MMSMessage mmsMessage) {
        if (mmsMessage != null) {
            mmsMessage.releaseMemoryFile();
        }
    }

    /**
     * 设置保存的状态
     *
     * @param mmsMessage
     */
    public static void storeState(MMSMessage mmsMessage) {
        if (mmsMessage != null) {
            mmsMessage.setState(DATA_TYPE_MEM);
        }
    }

    /**
     * 恢复状态
     *
     * @param mmsMessage
     */
    public static void restoreState(MMSMessage mmsMessage) {
        if (mmsMessage != null) {
            mmsMessage.changeState();
        }
    }
}
