package com.twl.mms.common;

/**
 * Created by y<PERSON><PERSON><PERSON><PERSON> on 2017/3/9.
 */

public class MqttOptions {
    private String mTopic = "chat";
    private int mConnectionTimeout = 30;
    private int mKeepAliveInterval = 300;
    private boolean mCleanSession = true;
    private int mQos = 1;

    public MqttOptions(){}

    public MqttOptions(String topic, int connectionTimeout, int keepAliveInterval, boolean cleanSession, int qos) {
        mTopic = topic;
        mConnectionTimeout = connectionTimeout;
        mKeepAliveInterval = keepAliveInterval;
        mCleanSession = cleanSession;
        mQos = qos;
    }

    public String getTopic() {
        return mTopic;
    }

    public void setTopic(String topic) {
        mTopic = topic;
    }

    public int getConnectionTimeout() {
        return mConnectionTimeout;
    }

    public void setConnectionTimeout(int connectionTimeout) {
        mConnectionTimeout = connectionTimeout;
    }

    public int getKeepAliveInterval() {
        return mKeepAliveInterval;
    }

    public void setKeepAliveInterval(int keepAliveInterval) {
        mKeepAliveInterval = keepAliveInterval;
    }

    public boolean isCleanSession() {
        return mCleanSession;
    }

    public void setCleanSession(boolean cleanSession) {
        mCleanSession = cleanSession;
    }

    public int getQos() {
        return mQos;
    }

    public void setQos(int qos) {
        mQos = qos;
    }

    public int getDefaultKeepAliveInterval() {
        return 180;
    }
}
