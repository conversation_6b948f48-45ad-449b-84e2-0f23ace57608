<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
                name="viewModel"
                type="com.twl.hi.module_email.view_edit.view.base.EmailBaseDetailsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="168dp"
            android:paddingTop="10dp"
            android:paddingBottom="13dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            >

        <ImageView
                android:id="@+id/icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/email_ic_list_attach"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
            />
        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_9999A3"
                app:layout_constraintStart_toEndOf="@id/icon"
                app:layout_constraintTop_toTopOf="parent"
                android:textSize="12sp"
                app:attachTextInfo="@{viewModel.mailLiveData}"
                tools:text="3个附件"/>

       <com.twl.hi.basic.views.MaxHeightLayout
               android:id="@+id/max_height"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               app:max_height="130dp"
               android:layout_marginTop="8dp"
               app:layout_constraintTop_toBottomOf="@id/icon"
               app:layout_constraintStart_toStartOf="parent"
               app:layout_constraintEnd_toEndOf="parent"
               app:layout_constraintBottom_toBottomOf="parent">

           <androidx.recyclerview.widget.RecyclerView
                   android:id="@+id/file_list"
                   android:layout_width="match_parent"
                   android:layout_height="wrap_content"
                   tools:listitem="@layout/email_item_attach_file"
                   tools:itemCount="4"
                   android:background="@drawable/email_bg_attach_file_list"
                   />
       </com.twl.hi.basic.views.MaxHeightLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>