<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View"/>
        <import type="android.text.TextUtils"/>
        <variable
                name="callback"
                type="com.twl.hi.module_email.callback.IEmailAccountSelectorCallback" />
        <variable
                name="viewModel"
                type="com.twl.hi.module_email.main.EmailMainViewModel" />
        <variable
                name="item"
                type="com.twl.hi.module_email.domain.AccountInfoBean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:onClick="@{(v) -> callback.onSwitchMailAccount(item)}"
            >

        <TextView
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@{item.currentShowAccount ? @color/color_5358DE : @color/color_0D0D1A}"
                android:text="@{item.mailAddress}"
                tools:text="哈哈哈"
                />


        <ImageView
                android:id="@+id/default_item"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:src="@drawable/email_ic_check_folder"
                android:visibility="@{item.currentShowAccount ? View.VISIBLE : View.GONE}"
                tools:visibility="gone"
                />

        <TextView
                android:id="@+id/unread_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:text="@{viewModel.getRealShowUnReadNumber(item.showNumber)}"
                android:paddingStart="2dp"
                android:paddingEnd="2dp"
                tools:text="25"
                tools:visibility="visible"
                tools:background="@drawable/email_ic_unread_mail_number"
                android:visibility="@{(item.showNumber > 0 &amp;&amp; !item.currentShowAccount) ? View.VISIBLE : View.GONE}"
                android:textSize="12sp"
                android:includeFontPadding="false"
                android:textColor="@color/app_white"
                android:gravity="center"
                android:background="@{item.pushSwitch ? @drawable/email_ic_unread_mail_number : @drawable/email_ic_unread_mail_number_close_push}"
                />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
