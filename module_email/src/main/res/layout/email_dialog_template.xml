<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="callback"
            type="com.twl.hi.module_email.view_edit.edit.template.EmailTemplateCallback" />

    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/email_bg_dialog_round_16dp"
        android:paddingStart="20dp"
        android:paddingTop="18dp"
        android:paddingEnd="20dp"
        android:paddingBottom="18dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/email_edit_template"
            android:textColor="@color/color_15181D"
            android:textSize="17sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/email_dialog_close"
            android:onClick="@{()->callback.close()}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_search"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginTop="14dp"
            android:background="@drawable/shape_solid_fff_corner_8dp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <ImageView
                android:id="@+id/iv_search"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/ic_icon_message_search"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.twl.hi.basic.views.ClearEditText
                android:id="@+id/et_input"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@null"
                android:hint="搜索模板名称"
                android:imeOptions="actionSearch"
                android:paddingStart="4dp"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:singleLine="true"
                android:textColor="@color/color_15181D"
                android:textSize="14sp"
                app:allowBlank="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_search"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_template_summary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="16dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/email_item_template_summary"
            android:background="@drawable/shape_solid_fff_corner_8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_search" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_empty"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="16dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/shape_solid_fff_corner_8dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_search">

            <androidx.constraintlayout.widget.Group
                android:id="@+id/group_empty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="iv_empty,tv_empty" />

            <ImageView
                android:id="@+id/iv_empty"
                android:layout_width="240dp"
                android:layout_height="178dp"
                android:src="@drawable/email_ic_template_empty"
                app:layout_constraintBottom_toTopOf="@id/tv_empty"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/tv_empty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:drawableStart="@drawable/email_ic_icon_info"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                android:text="@string/email_edit_template_empty_desc"
                android:textColor="@color/email_color_868F9E"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_empty" />

            <TextView
                android:id="@+id/tv_result_empty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/email_ic_icon_info"
                android:drawablePadding="4dp"
                android:text="@string/email_template_search_empty"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>