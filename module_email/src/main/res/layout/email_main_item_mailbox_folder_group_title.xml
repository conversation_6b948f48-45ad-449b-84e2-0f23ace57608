<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="customFolder"
            type="com.twl.hi.module_email.domain.MailFolderGroupTitle" />

        <variable
            name="viewModel"
            type="com.twl.hi.module_email.main.EmailMainViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@drawable/email_bg_main_mailbox_selection_item"
        android:orientation="vertical"
        android:paddingStart="20dp"
        android:paddingEnd="20dp">

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:text="@{customFolder.title}"
            android:textColor="@color/color_15181D"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="文件夹" />

    </LinearLayout>
</layout>
