<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="85dp"
    android:height="90dp"
    android:viewportWidth="85"
    android:viewportHeight="90">
  <path
      android:pathData="M0,33.5L84.5,33.5L84.5,81.5C84.5,85.918 80.918,89.5 76.5,89.5L4,89.5C1.791,89.5 0,87.709 0,85.5L0,33.5L0,33.5Z"
      android:strokeWidth="1"
      android:fillColor="#CECEDA"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M84.5,33.716L84.5,85.785C84.5,87.994 82.709,89.785 80.5,89.785L27.634,89.785C23.216,89.785 19.634,86.203 19.634,81.785C19.634,79.127 20.954,76.643 23.156,75.156L84.5,33.716L84.5,33.716Z"
      android:strokeWidth="1"
      android:fillColor="#D6D6E0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M0,33.716L78.711,89.785L4,89.785C1.791,89.785 0,87.994 0,85.785L0,33.716L0,33.716Z"
      android:strokeWidth="1"
      android:fillColor="#E8E8ED"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M84.5,53.987L5.789,-2.082L80.5,-2.082C82.709,-2.082 84.5,-0.291 84.5,1.918L84.5,53.987L84.5,53.987Z"/>
    <path
        android:pathData="M89.863,29.187L47.495,55.285C44.279,57.266 40.221,57.266 37.005,55.285L-5.363,29.187L-5.363,29.187L89.863,29.187Z"
        android:strokeWidth="1"
        android:fillColor="#E8E8ED"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M0,33.716L78.711,89.785L4,89.785C1.791,89.785 0,87.994 0,85.785L0,33.716L0,33.716Z"/>
  </group>
  <path
      android:pathData="M0,33.5L38.522,2.956C40.706,1.224 43.794,1.224 45.978,2.956L84.5,33.5L84.5,33.5L0,33.5Z"
      android:strokeWidth="1"
      android:fillColor="#CECEDA"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M8.5,15L69.5,15C72.814,15 75.5,17.686 75.5,21L75.5,39.924L75.5,39.924L41.685,63L8.5,39.332L8.5,15Z"
      android:strokeWidth="1"
      android:fillColor="#5D68E8"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M8.5,15L69.5,15C72.814,15 75.5,17.686 75.5,21L75.5,39.924L75.5,39.924L41.685,63L8.5,39.332L8.5,15Z"/>
    <path
        android:pathData="M34.214,32.571C34.214,33.992 33.159,35.143 31.857,35.143C30.555,35.143 29.5,33.992 29.5,32.571C29.5,31.151 30.555,30 31.857,30C33.159,30 34.214,31.151 34.214,32.571"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M8.5,15L69.5,15C72.814,15 75.5,17.686 75.5,21L75.5,39.924L75.5,39.924L41.685,63L8.5,39.332L8.5,15Z"/>
    <path
        android:pathData="M49.214,32.571C49.214,33.992 48.159,35.143 46.857,35.143C45.555,35.143 44.5,33.992 44.5,32.571C44.5,31.151 45.555,30 46.857,30C48.159,30 49.214,31.151 49.214,32.571"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M8.5,15L69.5,15C72.814,15 75.5,17.686 75.5,21L75.5,39.924L75.5,39.924L41.685,63L8.5,39.332L8.5,15Z"/>
    <path
        android:pathData="M31.218,41.571C31.217,41.608 31.214,41.643 31.214,41.679C31.214,46.354 34.956,50.143 39.572,50.143C44.187,50.143 47.929,46.354 47.929,41.679C47.929,41.643 47.927,41.608 47.926,41.571L31.218,41.571Z"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <path
      android:pathData="M0.001,23C0.001,23 -0,15 9.441,15L68.5,15C68.5,15 60.937,15 60.937,23L0.001,23Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="34.25"
          android:startY="15"
          android:endX="34.25"
          android:endY="23"
          android:type="linear">
        <item android:offset="0" android:color="#FFB5BDFF"/>
        <item android:offset="1" android:color="#FF7F89FF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
