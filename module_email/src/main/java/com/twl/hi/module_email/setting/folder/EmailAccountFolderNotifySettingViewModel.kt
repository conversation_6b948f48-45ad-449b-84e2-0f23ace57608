package com.twl.hi.module_email.setting.folder

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.techwolf.lib.tlog.TLog
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.email.bean.receive.MailFolder
import com.twl.hi.foundation.model.email.bean.request.MailFolderListRequest
import com.twl.hi.foundation.model.email.constants.MailConstants
import com.twl.hi.foundation.utils.point.MailPoint
import com.twl.hi.module_email.common.showMailCommonFailure
import com.twl.hi.module_email.domain.EmailBusinessCentre.toMailFolderWrapper
import com.twl.hi.module_email.domain.MailFolderWrapper
import lib.twl.common.base.BaseViewModel
import lib.twl.common.util.ExecutorFactory
import lib.twl.common.util.ToastUtils

/**
 * <AUTHOR>
 * @Date   2023/11/10 3:43 PM
 * @Desc   别删这行注释啊，删了程序就崩了
 */
class EmailAccountFolderNotifySettingViewModel(app: Application): BaseViewModel(app) {

    val TAG = "EmailAccountFolderNotifySettingViewModel"

    private val mailFolderList = MutableLiveData<MutableList<MailFolderWrapper>>()

    private var mAccountId = -1
    fun loadAccountFolder(accountId: Int) {
        mAccountId = accountId
        ExecutorFactory.execWorkTask {
            ServiceManager.getInstance().emailService.getMailFolderListByAccountId(accountId, MailFolderListRequest()) {
                val list = mutableListOf<MailFolderWrapper>()
                eachFolder(list, it.mailFolderList, 0, false, true)
                mailFolderList.postValue(list)
            }
        }
    }

    fun folderListLiveData() : LiveData<MutableList<MailFolderWrapper>> {
        return mailFolderList
    }

    fun eachFolder(
        listData: MutableList<MailFolderWrapper>,
        apiData: MutableList<MailFolder>,
        level: Int,
        includeFlagFolder: Boolean = false,
        moveMails: Boolean = false
    ) {
        for (i in 0 until apiData.size) {
            val mailFolderWrapper = apiData[i].toMailFolderWrapper(level)
            if (mailFolderWrapper.mailFolder.folderType == MailConstants.FolderType.FLAGGED) {
                // 不需要展示星标文件夹
                if (!includeFlagFolder) {
                    continue
                }
            }
            // 需要判断草稿箱, 已发送文件夹下
            // 是否包含子文件夹, 不包含则直接不展示, 包含则置灰
            if (moveMails) {
                if (mailFolderWrapper.mailFolder.folderType == MailConstants.FolderType.DRAFTS ||
                    mailFolderWrapper.mailFolder.folderType == MailConstants.FolderType.OUTBOX
                    ) {
                    // 为空不展示
                    if (mailFolderWrapper.mailFolder.subFolderList.isNullOrEmpty()) {
                        continue
                    }
                    // 不为空置灰
                    mailFolderWrapper.selectAble = false
                }
            }
            with(mailFolderWrapper) {
                listData.add(this)
            }
            val subFolderList = apiData[i].subFolderList
            if (!subFolderList.isNullOrEmpty()) {
                val levelNext = level + 1
                eachFolder(listData, subFolderList, levelNext)
            }
        }
    }

    fun updateFolderNotifySetting(data: List<MailFolderWrapper>, callback: () -> Unit) {
        ExecutorFactory.execWorkTask {
            data.map {
                Pair(it.mailFolder.folderId, it.mailFolder.isPushEnable)
            }.run {
                TLog.info(TAG, "pairs -> $this ")
                ServiceManager.getInstance().emailService.updateFolderNotifySetting(mAccountId, this) {
                    it?.run {
                        if (it.codeValue == MailConstants.Code.SUCCESS) {
                            ExecutorFactory.execMainTask { callback.invoke() }
                        } else {
                            showMailCommonFailure()
                        }
                    }
                }
            }
        }
        MailPoint.pointUpdateFolderNotifySetting()
    }
}