package com.twl.hi.module_email.view_edit

import android.app.Application
import android.text.*
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.techwolf.lib.tlog.TLog
import com.twl.hi.email.pb.constants.CommonType
import com.twl.hi.email.pb.receive.BshGetSignatureResponse
import com.twl.hi.email.pb.receive.BshResponse
import com.twl.hi.export.email.bean.MailInfoBean
import com.twl.hi.foundation.api.response.MailBigAttachFileUploadResponse
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.logic.email.EmailBigFileUpload
import com.twl.hi.foundation.model.ScheduleLocation
import com.twl.hi.foundation.model.ServerMailOption
import com.twl.hi.foundation.model.email.bean.receive.*
import com.twl.hi.foundation.model.email.bean.request.*
import com.twl.hi.foundation.model.email.constants.MailConstants
import com.twl.hi.foundation.model.email.listener.*
import com.twl.hi.foundation.utils.ScheduleUtils
import com.twl.hi.foundation.utils.point.MailPoint
import com.twl.hi.module_email.R
import com.twl.hi.module_email.common.showMailCommonFailure
import com.twl.hi.module_email.domain.AccountInfoBean
import com.twl.hi.module_email.domain.EmailBusinessCentre
import com.twl.hi.module_email.domain.MailContactSearchBean
import com.twl.hi.module_email.domain.ScheduleInfo
import com.twl.hi.module_email.domain.SignatureInfo
import com.twl.hi.module_email.domain.mailGroupToMailContactSearchBeanList
import com.twl.hi.module_email.domain.toSignatureInfo
import com.twl.hi.module_email.view_edit.edit.EmailEditFragment
import com.twl.utils.file.FileUtils
import hi.kernel.HiKernel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import lib.twl.common.util.ExecutorFactory
import lib.twl.common.util.MultiTimeRecord
import lib.twl.common.util.ProcessHelper
import lib.twl.common.util.TimeTag
import lib.twl.common.util.ToastUtils
import lib.twl.common.views.calendar.Calendar
import java.io.File
import java.lang.StringBuilder
import java.util.*
import java.util.concurrent.CountDownLatch
import kotlin.coroutines.resume

const val TIME_ERROR = 100

internal const val MAIL_SEND_TYPE_NORMAL = 0
internal const val MAIL_SEND_TYPE_SEPARATE = 1

open class EmailEditorViewModel(app: Application) : EmailBaseViewModel(app),
    IMultiMailAttachFileUploadCallback {

    open val TAG = "EmailEditorViewModel"

    // 邮件创建类型
    var type = -1
    var previewMailType = -1

    // 邮件id
    private var mailId: String = ""

    // 回复id
    private var replyToId: String = ""

    // 传参邮件信息
    private var mailInfoBean: MailInfoBean? = null

    // 发送邮件方式 0 - 正常发送 1 - 分别发送
    var mailSendTypeLiveData = MutableLiveData<Int>(MAIL_SEND_TYPE_NORMAL)

    // 接收人
    var mailReceiverLiveData = MutableLiveData<MutableList<MailPeople>>()

    // 抄送人
    var mailCcLiveData = MutableLiveData<MutableList<MailPeople>>()

    // 密送人
    var mailBscLiveData = MutableLiveData<MutableList<MailPeople>>()

    // 是否包含任意收件人
    var mailPeopleLiveData = MediatorLiveData<Boolean>()

    // 发送邮件按钮是否可用, 3:可用, 2:上传中为空, 1:收件人不为空
    var sendMailEnable = MediatorLiveData<Int>()

    // 顶部菜单栏
    var titleBar = MutableLiveData<String>("")

    // 界面展示的邮件
    var mailInfoLiveData = MutableLiveData<EmailInfo>()

    var templateBodyLiveData = MutableLiveData<String>()

    // 关键词搜索结果
    var contactSearchResultLiveData = MutableLiveData<List<MailContactSearchBean>>()

    // 查询邮件详情
    var queryMailDetailsStartLiveData = MutableLiveData<Boolean>(false)

    // 附件上传映射，用于更新文件上传进度
    val uploadFileMap = mutableMapOf<String, Int>()

    // 添加后又删除的图片 cid
    val tmpDeletedImgs = Vector<String>()

    // 用户上传附件列表进度数据，用户储存进度更新界面
    val uploadFileListLiveData = MutableLiveData<MutableList<UploadFileBean>>(mutableListOf())

    val serverMailOption =
        ServiceManager.getInstance().emailService.serverMailOption ?: ServerMailOption()

    var signatureInfoLiveData = MutableLiveData<SignatureInfo>()

    var mutliMailAccount = MutableLiveData<Boolean>()

    val sendMailAccountLiveData = MutableLiveData<AccountInfoBean>(getCurrentUseAccountInfo())

    val outputResult = MutableLiveData<Pair<OutputFileBean, Boolean>>()

    var updateMailInfoByTemplate = false

    /**
     * @param type             类型（创建，回复，回复全部，转发，查看草稿）
     * @param mailId           当前邮件id，创建新邮件时无邮件id
     * @param folderId         当前邮件所在文件夹id
     * @param replyToId        回复邮件id
     */
    fun initMailType(
        type: Int,
        mailId: String,
        replyToId: String,
        mailInfoBean: MailInfoBean?,
        previewMailType: Int
    ) {
        this.type = type
        this.mailId = mailId
        this.replyToId = replyToId
        this.mailInfoBean = mailInfoBean
        this.previewMailType = previewMailType
        TLog.info(
            TAG,
            "type = $type, mailId = $mailId, replyToId = $replyToId, previewMailType=$previewMailType"
        )
        resetMemberCache()

        initLiveData()
        ExecutorFactory.execWorkTask {
            initData()
            if (previewMailType != EmailEditFragment.TYPE_COPY_EDIT) {
                initSignature()
            }
        }
    }

    private fun resetMemberCache() {
        uploadFileMap.clear()
        tmpDeletedImgs.clear()
        imgUploadAll.clear()
        imgInMailsAll.clear()
        imgUploadingNumber.set(0)
        attachFiles.clear()
        cidListInEdit.clear()
        allAttachFiles.clear()
        uploadingNumber.set(0)
    }

    private fun initLiveData() {
        mailPeopleLiveData.removeSource(mailReceiverLiveData)
        mailPeopleLiveData.addSource(mailReceiverLiveData) {
            // 普通发送时观测收件人/抄送人/密送人
            // 分别发送时只观测收件人
            if (mailSendTypeLiveData.value == MAIL_SEND_TYPE_NORMAL) {
                mailPeopleLiveData.postValue(
                    it.isNullOrEmpty().not()
                            or mailCcLiveData.value.isNullOrEmpty().not()
                            or mailBscLiveData.value.isNullOrEmpty().not()
                )
            } else {
                mailPeopleLiveData.postValue(
                    it.isNullOrEmpty().not()
                )
            }
        }
        mailPeopleLiveData.removeSource(mailCcLiveData)
        mailPeopleLiveData.addSource(mailCcLiveData) {
            // 普通发送时才观测抄送人
            if (mailSendTypeLiveData.value == MAIL_SEND_TYPE_NORMAL) {
                mailPeopleLiveData.postValue(
                    it.isNullOrEmpty().not()
                            or mailReceiverLiveData.value.isNullOrEmpty().not()
                            or mailBscLiveData.value.isNullOrEmpty().not()
                )
            }
        }
        mailPeopleLiveData.removeSource(mailBscLiveData)
        mailPeopleLiveData.addSource(mailBscLiveData) {
            // 普通发送时才观测密送人
            if (mailSendTypeLiveData.value == MAIL_SEND_TYPE_NORMAL) {
                mailPeopleLiveData.postValue(
                    it.isNullOrEmpty().not()
                            or mailReceiverLiveData.value.isNullOrEmpty().not()
                            or mailCcLiveData.value.isNullOrEmpty().not()
                )
            }
        }
        sendMailEnable.removeSource(mailPeopleLiveData)
        sendMailEnable.addSource(mailPeopleLiveData) {
            val value = sendMailEnable.value
            if (it) {
                if (value == 2 || value == 3) {
                    sendMailEnable.postValue(3)
                } else {
                    sendMailEnable.postValue(1)
                }
            } else {
                if (value == 3) {
                    sendMailEnable.postValue(2)
                } else if (value == 1) {
                    sendMailEnable.postValue(0)
                }

            }
        }
        sendMailEnable.removeSource(uploadingNumberLiveDate)
        sendMailEnable.addSource(uploadingNumberLiveDate) {
            val value = sendMailEnable.value
            if (it == null || it.equals(0)) {
                if (value == 1 || value == 3) {
                    sendMailEnable.postValue(3)
                } else {
                    sendMailEnable.postValue(2)
                }
            } else {
                if (value == 3) {
                    sendMailEnable.postValue(1)
                } else if (value == 2) {
                    sendMailEnable.postValue(0)
                }
            }
        }

        mutliMailAccount.postValue(EmailBusinessCentre.getCacheMailAccountList().size > 1)

        mailInfoBean?.mailScheduleInfo?.run {
            val scheduleLocation = ScheduleLocation().apply {
                this.name = location
                this.addr = locationAddress
                this.lat = latitude
                this.lng = longitude
            }
            mailScheduleLiveData.postValue(
                ScheduleInfo(
                    this.id,
                    this.startDate,
                    this.endDate,
                    this.startTime,
                    this.endTime,
                    this.repeatType,
                    this.repeatEndDate,
                    scheduleLocation
                )
            )
        }
    }

    private fun initSignature() {
        // 草稿编辑不增加签名
        if (type == EmailEditFragment.TYPE_EDIT && previewMailType == EmailEditFragment.TYPE_EDIT) {
            return
        }

        ServiceManager.getInstance().emailService.getSignatureInfo(ServiceManager.getInstance().emailService.currentCacheAccountId) { code: Int, signature: BshGetSignatureResponse ->
            if (code == MailConstants.Code.SUCCESS) {
                val useSignature = signature.mobileSignatureInfo.enable
                val useMobile = useSignature && signature.mobileSignatureInfo.usePc.not()
                if (useSignature) {
                    TLog.info(TAG, "useSignature $useSignature useMobile $useMobile")
                    if (useMobile) {
                        signatureInfoLiveData.postValue(signature.mobileSignatureInfo.toSignatureInfo())
                    } else {
                        signatureInfoLiveData.postValue(signature.pcSignatureInfo.toSignatureInfo())
                    }
                }
            } else {
                TLog.info(TAG, "code is $code")
            }
        }
    }

    fun recordDeletedImg(tmpImg: String) {
        tmpDeletedImgs.add(tmpImg)
    }

    override fun initData() {
        super.initData()
        updateTitleBar()
        if (mailInfoBean?.templateId.isNullOrEmpty().not()) {
            val templateId = mailInfoBean?.templateId ?: return
            createMailByType {
                loadMailTemplate(mailId, TemplateSummary(templateId))
            }
        } else {
            updateEditContent()
        }
    }

    override fun getMailId(): String {
        return mailId
    }

    override fun updateCreateMailId(id: String?) {
        id?.run { mailId = id }
    }

    fun switchMailSendType(type: Int? = null) {
        TLog.info(TAG, "switchMailSendType -> $type")
        type?.let {
            mailSendTypeLiveData.postValue(it)
        } ?: mailSendTypeLiveData.postValue(mailSendTypeLiveData.value?.let {
            if (it == MAIL_SEND_TYPE_NORMAL) MAIL_SEND_TYPE_SEPARATE else MAIL_SEND_TYPE_NORMAL
        })
    }

    fun prepareSendSeparatelyData(): MutableList<MailPeople> {
        return prepareSendSeparatelyData(
            mailReceiverLiveData.value ?: mutableListOf(),
            mailCcLiveData.value ?: mutableListOf(),
            mailBscLiveData.value ?: mutableListOf()
        )
    }

    private fun prepareSendSeparatelyData(
        receiverList: MutableList<MailPeople>,
        ccList: MutableList<MailPeople>,
        bscList: MutableList<MailPeople>
    ): MutableList<MailPeople> {
        val mergedList = mutableListOf<MailPeople>()
        mergedList.addAll(receiverList)
        mergedList.addAll(ccList.filterNot { cc -> mergedList.any { it.mailAddress == cc.mailAddress } })
        mergedList.addAll(bscList.filterNot { bsc -> mergedList.any { it.mailAddress == bsc.mailAddress } })
        return mergedList
    }

    /**
     * 更新输入框的内容数据
     */
    private fun updateEditContent() {
        if (type != EmailEditFragment.TYPE_EDIT && type != EmailEditFragment.TYPE_REPLY_EML_FILE) {
            // 默认新建
            val emailInfo = EmailInfo()
            if (mailInfoBean != null) {
                val list = mutableListOf<MailPeople>()
                mailInfoBean?.receiveList?.forEach {
                    list.add(MailPeople(it, it))
                }
                emailInfo.receiverList = transferMailAddressToMailName(list)
                emailInfo.title = mailInfoBean?.title ?: ""
            }
            mailInfoLiveData.postValue(emailInfo)
        } else {
            // 编辑已有邮件类型。直接读取邮件id后展示即可，如查看草稿编辑
            fillMailContent()
        }
    }

    /**
     * 查询回复/转发邮件内容填充当前邮件
     */
    private fun fillMailContentFromReplyMail(mail: EmailInfo) {
        if (replyToId.isNotEmpty()) {
            ServiceManager.getInstance().emailService.getMailDetailsInfo(
                replyToId,
                IMailInfoCallback { mailSummaryList, bshResponse ->
                    run {
                        val success = mailSummaryList.code == MailConstants.Code.SUCCESS
                        if (!success || mailSummaryList.emailInfoList.isNullOrEmpty()) {
                            TLog.info(
                                TAG,
                                "fillMailContentFromReplyMail replyId $replyToId error! ${mailSummaryList.mailError}"
                            )
                            mailInfoLiveData.postValue(mail)
                            return@IMailInfoCallback
                        }
                        TLog.info(
                            TAG,
                            "fillMailContentFromReplyMail -> ${mailSummaryList.emailInfoList[0]} "
                        )
                        val email = mailSummaryList.emailInfoList[0]
                        fillMailHeaderInfoFromReply(email, mail)
                        mailInfoLiveData.postValue(mail)
                    }
                }, sendMailAccountId
            )
        }
    }

    /**
     * @param replyToMail 回复的原始邮件信息
     * @param mail        回复的邮件
     */
    private fun fillMailHeaderInfoFromReply(replyToMail: EmailInfo?, mail: EmailInfo) {
        replyToMail?.let { email ->
            mail.title = getMailTitleFromReply(email.title)
            mail.sender = getUserMailPeople()
            val receiver = getMailReceivePeopleFromReply(email)
            mail.receiverList = receiver
            mail.carbonCopyList = getMailCarbonCopyPeopleFromReply(email)
                .subtractMailPeople(receiver).toList()
            mail.encryptCopyList = getEncryptCopyList(email)
        }
    }

    private fun getEncryptCopyList(email: EmailInfo): MutableList<MailPeople> {
        return when (previewMailType) {
            EmailEditFragment.TYPE_COPY_EDIT -> {
                val receiveList = mutableListOf<MailPeople>()
                if (email.encryptCopyList != null) {
                    receiveList.addAll(transferMailAddressToMailName(email.encryptCopyList.filter {
                        filterNoSelfAddress(it)
                    }))
                }
                receiveList
            }

            else -> {
                mutableListOf<MailPeople>()
            }
        }
    }

    override fun multiUploadImg(fileList: MutableList<FileWrapper>, isEmlImgUpload: Boolean) {
        val fileInLimitList = mutableListOf<FileWrapper>()
        val currentSize = allAttachFiles.getImageFiles().filter {
            !tmpDeletedImgs.contains(it.cid)
        }.size
        if (currentSize > serverMailOption.inlineMaxNum) {
            ToastUtils.ss("单个邮件最多${serverMailOption.inlineMaxNum}个图片")
            return
        }

        val featureSize = currentSize + fileList.size
        val cutSize = featureSize - serverMailOption.inlineMaxNum
        if (cutSize > 0) {
            ToastUtils.ss("单个邮件最多${serverMailOption.inlineMaxNum}个图片")
            fileInLimitList.addAll(fileList.subList(0, fileList.size - cutSize))
        } else {
            fileInLimitList.addAll(fileList)
        }

        val fileShouldUpload = mutableListOf<FileWrapper>()
        fileInLimitList.forEach {
            // 邮件大小检测
            if (checkAttachFileInLimit(it.realFile.length())) {
                return@forEach
            }
            fileShouldUpload.add(it)
        }
        super.multiUploadImg(fileShouldUpload, isEmlImgUpload)
    }

    /**
     * 恢复携带的附件信息
     */
    private fun recoverAttachFile(attachFiles: Vector<AttachFile>, mailId: String) {
        allAttachFiles.clear()
        allAttachFiles.addAll(attachFiles)
        val normalAttachFiles = attachFiles.filter { attachFile ->
            !attachFile.isInline
        }
        val uploadFileList = mutableListOf<UploadFileBean>()
        synchronized(uploadFileMap) {
            uploadFileList.let {
                normalAttachFiles.forEach { normalAttachFile ->
                    if (normalAttachFile.isBigAttach) {
                        // 更新描述
                        normalAttachFile.fileSizeDesc = getFileSizeDesc(normalAttachFile.fileSize)
                        normalAttachFile.fileIconType = getFileIconType(normalAttachFile.fileName)
                        normalAttachFile.expireDesc = getExpireDesc(normalAttachFile.expireTime)
                        val uploadFileBean = UploadFileBean(
                            normalAttachFile.fileName,
                            normalAttachFile.fileSize,
                            100,
                            normalAttachFile.cid,
                            mailId,
                            "bigFile_${System.currentTimeMillis()}_${normalAttachFile.fileName}",
                            false,
                            normalAttachFile.id,
                            "",
                            true,
                            normalAttachFile.bigAttachUrl,
                            normalAttachFile.expireTime,
                            null
                        )
                        uploadFileMap[uploadFileBean.requestId] = uploadFileList.size
                        uploadFileList.add(uploadFileBean)
                    } else {
                        val uploadFileBean = UploadFileBean(
                            normalAttachFile.fileName,
                            normalAttachFile.fileSize,
                            100,
                            normalAttachFile.cid,
                            mailId,
                            "Hi_${System.currentTimeMillis()}",
                            false,
                            normalAttachFile.id
                        )
                        uploadFileMap[uploadFileBean.requestId] = uploadFileList.size
                        uploadFileList.add(uploadFileBean)
                    }
                }
            }
        }
        TLog.info(TAG, "recoverAttachFile -> ${uploadFileList.size} ")
        ExecutorFactory.execMainTask {
            uploadFileListLiveData.value = uploadFileList
        }
    }

    /**
     * 查询邮件id填充邮件内容
     */
    private fun fillMailContent() {
        if (mailId.isEmpty()) {
            TLog.info(TAG, "mail id is null!")
            return
        }
        queryMailDetailsStartLiveData.postValue(true)
        TLog.info(TAG, "edit for $mailId ")
        ServiceManager.getInstance().emailService.getMailDetailsInfo(
            mailId,
            IMailInfoCallback { mailSummaryList, bshResponse ->
                TLog.info(TAG, "edit response back $mailId ")
                run {
                    mailSummaryList?.let { it ->
                        val success = mailSummaryList.code == MailConstants.Code.SUCCESS
                        if (!success || it.emailInfoList.isNullOrEmpty()) {
                            TLog.info(
                                TAG,
                                "getMailDetailsInfo for $mailId error! ${mailSummaryList.mailError}"
                            )
                            queryMailDetailsStartLiveData.postValue(false)
                            ToastUtils.failure("数据请求异常")
                            return@IMailInfoCallback
                        }
                        val emailInfo = mailSummaryList.emailInfoList[0]
                        emailInfo?.let { mail ->
                            TLog.info(
                                TAG,
                                "edit mail ${mail.id} ${mail.title} ${mail.receiverList.size} ${mail.sender.mailAddress} ${mail.mailScheduleInfo}"
                            )
                            // 回复全部/转发/回复类型，需要reply的原邮件数据后展示收件人
                            if (type != EmailEditFragment.TYPE_REPLY_EML_FILE && previewMailType != EmailEditFragment.TYPE_EDIT && previewMailType != EmailEditFragment.TYPE_CREATE) {
                                if (replyToId.isNotEmpty() || previewMailType == EmailEditFragment.TYPE_COPY_EDIT) {
                                    TLog.info(TAG, "fillMailContentFromReplyMail ->  ")
                                    // 解析回复的邮件中的数据, 获取收件人信息填充
                                    fillMailContentFromReplyMail(mail)
                                }
                            } else {
                                TLog.info(TAG, "fillMailContent with current mail ->  ")
                                // 处理header相关信息
                                // 直接使用当前邮件数据. 解析填充邮箱的收件人展示，如果是内部联系人，需要展示姓名
                                if (type == EmailEditFragment.TYPE_CREATE || (type == EmailEditFragment.TYPE_EDIT && previewMailType == EmailEditFragment.TYPE_EDIT)) {
                                    fillHeaderInfoWithCurrent(mail)
                                    mailInfoLiveData.postValue(mail)
                                }
                                // 编辑eml的收件人需要从原邮件获取
                                if (type == EmailEditFragment.TYPE_REPLY_EML_FILE) {
                                    ServiceManager.getInstance().emailService.getEmlFileDetailsInfo(
                                        replyToId
                                    ) { replySummaryList, bshRe ->
                                        replySummaryList?.run {
                                            TLog.info(
                                                TAG,
                                                "emailInfoList[0] -> ${emailInfoList[0].title} ${
                                                    emailInfoList.get(0).receiverList.size
                                                } "
                                            )
                                            fillMailHeaderInfoFromReply(this.emailInfoList[0], mail)
                                            mailInfoLiveData.postValue(mail)
                                        }
                                    }
                                }
                            }
                            // 日程
                            mail.mailScheduleInfo?.run {
                                if (this.id.isNullOrEmpty()) {
                                    if (this.startDate.isNullOrEmpty().not()) {
                                        val scheduleLocation = ScheduleLocation().apply {
                                            this.name = location
                                            this.lat = latitude
                                            this.lng = longitude
                                        }
                                        mailScheduleLiveData.postValue(
                                            ScheduleInfo(
                                                this.id,
                                                this.startDate,
                                                this.endDate,
                                                this.startTime,
                                                this.endTime,
                                                this.repeatType,
                                                this.repeatEndDate,
                                                scheduleLocation
                                            )
                                        )
                                    }
                                }
                            }

                            // 处理content相关信息
                            // 处理附件
                            processEmailAttachFiles(mail, replyToId)
                        }
                    }
                    queryMailDetailsStartLiveData.postValue(false)
                }
            }, sendMailAccountId
        )
    }

    private suspend fun loadTemplate(templateSummary: TemplateSummary) =
        suspendCancellableCoroutine {
            ServiceManager.getInstance().emailService.getTemplateDetail(templateSummary) { code, emailInfo ->
                if (code == MailConstants.Code.SUCCESS) {
                    it.resume(emailInfo)
                } else {
                    it.resume(null)
                }
            }
        }

    /**
     * 加载邮件模版
     */
    fun loadMailTemplate(mailId: String, templateSummary: TemplateSummary) {
        viewModelScope.launch {
            val templateMailInfo = withContext(Dispatchers.IO) {
                loadTemplate(templateSummary)
            }
            templateMailInfo ?: return@launch
            val limit = serverMailOption.receiverMailMaxSize
            if (!checkTemplateLimit(templateMailInfo, limit)) {
                return@launch
            }
            // append receipt
            val originMailInfo = mailInfoLiveData.value
            fillHeaderInfoWithCurrent(templateMailInfo)
            updateMailInfoByTemplate = true
            originMailInfo?.run {
                if (templateMailInfo.isSeparatelySend) {
                    val separateSendList = (receiverList?: emptyList()) + (carbonCopyList ?: emptyList()) + (encryptCopyList?: emptyList()) + (templateMailInfo.receiverList ?: emptyList()) + (templateMailInfo.carbonCopyList?: emptyList()) + (templateMailInfo.encryptCopyList ?: emptyList())
                    receiverList = separateSendList.distinctBy { it.mailAddress }
                        .take(serverMailOption.receiverMailMaxSize)
                    carbonCopyList = emptyList()
                    encryptCopyList = emptyList()
                    if (!templateMailInfo.title.isNullOrEmpty()) {
                        title = templateMailInfo.title
                    }
                } else {
                    receiverList = ((receiverList ?: emptyList()) + (templateMailInfo.receiverList
                        ?: emptyList())).distinctBy { it.mailAddress }
                        .take(serverMailOption.receiverMailMaxSize)
                    carbonCopyList = ((carbonCopyList ?: emptyList()) + (templateMailInfo.carbonCopyList
                        ?: emptyList())).distinctBy { it.mailAddress }
                        .take(serverMailOption.ccMailMaxSize)
                    encryptCopyList =
                        ((encryptCopyList ?: emptyList()) + (templateMailInfo.encryptCopyList
                            ?: emptyList())).distinctBy { it.mailAddress }
                            .take(serverMailOption.bscMailMaxSize)
                    if (!templateMailInfo.title.isNullOrEmpty()) {
                        title = templateMailInfo.title
                    }
                }
                isSeparatelySend = templateMailInfo.isSeparatelySend
                mailInfoLiveData.postValue(this)
            } ?: run {
                mailInfoLiveData.postValue(templateMailInfo)
            }
            templateBodyLiveData.postValue(templateMailInfo.body)
            combineTemplateWithDraft(mailId, templateSummary.id)
        }
    }

    private fun checkTemplateLimit(templateMailInfo: EmailInfo, limit: Int): Boolean {
        if (mailSendTypeLiveData.value == MAIL_SEND_TYPE_SEPARATE) {
            val originMergeList = prepareSendSeparatelyData()
            val templateMergeList = prepareSendSeparatelyData(
                templateMailInfo.receiverList,
                templateMailInfo.carbonCopyList,
                templateMailInfo.encryptCopyList
            )
            val mergeList = mutableListOf<MailPeople>()
            mergeList.addAll(originMergeList)
            mergeList.addAll(templateMergeList.filterNot { template -> originMergeList.any { it.mailAddress == template.mailAddress } })
            return if (mergeList.size <= limit) {
                true
            } else {
                ToastUtils.ss("使用模板后所有收件人超过${limit}个，无法使用该模板")
                false
            }
        } else {
            val receiverList = mailReceiverLiveData.value ?: mutableListOf()
            val ccList = mailCcLiveData.value ?: mutableListOf()
            val bscList = mailBscLiveData.value ?: mutableListOf()
            val stringBuilder = StringBuilder()
            stringBuilder.append("使用模版后")
            var overLimit = 0
            if (receiverList.size + (templateMailInfo.receiverList?.size ?: 0) > limit) {
                stringBuilder.append("收件人")
                overLimit++
            }
            if (ccList.size + (templateMailInfo.carbonCopyList?.size ?: 0) > limit) {
                if (overLimit > 0) {
                    stringBuilder.append("/")
                }
                stringBuilder.append("抄送人")
                overLimit++
            }
            if (bscList.size + (templateMailInfo.encryptCopyList?.size ?: 0) > limit) {
                if (overLimit > 0) {
                    stringBuilder.append("/")
                }
                stringBuilder.append("密送人")
                overLimit++
            }
            return if (overLimit > 0) {
                stringBuilder.append("超过${limit}个，无法使用该模板")
                ToastUtils.ss(stringBuilder.toString())
                false
            } else {
                true
            }
        }
    }

    private suspend fun combineTemplateWithDraft(mailId: String, templateId: String) =
        withContext(Dispatchers.Default) {
            ServiceManager.getInstance().emailService.combineTemplateWithDraft(
                mailId,
                templateId
            ) { code, plusAttachFiles ->
                attachFiles.clear()
                // 缓存附件列表
                attachFiles.addAll(plusAttachFiles)
                // 删除过期的大附件
                for (i in attachFiles.size - 1 downTo 0) {
                    if (attachFiles[i].isBigAttach && attachFiles[i].expireTime > 0 && attachFiles[i].expireTime < System.currentTimeMillis()) {
                        val param = AttachFileDeleteParam()
                        param.id = attachFiles[i].id
                        param.mailId = getMailId()
                        param.type = 1
                        ServiceManager.getInstance().emailService.deleteAttachFile(
                            param,
                            null,
                            sendMailAccountId
                        )
                        attachFiles.removeAt(i)
                    }
                }
                // 下载正文图片
                downloadContentPic(cidListInEdit, attachFiles)
                recoverAttachFile(attachFiles, getMailId())
            }
        }

    private suspend fun detachSchedule(mailId: String, scheduleId: String) =
        withContext(Dispatchers.Default) {
            ServiceManager.getInstance().emailService.cancelScheduleInfo(
                mailId,
                scheduleId
            ) { code, errInf ->
                ExecutorFactory.execMainTask {
                    if (MailConstants.Code.SUCCESS == code) {
                        mailScheduleLiveData.postValue(null)
                    } else {
                        errInf.showMailCommonFailure()
                    }
                }
            }
        }

    private suspend fun updateSchedule(mailId: String, scheduleInfo: ScheduleInfo) =
        withContext(Dispatchers.Default) {
            ServiceManager.getInstance().emailService.updateScheduleInfo(
                mailId,
                scheduleInfo.toMailScheduleInfo()
            ) { code, errInf ->
                ExecutorFactory.execMainTask {
                    if (MailConstants.Code.SUCCESS == code) {
                        mailScheduleLiveData.postValue(scheduleInfo)
                    } else {
                        errInf.showMailCommonFailure()
                    }
                }
            }
        }

    fun detachSchedule() {
        mailScheduleLiveData.value?.run {
            if (id.isNullOrEmpty()) {
                mailScheduleLiveData.postValue(null)
            } else {
                viewModelScope.launch {
                    detachSchedule(mailId, id ?: "")
                }
            }
        }
    }

    fun updateSchedule(scheduleInfo: ScheduleInfo) {
        if (scheduleInfo.id.isNullOrEmpty()) {
            mailScheduleLiveData.postValue(scheduleInfo)
        } else {
            viewModelScope.launch {
                updateSchedule(mailId, scheduleInfo)
            }
        }
    }

    /**
     * 根据当前邮件直接读取展示
     * 适用于编辑草稿类型, 创建草稿类型
     */
    private fun fillHeaderInfoWithCurrent(mail: EmailInfo) {
        mail.receiverList = transferMailAddressToMailName(mail.receiverList)
        mail.carbonCopyList =
            transferMailAddressToMailName(mail.carbonCopyList)
        mail.encryptCopyList =
            transferMailAddressToMailName(mail.encryptCopyList)
        mailInfoBean?.run {
            val replyTitleZh = getMailTitleFromReply(title)
            if (replyTitleZh.isNotEmpty()) {
                mail.title = replyTitleZh
            }
        }
    }

    private fun processEmailAttachFiles(mail: EmailInfo, replyToId: String) {
        if (type == EmailEditFragment.TYPE_REPLY_EML_FILE) {
            // eml文件附件处理流程
            // 0.替换本地webview img标签内容为本地路径
            // 1.上传本地附件到远端
            // 2.更新邮件时, 替换内联图片
            processEmlFileAttachFile(mail, filePath = replyToId)
        } else {
            attachFiles.clear()
            // 缓存附件列表
            attachFiles.addAll(mail.attachedFiles)
            // 删除过期的大附件
            for (i in attachFiles.size - 1 downTo 0) {
                if (attachFiles[i].isBigAttach && attachFiles[i].expireTime > 0 && attachFiles[i].expireTime < System.currentTimeMillis()) {
                    val param = AttachFileDeleteParam()
                    param.id = attachFiles[i].id
                    param.mailId = getMailId()
                    param.type = 1
                    ServiceManager.getInstance().emailService.deleteAttachFile(
                        param,
                        null,
                        sendMailAccountId
                    )
                    attachFiles.removeAt(i)
                }
            }
            // 下载正文图片
            downloadContentPic(cidListInEdit, attachFiles)
            recoverAttachFile(attachFiles, getMailId())
        }
    }

    open fun processEmlFileAttachFile(mail: EmailInfo, filePath: String) {
    }

    /**
     * 根据新建邮件来填充内容
     */
    fun fillNewMailContent(emailInfo: EmailInfo) {
        mailInfoLiveData.value?.let { mailInfo ->
            mailInfo.id = emailInfo.id
        } ?: mailInfoLiveData.postValue(emailInfo)
        mailId = emailInfo.id
    }

    /**
     * 获取不同情形下的默认收件人列表
     */
    private fun getMailReceivePeopleFromReply(replyToEmailInfo: EmailInfo): MutableList<MailPeople> {
        return when (previewMailType) {
            EmailEditFragment.TYPE_REPLY_TO -> {
                mutableListOf<MailPeople>(replyToEmailInfo.sender)
            }

            EmailEditFragment.TYPE_REPLY_ALL -> {
                val receiveList = mutableListOf<MailPeople>()
                val sourceList = mutableListOf<MailPeople>()
                if (replyToEmailInfo.sender != null) {
                    sourceList.add(replyToEmailInfo.sender)
                }
                if (replyToEmailInfo.receiverList != null) {
                    sourceList.addAll(replyToEmailInfo.receiverList)
                }
                receiveList.addAll(transferMailAddressToMailName(sourceList.filter {
                    filterNoSelfAddress(it)
                }))
                receiveList
            }

            EmailEditFragment.TYPE_REPLY_FORWARD -> {
                mutableListOf<MailPeople>()
            }

            EmailEditFragment.TYPE_COPY_EDIT -> {
                val receiveList = mutableListOf<MailPeople>()
                val sourceList = mutableListOf<MailPeople>()
                if (replyToEmailInfo.receiverList != null) {
                    sourceList.addAll(replyToEmailInfo.receiverList)
                }
                receiveList.addAll(transferMailAddressToMailName(sourceList.filter {
                    filterNoSelfAddress(it)
                }))
                receiveList
            }

            else -> {
                mutableListOf<MailPeople>()
            }
        }
    }

    /**
     * 过滤掉自己的邮箱地址和别名地址
     */
    private fun filterNoSelfAddress(it: MailPeople): Boolean {
        return getCurrentUseAccountInfo()?.run {
            var moreAccountRepeat = true
            proxyAccountList.forEach { moreAccount ->
                moreAccountRepeat = moreAccount.mailAddress != it.mailAddress && moreAccountRepeat
            }
            return mailAddress != it.mailAddress && moreAccountRepeat
        } ?: kotlin.run { getCurrentUseMailAddress() != it.mailAddress }
    }

    private fun getMailCarbonCopyPeopleFromReply(replyToEmailInfo: EmailInfo): MutableList<MailPeople> {
        return when (previewMailType) {
            EmailEditFragment.TYPE_REPLY_ALL -> {
                val receiveList = mutableListOf<MailPeople>()
                if (replyToEmailInfo.carbonCopyList != null) {
                    receiveList.addAll(transferMailAddressToMailName(replyToEmailInfo.carbonCopyList.filter {
                        filterNoSelfAddress(it)
                    }))
                }
                receiveList
            }

            EmailEditFragment.TYPE_COPY_EDIT -> {
                val receiveList = mutableListOf<MailPeople>()
                if (replyToEmailInfo.carbonCopyList != null) {
                    receiveList.addAll(transferMailAddressToMailName(replyToEmailInfo.carbonCopyList.filter {
                        filterNoSelfAddress(it)
                    }))
                }
                receiveList
            }

            else -> {
                mutableListOf<MailPeople>()
            }
        }
    }


    private fun getMailTitleFromReply(title: String): String {
        return when (previewMailType) {
            EmailEditFragment.TYPE_REPLY_TO -> {
                String.format(
                    ProcessHelper.getContext().getString(R.string.email_reply_header),
                    title
                )
            }

            EmailEditFragment.TYPE_REPLY_ALL -> {
                String.format(
                    ProcessHelper.getContext().getString(R.string.email_reply_header),
                    title
                )
            }

            EmailEditFragment.TYPE_REPLY_FORWARD -> {
                String.format(
                    ProcessHelper.getContext().getString(R.string.email_forward_header),
                    title
                )
            }

            EmailEditFragment.TYPE_COPY_EDIT -> {
                title
            }

            else -> {
                ""
            }
        }
    }

    /**
     * 更新菜单主题
     */
    private fun updateTitleBar() {
        val titleName = when (previewMailType) {
            EmailEditFragment.TYPE_REPLY_TO -> {
                ProcessHelper.getContext().getString(R.string.email_reply_email_edit)
            }

            EmailEditFragment.TYPE_REPLY_ALL -> {
                ProcessHelper.getContext().getString(R.string.email_reply_email_edit)
            }

            EmailEditFragment.TYPE_REPLY_FORWARD -> {
                ProcessHelper.getContext().getString(R.string.email_forward_email_edit)
            }

            else -> ProcessHelper.getContext().getString(R.string.email_email_edit)
        }
        titleBar.postValue(titleName)
    }

    fun searchContact(searchKey: String) {
        TLog.info(TAG, "searchKey is [$searchKey]")
        ExecutorFactory.execWorkTask {
            val request = BshSearchRequest()
            request.keyword = searchKey
            request.searchSense =
                CommonType.BSH_SEARCH_ACCOUNT_INFO_SCENE.SEARCH_SCENE_EDITOR_SEARCH_VALUE
            request.requestId = System.currentTimeMillis().toString()
            request.pushMethod = MailConstants.PushMethod.BSH_PUSH_FRONT
            ServiceManager.getInstance().emailService.searchMailGroupList(request, object :
                IGetMailGroupListCallback {
                override fun callback(response: BshMailGroupResponse?) {
                    response?.run {
                        TLog.info(TAG, "searchMailGroupList size ${response.groupList?.size}")
                        if (code == MailConstants.Code.SUCCESS) {
                            val mailGroupList =
                                this.groupList?.mailGroupToMailContactSearchBeanList()
                                    ?: emptyList<MailContactSearchBean>()
                            if (mailGroupList.isNotEmpty()) {
                                contactSearchResultLiveData.postValue(mailGroupList)
                            } else {
                                contactSearchResultLiveData.postValue(mutableListOf())
                            }
                        } else {
                            ToastUtils.failure("搜索失败请重试")
                            contactSearchResultLiveData.postValue(mutableListOf())
                            TLog.error(TAG, "searchMailGroupList error $mailError")
                        }
                    }
                }
            }, sendMailAccountId)
        }
    }

    /**
     * 创建回复转发邮件类型后
     * 需要继续调用update接口
     * 将数据更新为本地自定义的引用数据
     * 否则将使用outlook默认的英文格式
     */
    fun createMailByType(createCallback: ((MailUpdateResponse) -> Unit)? = null) {
        if (hasMailCreated) {
            val response = MailUpdateResponse()
            response.result = MailConstants.Code.SUCCESS
            createCallback?.invoke(response)
            TLog.info(TAG, "createMail return -> hasMailCreated ")
            return
        }
        when (type) {
            EmailEditFragment.TYPE_CREATE -> {
                createMail(MailConstants.CreateMailType.DEFAULT, "", EmailInfo(), createCallback)
            }

            EmailEditFragment.TYPE_EDIT -> {
            }

            else -> {
                TLog.info(tag, "no this type to create $type")
            }
        }
    }

    /**
     * 导出附件
     */
    fun outPutAttachFile(outputFileBean: OutputFileBean) {
        if (mailId.isEmpty()) {
            ToastUtils.ss("邮件生成失败，请重新选择附件！")
            return
        }
        uploadingNumber.incrementAndGet()
        updateSendMailEnableStatus()
        val cid = generateCid()
        val traceId = ServiceManager.getInstance().emailService.callUuid
        outputFileBean.traceId = traceId
        val uploadFileBean = UploadFileBean(
            outputFileBean.fileName,
            outputFileBean.file.realFile.length(),
            0,
            cid,
            mailId,
            traceId,
            showProgress = false,
            "",
            outputFileBean.file.realFile.absolutePath,
            isEmlAttach = true,
            showOutputProgress = true
        )
        recordUploadFile(uploadFileBean)
        TLog.info(TAG, "开始导出文件 $uploadFileBean")
        ServiceManager.getInstance().emailService.outPutAttachFile(outputFileBean.mailId) { code, response ->
            if (code == MailConstants.Code.SUCCESS) {
                TLog.info(TAG, "导出文件 $uploadFileBean 成功")
                if (response.emlFileListList.isNotEmpty()) {
                    // 将 UI 从导入中更新为上传中
                    uploadFileListLiveData.value?.run {
                        val index = uploadFileMap[traceId] ?: -1
                        if (index > -1 && index < size) {
                            set(index, uploadFileBean.apply {
                                val filePath = response.getEmlFileList(0)
                                val realFile = File(filePath)
                                fileSize = realFile.length()
                                isBigFile = fileSize > serverMailOption.mailAttachmentMaxSize
                                showOutputProgress = false
                                <EMAIL> = filePath
                                showProgress = true
                            })
                        }
                        uploadFileListLiveData.postValue(this)
                    }
                    // 开始上传
                    if (uploadFileBean.fileSize > serverMailOption.mailAttachmentMaxSize) {
                        if (uploadFileBean.fileSize <= serverMailOption.urlAttachFileMaxSize) {
                            executeUploadBigFile(uploadFileBean) {
                                if (!it) {
                                    ToastUtils.failure("文件上传失败")
                                }
                            }
                        } else {
                            ToastUtils.ss("不支持上传大于${serverMailOption.urlAttachmentMaxSizeMb}M的附件")
                        }
                    } else {
                        uploadOutputAttachFile(uploadFileBean)
                    }
                } else {
                    TLog.info(TAG, "outPutSuccess, ")
                }
            } else {
                outputResult.postValue(Pair(outputFileBean, false))
                TLog.info(TAG, "outPutAttachFile failed, code: $code")
            }
        }
    }

    fun notifyRemovedOutputFailed(traceId: String) {
        uploadFileListLiveData.value?.run {
            uploadFileMap.remove(traceId)?.also {
                if (it > -1 && it < size) {
                    removeAt(it)
                }
            }
            uploadFileListLiveData.postValue(this)
            uploadingNumber.decrementAndGet()
            updateSendMailEnableStatus()
        }
    }

    /**
     * 上传导出的附件
     */
    private fun uploadOutputAttachFile(outPutFile: UploadFileBean) {
        if (mailId.isEmpty()) {
            ToastUtils.ss("邮件生成失败，请重新选择附件！")
            return
        }

        // 普通附件数量检测
        if (allAttachFiles.getNormalFiles().size >= serverMailOption.attachmentMaxNum) {
            ToastUtils.ss("单个邮件最多${serverMailOption.attachmentMaxNum}个附件")
            TLog.info(TAG, "allAttachFiles -> getNormalFiles limit ")
            return
        }
        // 邮件大小检测
        if (checkAttachFileInLimit(outPutFile.fileSize)) {
            TLog.info(TAG, "checkAttachFileInLimit - limit ")
            return
        }
        recordAttachFile(
            FileWrapper(File(outPutFile.filePath), outPutFile.titleName),
            outPutFile.cid
        )
        val request = AttachFileUploadRequest()
        val param = AttachFileUploadParam().apply {
            this.mailId = <EMAIL>
            this.cid = outPutFile.cid
            this.pushMethod = 1
            this.filePath = outPutFile.filePath
            this.inline = 0
            this.attachFileName = outPutFile.titleName
            this.traceId = outPutFile.requestId
            this.mailBoxId = sendMailAccountId
            this.contentType =
                FileUtils.getMimeType(ProcessHelper.getContext(), outPutFile.filePath)
        }
        request.paramList = listOf(param)
        request.traceId = outPutFile.requestId
        request.mailBoxId = sendMailAccountId
        MultiTimeRecord.startTimeFirstByTraceId(TimeTag.MAIL_ATTACH_FILE_UPLOAD, param.traceId)
        ServiceManager.getInstance().emailService.multiUploadAttachFile(request, this)
    }

    /**
     * 上传附件
     */
    fun uploadAttachFile(file: FileWrapper, isEmlFile: Boolean = false) {
        if (mailId.isEmpty()) {
            ToastUtils.ss("邮件生成失败，请重新选择附件！")
            return
        }

        // 普通附件数量检测
        if (allAttachFiles.getNormalFiles().size >= serverMailOption.attachmentMaxNum) {
            ToastUtils.ss("单个邮件最多${serverMailOption.attachmentMaxNum}个附件")
            TLog.info(TAG, "allAttachFiles -> getNormalFiles limit ")
            return
        }
        // 邮件大小检测
        if (checkAttachFileInLimit(file.realFile.length())) {
            TLog.info(TAG, "checkAttachFileInLimit - limit ")
            return
        }
        // 普通上传文件流程
        uploadingNumber.incrementAndGet()
        updateSendMailEnableStatus()

        val cid = generateCid()
        val traceId = ServiceManager.getInstance().emailService.callUuid

        recordUploadFile(
            UploadFileBean(
                file.fileName.ifEmpty { file.realFile.name },
                file.realFile.length(),
                0,
                cid,
                mailId,
                traceId,
                showProgress = true,
                "",
                file.realFile.absolutePath,
                isEmlAttach = isEmlFile,
                showOutputProgress = false
            )
        )
        recordAttachFile(file, cid)

        val request = AttachFileUploadRequest()
        val param = AttachFileUploadParam().apply {
            this.mailId = <EMAIL>
            this.cid = cid
            this.pushMethod = 1
            this.filePath = file.realFile.absolutePath
            this.inline = 0
            this.attachFileName = file.fileName.ifEmpty { file.realFile.name }
            this.traceId = traceId
            this.mailBoxId = sendMailAccountId
            this.contentType =
                FileUtils.getMimeType(ProcessHelper.getContext(), file.realFile.absolutePath)
        }
        request.paramList = listOf(param)
        request.traceId = traceId
        request.mailBoxId = sendMailAccountId
        MultiTimeRecord.startTimeFirstByTraceId(TimeTag.MAIL_ATTACH_FILE_UPLOAD, param.traceId)
        ServiceManager.getInstance().emailService.multiUploadAttachFile(request, this)
    }

    /**
     * 生成 uploadFileBean 添加到 uploadFileListLiveData 中，并通过 uploadFileMap 记录其 index [requestId, index]
     */
    private fun recordUploadFile(
        uploadFileBean: UploadFileBean
    ) {
        TLog.info(TAG, "uploadAttachFile $uploadFileBean")
        val uploadFileList = uploadFileListLiveData.value ?: mutableListOf()
        // 根据 requestId 记录每个文件在 uploadFileList 集合中的位置 [requestId, index]
        synchronized(uploadFileMap) {
            uploadFileList.run {
                uploadFileMap[uploadFileBean.requestId] = size
                this.add(uploadFileBean)
            }
        }
        uploadFileListLiveData.postValue(uploadFileList)
    }

    /**
     * 生成 attachFile 并记录在 allAttachFiles 中
     */
    private fun recordAttachFile(file: FileWrapper, cid: String): AttachFile {
        val attachFile = AttachFile()
        attachFile.isBigAttach = false
        attachFile.fileSize = file.realFile.length()
        attachFile.fileName = file.fileName.ifEmpty { file.realFile.name }
        attachFile.inline = 0
        attachFile.cid = cid
        allAttachFiles.add(attachFile)
        return attachFile
    }


    override fun uploadSingleFileCallback(response: AttachFileUploadResponse?) {
        response?.let { fileResponse ->
            TLog.info(TAG, "fileResponse is $fileResponse")
            synchronized(uploadFileMap) {
                val uploadFileList = uploadFileListLiveData.value ?: mutableListOf()
                val index = uploadFileMap[response.requestId] ?: -1
                if (uploadFileList.isNotEmpty() && index >= 0 && index < (uploadFileList.size)) {
                    MultiTimeRecord.finishTimeFirstByTraceId(
                        TimeTag.MAIL_ATTACH_FILE_UPLOAD,
                        response.requestId
                    )
                    if (fileResponse.code == 0) {
                        uploadFileList.let {
                            it[index].uploadProgress = 100
                            it[index].showProgress = false
                            it[index].fileId = fileResponse.attachFileId
                        }
                        uploadFileListLiveData.postValue(uploadFileList)

                        allAttachFiles.firstOrNull { TextUtils.equals(it.cid, fileResponse.cid) }
                            ?.also {
                                it.id = fileResponse.attachFileId
                                val groupTimeValue = MultiTimeRecord.getRecordTime(
                                    TimeTag.MAIL_ATTACH_FILE_UPLOAD,
                                    fileResponse.requestId
                                )
                                MailPoint.pointMailAttachDownOrUp(
                                    size = it.fileSize,
                                    costTime = groupTimeValue.firstTime,
                                    sdkCostTime = groupTimeValue.secondTime,
                                    sdkPointJson = response.buryInfo,
                                    download = false
                                )
                            }
                    } else {
                        ToastUtils.failure("文件上传失败")
                        TLog.info(TAG, "uploadSingleFileCallback $fileResponse")
                        // 删除附件缓存
                        allAttachFiles.firstOrNull { TextUtils.equals(it.cid, fileResponse.cid) }
                            ?.also {
                                allAttachFiles.remove(it)
                                TLog.info(TAG, "remove -> success $it")
                            }
                        // 更新附件列表数据
                        uploadFileList.removeAt(index)
                        // 删除附件后需要更新index索引
                        if (uploadFileList.isNotEmpty()) {
                            uploadFileList.forEachIndexed { index, uploadFileBean ->
                                uploadFileMap[uploadFileBean.requestId] = index
                            }
                        } else {
                            uploadFileMap.clear()
                        }
                        uploadFileListLiveData.postValue(uploadFileList)
                    }
                } else {
                    TLog.info(
                        TAG,
                        "uploadSingleFileCallback uploadFileMap index $index ${uploadFileList.size}"
                    )
                }
            }
        }
        uploadingNumber.decrementAndGet()
        updateSendMailEnableStatus()
    }

    override fun uploadAllFileCallback() {
        TLog.info(TAG, "uploadAllFileCallback ");
        updateSendMailEnableStatus()
    }

    override fun uploadSingleFileProgress(progress: AttachFileTransferProgress?) {
        progress?.let { p ->
            synchronized(uploadFileMap) {
                val uploadFileList = uploadFileListLiveData.value ?: mutableListOf()
                val index = uploadFileMap[p.requestId] ?: -1
                if (index >= 0 && index < uploadFileList.size) {
                    val diffValue = p.progress - uploadFileList[index].uploadProgress
                    if (diffValue > 9 || p.progress == 100) {
                        uploadFileList.let {
                            val uploadFileBean = it[index]
                            uploadFileBean.uploadProgress = p.progress.toInt()
                            uploadFileBean.showProgress = true
                        }
                        ExecutorFactory.execMainTask {
                            uploadFileListLiveData.value = uploadFileList
                        }
                    }
                } else {
                    TLog.info(TAG, "uploadSingleFileProgress uploadFileMap index -1")
                }
            }
        }
    }

    fun mailAccountFormat(accountInfoBean: AccountInfoBean): String {
        return "${accountInfoBean.name} <${accountInfoBean.primaryMailAddress}>"
    }

    /**
     * 取消上传
     */
    fun cancelUploadFile(uploadBean: UploadFileBean) {
        ExecutorFactory.execWorkTask {
            synchronized(uploadFileMap) {
                TLog.info(TAG, "cancelUploadFile start $uploadBean")
                // 取消普通附件下载
                if (uploadBean.isBigFile) {
                    cancelUploadBigFile(uploadBean)
                } else {
                    cancelUploadNormalAttachFile(uploadBean)
                }

                TLog.info(
                    TAG,
                    "cancelUploadFile Result -> allAttachSize ${allAttachFiles.size}"
                )
            }
        }
    }

    private fun cancelUploadNormalAttachFile(uploadBean: UploadFileBean) {
        if (uploadBean.uploadProgress != 100 && uploadBean.showProgress) {
            // 普通附件上传无法取消
            val result =
                ServiceManager.getInstance().emailService.cancelRequest(uploadBean.requestId)
            TLog.info(TAG, "cancelUploadNormalAttachFile -> $result ")
        } else {
            // 附件已经上传成功后, 遍历查找附件详情解除和邮件的联系
            notifyAttachListChange(uploadBean)
        }
    }

    private fun cancelUploadBigFile(uploadBean: UploadFileBean) {
        if (uploadBean.uploadProgress != 100 && uploadBean.showProgress) {
            // 上传中, 不用处理附件列表, 上传失败处逻辑处理
            uploadBean.uploadRequest?.cancelUpload()
        } else {
            // 附件已经上传成功后, 遍历查找附件详情解除和邮件的联系
            notifyAttachListChange(uploadBean)
        }
    }

    private fun notifyAttachListChange(uploadBean: UploadFileBean) {
        // 附件已经上传成功后, 遍历查找附件详情
        var needRemove: AttachFile? = null
        run loop@{
            allAttachFiles.forEach {
                TLog.info(
                    TAG,
                    "equals -> ${it.cid} = ${uploadBean.cid}, ${it.id} = ${uploadBean.fileId}"
                )
                // 此处只能用cid比较, 附件上传未完成时没有文件id, 此时永远为false
                if (!it.isBigAttach && TextUtils.equals(it.cid, uploadBean.cid)) {
                    TLog.info(TAG, "foreach file equals success normalFile")
                    needRemove = it
                    return@loop
                }
                if (it.isBigAttach && TextUtils.equals(it.id, uploadBean.fileId)) {
                    TLog.info(TAG, "foreach file equals success bigFile")
                    needRemove = it
                    return@loop
                }
            }
        }

        val uploadFileList = uploadFileListLiveData.value ?: mutableListOf()
        val deleteParam = AttachFileDeleteParam()
        deleteParam.id = uploadBean.fileId
        deleteParam.mailId = getMailId()
        deleteParam.type = if (uploadBean.isBigFile) 1 else 0
        // 取消附件和邮件的关系
        ServiceManager.getInstance().emailService.deleteAttachFile(deleteParam, { code, mailId ->
            synchronized(uploadFileMap) {
                TLog.info(TAG, "cancel delete $code -> $needRemove ")
                if (code == MailConstants.Code.SUCCESS) {
                    uploadFileList.remove(uploadBean)
                    uploadFileMap.clear()
                    // 删除附件后需要更新index索引
                    if (uploadFileList.isNotEmpty()) {
                        uploadFileList.forEachIndexed { index, uploadFileBean ->
                            uploadFileMap[uploadFileBean.requestId] = index
                        }
                    }
                    if (needRemove != null) {
                        allAttachFiles.remove(needRemove)
                    }
                    // 更新界面
                    ExecutorFactory.execMainTask {
                        uploadFileListLiveData.value = uploadFileList
                    }
                } else {
                    ToastUtils.failure("附件删除失败, 请重试")
                }
            }
        }, sendMailAccountId)
    }

    override fun updateSendMailEnableStatus() {
        uploadingNumberLiveDate.postValue(uploadingNumber.get())
    }

    fun autoDownload(uploadBean: UploadFileBean, openFile: (File?) -> Unit) {
        ExecutorFactory.execWorkTask {
            val request = AttachFileDownloadRequest()
            val param = AttachFileDownloadParam()
            param.id = uploadBean.fileId
            param.cid = uploadBean.cid
            param.mailId = uploadBean.mailId
            param.inline = 0
            param.fileName = uploadBean.titleName
            param.mailBoxId = sendMailAccountId
            param.traceId = ServiceManager.getInstance().emailService.callUuid

            request.paramList = listOf(param)
            request.mailBoxId = sendMailAccountId
            request.traceId = ServiceManager.getInstance().emailService.callUuid
            MultiTimeRecord.startTimeFirstByTraceId(
                TimeTag.MAIL_ATTACH_FILE_DOWNLOAD,
                param.traceId
            )
            ServiceManager.getInstance().emailService.multiDownloadAttachFile(request, object :
                IMultiMailAttachFileDownloadCallback {
                override fun downloadSingleFileCallback(response: AttachFileDownloadResult?) {
                    response?.let {
                        var file: File? = null
                        TLog.info(TAG, "downloadSingleFileCallback $response")
                        if (it.result == 0 && response.savePath.isNotEmpty()) {
                            file = File(response.savePath)
                            openFile(file)
                        } else {
                            openFile(null)
                        }
                        MultiTimeRecord.finishTimeFirstByTraceId(
                            TimeTag.MAIL_ATTACH_FILE_DOWNLOAD,
                            response.requestId
                        )
                        val groupTimeValue = MultiTimeRecord.getRecordTime(
                            TimeTag.MAIL_ATTACH_FILE_DOWNLOAD,
                            response.requestId
                        )
                        MailPoint.pointMailAttachDownOrUp(
                            size = file?.length() ?: 0,
                            costTime = groupTimeValue.firstTime,
                            sdkCostTime = groupTimeValue.secondTime,
                            sdkPointJson = response.buryInfo
                        )
                    }
                }

                override fun downloadFileAllCallback() {
                }

                override fun downloadSingleFileProgress(progress: AttachFileTransferProgress?) {
                }
            })
        }
    }

    fun deleteMailComplete(mailId: String) {
        if (mailId.isEmpty()) {
            return
        }
        ServiceManager.getInstance().emailService.deleteEmailCompletely(
            mailId,
            sendMailAccountId,
            object :
                IMailOperateCallback {
                override fun callback(result: MailOperateResult?) {
                    TLog.info(TAG, "delete draft Complete ${result?.code}")
                }
            })
    }

    private fun getCurrentUseMailAddress(): String {
        return EmailBusinessCentre.getCurrentMailAccount()?.mailAddress
            ?: HiKernel.getHikernel().account.emailAddress
    }

    private fun getCurrentUseAccountInfo(): AccountInfoBean? {
        return EmailBusinessCentre.getCurrentMailAccount()
    }

    /**
     * 大图片上传（大于30）
     */
    fun uploadBigFileList(bigFileList: List<FileWrapper>) {
        ExecutorFactory.execWorkTask {
            bigFileList.forEach {
                val countDownLatch = CountDownLatch(1)
                uploadBigFile(it) {
                    countDownLatch.countDown()
                }
                countDownLatch.await()
            }
        }
    }

    /**
     * 大附件上传（大于30）
     */
    fun uploadBigFile(file: FileWrapper, callback: (Boolean) -> Unit) {
        ExecutorFactory.execWorkTask {
            uploadingNumber.incrementAndGet()
            updateSendMailEnableStatus()
            val requestId = "bigFile_${System.currentTimeMillis()}_${file.realFile.name}"
            MultiTimeRecord.startTimeFirstByTraceId(
                TimeTag.MAIL_ATTACH_FILE_UPLOAD,
                requestId
            )
            val uploadFileBean = UploadFileBean(
                file.fileName,
                file.realFile.length(),
                0,
                "",
                mailId,
                "bigFile_${System.currentTimeMillis()}_${file.realFile.name}",
                true,
                "",
                file.realFile.absolutePath,
                true
            )
            recordUploadFile(uploadFileBean)
            executeUploadBigFile(uploadFileBean, callback)
        }
    }

    private fun executeUploadBigFile(uploadFileBean: UploadFileBean, callback: (Boolean) -> Unit) {
        val file = File(uploadFileBean.filePath)
        if (file.exists().not()) {
            TLog.info(TAG, "uploadBigFile file not exists")
            return
        }
        ServiceManager.getInstance().emailService.uploadBigAttachFile(
            uploadFileBean.titleName,
            file,
            getMailId(), sendMailAccountId
        ) { result: Int, progress: Int, response: MailBigAttachFileUploadResponse?, bigRequest: EmailBigFileUpload ->
            TLog.info(
                TAG,
                "upload big file ${file.absolutePath} ${uploadFileBean.filePath} result $result progress=$progress id=${response?.id} expireTime=${response?.expireTime}"
            )
            MultiTimeRecord.finishTimeFirstByTraceId(
                TimeTag.MAIL_ATTACH_FILE_UPLOAD,
                uploadFileBean.requestId
            )
            val groupTimeValue = MultiTimeRecord.getRecordTime(
                TimeTag.MAIL_ATTACH_FILE_UPLOAD,
                uploadFileBean.requestId
            )
            MailPoint.pointMailAttachDownOrUp(
                size = uploadFileBean.fileSize,
                costTime = groupTimeValue.firstTime,
                sdkCostTime = groupTimeValue.secondTime,
                sdkPointJson = "",
                download = false
            )

            ExecutorFactory.execWorkTask {
                synchronized(uploadFileMap) {
                    val uploadFileList = uploadFileListLiveData.value
                    uploadFileList?.run {
                        val index = uploadFileMap[uploadFileBean.requestId]
                        index?.run {
                            uploadFileList[index].uploadRequest = bigRequest
                            if (result == 1) {
                                uploadFileList[index].fileUrl = response?.url ?: ""
                                uploadFileList[index].uploadProgress = 100
                                uploadFileList[index].showProgress = false
                                uploadFileList[index].fileId = response?.id ?: ""
                                uploadFileList[index].expireTime = response?.expireTime ?: -1
                            } else if (result == 0) {
                                uploadFileList[index].uploadProgress = progress
                                uploadFileList[index].showProgress = true
                            } else {
                                uploadFileList.removeAt(index)
                                if (uploadFileList.isNotEmpty()) {
                                    uploadFileList.forEachIndexed { index, uploadFileBean ->
                                        uploadFileMap[uploadFileBean.requestId] = index
                                    }
                                } else {
                                    uploadFileMap.clear()
                                }
                            }
                        }
                    }
                    uploadFileList?.run {
                        uploadFileListLiveData.postValue(this)
                    }
                }
            }
            ExecutorFactory.execMainTask {
                if (result == 1 && progress == 100) {
                    val attachFile = AttachFile()
                    attachFile.id = response?.id
                    attachFile.isBigAttach = true
                    attachFile.bigAttachUrl = response?.url
                    attachFile.expireTime = response?.expireTime ?: -1
                    attachFile.fileSize = uploadFileBean.fileSize
                    attachFile.fileSizeDesc = getFileSizeDesc(uploadFileBean.fileSize)
                    attachFile.expireDesc = getExpireDesc(response?.expireTime)
                    attachFile.fileIconType =
                        getFileIconType(uploadFileBean.titleName)
                    attachFile.fileName = uploadFileBean.titleName
                    allAttachFiles.add(attachFile)
                    uploadingNumber.decrementAndGet()
                    updateSendMailEnableStatus()
                    callback.invoke(true)
                }
                if (result == -1) {
                    uploadingNumber.decrementAndGet()
                    updateSendMailEnableStatus()
                    callback.invoke(false)
                }
            }
        }
    }


    private val SIZE_KB = 1024f
    private val SIZE_MB = 1024f * SIZE_KB
    private val SIZE_GB = 1024f * SIZE_MB
    fun getFileSizeDesc(fileSize: Long): String {
        if (fileSize >= 0) {
            var sizeShow = 0f
            if (fileSize > SIZE_GB) {
                sizeShow = fileSize / SIZE_GB
                return String.format(Locale.CHINA, "%.2f G", sizeShow)
            } else if (fileSize > SIZE_MB) {
                sizeShow = fileSize / SIZE_MB
                return String.format(Locale.CHINA, "%.2f M", sizeShow)
            } else if (fileSize > SIZE_KB) {
                sizeShow = fileSize / SIZE_KB
                return String.format(Locale.CHINA, "%.2f K", sizeShow)
            } else {
                sizeShow = fileSize.toFloat()
                return String.format(Locale.CHINA, "%.0f B", sizeShow)
            }
        }
        return "0B"
    }

    private fun getExpireDesc(expireTime: Long?): String {
        expireTime?.run {
            if (expireTime > 0) {
                val calendar = Calendar.getCalendarFromTime(expireTime)
                return String.format(
                    ProcessHelper.getContext().getString(R.string.email_attach_file_expired_in),
                    calendar.year,
                    calendar.month,
                    calendar.day
                )
            } else {
                return ""
            }
        }
        return ""
    }

    fun deleteUnusedAttachFile(fileId: String, isBigAttach: Boolean = false) {
        ExecutorFactory.execWorkTask {
            val param = AttachFileDeleteParam()
            param.id = fileId
            param.mailId = getMailId()
            param.type = if (isBigAttach) 1 else 0
            ServiceManager.getInstance().emailService.deleteAttachFile(
                param,
                null,
                sendMailAccountId
            )
            TLog.info(TAG, "delete unused file $fileId")
        }
    }

    /**
     * 定时发送邮件
     * @param emailInfo       邮件详情
     * @param scheduleDate    2023-10-16
     * @param scheduleTime    21:30
     */
    fun sendMailSchedule(
        emailInfo: EmailInfo,
        scheduleDate: String,
        scheduleTime: String,
        block: (Int, String?) -> Unit
    ): Boolean {
        if (emailInfo.id.isNullOrEmpty()) {
            TLog.info(tag, "send email id is null")
            return false
        }
        ExecutorFactory.execWorkTask {
            TLog.info(TAG, "sendMailSchedule -> $scheduleDate $scheduleTime ${emailInfo.id}")
            ScheduleUtils.getCalendarByTargetDate(scheduleDate, scheduleTime)?.run {
                if (biggerInMinutes()) {
                    ServiceManager.getInstance().emailService.sendMailSchedule(
                        emailInfo,
                        this.timeInMillis.toString(),
                        { response: BshResponse ->
                            sendMailEnable.postValue(3)
                            ExecutorFactory.execMainTask {
                                block.invoke(
                                    response.codeValue,
                                    if (response.shown) response.errorInfo?.errorMsg ?: "" else ""
                                )
                            }
                            if (response.codeValue == MailConstants.Code.SUCCESS) {
                                MailPoint.pointScheduleSend(this.timeInMillis.toString())
                            }
                        },
                        sendMailAccountId
                    )
                } else {
                    ExecutorFactory.execMainTask {
                        block.invoke(TIME_ERROR, "")
                    }
                    sendMailEnable.postValue(3)
                }
            }
        }
        return true
    }

    /**
     * 检测图片和文件附件总大小
     * 是否超出服务端上限
     * @param length 选择的图片/文件的大小
     */
    fun checkAttachFileInLimit(length: Long): Boolean {
        TLog.info(
            tag,
            "checkAttachFileInLimit ${length + getAttachFileSize()}   limit  ${serverMailOption.mailMaxSize}"
        )
        if (length + getAttachFileSize() > serverMailOption.mailMaxSize) {
            ToastUtils.sl(
                String.format(
                    ProcessHelper.getContext()
                        .getString(R.string.email_do_not_support_max_mail_size),
                    serverMailOption.mailMaxSizeMb
                )
            )
            return true
        }
        return false
    }

    fun getAttachFileSize(): Long {
        val files = allAttachFiles
        var fileSizeAll = 0L
        files.filter { !it.isBigAttach }.forEach {
            fileSizeAll += it.fileSize
        }
        return fileSizeAll
    }

    fun switchSendMail(
        toAccount: AccountInfoBean,
        fromAccount: AccountInfoBean,
        function: (Int, String) -> Unit
    ) {
        ExecutorFactory.execWorkTask {
            lockUpdateOrSwitchOperation {
                TLog.info(TAG, "switchSendMail -> from $fromAccount to $toAccount")
                ServiceManager.getInstance().emailService.switchSendMail(
                    fromAccount.indexKey,
                    toAccount.indexKey,
                    mailId
                ) { bshResponse, mailSwitchId ->
                    if (bshResponse.codeValue == MailConstants.Code.SUCCESS) {
                        sendMailAccountId = toAccount.indexKey
                        sendMailAccountLiveData.postValue(toAccount)
                        function.invoke(0, mailSwitchId)
                        TLog.info(TAG, "switch send mail success -> $sendMailAccountId ")
                    } else {
                        ToastUtils.failure("邮箱${toAccount.mailAddress}不可用")
                        function.invoke(bshResponse.codeValue, "")
                    }
                    release()
                }
            }
        }
    }

    fun getAccountSignatureInfo(
        accountInfoBean: AccountInfoBean,
        function: (SignatureInfo?) -> Unit
    ) {
        ExecutorFactory.execWorkTask {
            ServiceManager.getInstance().emailService.getSignatureInfo(accountInfoBean.indexKey) { code: Int, signature: BshGetSignatureResponse ->
                TLog.info(TAG, "getAccountSignatureInfo code is $code")
                if (code == MailConstants.Code.SUCCESS) {
                    val useSignature = signature.mobileSignatureInfo.enable
                    val useMobile = useSignature && signature.mobileSignatureInfo.usePc.not()
                    if (useSignature) {
                        if (useMobile) {
                            function.invoke(signature.mobileSignatureInfo.toSignatureInfo())
                        } else {
                            function.invoke(signature.pcSignatureInfo.toSignatureInfo())
                        }
                    } else {
                        function.invoke(null)
                    }
                } else {
                    function.invoke(null)
                }
            }
        }
    }

}

private fun java.util.Calendar.biggerInMinutes(): Boolean {
    val currentTime = java.util.Calendar.getInstance()
    currentTime.set(java.util.Calendar.MILLISECOND, 0)
    currentTime.set(java.util.Calendar.SECOND, 0)
    return timeInMillis > currentTime.timeInMillis
}

/**
 * 列表去重
 */
private fun MutableList<MailPeople>.subtractMailPeople(receiver: MutableList<MailPeople>): MutableList<MailPeople> {
    receiver.forEach { mailPeople ->
        for (i in (this.size - 1) downTo 0) {
            if (TextUtils.equals(mailPeople.mailAddress, this[i].mailAddress)) {
                this.removeAt(i)
            }
        }
    }
    return this
}


data class OutputFileBean(
    val mailId: String,
    var traceId: String,
    val fileName: String,
    val file: FileWrapper
)

data class UploadFileBean(
    var titleName: String,
    var fileSize: Long,
    var uploadProgress: Int,
    var cid: String,
    var mailId: String,
    var requestId: String,
    var showProgress: Boolean = true,
    var fileId: String = "",
    var filePath: String = "",
    var isBigFile: Boolean = false, // 是否大附件
    var fileUrl: String = "",       // 大附件url
    var expireTime: Long = -1,       // 大附件过期时间
    var uploadRequest: EmailBigFileUpload? = null, // 大文件上传请求
    var isEmlAttach: Boolean = false,
    var showOutputProgress: Boolean = false
)