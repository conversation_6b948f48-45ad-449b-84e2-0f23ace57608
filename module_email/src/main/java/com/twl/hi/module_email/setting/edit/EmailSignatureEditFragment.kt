package com.twl.hi.module_email.setting.edit

import android.content.DialogInterface
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.util.Base64
import android.view.View
import androidx.lifecycle.Observer
import com.twl.hi.basic.BottomDialogFragment
import com.twl.hi.basic.dialog.DialogUtils
import com.twl.hi.foundation.model.email.constants.MailConstants
import com.twl.hi.module_email.BR
import com.twl.hi.module_email.R
import com.twl.hi.module_email.callback.IBackListener
import com.twl.hi.module_email.callback.IEmailSignatureEditCallback
import com.twl.hi.module_email.databinding.EmailFragmentSignatureEditBinding
import com.twl.hi.module_email.domain.SignatureInfo
import com.twl.hi.module_email.util.EmailUtil
import com.twl.hi.webeditor.RichEditor
import com.twl.hi.webeditor.bean.BoldMenu
import com.twl.hi.webeditor.bean.FontSizeMenu
import com.twl.hi.webeditor.bean.ImageMenu
import com.twl.hi.webeditor.bean.ItalicMenu
import com.twl.hi.webeditor.bean.UnderlineMenu
import com.twl.hi.webeditor.menu.IMenuStyle
import com.twl.hi.webeditor.menu.MenuType
import kz.log.TLog
import lib.twl.common.photoselect.PhotoSelectManager
import lib.twl.common.util.ProcessHelper
import lib.twl.common.util.QMUIKeyboardHelper
import lib.twl.common.util.ToastUtils
import org.json.JSONObject
import java.io.File

class EmailSignatureEditFragment() :
    BottomDialogFragment<EmailFragmentSignatureEditBinding, EmailSignatureEditViewModel>(),
    IEmailSignatureEditCallback {

    val TAG = "EmailSignatureEditFragment"

    private var backListener: IBackListener? = null

    private var dialogUtils : DialogUtils? = null

    private var hasChangeContent = false

    companion object {
        fun instance(mailBoxId: Int) = EmailSignatureEditFragment().also {
            var bundle = Bundle()
            bundle.putInt("mailBoxId", mailBoxId)
            it.arguments = bundle
            it.setDialogTransparent()
            it.isCancelable = false
        }
    }

    override fun getContentLayoutId() = R.layout.email_fragment_signature_edit

    override fun initFragment() {
        arguments?.run {
            viewModel.initAccount(getInt("mailBoxId", 0))
        }
        initWebView()
        initMenu()
        observeStatus()
    }

    private fun observeStatus() {
        viewModel.signatureInfoLiveData.observe(this, object : Observer<SignatureInfo> {
            override fun onChanged(signatureInfo: SignatureInfo?) {
                dataBinding.signatureContent.setHtml(signatureInfo?.signature ?: "", false)
            }
        })
    }

    private fun initMenu() {
        dataBinding.menuToolbar.setMenuListener { menuType ->
            processMenuClick(menuType)
        }
        val list = mutableListOf<IMenuStyle>(ImageMenu(), BoldMenu(), ItalicMenu(), UnderlineMenu(), FontSizeMenu())
        dataBinding.menuToolbar.setMenuList(list)
        dataBinding.signatureContent.setRichEditMenu(dataBinding.menuToolbar, list)
    }

    private fun initWebView() {
        dataBinding.signatureContent.setEditorFontSize(14)
        val setting = dataBinding.signatureContent.settings
        setting.javaScriptEnabled = true
        setting.allowFileAccess = true
        setting.domStorageEnabled = true
        setting.databaseEnabled = true
        setting.allowFileAccessFromFileURLs = true
        setting.allowUniversalAccessFromFileURLs = true
        dataBinding.signatureContent.setEditorFontColor(
            ProcessHelper.getContext().getColor(R.color.color_0D0D1A)
        )
        dataBinding.signatureContent.showKeyboardAndFocusEditorWhenWindowClick()
        dataBinding.signatureContent.setOnTextChangeListener(object :RichEditor.OnTextChangeListener {
            override fun onTextChange(text: String?) {
                hasChangeContent = true
            }

            override fun onEditBodyClick() {
            }
        })
    }

    private fun processMenuClick(menuType: Int) : Boolean {
        when (menuType) {
            MenuType.TYPE_BOLD -> dataBinding.signatureContent.setBold()
            MenuType.TYPE_ITALIC -> dataBinding.signatureContent.setItalic()
            MenuType.TYPE_UNDLERLINE -> dataBinding.signatureContent.setUnderline()
            MenuType.TYPE_IMAGE -> handleImageSelect()

            MenuType.FontSizeMenuType.TYPE_SMALL -> dataBinding.signatureContent.setFontSize(3)
            MenuType.FontSizeMenuType.TYPE_NORMAL -> dataBinding.signatureContent.setFontSize(4)
            MenuType.FontSizeMenuType.TYPE_LARGE -> dataBinding.signatureContent.setFontSize(5)
        }
        return false
    }

    private fun handleImageSelect() {
        PhotoSelectManager.jumpForGalleryFromResult(
            activity,
            1,
            false,
            object : PhotoSelectManager.OnGalleryCountCallBack {
                override fun onGalleryListener(
                    fileList: MutableList<File>?,
                    originalEnable: Boolean
                ) {
                    if (fileList.isNullOrEmpty()) {
                        return
                    }
                    val hasBigFile = fileList.filter { file -> file.length() > 10 * 1024 * 1024 }.isNotEmpty()
                    if (hasBigFile) {
                        ToastUtils.failure("暂时不支持上传大于10M的图片")
                        return
                    }

                    viewModel.enabledRightLivaData.postValue(false)
                    val fileUri = Uri.fromFile(fileList[0]).toString()
                    val size = EmailUtil.getAdapterSizeForImageFile(fileList[0], 335, 335)
                    dataBinding.signatureContent.insertImageWithPaddingTop(
                        fileUri,
                        fileUri,
                        size.width,
                        size.height,
                        8
                    )
                    viewModel.uploadImgFile(fileList[0]) { url: String ->
                        viewModel.enabledRightLivaData.postValue(true)
                        if (url.isNullOrEmpty()) {
                            dataBinding.signatureContent.stopInsertImageLoading(
                                fileUri,
                                fileUri,
                                -1
                            )
                            dataBinding.signatureContent.deleteFailedImg(fileUri, fileUri)
                            ToastUtils.failure("文件上传失败")
                            return@uploadImgFile
                        }
                        TLog.info(TAG, "uploadImg -> $url  ")
                        dataBinding.signatureContent.stopInsertImageLoading(
                            url,
                            fileUri,
                            -1
                        )
                    }
                }
            })
    }

    override fun getCallbackVariable() = BR.callback

    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel

    override fun onClickCancel() {
        QMUIKeyboardHelper.hideKeyboard(dataBinding.signatureContent)
        if (hasChangeContent) {
            showCloseWarningDialog()
        } else {
            dismiss()
        }
    }

    private fun showCloseWarningDialog() {
        if (dialogUtils == null) {
            activity?.run {
                dialogUtils = DialogUtils.Builder(this)
                    .setContent("取消后,本次编辑内容将无法保存,是否确认取消?")
                    .setPositive("确定")
                    .setNegative("取消")
                    .setPositiveListener {
                        <EMAIL>()
                        dialogUtils?.dismiss()
                    }
                    .setNegativeListener {
                        dialogUtils?.dismiss()
                    }
                    .build()
            }
        }
        dialogUtils?.show()
    }

    override fun onClickIcon(view: View) {
    }

    override fun onClickTitle() {
    }

    override fun onClickConfirm() {
        viewModel.enabledRightLivaData.postValue(false)
        dataBinding.signatureContent.getReContentHtml {
            try {
                var json = it
                val jsonObject =
                    JSONObject(json.substring(1, json.length - 1).replace("\\\"", "\""))
                val html = String(Base64.decode(jsonObject.getString("inputHtml"), Base64.NO_WRAP))
                val text = String(Base64.decode(jsonObject.getString("inputText"), Base64.NO_WRAP))
                val imgNumber = jsonObject.getInt("imgNumber")
                if (text.length > 500) {
                    ToastUtils.failure("签名最多500字")
                    viewModel.enabledRightLivaData.postValue(true)
                    return@getReContentHtml
                }
                TLog.info(TAG, "onClickConfirm -> $imgNumber $html ")
                if (TextUtils.isEmpty(text) && imgNumber == 0) {
                    saveMailSignature("")
                } else {
                    saveMailSignature(html)
                }
            } catch (e: Exception) {
                TLog.info(TAG, "jsonFromat -> ${e?.message} ")
            }
        }
    }

    private fun saveMailSignature(html: String) {
        viewModel.updateSignature(html) { code: Int ->
            viewModel.enabledRightLivaData.postValue(true)
            if (code == MailConstants.Code.SUCCESS) {
                QMUIKeyboardHelper.hideKeyboard(dataBinding.signatureContent)
                dismiss()
            } else {
                ToastUtils.failure("保存签名失败")
            }
        }
    }

    fun setBackListener(backListener: IBackListener) {
        this.backListener = backListener
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        backListener?.onBack()
    }
}