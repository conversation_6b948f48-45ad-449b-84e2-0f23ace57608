package com.twl.hi.module_email.view_edit.view.label

import android.os.Bundle
import android.text.Editable
import android.view.View
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import com.twl.hi.basic.BottomSheetBehaviorBaseDialog
import com.twl.hi.basic.callback.TitleBarCallback
import com.twl.hi.basic.views.GridSpacingItemDecoration
import com.twl.hi.module_email.BR
import com.twl.hi.module_email.R
import com.twl.hi.module_email.databinding.EmailDialogCreateLabelBinding
import com.twl.hi.module_email.domain.EmailLabel
import com.twl.hi.module_email.domain.LabelColor
import com.twl.hi.module_email.util.LabelUtil
import lib.twl.common.ext.dp
import lib.twl.common.util.QMUIDisplayHelper
import lib.twl.common.util.QMUIKeyboardHelper
import lib.twl.common.util.ToastUtils

/**
 * Author : Xuweixiang .
 * Date   : On 2024/6/26
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

fun FragmentManager.showCreateLabelDialog(
    emailLabel: EmailLabel? = null,
    listener: CreateLabelDialog.LabelCreateListener? = null
) = CreateLabelDialog().apply {
    this.listener = listener
    this.arguments = Bundle().apply {
        putSerializable("label", emailLabel)
    }
}.show(this, "label-color")

class CreateLabelDialog :
    BottomSheetBehaviorBaseDialog<EmailDialogCreateLabelBinding, CreateLabelViewModel>(), TitleBarCallback {

    private val spanCount = 6

    private val maxLabelNameLength = 100

    private val adapter = CreateLabelColorAdapter(spanCount, R.layout.email_item_label_color)

    var listener: LabelCreateListener? = null

    private var originLabel : EmailLabel? = null

    override fun getContentLayoutId(): Int {
        return R.layout.email_dialog_create_label
    }

    override fun getBottomSheetLayoutParamsHeight(): Int {
        return QMUIDisplayHelper.getScreenHeight(context)
    }

    override fun initFragment() {
        val target = arguments?.getSerializable("label") as? EmailLabel
        viewModel.updateLabel(target)
        originLabel = target
        dataBinding.titleBar.title =
            if (target?.id.isNullOrEmpty()) resources.getString(R.string.email_create_label) else resources.getString(
                R.string.email_edit_label
            )
        viewModel.labelLiveData().observe(this, Observer { label ->
            label?.run {
                dataBinding.etName.setText(name)
                if (label.colorIndex in 0 until 12) {
                    selectColor(label.colorIndex ?: 0)
                }
            } ?: run {
                dataBinding.etName.setText("")
                selectColor(0)
            }
            checkSaveEnable()
        })
        viewModel.editLabelLiveData().observe(this, Observer { success ->
            if (success) {
                ToastUtils.ss("编辑成功")
                invokeListener()
                dismiss()
            }
        })
        viewModel.createLabelLiveData().observe(this, Observer { success ->
            if (success) {
                ToastUtils.ss("新建成功")
                invokeListener()
                dismiss()
            }
        })
        dataBinding.rvLabels.adapter = adapter
        dataBinding.rvLabels.layoutManager = GridLayoutManager(activity, spanCount)
        dataBinding.rvLabels.addItemDecoration(
            GridSpacingItemDecoration(
                spanCount,
                10f.dp.toInt(),
                6f.dp.toInt(),
                false
            )
        )
        adapter.setOnItemClickListener { adapter, view, position ->
            selectColor(position)
            checkSaveEnable()
        }
        adapter.setNewData(LabelUtil.labelColors)
        dataBinding.etName.addTextChangedListener(
            beforeTextChanged = { _, _, _, _ -> },
            onTextChanged = { text: CharSequence?, start: Int, before: Int, count: Int ->

            },
            afterTextChanged = { text: Editable? ->
                checkSaveEnable()
                text?.run {
                    if (length > maxLabelNameLength) {
                        ToastUtils.ss("标签名称最多100字")
                        dataBinding.etName.setText(subSequence(0, maxLabelNameLength))
                        dataBinding.etName.setSelection(maxLabelNameLength)
                    }
                }
            }
        )
    }

    private fun checkSaveEnable() {
        val text = dataBinding.etName.text
        originLabel?.run {
            val index = adapter.data.indexOfFirst { it.selected }
            dataBinding.titleBar.rightEnabled = !text.isNullOrEmpty() && text.trim().isNotEmpty() && (name != text.toString().trim() || colorIndex != index)
        } ?: run {
            dataBinding.titleBar.rightEnabled = !text.isNullOrEmpty() && text.trim().isNotEmpty()
        }
    }

    private fun selectColor(position: Int) {
        val data = adapter.data as List<LabelColor>
        val index = data.indexOfFirst { it.selected }
        if (index > -1) {
            data.getOrNull(index)?.selected = false
            adapter.notifyItemChanged(index)
        }
        data[position].selected = true
        adapter.notifyItemChanged(position)
    }

    private fun invokeListener() {
        QMUIKeyboardHelper.hideKeyboard(dataBinding.etName)
        val data = adapter.data as List<LabelColor>
        val colorIndex = data.indexOfFirst { it.selected }
        val labelColor = data[colorIndex]
        val label = viewModel.labelLiveData().value
        listener?.onSave(
            label ?: EmailLabel(
                name = dataBinding.etName.text.toString().trim(),
                textColor = labelColor.textColor,
                bgColor = labelColor.bgColor,
                colorIndex = colorIndex
            )
        )
    }

    override fun getCallbackVariable(): Int {
        return BR.callback
    }

    override fun getCallback(): Any {
        return this
    }

    override fun getBindingVariable(): Int {
        return BR.viewModel
    }

    interface LabelCreateListener {
        fun onSave(emailLabel: EmailLabel)
    }

    override fun clickLeft(view: View?) {
        dismiss()
    }

    override fun clickRight(view: View?) {
        val data = adapter.data as List<LabelColor>
        val colorIndex = data.indexOfFirst { it.selected }
        val label = viewModel.labelLiveData().value
        val name = dataBinding.etName.text.toString().trim()
        if (label?.id.isNullOrEmpty()) {
            viewModel.createLabel(name, colorIndex)
        } else {
            viewModel.editLabel(name, colorIndex)
        }
    }

}