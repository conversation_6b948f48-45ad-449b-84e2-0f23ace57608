package com.twl.hi.module_email.domain

object Constants {
    const val KEY_FOLDER_OPERATION_TYPE = "folder_operation_type"
    const val KEY_CURRENT_FOLDER_INFO = "current_folder_info"
    const val KEY_PARENT_FOLDER_INFO = "parent_folder_info"

    const val KEY_SELECT_FOLDER_CUSTOM_TYPE = "parent_folder_info"
    const val KEY_SELECT_FOLDER_DEFAULT_FOLDER_ID = "defaule_folder_id"

    // 创建新建文件夹时选择文件夹位置，列表不需要置灰不可选
    const val TypeCreate = 0
    // 编辑更新文件夹时选择文件夹位置，文件夹自身置灰，以及子文件夹隐藏不可选
    const val TypeEdit = 1
    // 普通选择文件夹位置
    const val TypeSelect = 2
}