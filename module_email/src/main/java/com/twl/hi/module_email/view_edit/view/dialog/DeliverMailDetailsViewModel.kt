package com.twl.hi.module_email.view_edit.view.dialog

import android.app.Application
import android.graphics.drawable.Drawable
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.techwolf.lib.tlog.TLog
import com.twl.hi.email.pb.constants.CommonType
import com.twl.hi.email.pb.constants.CommonType.BSH_MAIL_DELIVER_STATUS
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.email.bean.receive.MailDeliverDetails
import com.twl.hi.foundation.model.email.bean.receive.MailPeopleDeliverDetails
import com.twl.hi.foundation.model.email.constants.MailConstants
import com.twl.hi.module_email.R
import com.twl.hi.module_email.common.showMailCommonFailure
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import lib.twl.common.base.BaseViewModel
import lib.twl.common.util.ExecutorFactory
import lib.twl.common.util.ProcessHelper

/**
 * <AUTHOR>
 * @Date   2024/1/3 11:02 AM
 * @Desc   别删这行注释啊，删了程序就崩了
 */
class DeliverMailDetailsViewModel(app: Application): BaseViewModel(app) {

    val TAG = "DeliverMailDetailsViewModel"

    private var mailId = ""

    private val deliverStatusLiveData = MediatorLiveData<Int>()

    private val deliverDetailsListLiveData = MediatorLiveData<MutableList<MailPeopleDeliverDetails>>()

    fun addDeliverDetailsListSource(liveData: LiveData<MailDeliverDetails>) {
        deliverStatusLiveData.removeSource(liveData)
        deliverStatusLiveData.addSource(liveData) {
            it?.run { deliverStatusLiveData.postValue(it.result) }
        }
        deliverDetailsListLiveData.removeSource(liveData)
        deliverDetailsListLiveData.addSource(liveData) {
            it?.run { deliverDetailsListLiveData.postValue(it.accountListStatus) }
        }
    }

    fun loadDetails(deliverDetails: MailDeliverDetails) {
        mailId = deliverDetails.mailId
        deliverStatusLiveData.postValue(deliverDetails.result)
        deliverDetailsListLiveData.postValue(deliverDetails.accountListStatus)
    }

    fun deliverStatus() :LiveData<Int> {
        return deliverStatusLiveData
    }

    fun deliverDetailsInfo() : LiveData<MutableList<MailPeopleDeliverDetails>> {
        return deliverDetailsListLiveData
    }

    fun getMailAddrInfo(bean: MailPeopleDeliverDetails) : String {
        return "${bean.name} <${bean.address}>"
    }

    fun getDeliverIcon(bean: MailPeopleDeliverDetails): Drawable? {
        TLog.info(TAG, "getDeliverIcon -> $bean ")
        val icon = when (bean.iconType) {
            CommonType.BSH_PERSON_DELIVER_STATUS_ICON.INCOMPLETE_ICON_VALUE -> R.drawable.email_ic_sending_mail
            CommonType.BSH_PERSON_DELIVER_STATUS_ICON.SUCCESS_ICON_VALUE -> R.drawable.email_withdraw_success
            CommonType.BSH_PERSON_DELIVER_STATUS_ICON.FAIL_ICON_VALUE -> R.drawable.email_withdraw_failed
            CommonType.BSH_PERSON_DELIVER_STATUS_ICON.NOT_ICON_VALUE -> R.drawable.email_ic_sending_mail
            else -> R.drawable.email_ic_sending_mail
        }
        return ProcessHelper.getContext().getDrawable(icon)
    }

    fun sendNotFailedStatus(bean: MailPeopleDeliverDetails): Boolean {
        return bean.iconType != CommonType.BSH_PERSON_DELIVER_STATUS_ICON.FAIL_ICON_VALUE
    }

    fun retryDeliver(recordId: String, block: () -> Unit) {
        ExecutorFactory.execWorkTask {
            ServiceManager.getInstance().emailService.retryDeliverMail(mailId, recordId) {
                if (it?.codeValue == MailConstants.Code.SUCCESS) {
                } else {
                    it.showMailCommonFailure()
                }
                ExecutorFactory.execMainTask { block.invoke() }
            }
        }
    }

    fun getMailId(): String {
        return mailId
    }

}