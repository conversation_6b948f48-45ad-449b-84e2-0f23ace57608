package com.twl.hi.module_email.domain

import java.io.Serializable

/**
 * Author : <PERSON><PERSON><PERSON><PERSON> .
 * Date   : On 2024/6/25
 * Email  : Contact <EMAIL>
 * Desc   : 邮件标签
 *
 */


data class EmailLabel(
    val id: String? = "",
    var name: String? = "",
    var textColor: Int? = 0,
    var bgColor: Int? = 0,
    var colorIndex: Int? = 0,
    var selected: Boolean = false
): Serializable