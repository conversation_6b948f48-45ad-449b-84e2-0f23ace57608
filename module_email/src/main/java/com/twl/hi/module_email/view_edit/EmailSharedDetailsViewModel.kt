package com.twl.hi.module_email.view_edit

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.techwolf.lib.tlog.TLog
import com.twl.hi.email.pb.constants.CommonType.BSH_MAIL_EXTEND_INFO_TYPE
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.email.bean.receive.ExtInfoDetailsBean
import com.twl.hi.foundation.model.email.bean.receive.MailDeliverDetails
import com.twl.hi.foundation.model.email.constants.MailConstants
import com.twl.hi.module_email.common.showMailCommonFailure
import com.twl.hi.module_email.domain.EmailAction
import com.twl.hi.module_email.domain.EmailBusinessCentre
import com.twl.hi.module_email.domain.EmailWithdrawStatus
import com.twl.hi.module_email.domain.ExtInfoUpdateResponsibility
import com.twl.hi.module_email.domain.MailRefreshResponsibility
import lib.twl.common.base.BaseViewModel
import lib.twl.common.util.ExecutorFactory

/**
 * <AUTHOR>
 * @Date   2023/11/14 10:03 AM
 * @Desc   别删这行注释啊，删了程序就崩了
 */
class EmailSharedDetailsViewModel(app: Application) : BaseViewModel(app) {

    private var mMailId = ""

    private val TAG = "EmailSharedDetailsViewModel"

    /**
     * 撤回邮件更新
     */
    private val withdrawRetryRefreshEvent = MutableLiveData<EmailWithdrawStatus>()

    private val deliverMailDetailsRefreshEvent = MutableLiveData<MailDeliverDetails>()

    private val businessCentre = EmailBusinessCentre

    private val emailService = ServiceManager.getInstance().emailService

    private val mFocusMailEvent = MutableLiveData<Int>()

    /**
     * 邮件变更事件通知
     */
    private val responsibility = MailRefreshResponsibility {
        ExecutorFactory.execWorkTask {
            dispatchMailRefresh(it)
        }
    }.also { businessCentre.registerResponsibility(it) }

    /**
     * 邮件扩展信息刷新
     */
    private val extInfoResponsibility = ExtInfoUpdateResponsibility { extType , extInfo ->
        ExecutorFactory.execWorkTask {
            dispatchMailExtInfoRefresh(extType, extInfo)
        }
    }.also { businessCentre.registerResponsibility(it) }

    fun focusMailId(mailId: String) {
        mMailId = mailId
    }

    fun focusMailRefreshEvent(): LiveData<Int> {
        return mFocusMailEvent
    }

    private fun dispatchMailRefresh(emailAction: EmailAction) {
        if (emailAction.accountId == emailService.currentCacheAccountId) {
            val collection = emailAction.digests?.values?: mutableListOf()
            collection.forEach { list ->
                list.filter { it.id == mMailId }.run {
                    if (isNotEmpty()) {
                        TLog.info(TAG, "dispatchMailRefresh -> ${this.size} $mMailId ${emailAction.subType} ")
                        mFocusMailEvent.postValue(emailAction.subType)
                    }
                }
            }
        }
    }

    private fun dispatchMailExtInfoRefresh(extType: Int, extInfo: ExtInfoDetailsBean) {
        when (extType) {
            BSH_MAIL_EXTEND_INFO_TYPE.EXTEND_INFO_TYPE_DELIVER_STATUS_VALUE -> {
                refreshDeliverStatus(mMailId) {}
            }
            else -> {}
        }
    }

    fun withdrawRetryEvent(): LiveData<EmailWithdrawStatus> {
        return withdrawRetryRefreshEvent
    }

    fun updateWithdrawStatus(emailWithdrawStatus: EmailWithdrawStatus) {
        withdrawRetryRefreshEvent.postValue(emailWithdrawStatus)
    }

    fun deliverMailDetailsRefresh() : LiveData<MailDeliverDetails> {
        return deliverMailDetailsRefreshEvent
    }

    fun updateDeliverMailDetailsStatus(mailDeliverDetails: MailDeliverDetails) {
        deliverMailDetailsRefreshEvent.postValue(mailDeliverDetails)
    }

    fun refreshDeliverStatus(mailId: String, block: (MailDeliverDetails?) -> Unit) {
        ExecutorFactory.execWorkTask {
            TLog.info(TAG, "refreshDeliverStatus -> $mailId")
            ServiceManager.getInstance().emailService.getMailDeliverResult(mailId) { bshResp, mailDeliverResult ->
                if (bshResp.codeValue == MailConstants.Code.SUCCESS) {
                    block.invoke(mailDeliverResult)
                    deliverMailDetailsRefreshEvent.postValue(mailDeliverResult)
                } else {
                    block.invoke(null)
                    bshResp.showMailCommonFailure()
                }
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        businessCentre.unregisterResponsibility(responsibility)
        businessCentre.unregisterResponsibility(extInfoResponsibility)
    }
}