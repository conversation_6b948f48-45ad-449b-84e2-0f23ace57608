package com.twl.hi.module_email.folder.create

import android.os.Bundle
import android.os.Handler
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import androidx.lifecycle.Observer
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.util.ThemeUtils
import com.twl.hi.foundation.utils.point.MailPoint
import com.twl.hi.module_email.BR
import com.twl.hi.module_email.R
import com.twl.hi.module_email.callback.ICreateUpdateFolderCallback
import com.twl.hi.module_email.databinding.EmailFragmentCreateFolderBinding
import com.twl.hi.module_email.domain.Constants
import com.twl.hi.module_email.domain.Constants.KEY_CURRENT_FOLDER_INFO
import com.twl.hi.module_email.domain.Constants.KEY_FOLDER_OPERATION_TYPE
import com.twl.hi.module_email.domain.Constants.KEY_PARENT_FOLDER_INFO
import com.twl.hi.module_email.domain.Constants.TypeCreate
import com.twl.hi.module_email.domain.FolderInfoBean
import com.twl.hi.module_email.domain.toFolderInfoBean
import com.twl.hi.module_email.folder.FixHeightBottomSheetBehaviorDialog
import com.twl.hi.module_email.folder.select.SelectFolderFragment
import com.twl.hi.module_email.main.base.SelectFolderResult
import lib.twl.common.util.ExecutorFactory
import lib.twl.common.util.QMUIKeyboardHelper
import lib.twl.common.util.TimeDifferenceUtil
import lib.twl.common.util.TimeTag.MAIL_EDIT_FOLDER
import lib.twl.common.util.TimeTag.MAIL_NEW_FOLDER
import lib.twl.common.util.ToastUtils

/**
 * 新建文件夹或更新文件夹名称位置的属性
 */
class CreateUpdateFolderFragment : FixHeightBottomSheetBehaviorDialog<EmailFragmentCreateFolderBinding, CreateUpdateFolderViewModel>(), ICreateUpdateFolderCallback,
    TextWatcher {

    var currentFolderInfo: FolderInfoBean ?= null
    var parentFolderInfo: FolderInfoBean ?= null
    var operationType = TypeCreate

    companion object{
        private const val TAG = "CreateUpdateFolderFragment"

        /**
         * @param currentFolderInfo     操作的文件夹
         * @param parentFolderInfo      父文件夹
         * @param type                  操作类型
         */
        @JvmStatic
        fun instance(currentFolderInfo: FolderInfoBean ?= null, parentFolderInfo: FolderInfoBean ?= null, type: Int = TypeCreate) = CreateUpdateFolderFragment().apply {
            val bundle = Bundle()
            TLog.info(TAG, "instance $currentFolderInfo ,\n  $parentFolderInfo,\n $type")
            currentFolderInfo?.run {
                bundle.putParcelable(Constants.KEY_CURRENT_FOLDER_INFO, currentFolderInfo)
            }
            parentFolderInfo?.run {
                bundle.putParcelable(Constants.KEY_PARENT_FOLDER_INFO, parentFolderInfo)
            }
            bundle.putInt(KEY_FOLDER_OPERATION_TYPE, type)
            arguments = bundle
        }
    }

    override fun getContentLayoutId() = R.layout.email_fragment_create_folder

    override fun getCallbackVariable() = BR.callback

    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel

    override fun initFragment() {
        initArguments()
        initViewStatus()
        initObserver()
    }

    private fun initArguments() {
        arguments?.run {
            currentFolderInfo = getParcelable<FolderInfoBean>(KEY_CURRENT_FOLDER_INFO)
            parentFolderInfo = getParcelable<FolderInfoBean>(KEY_PARENT_FOLDER_INFO)
            operationType = getInt(KEY_FOLDER_OPERATION_TYPE)
            TLog.info(TAG, "currentFolderInfo $currentFolderInfo ,\n parentFolderInfo $parentFolderInfo,\n operationType $operationType")
        }
    }

    private fun initViewStatus() {
        viewModel.setShowEditTitle(currentFolderInfo != null)
        dataBinding.titleBar.rightBtn.setTextColor(ThemeUtils.getColorStateList(if (ThemeUtils.useNewTheme) R.color.common_text_color_primary_disabled else R.color.common_text_color_primary_disabled_old))
        currentFolderInfo?.run {
            dataBinding.folderNames.setText(folderName)
        }
        Handler().postDelayed({
            dataBinding.folderNames.requestEditFocus()
        }, 500)
        parentFolderInfo?.run {
            TLog.info(TAG, "parentFolderInfo -> $parentFolderInfo ")
            if (TextUtils.isEmpty(folderName)) {
                dataBinding.selectFolder.setSelectContent("文件夹")
            } else {
                dataBinding.selectFolder.setSelectContent(folderName)
            }
            dataBinding.selectFolder.selectValue = this
        } ?: run {
            dataBinding.selectFolder.setSelectContent("文件夹")
            dataBinding.selectFolder.selectValue = null
        }
        dataBinding.folderNames.addTextWatcher(this)

        dataBinding.selectFolder.setOnClickListener {
            fragmentManager?.let { fragmentManager ->
                SelectFolderFragment.instance(currentFolderInfo = currentFolderInfo, parentFolderInfo = parentFolderInfo, operationType = operationType).show(fragmentManager, "SelectFolderFragment_")
            }
        }

        sharedViewModel.mSelectFolderFragmentLiveData.observe(this, object : Observer<SelectFolderResult>{
            override fun onChanged(selectFolderResult: SelectFolderResult?) {
                selectFolderResult?.let {
                    val selected = it.mailFolderWrapper.toFolderInfoBean()
                    dataBinding.selectFolder.selectValue = selected
                    dataBinding.selectFolder.setSelectContent(selected.folderName)
                    parentFolderInfo = selected
                    checkCreateFolderCondition()
                    // 使用完需要重置状态，否则会在每次进入都触发
                    sharedViewModel.mSelectFolderFragmentLiveData.postValue(null)
                }
            }
        })
    }

    private fun initObserver() {
    }

    override fun onClickCancel() {
        QMUIKeyboardHelper.hideKeyboard(dataBinding.folderNames)
        dismiss()
    }

    override fun onClickIcon(view: View) {
    }

    override fun onClickTitle() {
    }

    override fun onClickConfirm() {
        viewModel.enableSaveBtnLiveData.value = false
        if (operationType == TypeCreate) {
            TimeDifferenceUtil.getInstance().start(MAIL_NEW_FOLDER)
            viewModel.createFolder(dataBinding.folderNames.getText(), parentFolderInfo) {
                ExecutorFactory.execMainTask {
                    val time = TimeDifferenceUtil.getInstance().finish(MAIL_NEW_FOLDER)
                    if (it) {
                        MailPoint.pointFolderManagerOperation("new", time)
                        ToastUtils.success("新建成功")
                        onClickCancel()
                    } else {
                        viewModel.enableSaveBtnLiveData.value = true
                    }
                }
                sharedViewModel.mCreateFolderFragmentLiveData.postValue(Unit)
            }
        } else {
            TimeDifferenceUtil.getInstance().start(MAIL_EDIT_FOLDER)
            val name = dataBinding.folderNames.getText().trim()
            viewModel.updateFolder(name, currentFolderInfo?.folderId?:"", parentFolderInfo) {
                ExecutorFactory.execMainTask {
                    val time = TimeDifferenceUtil.getInstance().finish(MAIL_EDIT_FOLDER)
                    if (it) {
                        MailPoint.pointFolderManagerOperation("edit", time)
                        ToastUtils.success("文件夹\"$name\"编辑成功")
                        onClickCancel()
                    } else {
                        viewModel.enableSaveBtnLiveData.value = true
                    }
                }
                sharedViewModel.mCreateFolderFragmentLiveData.postValue(Unit)
            }
        }
    }

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
    }

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
    }

    override fun afterTextChanged(s: Editable?) {
        checkCreateFolderCondition()
    }

    /**
     * 检测文件夹是否符合
     * 编辑保存的条件
     */
    private fun checkCreateFolderCondition() {
        if (viewModel.checkFolderNameInValid(dataBinding.folderNames.getText())) {
            dataBinding.folderNames.requestWarningMsg("不可包含特殊字符|、/")
            viewModel.enableSaveBtnLiveData.value = false
            return
        } else {
            viewModel.enableSaveBtnLiveData.value = true
        }

        val folderName = dataBinding.folderNames.getText().trim()
        val repeatName = viewModel.isFolderNameExistInSameLevel(folderName, currentFolderInfo?.folderId?:"", parentFolderInfo?.folderId?:"")
        if (repeatName) {
            dataBinding.folderNames.requestDefaultWarning()
        } else {
            if (folderName.length > 64) {
                dataBinding.folderNames.requestWarningMsg("名称最多支持64个字符")
            } else {
                dataBinding.folderNames.clearWarning()
            }
        }

        var subFolderLengthLimit =
            (parentFolderInfo?.subFolderLength ?: 0) >= sharedViewModel.mailOption.folderListLength
        if (subFolderLengthLimit) {
            dataBinding.selectFolder.requestWarning("同级文件夹仅支持创建${sharedViewModel.mailOption.folderListLength}个")
        } else {
            dataBinding.selectFolder.clearWarning()
        }
        viewModel.enableSaveBtnLiveData.value = !repeatName && folderName.isNotEmpty() && folderName.length <= 64 && !subFolderLengthLimit
    }

    override fun onDestroyView() {
        super.onDestroyView()
        dataBinding.folderNames.removeTextWatcher(this)
        dataBinding.selectFolder.setOnClickListener(null)
    }
}