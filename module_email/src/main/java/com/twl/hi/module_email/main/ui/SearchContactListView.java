package com.twl.hi.module_email.main.ui;

import static com.twl.hi.module_email.domain.MailContactSearchBeanKt.TYPE_HI_CONTACT;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.twl.hi.basic.adapter.MyMultiTypeAdapter;
import com.twl.hi.basic.views.group.LinearLayoutManagerWrapper;
import com.twl.hi.foundation.model.email.bean.receive.BshMailGroup;
import com.twl.hi.module_email.BR;
import com.twl.hi.module_email.R;
import com.twl.hi.module_email.databinding.EmailViewItemMailPersonContactBinding;
import com.twl.hi.module_email.databinding.EmailViewItemMailPublicMailBinding;
import com.twl.hi.module_email.domain.HiContactBean;
import com.twl.hi.module_email.domain.MailContactSearchBean;

import java.util.ArrayList;
import java.util.List;

import lib.twl.common.ext.ViewExtKt;
import lib.twl.common.util.LList;
import lib.twl.common.util.QMUIDisplayHelper;

/**
 * <AUTHOR>
 * 联系人自动联想提示
 */
public class SearchContactListView extends FrameLayout {

    private RecyclerView mRecyclerView;
    private LinearLayoutManagerWrapper linearLayoutManagerWrapper;
    private MultiTypeSearchAdapter mTypeSearchAdapter = new MultiTypeSearchAdapter();

    public SearchContactListView(@NonNull Context context) {
        super(context);
        init(context, null);
    }

    public SearchContactListView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public SearchContactListView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, null);
    }

    private void init(Context context, AttributeSet attrs) {
        View view = LayoutInflater.from(context).inflate(R.layout.email_view_layout_contact_list, this, false);
        mRecyclerView = view.findViewById(R.id.contact_list);
        linearLayoutManagerWrapper = new LinearLayoutManagerWrapper(context);
        linearLayoutManagerWrapper.setOrientation(LinearLayoutManager.VERTICAL);
        mRecyclerView.setLayoutManager(linearLayoutManagerWrapper);
        mRecyclerView.setAdapter(mTypeSearchAdapter);
        addView(view);
    }

    /**
     * 重置列表位置
     * @param view  指定的输入框view
     */
    public void resetLayout(View view) {
        FrameLayout.LayoutParams params = (LayoutParams) this.getLayoutParams();
        int[] window = new int[2];
        view.getLocationOnScreen(window);
        params.topMargin = view.getHeight() + window[1] - ViewExtKt.getStatusBarsHeightCache() - QMUIDisplayHelper.dpToPx(1f);
        setLayoutParams(params);
    }

    private static class MultiTypeSearchAdapter extends MyMultiTypeAdapter{
        private OnClickContactListener mClickContactListener;

        public void setClickContactListener(OnClickContactListener clickContactListener) {
            mClickContactListener = clickContactListener;
        }

        MultiTypeSearchAdapter() {
            register(HiContactBean.class, R.layout.email_view_item_mail_person_contact, new ItemViewBinder<EmailViewItemMailPersonContactBinding, HiContactBean>() {
                @Override
                public void bind(EmailViewItemMailPersonContactBinding vdb, HiContactBean item, int linkIndex) {
                    vdb.setVariable(BR.contact, item);
                    vdb.setVariable(BR.callback, mClickContactListener);
                }
            });
            register(BshMailGroup.class, R.layout.email_view_item_mail_public_mail, new ItemViewBinder<EmailViewItemMailPublicMailBinding, BshMailGroup>() {
                @Override
                public void bind(EmailViewItemMailPublicMailBinding vdb, BshMailGroup item, int linkIndex) {
                    vdb.setVariable(BR.mailGroup, item);
                    vdb.setVariable(BR.callback, mClickContactListener);
                }
            });
        }
    }

    /**
     * 更新联系人数据
     * @param contactList
     */
    public void setContactList(List<MailContactSearchBean> contactList) {
        clearOldContact();
        if (LList.isEmpty(contactList)) {
            return;
        }
        List<Object> objectList = new ArrayList<>();
        for (MailContactSearchBean searchBean : contactList) {
            if (searchBean.getType() == TYPE_HI_CONTACT) {
                objectList.add(searchBean.getContact());
            } else {
                objectList.add(searchBean.getMailGroup());
            }
        }
        mTypeSearchAdapter.setData(objectList);
        mTypeSearchAdapter.notifyDataSetChanged();
        setVisibility(VISIBLE);
        linearLayoutManagerWrapper.scrollToPositionWithOffset(0, 0);
    }

    /**
     * 清除旧数据
     */
    private void clearOldContact() {
        int size = mTypeSearchAdapter.getData().size();
        if (size <= 0) {
            setVisibility(GONE);
            return;
        }
        mTypeSearchAdapter.clear();
        mTypeSearchAdapter.notifyDataSetChanged();
        setVisibility(GONE);
    }

    /**
     * 点击监听
     */
    public interface OnClickContactListener {
        void onClickContact(HiContactBean contact);
        void onClickMailGroup(BshMailGroup contact);
    }

    public void setListener(OnClickContactListener listener) {
        mTypeSearchAdapter.setClickContactListener(listener);
    }
}
