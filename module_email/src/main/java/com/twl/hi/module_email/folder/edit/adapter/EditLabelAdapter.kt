package com.twl.hi.module_email.folder.edit.adapter

import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.marginEnd
import androidx.core.view.updateLayoutParams
import com.twl.hi.module_email.R
import com.twl.hi.module_email.databinding.EmailItemEditLabelBinding
import com.twl.hi.module_email.domain.EmailLabel
import com.twl.hi.module_email.folder.edit.callback.EditLabelCallback
import lib.twl.common.adapter.BaseDataBindingAdapter
import lib.twl.common.adapter.BaseDataBindingViewHolder
import lib.twl.common.ext.dp

/**
 * Author : Xuweixiang .
 * Date   : On 2024/7/3
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

class EditLabelAdapter(private val callback: EditLabelCallback? = null) :
    BaseDataBindingAdapter<EmailLabel, EmailItemEditLabelBinding>(R.layout.email_item_edit_label) {
    override fun bind(
        helper: BaseDataBindingViewHolder<EmailItemEditLabelBinding>?,
        binding: EmailItemEditLabelBinding?,
        item: EmailLabel?
    ) {
        binding?.label = item
        binding?.callback = callback
        binding?.labelItem?.run {
            tvName?.updateLayoutParams<ConstraintLayout.LayoutParams> {
                marginEnd = 72f.dp.toInt()
            }
            val originDrawable = mContext.resources.getDrawable(R.drawable.email_ic_label_data)
            val colorFilter = PorterDuffColorFilter(item?.bgColor ?: 0, PorterDuff.Mode.SRC_IN)
            originDrawable.colorFilter = colorFilter
            ivIcon?.setImageDrawable(originDrawable)
        }
    }
}