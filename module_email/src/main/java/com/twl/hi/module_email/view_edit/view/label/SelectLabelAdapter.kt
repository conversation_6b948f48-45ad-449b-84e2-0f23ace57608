package com.twl.hi.module_email.view_edit.view.label

import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import com.twl.hi.module_email.R
import com.twl.hi.module_email.databinding.EmailItemLabelBinding
import com.twl.hi.module_email.domain.EmailLabel
import lib.twl.common.adapter.BaseDataBindingAdapter
import lib.twl.common.adapter.BaseDataBindingViewHolder
import lib.twl.common.ext.dp

/**
 * Author : Xuweixiang .
 * Date   : On 2024/6/26
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

class SelectLabelAdapter(layoutResId: Int) :
    BaseDataBindingAdapter<EmailLabel, EmailItemLabelBinding>(layoutResId) {
    override fun bind(
        helper: BaseDataBindingViewHolder<EmailItemLabelBinding>?,
        binding: EmailItemLabelBinding?,
        item: EmailLabel?
    ) {
        item ?: return
        binding ?: return
        binding.label = item
        val originDrawable = mContext.resources.getDrawable(R.drawable.email_ic_label_data)
        val colorFilter = PorterDuffColorFilter(item.bgColor ?: 0, PorterDuff.Mode.SRC_IN)
        originDrawable.colorFilter = colorFilter
        binding.ivIcon.setImageDrawable(originDrawable)
    }
}