package com.twl.hi.module_email.common

import com.twl.hi.module_email.domain.EmailDigest

/**
 * 邮件列表数据模型
 */
interface UiModel {


    data class Date(val timestamp: Long) : UiModel {
        override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
            if (other is Date) {
                return other.timestamp sameDayTo timestamp
            }
            return super.equals(other)
        }

        override fun hashCode(): Int {
            return timestamp.toDayCode().toInt()
        }
    }
}

data class Digest(var digest: EmailDigest) : UiModel {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (other == digest) {
            return true
        }
        if (other is Digest) {
            return other.digest.id == digest.id
                    && other.digest.selected == digest.selected
                    && other.digest.unread == digest.unread
                    && other.digest.flagged == digest.flagged
                    && other.digest.sender == digest.sender
                    && other.digest.receivers == digest.receivers
                    && other.digest.subject == other.digest.subject
                    && other.digest.content == other.digest.content
        }
        return super.equals(other)
    }

    override fun hashCode(): Int {
        var hash = 17
        hash = hash * 31 + digest.id.hashCode()
        hash = hash * 31 + digest.selected.hashCode()
        hash = hash * 31 + digest.unread.hashCode()
        hash = hash * 31 + digest.flagged.hashCode()
        hash = hash * 31 + digest.sender.hashCode()
        hash = hash * 31 + digest.receivers.hashCode()
        hash = hash * 31 + digest.subject.hashCode()
        hash = hash * 31 + digest.content.hashCode()
        return hash
    }
}