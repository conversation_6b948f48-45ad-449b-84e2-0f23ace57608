package com.twl.hi.module_email.view_edit.view.eml

import android.os.Bundle
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.model.SelectBottomBean
import com.twl.hi.basic.util.FilePreviewUtil
import com.twl.hi.foundation.model.email.bean.receive.AttachFile
import com.twl.hi.foundation.model.email.bean.receive.EmailInfo
import com.twl.hi.foundation.model.email.constants.MailConstants
import com.twl.hi.module_email.BR
import com.twl.hi.module_email.R
import com.twl.hi.module_email.callback.IEmailEmlFileDetailsCallback
import com.twl.hi.module_email.databinding.EmailFragmentDetailsBinding
import com.twl.hi.module_email.view_edit.edit.EmailEditFragment
import com.twl.hi.module_email.view_edit.view.base.EmailBaseDetailsFragment
import com.twl.hi.module_email.view_edit.view.dialog.DynamicActionBean
import com.twl.hi.module_email.view_edit.view.dialog.MoreAction
import hi.kernel.BundleConstants
import lib.twl.common.util.ToastUtils
import java.io.File

/**
 * <AUTHOR>
 * @Date   2023/12/5 9:23 PM
 * @Desc   eml格式文件详情页
 */
class EmailEmlFileDetailsFragment : EmailBaseDetailsFragment<EmailFragmentDetailsBinding, EmailEmlFileDetailsViewModel>() , IEmailEmlFileDetailsCallback {

    var TAG = "EmailEmlFileDetailsFragment"

    var filePath: String = ""

    companion object {
        fun instance(filePath: String) = EmailEmlFileDetailsFragment().apply {
            arguments = Bundle().also {
                it.putString(BundleConstants.BUNDLE_EMAIL_FILE_PATH, filePath)
            }
        }
    }

    override fun getWebViewDataBinding() = dataBinding.mailWebPreview

    override fun getTitleBarDataBinding() = dataBinding.titleBar

    override fun getBottomBarDataBinding() = dataBinding.bottomBar

    override fun getAttachFileListBinding() = dataBinding.attachFileRoot

    override fun getSpinKitView() = dataBinding.loadingDetails

    override fun getDetailsReceiveSummaryInfoBinding() = dataBinding.receiveInfo

    override fun getSplitLine() = dataBinding.splitLine

    override fun getErrorLayout() = dataBinding.errorLayout

    override fun getEmailTitleBinding() = dataBinding.emailTitle

    override fun getHiScrollView() = dataBinding.scrollView

    override fun initArgument(showProgress: Boolean) {
        filePath = arguments?.getString(BundleConstants.BUNDLE_EMAIL_FILE_PATH) ?:""
        if (filePath.isNotEmpty()) {
            viewModel.setEmlFilePath(filePath)
            if (showProgress) {
                mH.removeCallbacks(loadingProgress)
                mH.postDelayed(loadingProgress, 500)
            }
        } else {
            ToastUtils.failure("文件不存在")
            activity?.finish()
        }
    }

    override fun onBottomMenuClick(action: MoreAction) {
        viewModel.mailLiveData.value?.let {
            when (action) {
                MoreAction.CopyAndEditAction -> onClickCopyAndEdit(it)
                MoreAction.ForwardAsAttachFileAction -> onClickForwardAsAttachFile(it)
                MoreAction.ForwardAction -> onClickForwardMail(it)
                MoreAction.ReplyAllAction -> onClickReplyAll(it)
                MoreAction.ReplyAction -> onClickReplyMail(it)
                else -> {}
            }
        }
    }

    private fun onClickForwardAsAttachFile(it: EmailInfo) {

    }

    override fun onClickReplyAll(emailInfo: EmailInfo) {
        createMailFromEmlFile(emailInfo, MailConstants.CreateMailType.REPLY_ALL)
    }

    private fun onClickCopyAndEdit(it: EmailInfo) {
        createMailFromEmlFile(it, MailConstants.CreateMailType.COPY_AND_EDIT)
    }

    override fun onClickReplyMail(emailInfo: EmailInfo) {
        createMailFromEmlFile(emailInfo, MailConstants.CreateMailType.REPLY)
    }

    override fun onClickForwardMail(emailInfo: EmailInfo) {
        createMailFromEmlFile(emailInfo, MailConstants.CreateMailType.FORWARD)
    }

    override fun onEditExit(changed: Boolean, deleteMail: Boolean) {
        clickTime = 0L
    }

    private fun createMailFromEmlFile(info: EmailInfo, type: Int) {
        if (System.currentTimeMillis() - clickTime <= CLICK_TIME_LIMIT) {
            ToastUtils.ss(getString(R.string.email_please_wait_for_switch_reply_operation))
            return
        }
        clickTime = System.currentTimeMillis()
        viewModel.createMailFromEmlFile(info, type) { emailId ->
            fragmentManager?.let {
                val editFragment = EmailEditFragment.instance(
                    type = EmailEditFragment.TYPE_REPLY_EML_FILE,
                    emailId = emailId,
                    previewType = type,
                    title = "",
                    replyToId = filePath
                )
                editFragment.show(it, "EmailEditFragment_")
                editFragment.setEditListener(this)
            }
        }
    }

    override fun getMenuList(): ArrayList<DynamicActionBean> = arrayListOf(
        DynamicActionBean(getString(R.string.copy_and_edit), R.drawable.email_icon_copy_and_edit, MoreAction.CopyAndEditAction.hashCode()),
    )

    override fun getContentLayoutId(): Int {
        return R.layout.email_fragment_details
    }

    override fun getCallbackVariable(): Int {
        return BR.callback
    }

    override fun getCallback(): Any {
        return this
    }

    override fun getBindingVariable(): Int {
        return BR.viewModel
    }

    override fun onAiSummary(emailInfo: EmailInfo) {
    }

    override fun onClickExpandLabel() {

    }

    override fun onClickAttachFile(file: AttachFile) {
        TLog.info(TAG, "onClickAttachFile $file")
        File(file.emlAttachFilePath).also {
            if (it.exists()) {
                FilePreviewUtil.openAttachFile(activity, it)
            } else {
                ToastUtils.failure("文件不存在")
            }
        }
    }

    override fun onClickWithdrawDetails() {
    }

    override fun onClickSendMailResultDetails() {
    }

    override fun onCancelScheduleSend(emailId: String) {
    }


}