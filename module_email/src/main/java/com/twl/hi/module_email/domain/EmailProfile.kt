package com.twl.hi.module_email.domain

import java.io.Serializable

/**
 * Author : <PERSON>weixia<PERSON> .
 * Date   : On 2024/7/8
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

data class EmailProfile(
    val displayName: String? = "",
    val mailAddress: String? = "",
    val unDelete: Boolean? = true,
    val isEnable: Boolean? = true,
    val accountType: Int,
    val ownerName: String? = ""
) : Serializable {

    fun fixedDisPlayName(): String {
        if (!displayName.isNullOrEmpty()) {
            return displayName
        }
        if (!mailAddress.isNullOrEmpty()) {
            return mailAddress
        }
        return ""
    }

}
