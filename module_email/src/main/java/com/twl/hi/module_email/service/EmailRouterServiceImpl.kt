package com.twl.hi.module_email.service

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.sankuai.waimai.router.annotation.RouterService
import com.twl.hi.export.email.router.EmailPageRouter
import com.twl.hi.export.email.service.IEmailRouterService
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.security.BusinessModuleEnum
import com.twl.hi.module_email.R
import com.twl.hi.module_email.domain.EmailBusinessCentre
import com.twl.hi.module_email.view_edit.edit.EmailEditFragment
import hi.kernel.BundleConstants
import lib.twl.common.util.ActivityAnimType
import lib.twl.common.util.AppUtil
import lib.twl.common.util.ToastUtils

@RouterService(interfaces = [IEmailRouterService::class], key = [EmailPageRouter.EMAIL_SERVICE])
class EmailRouterServiceImpl:
    IEmailRouterService {

    override fun jumpToCreateEmailDialog(activity: FragmentActivity?) {
        if (checkIsEmailForbidden()) return
        activity?.let {
            EmailEditFragment.instance(EmailEditFragment.TYPE_CREATE, "","").show(it.supportFragmentManager, "EmailEditFragment")
        }
    }

    override fun jumpToCreateEmailDialog(activity: FragmentActivity?, bundle: Bundle?) {
        if (checkIsEmailForbidden()) return
        activity?.let {
            EmailEditFragment.instance(EmailEditFragment.TYPE_CREATE, bundle).show(it.supportFragmentManager, "EmailEditFragment")
        }
    }

    override fun jumpToEditEmailDialog(activity: FragmentActivity?, mailId: String?) {
        if (checkIsEmailForbidden()) return
        activity?.let {
            EmailEditFragment.instance(EmailEditFragment.TYPE_EDIT, mailId, "").show(it.supportFragmentManager, "EmailEditFragment")
        }
    }

    override fun jumpToViewEmailDetails(
        context: Context,
        mailId: String,
        fromPush: Boolean
    ) {
        if (checkIsEmailForbidden()) return
        val bundle = Bundle()
        bundle.putBoolean(BundleConstants.BUNDLE_EMAIL_FROM_PUSH, fromPush)
        bundle.putString(BundleConstants.BUNDLE_EMAIL_ID, mailId)
        AppUtil.getDefaultUriRequest(context, EmailPageRouter.EMAIL_DETAILS_PAGE, bundle, ActivityAnimType.DEFAULT).setIntentFlags(Intent.FLAG_ACTIVITY_NEW_TASK).start()
    }

    override fun jumpToEmailSetting(context: Context) {
        if (checkIsEmailForbidden()) return
        AppUtil.getDefaultUriRequest(context, EmailPageRouter.EMAIL_SETTING_PAGE).setIntentFlags(Intent.FLAG_ACTIVITY_NEW_TASK).start()
    }

    override fun jumpToViewEmailFileDetails(activity: Activity?, filePath: String) {
        if (checkIsEmailForbidden()) return
        activity?.let {
            val bundle = Bundle()
            bundle.putString(BundleConstants.BUNDLE_EMAIL_FILE_PATH, filePath)
            AppUtil.getDefaultUriRequest(activity, EmailPageRouter.EMAIL_DETAILS_PAGE, bundle, ActivityAnimType.DEFAULT).setIntentFlags(Intent.FLAG_ACTIVITY_NEW_TASK).start()
        }
    }

    /**
     * 检查邮箱可用性
     */
    private fun checkIsEmailForbidden(): Boolean {
        return ServiceManager.getInstance().securityService.isForbidden(BusinessModuleEnum.EMAIL).apply {
            if (this) {
                ToastUtils.failure(R.string.no_permission_access)
            }
        }
    }
}