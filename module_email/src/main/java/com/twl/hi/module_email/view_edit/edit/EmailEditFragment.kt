package com.twl.hi.module_email.view_edit.edit

import android.animation.Animator
import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.LinearInterpolator
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.documentfile.provider.DocumentFile
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.BottomDialogFragment
import com.twl.hi.basic.PAGE_FROM
import com.twl.hi.basic.SINGLE_CONVERSATION_PAGE_TITLE
import com.twl.hi.basic.adapter.MyMultiTypeAdapter
import com.twl.hi.basic.dialog.DialogUtils
import com.twl.hi.basic.model.FilePreviewBean
import com.twl.hi.basic.util.FilePreviewUtil
import com.twl.hi.basic.util.ThemeUtils
import com.twl.hi.basic.views.group.LinearLayoutManagerWrapper
import com.twl.hi.export.email.bean.MailInfoBean
import com.twl.hi.export.organization.router.OrganizationPageRouter
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.email.bean.receive.AttachFileDownloadResult
import com.twl.hi.foundation.model.email.bean.receive.BshMailGroup
import com.twl.hi.foundation.model.email.bean.receive.EmailInfo
import com.twl.hi.foundation.model.email.bean.receive.MailPeople
import com.twl.hi.foundation.model.email.bean.receive.MailUpdateResponse
import com.twl.hi.foundation.model.email.bean.receive.TemplateSummary
import com.twl.hi.foundation.model.email.constants.MailConstants
import com.twl.hi.foundation.model.email.constants.MailConstants.FolderType
import com.twl.hi.foundation.model.message.MessageForFile
import com.twl.hi.foundation.utils.point.MailPoint
import com.twl.hi.module_email.BR
import com.twl.hi.module_email.R
import com.twl.hi.module_email.callback.IEmailAccountSelectorCallback
import com.twl.hi.module_email.callback.IEmailEditExitCallback
import com.twl.hi.module_email.callback.IEmailEditorCallback
import com.twl.hi.module_email.databinding.EmailAttachFileUploadItemBinding
import com.twl.hi.module_email.databinding.EmailFragmentEditorBinding
import com.twl.hi.module_email.databinding.EmailSendItemMailAccountItemBinding
import com.twl.hi.module_email.domain.AccountInfoBean
import com.twl.hi.module_email.domain.EmailBusinessCentre
import com.twl.hi.module_email.domain.HiContactBean
import com.twl.hi.module_email.domain.MailContactSearchBean
import com.twl.hi.module_email.domain.Mailbox
import com.twl.hi.module_email.domain.ScheduleInfo
import com.twl.hi.module_email.domain.SignatureInfo
import com.twl.hi.module_email.domain.toAccountBean
import com.twl.hi.module_email.main.activity.EmailMainActivity
import com.twl.hi.module_email.main.ui.HiMailEdit
import com.twl.hi.module_email.main.ui.HiScrollView
import com.twl.hi.module_email.main.ui.SearchContactListView
import com.twl.hi.module_email.main.webview.JavaScript
import com.twl.hi.module_email.profile.showEmailProfileDialog
import com.twl.hi.module_email.util.EmailUtil
import com.twl.hi.module_email.util.observe
import com.twl.hi.module_email.view_edit.FileWrapper
import com.twl.hi.module_email.view_edit.MAIL_SEND_TYPE_NORMAL
import com.twl.hi.module_email.view_edit.MAIL_SEND_TYPE_SEPARATE
import com.twl.hi.module_email.view_edit.OutputFileBean
import com.twl.hi.module_email.view_edit.TIME_ERROR
import com.twl.hi.module_email.view_edit.UploadFileBean
import com.twl.hi.module_email.view_edit.UploadImgBean
import com.twl.hi.module_email.view_edit.edit.dialog.AddScheduleListener
import com.twl.hi.module_email.view_edit.edit.dialog.OnEditMoreActionListener
import com.twl.hi.module_email.view_edit.edit.dialog.showAddScheduleDialog
import com.twl.hi.module_email.view_edit.edit.dialog.showEmailEditMoreActionDialog
import com.twl.hi.module_email.view_edit.edit.template.IEmailTemplateListener
import com.twl.hi.module_email.view_edit.edit.template.showTemplateDialog
import com.twl.hi.module_email.view_edit.getImageFiles
import com.twl.hi.module_email.view_edit.getNormalFiles
import com.twl.hi.module_email.view_edit.view.normal.EmailDetailsFragment
import com.twl.hi.webeditor.RichEditor
import com.twl.hi.webeditor.bean.AttachFileMenu
import com.twl.hi.webeditor.bean.BoldMenu
import com.twl.hi.webeditor.bean.FontSizeMenu
import com.twl.hi.webeditor.bean.ImageMenu
import com.twl.hi.webeditor.bean.ItalicMenu
import com.twl.hi.webeditor.bean.ScheduleMenu
import com.twl.hi.webeditor.bean.TemplateMenu
import com.twl.hi.webeditor.bean.UnderlineMenu
import com.twl.hi.webeditor.menu.IMenuStyle
import com.twl.hi.webeditor.menu.MenuType
import com.twl.utils.GsonUtils
import com.twl.utils.file.FileUtils
import hi.kernel.BundleConstants
import lib.twl.common.dialog.CommonDialog
import lib.twl.common.kpswitch.util.KeyboardUtil
import lib.twl.common.permission.PermissionAvoidManager
import lib.twl.common.photoselect.PhotoSelectManager
import lib.twl.common.util.AppUtil
import lib.twl.common.util.DecorToast
import lib.twl.common.util.ExecutorFactory
import lib.twl.common.util.HiPermissionUtil
import lib.twl.common.util.LList
import lib.twl.common.util.ProcessHelper
import lib.twl.common.util.QMUIDisplayHelper
import lib.twl.common.util.QMUIKeyboardHelper
import lib.twl.common.util.TimeDifferenceUtil
import lib.twl.common.util.TimeTag
import lib.twl.common.util.ToastUtils
import lib.twl.common.util.widget.HiPopupWindow
import lib.twl.common.views.time.DateTimePickerView
import lib.twl.common.views.time.adapter.DatePickerWheelAdapter.DatePickerModel
import lib.twl.common.views.time.adapter.TimePickerWheelAdapter.TimePickerModel
import lib.twl.common.views.time.builder.DateTimePickerBuilder
import java.io.File
import java.io.FileNotFoundException
import java.util.Vector
import kotlin.math.abs

/**
 * <AUTHOR>
 * 邮箱编辑页面
 * 包含发送，回复，转发，全部回复
 */
class EmailEditFragment : BottomDialogFragment<EmailFragmentEditorBinding, EmailEmlEditorViewModel>(),
    IEmailEditorCallback,
    TextWatcher, RichEditor.OnTextChangeListener {

    val TAG = "EmailEditFragment"

    val FILE_REQUEST_CODE = 1

    var initType = -1
    var initPreviewType = -1

    // 编辑类型
    var currentType: Int = -1
    var currentPreviewMailType = -1

    var signatureSwitch = false

    // 当前编辑邮件id
    lateinit var mailId: String

    // 回复邮件的id
    lateinit var replyToId: String

    // 邮件编辑变动记录
    private var mailContentAnyModify = false

    // 收件人异常弹框提醒
    var errorMailAddressDialog: CommonDialog? = null

    // 收件人异常弹框提醒
    var mSendNoTitleMailDialog: CommonDialog? = null

    // 大图片提示弹框
    var mShowHasBigPicDialog: CommonDialog? = null

    // 是否发送邮件成功
    var clickedSendMail = false

    // 附件列表
    val fileListAdapter = MyMultiTypeAdapter()

    val handler = Handler()

    // 是否允许发送无主题邮件
    var sendNoTitleMail = false

    // 是否下载正文图片
    var hasDownload = false

    // 是否更新了邮件
    var hasUpdateMail = false

    // 回调监听
    var listener: IEmailEditExitCallback? = null

    val ZIP_FILE_MAX_SIZE = 3 * 1024 * 1024L

    var removeLoadingTime = 0L

    private var mMinutePickerView: DateTimePickerView? = null

    private val mDealLaterTargetDate = StringBuilder()

    private val mDealLaterEndTime = StringBuilder()

    private val lessMenuList = listOf<IMenuStyle>(ScheduleMenu(), TemplateMenu())

    private val moreMenuList = listOf(AttachFileMenu(), ImageMenu(), BoldMenu(), ItalicMenu(), UnderlineMenu(), FontSizeMenu(), ScheduleMenu(), TemplateMenu())

    private val rotateAnim by lazy {
        AnimationUtils.loadAnimation(activity, R.anim.email_rorate_animation).apply {
            interpolator = LinearInterpolator()
        }
    }

    val replaceSingleCidToImgPath = fun(downloadFileResult: AttachFileDownloadResult) {
        if (downloadFileResult.result == 0) {
            hasDownload = true
            JavaScript.replaceCidToLocalImgPath(
                dataBinding.mailContent, downloadFileResult.cid, Uri.fromFile(
                    File(downloadFileResult.savePath)
                ).toString()
            )
        }
    }

    // 超过限制级的内联图片自动转大附件
    val deleteBigInlineImgTag = fun(downloadFileResult: AttachFileDownloadResult) {
        if (downloadFileResult.result == 0) {
            JavaScript.deleteBigInlineImgTag(
                dataBinding.mailContent, downloadFileResult.cid, Uri.fromFile(
                    File(downloadFileResult.savePath)
                ).toString()
            )
        }
    }

    // 加载中提示
    val loadingProgress = Runnable {
        if (isAdded) {
            showProgressDialog(ProcessHelper.getContext().getString(R.string.email_please_wait), false)
        }
    }

    private var detachScheduleDialogUtils : DialogUtils? = null

    override fun getContentLayoutId() = R.layout.email_fragment_editor

    override fun getCallbackVariable() = BR.callback

    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel

    override fun initFragment() {
        initLifecycleOwner()
        initArgument()
        initTextEditor()
        initMailEdit()
        initWebView()
        initAttachFileList()
        initObserve()
    }

    private fun initAttachFileList() {
        QMUIKeyboardHelper.setVisibilityEventListener(
            activity,
            object : QMUIKeyboardHelper.KeyboardVisibilityEventListener {
                override fun onVisibilityChanged(isOpen: Boolean, heightDiff: Int): Boolean {
                    if (isOpen) {
                        dataBinding.funcPanel.isGone = true
                        dataBinding.menuToolbar.setAttachFileMenuSelectedStatus(!isOpen)
                    }
                    return false
                }
            })
        dataBinding.fileList.layoutManager =
            LinearLayoutManagerWrapper(context, LinearLayoutManager.HORIZONTAL, false)
        dataBinding.fileList.adapter = fileListAdapter
        fileListAdapter.register(
            UploadFileBean::class.java,
            R.layout.email_attach_file_upload_item,
            object :
                MyMultiTypeAdapter.ItemViewBinder<EmailAttachFileUploadItemBinding, UploadFileBean> {
                override fun bind(
                    vdb: EmailAttachFileUploadItemBinding?,
                    item: UploadFileBean?,
                    linkIndex: Int
                ) {
                    val params = vdb?.attachFileCl?.layoutParams
                    params?.run {
                        width = QMUIDisplayHelper.dp2px(
                            context,
                            if (item?.isBigFile == true) 200 else 110
                        )
                        vdb.attachFileCl.layoutParams = this
                    }
                    val progressLap = vdb?.progress?.layoutParams
                    progressLap?.run {
                        width = QMUIDisplayHelper.dp2px(
                            context,
                            if (item?.isBigFile == true) 200 else 110
                        )
                        vdb.progress.layoutParams = this
                    }
                    vdb?.setVariable(BR.callback, this@EmailEditFragment)
                    vdb?.setVariable(BR.uploadBean, item)
                    if (item?.showOutputProgress == true) {
                        vdb?.progressOutput?.run {
                            startAnimation(rotateAnim)
                            visibility = View.VISIBLE
                        }
                    } else {
                        vdb?.progressOutput?.run {
                            clearAnimation()
                            visibility = View.GONE
                        }
                    }
                }
            })
    }

    private fun initLifecycleOwner() {
        dataBinding.receive.lifecycleOwner = viewLifecycleOwner
        dataBinding.titleBar.lifecycleOwner = viewLifecycleOwner
        dataBinding.lifecycleOwner = viewLifecycleOwner
    }

    private fun initArgument() {
        arguments?.let {
            this.currentType = it.getInt(BundleConstants.BUNDLE_EMAIL_CREATE_TYPE, 0)
            this.mailId = it.getString(BundleConstants.BUNDLE_EMAIL_ID, "")
            this.replyToId = it.getString(BundleConstants.BUNDLE_EMAIL_REPLY_ID, "")
            this.currentPreviewMailType = it.getInt(BundleConstants.BUNDLE_EMAIL_PREVIEW_MAIL_TYPE, 0)
            val mailInfoBean = it.getParcelable<MailInfoBean>(BundleConstants.BUNDLE_EMAIL_INFO)

            initType = currentType
            initPreviewType = currentPreviewMailType
            viewModel.initMailType(currentType, mailId, replyToId, mailInfoBean, currentPreviewMailType)
            if (currentType == TYPE_CREATE) {
                dataBinding.mailCorner.setBackgroundColor(Color.WHITE)
            }

            it.getString(BundleConstants.BUNDLE_EMAIL_OUTPUT_ID, "").takeIf { mailId -> !mailId.isNullOrEmpty() }?.run {
                // 导出为附件发送，立即创建邮件
                mailContentAnyModify = true
                val title = it.getString(BundleConstants.BUNDLE_EMAIL_OUTPUT_NAME, "无主题")
                createMailIfNeed {
                    val dir = File(activity?.filesDir?.absolutePath + "/email/output/").also { dir ->
                        dir.mkdirs()
                    }
                    val fileName = "${title}.eml"
                    val filepath = "${dir}/${fileName}"
                    TLog.info(TAG, "作为附件转发，$filepath")
                    viewModel.outPutAttachFile(OutputFileBean(this, "", fileName, FileWrapper(File(filepath))))
                }
            }
        }
    }

    /**
     * 编辑器初始化
     */
    private fun initTextEditor() {
        // 菜单点击
        dataBinding.menuToolbar.setMenuListener { menuType ->
            processMenuClick(menuType)
        }
        dataBinding.menuToolbar.setMenuList(moreMenuList)
        dataBinding.mailContent.setRichEditMenu(dataBinding.menuToolbar, moreMenuList)
        dataBinding.mailContent.setOnFocusChangeListener { v, hasFocus ->
            dataBinding.menuToolbar.visibility = View.VISIBLE
            checkInputHintVisible(!hasFocus && dataBinding.mailContent.html.isNullOrEmpty())
            checkMenuList()
        }
        dataBinding.scrollView.setListener(object : HiScrollView.OnSlopDownListener {
            override fun onSlopDown(distance: Float) {
                dataBinding.editorRoot.translationY = distance
            }

            override fun onSlopDownFinish() {
                val propertyValuesHolder = PropertyValuesHolder.ofFloat(
                    "translationY", dataBinding.editorRoot.translationY,
                    dataBinding.editorRoot.height * 1.0f
                )
                val objectAnimator = ObjectAnimator.ofPropertyValuesHolder(
                    dataBinding.editorRoot,
                    propertyValuesHolder
                )
                objectAnimator.duration = 300
                objectAnimator.addListener(object : Animator.AnimatorListener {
                    override fun onAnimationStart(animation: Animator) {

                    }

                    override fun onAnimationEnd(animation: Animator) {
                        onClickCancel()
                    }

                    override fun onAnimationCancel(animation: Animator) {
                    }

                    override fun onAnimationRepeat(animation: Animator) {
                    }
                })
                objectAnimator.start()
            }
        })

        observe(viewModel.sendMailAccountLiveData) {
            it?.run {
                TLog.info(TAG, "sendMailAccountLiveData -> $it ")
                dataBinding.receive.sendMailPeople.text = "$name <${primaryMailAddress}>"
                checkMenuList()
            }
        }
        observe(viewModel.outputResult) {
            if (!it.second) {
                showOutputFailedDialog(it.first)
            }
        }
    }

    private fun showOutputFailedDialog(outputFileBean: OutputFileBean) {
        val builder = CommonDialog.Builder(context)
        builder.setCancelable(false)
            .setLayoutId(R.layout.common_dialog_only_content_layout)
            .add(R.id.tv_sure_common)
            .add(R.id.tv_cancel_common)
            .setDisplayTextById(
                R.id.tv_content,
                "邮件导出失败，请重试"
            )
            .setDisplayTextById(
                R.id.tv_sure_common,
                ProcessHelper.getContext().getString(R.string.retry)
            ).setDisplayTextById(
                R.id.tv_cancel_common,
                ProcessHelper.getContext().getString(R.string.cancel)
            )
            .setGravity(Gravity.CENTER)
            .setCanceledOnTouchOutside(false)
            .setOnItemClickListener { dialog, v ->
                dialog.dismiss()
                viewModel.notifyRemovedOutputFailed(outputFileBean.traceId)
                if (v.id == R.id.tv_sure_common) {
                    viewModel.outPutAttachFile(outputFileBean)
                }
                mShowHasBigPicDialog = null
            }
        mShowHasBigPicDialog = builder.create()
        mShowHasBigPicDialog?.show()
    }

    /**
     * 编辑器菜单点击事件
     */
    private fun processMenuClick(menuType: Int): Boolean {
        when (menuType) {
            MenuType.TYPE_BOLD -> dataBinding.mailContent.setBold()
            MenuType.TYPE_ITALIC -> dataBinding.mailContent.setItalic()
            MenuType.TYPE_UNDLERLINE -> dataBinding.mailContent.setUnderline()
            MenuType.TYPE_IMAGE -> onClickInsertImage()
            MenuType.TYPE_ATTACH_FILE -> onClickUploadAttach()
            MenuType.TYPE_TEMPLATE -> onClickTemplate()
            MenuType.TYPE_SCHEDULE -> onClickSchedule()
            MenuType.FontSizeMenuType.TYPE_SMALL -> dataBinding.mailContent.setFontSize(3)
            MenuType.FontSizeMenuType.TYPE_NORMAL -> dataBinding.mailContent.setFontSize(4)
            MenuType.FontSizeMenuType.TYPE_LARGE -> dataBinding.mailContent.setFontSize(5)
        }
        return false
    }

    private fun onClickTemplate() {
        fragmentManager?.showTemplateDialog(object : IEmailTemplateListener {
            override fun onSelectTemplate(templateSummary: TemplateSummary) {
                createMailIfNeed(true) {
                    viewModel.loadMailTemplate(viewModel.getMailId(), templateSummary)
                }
            }
        })
    }

    private fun onClickSchedule() {
        fragmentManager?.showAddScheduleDialog(viewModel.mailScheduleLiveData.value, object : AddScheduleListener {
            override fun onSave(scheduleInfo: ScheduleInfo?) {
                mailContentAnyModify = true
                scheduleInfo?.run {
                    viewModel.updateSchedule(scheduleInfo)
                }
            }
        })
    }

    private fun onClickUploadAttach() {
        activity?.run {
            if (QMUIKeyboardHelper.isKeyboardVisible(this)) {
                KeyboardUtil.hideKeyboard(dataBinding.mailContent)
                dataBinding.funcPanel.postDelayed({
                    dataBinding.funcPanel.isGone = false
                }, 100)
            } else {
                val visible = dataBinding.funcPanel.visibility == View.VISIBLE
                dataBinding.funcPanel.visibility = if (visible) View.GONE else View.VISIBLE
            }
        }
    }

    private fun onClickInsertImage() {
        if (viewModel.allAttachFiles.getImageFiles().filter {
                !viewModel.tmpDeletedImgs.contains(it.cid)
            }.size >= viewModel.serverMailOption.inlineMaxNum) {
            ToastUtils.ss("单个邮件最多${viewModel.serverMailOption.inlineMaxNum}个图片")
            return
        }
        QMUIKeyboardHelper.hideKeyboard(dataBinding.mailContent)
        // 直接插入图片，则立即创建邮件
        mailContentAnyModify = true
        createMailIfNeed { it ->
            if (it.result == MailConstants.Code.FAILED) {
                return@createMailIfNeed
            }
            PhotoSelectManager.jumpForGalleryFromResultWithZipStrategy(
                activity,
                9.coerceAtMost(viewModel.serverMailOption.inlineMaxNum),
                false,
                ZIP_FILE_MAX_SIZE,
                object : PhotoSelectManager.OnGalleryCountCallBack {
                    override fun onGalleryListener(
                        fileList: MutableList<File>?,
                        originalEnable: Boolean
                    ) {
                        if (fileList.isNullOrEmpty()) {
                            return
                        }

                        var allFileLen = 0L
                        var hasBigFile = false
                        TLog.info(TAG, "${viewModel.serverMailOption}")
                        fileList.forEach { file ->
                            TLog.info(TAG, "file ${file.absolutePath} ${file.length()}")
                            if (file.length() > viewModel.serverMailOption.mailAttachmentMaxSize) {
                                hasBigFile = true
                            } else {
                                allFileLen += file.length()
                            }
                        }
                        if (viewModel.checkAttachFileInLimit(allFileLen)) {
                            return
                        }
                        // 有大于30m的图片则将其转成大附件发送
                        if (hasBigFile) {
                            showHasBigPicDialog(fileList)
                        } else {
                            // 上传图片
                            viewModel.multiUploadImg(fileList.map { f -> FileWrapper(f, f.name) }.toMutableList())
                        }
                    }
                })
        }
    }

    /**
     * 图片尺寸大于30m需要提示
     * 转换为大附件上传
     */
    private fun showHasBigPicDialog(fileList: MutableList<File>) {
        val builder = CommonDialog.Builder(context)
        builder.setCancelable(false)
            .setLayoutId(R.layout.common_dialog_only_content_layout)
            .add(R.id.tv_sure_common)
            .add(R.id.tv_cancel_common)
            .setDisplayTextById(
                R.id.tv_content,
                "超过${viewModel.serverMailOption.mailAttachmentMaxSizeMb}M图片将转成附件发送"
            )
            .setDisplayTextById(
                R.id.tv_sure_common,
                ProcessHelper.getContext().getString(R.string.sure)
            ).setDisplayTextById(
                R.id.tv_cancel_common,
                ProcessHelper.getContext().getString(R.string.cancel)
            )
            .setGravity(Gravity.CENTER)
            .setCanceledOnTouchOutside(false)
            .setOnItemClickListener { dialog, v ->
                dialog.dismiss()
                if (R.id.tv_sure_common == v.id) {
                    mailContentAnyModify = true
                    val normalFileList =
                        fileList.filter { it.length() < viewModel.serverMailOption.mailAttachmentMaxSize }
                            .toMutableList()
                    val bigFileList =
                        fileList.filter { it.length() >= viewModel.serverMailOption.mailAttachmentMaxSize }
                    viewModel.multiUploadImg(normalFileList.map { f -> FileWrapper(f, f.name) }.toMutableList())
                    viewModel.uploadBigFileList(bigFileList.map { f -> FileWrapper(f, f.name) })
                }
                mShowHasBigPicDialog = null
            }
        mShowHasBigPicDialog = builder.create()
        mShowHasBigPicDialog?.show()
    }

    /**
     * 获取打开文件管理器的Intent
     * 某些手机需要不支持，需要兜底
     */
    fun getOpenDocumentIntent(isOrdinary: Boolean): Intent {
        var intent: Intent = if (isOrdinary) { //一般打开文件管理器
            Intent(Intent.ACTION_OPEN_DOCUMENT)
        } else { //部分机型用上边的打不开，这个。比如小米六
            Intent(Intent.ACTION_GET_CONTENT)
        }
        intent.addCategory(Intent.CATEGORY_OPENABLE)
        intent.type = "*/*" //设置Mimetype，我这里是任意文件类型，任意后缀的可以这样写。
        intent.putExtra("android.content.extra.SHOW_ADVANCED", true)
        return intent
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (data == null || resultCode != Activity.RESULT_OK) {
            return
        }

        if (requestCode == FILE_REQUEST_CODE) {
            ExecutorFactory.execWorkTask {
                val uri = data.data
                uri?.let {
                    val file: File = getFileFromUri(uri)
                    val length = file.length()
                    var checkAttachFileLen = 0L
                    if (length in 1..viewModel.serverMailOption.mailAttachmentMaxSize) {
                        checkAttachFileLen = length
                    }
                    if (viewModel.checkAttachFileInLimit(checkAttachFileLen)) {
                        return@execWorkTask
                    }

                    if (length in 1..viewModel.serverMailOption.mailAttachmentMaxSize) {
                        mailContentAnyModify = true
                        viewModel.uploadAttachFile(FileWrapper(file, file.name))
                    } else if (length > viewModel.serverMailOption.mailAttachmentMaxSize && length <= viewModel.serverMailOption.urlAttachFileMaxSize) {
                        mailContentAnyModify = true
                        // 上传大附件
                        viewModel.uploadBigFile(FileWrapper(file, file.name)) {
                            if (!it) {
                                ToastUtils.failure("文件上传失败")
                            }
                        }
                    } else if (length > viewModel.serverMailOption.urlAttachFileMaxSize) {
                        ToastUtils.ss("不支持上传大于${viewModel.serverMailOption.urlAttachmentMaxSizeMb}M的附件")
                    } else {
                        ToastUtils.ss("文件已损坏或不存在")
                    }
                } ?: run {
                    ToastUtils.failure("获取文件失败")
                }
            }
        }
    }

    private fun getFileFromUri(uri: Uri): File {
        try {
            val localContext = context ?: throw FileNotFoundException()
            val documentFile = DocumentFile.fromSingleUri(localContext, uri) ?: throw FileNotFoundException()
            val file = File(
                ServiceManager.getInstance().fileService.cachePath,
                documentFile.name ?: "temp"
            )
            if (!file.exists()) {
                if (!file.isFile) {
                    file.createNewFile()
                }
                FileUtils.copyFile(
                    ProcessHelper.getContext().contentResolver.openInputStream(uri),
                    file
                )
            }
            return file
        } catch (e: FileNotFoundException) {
            TLog.error(TAG, e, "getFileFromUri 异常")
            return File("")
        }
    }

    /**
     * 收件人初始化
     */
    private fun initMailEdit() {
        dataBinding.searchContact.visibility = View.GONE
        with(viewModel.serverMailOption) {
            dataBinding.receive.mailEditReceive.maxPeopleNum = receiverMailMaxSize
            dataBinding.receive.mailEditCc.maxPeopleNum = ccMailMaxSize
            dataBinding.receive.mailEditBsc.maxPeopleNum = bscMailMaxSize
        }
        dataBinding.receive.mailEditReceive.setMailEditListener(object :
            HiMailEdit.MailEditListener {
            override fun onClickCcOrBsc() {
                dataBinding.receive.mailEditGroup.visibility = View.VISIBLE
                dataBinding.receive.mailEditReceive.setBottomLineVisible(false);
                dataBinding.receive.mailEditCc.adjustHeight();
            }

            override fun afterMailEditTextChange(
                editable: Editable?,
                incrementString: String?,
                incrementStartPosition: Int,
                incrementEndPosition: Int
            ) {
                searchContactFromKey(incrementString, dataBinding.receive.mailEditReceive)
            }

            override fun onClickMailPeople(
                mailPeople: MailPeople
            ) {
                viewModel.getMailAccountType(mailPeople)
            }

            override fun onMailPeopleChange(mailPeopleList: MutableList<MailPeople>?) {
                viewModel.mailReceiverLiveData.postValue(mailPeopleList)
            }

            override fun onFocusChange(hasFocus: Boolean) {
                if (hasFocus) {
                    singleTemplateMenu()
                }
            }
        })
        dataBinding.receive.mailEditCc.setMailEditListener(object : HiMailEdit.MailEditListener {
            override fun onClickCcOrBsc() {
            }

            override fun afterMailEditTextChange(
                editable: Editable?,
                incrementString: String?,
                incrementStartPosition: Int,
                incrementEndPosition: Int
            ) {
                searchContactFromKey(incrementString, dataBinding.receive.mailEditCc)
            }

            override fun onClickMailPeople(
                mailPeople: MailPeople
            ) {
                viewModel.getMailAccountType(mailPeople)
            }

            override fun onMailPeopleChange(mailPeopleList: MutableList<MailPeople>?) {
                viewModel.mailCcLiveData.postValue(mailPeopleList)
            }
            override fun onFocusChange(hasFocus: Boolean) {
                if (hasFocus) {
                    singleTemplateMenu()
                }
            }
        })
        dataBinding.receive.mailEditBsc.setMailEditListener(object : HiMailEdit.MailEditListener {
            override fun onClickCcOrBsc() {
            }

            override fun afterMailEditTextChange(
                editable: Editable?,
                incrementString: String?,
                incrementStartPosition: Int,
                incrementEndPosition: Int
            ) {
                searchContactFromKey(incrementString, dataBinding.receive.mailEditBsc)
            }

            override fun onClickMailPeople(
                mailPeople: MailPeople
            ) {
                viewModel.getMailAccountType(mailPeople)
            }

            override fun onMailPeopleChange(mailPeopleList: MutableList<MailPeople>?) {
                viewModel.mailBscLiveData.postValue(mailPeopleList)
            }
            override fun onFocusChange(hasFocus: Boolean) {
                if (hasFocus) {
                    singleTemplateMenu()
                }
            }
        })
        dataBinding.receive.mailEditTitle.setMailEditListener(object : HiMailEdit.MailEditListener{
            override fun onClickCcOrBsc() {
            }

            override fun afterMailEditTextChange(
                editable: Editable?,
                incrementString: String?,
                incrementStartPosition: Int,
                incrementEndPosition: Int
            ) {

            }

            override fun onClickMailPeople(
                mailPeople: MailPeople
            ) {
            }

            override fun onMailPeopleChange(mailPeopleList: MutableList<MailPeople>?) {

            }
            override fun onFocusChange(hasFocus: Boolean) {
                if (hasFocus) {
                    singleTemplateMenu()
                }
            }
        })
        dataBinding.mailContent.setOnTextChangeListener(this)
        dataBinding.receive.mailEditTitle.addTextChangedListener(this)
        dataBinding.receive.mailEditReceive.addTextChangedListener(this)
        dataBinding.receive.mailEditCc.addTextChangedListener(this)
        dataBinding.receive.mailEditBsc.addTextChangedListener(this)
        dataBinding.receive.mailEditTitle.setContentSingleLine(true)
    }

    private fun singleTemplateMenu() {
        checkMenuList()
        dataBinding.menuToolbar.visibility = View.VISIBLE
    }

    /**
     * 搜索关键字匹配的联系人
     */
    private fun searchContactFromKey(incrementString: String?, mailEdit: HiMailEdit) {
        incrementString?.let { inst ->
            if (inst.isNotEmpty()) {
                dataBinding.searchContact.resetLayout(mailEdit)
                dataBinding.searchContact.setListener(object : SearchContactListView.OnClickContactListener {
                    override fun onClickContact(contact: HiContactBean?) {
                        contact?.run {
                            if (!mailEdit.addMailPeople(
                                    MailPeople(
                                        contact.mailName,
                                        contact.mailAddress
                                    )
                                ) && mailEdit.mailPeople.size == mailEdit.maxPeopleNum
                            ) {
                                showAddMailPeopleFailedToast(
                                    mailEdit.headerText,
                                    mailEdit.maxPeopleNum
                                )
                            }
                        }
                        dataBinding.searchContact.isGone = true
                    }

                    override fun onClickMailGroup(mailGroup: BshMailGroup?) {
                        mailGroup?.account?.run {
                            if (!mailEdit.addMailPeople(this) && mailEdit.mailPeople.size == mailEdit.maxPeopleNum) {
                                showAddMailPeopleFailedToast(
                                    mailEdit.headerText,
                                    mailEdit.maxPeopleNum
                                )
                            }
                        }
                        dataBinding.searchContact.isGone = true
                    }

                })
                viewModel.searchContact(incrementString)
            } else {
                viewModel.contactSearchResultLiveData.value = null
            }
        }
    }

    private fun initWebView() {
        dataBinding.mailContent.setEditorFontSize(16)
        dataBinding.mailContent.setEditorLineHeight(24)
        val setting = dataBinding.mailContent.settings
        setting.javaScriptEnabled = true
        setting.allowFileAccess = true
        setting.domStorageEnabled = true
        setting.databaseEnabled = true
        setting.allowFileAccessFromFileURLs = true
        setting.allowUniversalAccessFromFileURLs = true
        dataBinding.mailContent.setOnInitialLoadListener {
            TLog.info(TAG, "OnInitialLoad OK")
        }
        JavaScript.registerCallback { fileId ->
            viewModel.deleteUnusedAttachFile(fileId)
        }
        JavaScript.registerImageRemoveCallback { cid ->
            viewModel.recordDeletedImg(cid)
        }
        dataBinding.mailContent.setEditorFontColor(
            ProcessHelper.getContext().getColor(R.color.email_color_111928)
        )
        dataBinding.mailContent.addJavascriptInterface(JavaScript, "HiJavaScript")
    }

    private fun initObserve() {
        // 邮件信息
        viewModel.mailInfoLiveData.observe(this, object : Observer<EmailInfo> {
            override fun onChanged(it: EmailInfo?) {
                TLog.info(TAG, "mailInfoLiveData, $it")
                it?.let {
                    it.receiverList?.forEach { toAdd ->
                        dataBinding.receive.mailEditReceive.run {
                            if (!addMailPeople(toAdd) && mailPeople.size == maxPeopleNum) {
                                showAddMailPeopleFailedToast(headerText, maxPeopleNum)
                            }
                        }
                    }
                    it.carbonCopyList?.forEach { toAdd ->
                        dataBinding.receive.mailEditCc.run {
                            if (!addMailPeople(toAdd) && mailPeople.size == maxPeopleNum) {
                                showAddMailPeopleFailedToast(headerText, maxPeopleNum)
                            }
                        }
                    }
                    it.encryptCopyList?.forEach { toAdd ->
                        dataBinding.receive.mailEditBsc.run {
                            if (!addMailPeople(toAdd) && mailPeople.size == maxPeopleNum) {
                                showAddMailPeopleFailedToast(headerText, maxPeopleNum)
                            }
                        }
                    }
                    dataBinding.receive.mailEditTitle.setText(it.title)
                    if (currentType != TYPE_CREATE && !it.body.isNullOrEmpty() && !viewModel.updateMailInfoByTemplate) {
                        if (currentPreviewMailType != TYPE_EDIT) {
                            dataBinding.mailContent.setReplyHtml(it.body, TYPE_COPY_EDIT != currentPreviewMailType) { result ->
                                handlePageLoadFinish(it)
                            }
                        } else {
                            dataBinding.mailContent.setEditHtml(it.body) { result ->
                                handlePageLoadFinish(it)
                            }
                        }
                        QMUIKeyboardHelper.showKeyboardDelay(activity)
                    } else {
                        dataBinding.mailContent.setUpEditorMinHeight()
                    }
                    viewModel.updateMailInfoByTemplate = false
                    requestFocusPosition(it)
                    val type = if (it.isSeparatelySend) {
                        TLog.info(TAG, "MAIL_SEND_TYPE_SEPARATE")
                        MAIL_SEND_TYPE_SEPARATE
                    } else {
                        TLog.info(TAG, "MAIL_SEND_TYPE_NORMAL")
                        MAIL_SEND_TYPE_NORMAL
                    }
                    switchMailSendType(type)
                }
                checkInputHintVisible(dataBinding.mailContent.html.isNullOrEmpty())
            }
        })
        // 会议邀请
        viewModel.mailScheduleLiveData.observe(this, object : Observer<ScheduleInfo?> {
            override fun onChanged(t: ScheduleInfo?) {
                checkMenuList()
            }
        })
        // 收件人抄送人等搜索结果
        viewModel.contactSearchResultLiveData.observe(
            this,
            object : Observer<List<MailContactSearchBean>> {
                override fun onChanged(it: List<MailContactSearchBean>?) {
                    if (LList.isEmpty(it)) {
                        dataBinding.searchContact.setContactList(null)
                        return
                    }
                    dataBinding.searchContact.visibility = View.VISIBLE
                    dataBinding.searchContact.setContactList(it)
                }
            })

        // 创建邮件结果
        viewModel.createMutableLiveData.observe(this, object : Observer<MailUpdateResponse> {
            override fun onChanged(it: MailUpdateResponse?) {
                it?.let {
                    TLog.info(TAG, "create ${it.result} ${it?.emailInfo?.id}")
                    if (it.result == MailConstants.Code.SUCCESS) {
                        viewModel.fillNewMailContent(it.emailInfo)
                        initJsEnv(it.emailInfo.id) {
                            updateMailInfoDelay(-1)
                        }
                    } else {
                        viewModel.hasMailCreated = false
                        ToastUtils.failure("网络异常，邮件创建失败")
                        TLog.error(TAG, "邮件创建失败 ${it.mailError}")
                    }
                }
            }
        })
        // 更新邮件结果
        viewModel.updateMutableLiveData.observe(this, object : Observer<MailUpdateResponse> {
            override fun onChanged(it: MailUpdateResponse?) {
                it?.let {
                    TLog.info(TAG, "update ${it.result}")
                    if (it.result != MailConstants.Code.SUCCESS) {
                        ToastUtils.failure(
                            ProcessHelper.getContext().getString(R.string.email_update_error)
                        )
                        TLog.info(TAG, "邮件更新失败 ${it.mailError}")
                    }
                }
            }
        })
        // 发送邮件结果
        viewModel.sendMutableLiveData.observe(this, object : Observer<MailUpdateResponse> {
            override fun onChanged(it: MailUpdateResponse?) {
                TLog.info(TAG, "send ${it?.result}")
                handler.removeCallbacks(sendMailProgress)
                dismissProgressDialog()
                it?.let {
                    MailPoint.pointMailSendScenerioCost(
                        TimeDifferenceUtil.getInstance().finish(TimeTag.MAIL_SEND_MAIL), getMailSize()
                    )
                    if (it.result == MailConstants.Code.SUCCESS) {
                        clickedSendMail = true
                        QMUIKeyboardHelper.hideKeyboard(dataBinding.mailContent)
                        val window = activity
                        DecorToast.toastSuccessClickAble(
                            window,
                            ProcessHelper.getContext().getString(R.string.email_send_success), "查看", 5000
                        ) {
                            // 切换发件账号并定位至已发送文件夹
                            switchShowMailFolderWhenClick(window, FolderType.SENDED)
                        }
                        dialog?.cancel()
                    } else {
                        clickedSendMail = false
                        if (isAdded) {
                            showErrorDialog(
                                ProcessHelper.getContext().getString(R.string.send_failed),
                                ProcessHelper.getContext()
                                    .getString(R.string.email_please_check_network)
                            )
                        }
                        TLog.info(TAG, "${it.mailError}")
                    }
                }
            }
        })
        // 图片上传插入开始
        viewModel.imgInsertStart.observe(this, object : Observer<MutableList<UploadImgBean>> {
            override fun onChanged(it: MutableList<UploadImgBean>?) {
                it?.let {
                    handleImageInsertStartEvent(it)
                }
            }
        })
        // 图片进度
        viewModel.imgInsertProgress.observe(this, object : Observer<UploadImgBean> {
            override fun onChanged(uploadImgBean: UploadImgBean?) {
                uploadImgBean?.run {
                    handleImageInsertProgressUpdateEvent(uploadImgBean)
                }
            }
        })
        // 图片上传插入结束
        viewModel.imgInsertEnd.observe(this, object : Observer<UploadImgBean> {
            override fun onChanged(it: UploadImgBean?) {
                it?.let { image ->
                    handleImageInsertEndEvent(image)
                }
            }
        })
        // 正文图片附件
        viewModel.attachFileMultiDownloadLiveData.observe(
            this,
            object : Observer<AttachFileDownloadResult> {
                override fun onChanged(it: AttachFileDownloadResult?) {
                    it?.let {
                        TLog.info(TAG, "SingleFileDownloadResult ${it.cid} ${it.savePath}")
                        replaceSingleCidToImgPath(it)
                    }
                }
            })
        viewModel.bigInlineImgDelete.observe(this) {
            it?.let {
                TLog.info(TAG, "bigInlineImgDelete -> $it ")
                deleteBigInlineImgTag(it)
            }
        }
        // 加载进度，编辑或者回复邮件时，如果是大邮件，可能耗时
        viewModel.queryMailDetailsStartLiveData.observe(this, object : Observer<Boolean> {
            override fun onChanged(start: Boolean?) {
                start?.let {
                    if (start) {
                        handler.postDelayed(loadingProgress, 500)
                    } else {
                        handler.removeCallbacks(loadingProgress)
                        dismissProgressDialog()
                    }
                }
            }
        })

        // 邮件非正文附件列表
        viewModel.uploadFileListLiveData.observe(
            this,
            object : Observer<MutableList<UploadFileBean>> {
                override fun onChanged(list: MutableList<UploadFileBean>?) {
                    list?.let {
                        fileListAdapter.data = it.toList()
                        fileListAdapter.notifyDataSetChanged()
                        moreMenuList.forEach { style ->
                            if (style.menuType == MenuType.TYPE_ATTACH_FILE) {
                                style.menuShowNumber = it.size
                            }
                        }
                        dataBinding.menuToolbar.setMenuList(moreMenuList)
                        if (fileListAdapter.data.size > 2) {
                            dataBinding.fileList.scrollToPosition(fileListAdapter.data.size - 1)
                        }
                    }
                }
            })
        viewModel.uploadingNumberLiveDate.observe(this, object : Observer<Int> {
            override fun onChanged(number: Int?) {
                val uploadingNumber = number ?: 0
                isCancelable = uploadingNumber == 0
                TLog.info(TAG, "uploadingNumberLiveDate $uploadingNumber $isCancelable")
            }
        })

        viewModel.signatureInfoLiveData.observe(this, object : Observer<SignatureInfo> {
            override fun onChanged(signatureInfo: SignatureInfo?) {
                signatureInfo?.run {
                    if (this.signature.isNullOrEmpty()) {
                        return
                    }
                    dataBinding.mailContent.setSignatureHtml(signatureInfo.signature)
                }
            }
        })

        //防止在首页打开时，安全禁令下发，这个Fragment不关闭，因此订阅禁令通知，主动关闭
        ServiceManager.getInstance().securityService.addProhibitionIsAdapterEvent().observe(viewLifecycleOwner){
            dismiss()
        }

        // 监听账号刷新更新EmailBusinessCentre账号列表cache
        observe(EmailBusinessCentre.obtainAccountList()) {
            TLog.info(TAG, "账号列表变更 -> ${it.size} ")
        }
        viewModel.templateBodyLiveData.observe(this, object : Observer<String> {
            override fun onChanged(body: String?) {
                if (body.isNullOrEmpty().not()) {
                    body ?: return
                    dataBinding.mailContent.insertTextAtCursor(body)
                    dataBinding.mailContent.requestFocus()
                    dataBinding.mailContent.isSelected = true
                    QMUIKeyboardHelper.showKeyboardDelay(activity)
                    checkInputHintVisible(!dataBinding.mailContent.hasFocus() && dataBinding.mailContent.html.isNullOrEmpty())
                    JavaScript.fetchAllCidImgFromHtml(dataBinding.mailContent) { cidList: String ->
                        viewModel.handleCidList(cidList)
                    }
                }
            }
        })
        viewModel.interestingMailAccountAddress.observe(
            this,
            Observer { (userId, emailProfile, type) ->
                if (emailProfile.accountType == MailConstants.MailAccountType.PERSONAL) {
                    val bundle = Bundle()
                    bundle.putString(BundleConstants.BUNDLE_DATA_LONG, userId)
                    bundle.putString(PAGE_FROM, SINGLE_CONVERSATION_PAGE_TITLE)
                    AppUtil.startUri(
                        activity,
                        OrganizationPageRouter.USER_INFO_ACTIVITY,
                        bundle
                    )
                } else {
                    if (emailProfile.accountType == MailConstants.MailAccountType.EXTERNAL) {
                        fragmentManager?.showEmailProfileDialog(
                            emailProfile,
                            null,
                            true
                        )
                    } else {
                        fragmentManager?.showEmailProfileDialog(
                            emailProfile.mailAddress ?: "",
                            null,
                            true
                        )
                    }
                }
            })
        viewModel.mailSendTypeLiveData.observe(this, Observer {
            if (it == MAIL_SEND_TYPE_NORMAL) {
                dataBinding.receive.mailEditReceive.setMailPeopleList(viewModel.mailReceiverLiveData.value?: mutableListOf())
                dataBinding.receive.mailEditCc.isVisible = dataBinding.receive.mailEditCc.mailPeople?.isNotEmpty() == true
                dataBinding.receive.mailEditBsc.isVisible = dataBinding.receive.mailEditBsc.mailPeople?.isNotEmpty() == true
                dataBinding.receive.mailEditReceive.setCcBscVisible(dataBinding.receive.mailEditBsc.isVisible.not() && dataBinding.receive.mailEditCc.isVisible.not())
                dataBinding.receive.mailEditReceive.headerText = "收件人："
            } else {
                val mergedList = viewModel.prepareSendSeparatelyData()
                val limit = viewModel.serverMailOption.receiverMailMaxSize
                if (mergedList.size > limit) {
                    ToastUtils.ss("所有收件人超过${limit}个不可分别发送")
                    switchMailSendType()
                    return@Observer
                }
                dataBinding.receive.mailEditCc.isVisible = false
                dataBinding.receive.mailEditBsc.isVisible = false
                dataBinding.receive.mailEditReceive.setCcBscVisible(false)
                dataBinding.receive.mailEditReceive.setBottomLineVisible(true)
                dataBinding.receive.mailEditReceive.setMailPeopleList(mergedList)
                dataBinding.receive.mailEditBsc.setMailPeopleList(listOf())
                dataBinding.receive.mailEditCc.setMailPeopleList(listOf())
                dataBinding.receive.mailEditReceive.headerText = "分别发送："
            }
        })
    }

    private fun checkMenuList() {
        val baseMenuList = if (dataBinding.mailContent.hasFocus()) moreMenuList else lessMenuList
        val isPersonalEmail = viewModel.sendMailAccountLiveData.value?.isPersonAccount == true
        val menuList = if (viewModel.mailScheduleLiveData.value?.startDate.isNullOrEmpty() && isPersonalEmail) {
            baseMenuList
        } else {
            baseMenuList.filterNot { it.menuType == MenuType.TYPE_SCHEDULE }
        }
        dataBinding.menuToolbar.setMenuList(menuList)
        dataBinding.mailContent.setRichEditMenu(dataBinding.menuToolbar, menuList)
    }

    private fun switchShowMailFolderWhenClick(window: FragmentActivity?, targetFolderType: Int) {
        TLog.info(TAG, "switch to send address folder -> ${viewModel.sendMailAccountId} $targetFolderType")
        if (viewModel.sendMailAccountId != ServiceManager.getInstance().emailService.currentCacheAccountId) {
            val account = ServiceManager.getInstance().emailService.getMailAccountInfoById(viewModel.sendMailAccountId)
            account?.run {
                ServiceManager.getInstance().emailService.switchMailAccount(this.mailboxId, this.accountAddress) { code, list ->
                    TLog.info(TAG, "switchMailAccount to send address -> $code ")
                    if (code == MailConstants.Code.SUCCESS) {
                        ServiceManager.getInstance().emailService.selectMailBoxAt(targetFolderType)
                    }
                }
            }
        } else {
            ServiceManager.getInstance().emailService.selectMailBoxAt(targetFolderType)
        }
        if (window is EmailMainActivity) window.finish()
    }

    private fun handleImageInsertEndEvent(image: UploadImgBean) {
        TLog.info(TAG, "handleImageInsertEndEvent -> $image ")
        val current = System.currentTimeMillis()
        if (current - removeLoadingTime > 50) {
            removeLoadingTime = current
            stopImgUploadLoadingAnimation(image)
        } else {
            ExecutorFactory.execMainTaskDelay({
                stopImgUploadLoadingAnimation(image)
            }, 50 + abs(current - removeLoadingTime))
            removeLoadingTime = System.currentTimeMillis() + 50
        }
    }

    private fun handleImageInsertProgressUpdateEvent(uploadImgBean: UploadImgBean) {
        TLog.info(TAG, "handleImageInsertProgressUpdateEvent -> ${uploadImgBean} ")
        dataBinding.mailContent.updateImageProgress(
            uploadImgBean.cid,
            uploadImgBean.progress
        )
    }

    /**
     * 处理附件插入逻辑
     * 区分正常主动插入以及eml文件解析附件插入逻辑
     */
    private fun handleImageInsertStartEvent(it: MutableList<UploadImgBean>) {
        TLog.info(TAG, "handleImageInsertStartEvent -> ${it.size} ")
        if (it.isNotEmpty()) {
            val emlAttachFile = it.filter { it.isEmlAttachFile }
            val normalAttach = it.filter { it.isEmlAttachFile.not() }
            if (emlAttachFile.isNotEmpty()) {
                dataBinding.mailContent.updateEmlImageList(GsonUtils.getGson().toJsonTree(emlAttachFile))
            }
            if (normalAttach.isNotEmpty()) {
                dataBinding.mailContent.insertImageList(GsonUtils.getGson().toJsonTree(normalAttach))
            }
        }
    }

    /**
     * 初始化光标位置设置
     */
    private fun requestFocusPosition(email: EmailInfo?) {
        val receiveEmpty = email?.receiverList?.isEmpty() ?: true
        val titleEmpty = email?.title?.isEmpty() ?: true
        if (dataBinding.mailContent.isSelected) return
        if (receiveEmpty) {
            dataBinding.receive.mailEditReceive.requestEditInputFocus()
            return
        }
        if (titleEmpty) {
            dataBinding.receive.mailEditTitle.requestEditInputFocus()
            return
        }
        dataBinding.mailContent.focusEditorOnStartPosition()
    }

    private fun stopImgUploadLoadingAnimation(image: UploadImgBean) {
        if (image.imageWidth == -1) {
            dataBinding.mailContent.stopInsertImageLoading(image.uri.toString(), image.cid)
        } else {
            dataBinding.mailContent.stopInsertImageLoading(
                image.uri.toString(),
                image.cid,
                image.imageWidth
            )
        }
    }

    private fun handlePageLoadFinish(mail: EmailInfo) {
        // 先隐藏大附件html，后续
        JavaScript.hideAttachBodyOnLoad(dataBinding.mailContent);
        // html加载后，才能查询到包含cid的图片标签
        JavaScript.fetchAllCidImgFromHtml(dataBinding.mailContent) {
            it.let {
                viewModel.handleCidList(it)
            }
        }
        // 隐藏大附件html
        val bigAttachFiles = mail.attachedFiles?.filter { file -> file.isBigAttach }
        TLog.info(TAG, "hideAttachDivByFileIds size ${bigAttachFiles?.size ?: 0}")
        if (bigAttachFiles.isNullOrEmpty()) {
            JavaScript.showAttachBodyWhenNoBigAttach(dataBinding.mailContent)
        } else {
            JavaScript.hideAttachDivByFileIds(
                dataBinding.mailContent,
                GsonUtils.getGson().toJsonTree(bigAttachFiles)
            )
        }
        JavaScript.adaptNoahHtml(dataBinding.mailContent)
        initJsEnv(mail.id)
    }

    private fun initJsEnv(id: String, block: (() -> Unit)? = null) {
        TLog.info(TAG, "initJsEnv -> $signatureSwitch $id ")
        if (signatureSwitch) {
            replaceSignature(
                ServiceManager.getInstance().emailService.getMailAccountInfoById(viewModel.sendMailAccountId)?.toAccountBean()
                    ?: viewModel.sendMailAccountLiveData.value
            ) {
                ExecutorFactory.execMainTask {
                    JavaScript.initWebViewEditor(dataBinding.mailContent, id) {
                        dismissProgressDialog()
                        block?.invoke()
                    }
                }
            }
            signatureSwitch = false
        } else {
            JavaScript.initWebViewEditor(dataBinding.mailContent, id) {
                block?.invoke()
            }
        }
    }

    override fun onClickConfirm() {
        viewModel.sendMailEnable.value?.let {
            if (it != 3) {
                viewModel.updateSendMailEnableStatus()
                return
            }
        }

        QMUIKeyboardHelper.hideKeyboard(dataBinding.mailContent)
        TimeDifferenceUtil.getInstance().start(TimeTag.MAIL_SEND_MAIL)
        checkMailSendCondition(false) { email, checkResult ->
            realSendMail(email)
        }
    }

    /**
     * 获取邮件大小
     */
    private fun getMailSize(): Long {
        val content = dataBinding.mailContent.html
        val contentSize = content?.toByteArray(Charsets.UTF_8)?.size ?: 0
        val attachFileSize = viewModel.getAttachFileSize()
        TLog.info(TAG, "mail size %d %d", contentSize, attachFileSize)
        return contentSize + attachFileSize
    }

    /**
     * 发送前检测参数
     */
    private fun checkMailSendCondition(scheduleSend: Boolean, block: ((EmailInfo, Boolean) -> Unit)?) {
        prepareMailImgToSend { html, text ->
            val length = text.length
            val bodyMaxLength = viewModel.serverMailOption.bodyMaxLength
            if (length >= bodyMaxLength) {
                ToastUtils.sl("正文文本最多${bodyMaxLength}字，当前${length}字")
                return@prepareMailImgToSend
            }
            if (getMailSize() > viewModel.serverMailOption.mailMaxSize) {
                ToastUtils.sl("该邮件大小已超过最大限制${viewModel.serverMailOption.mailMaxSizeMb}MB")
                return@prepareMailImgToSend
            }
            val email = getMailInfoFromEditor(html)
            if (email == null) {
                TLog.info(TAG, "send email info error")
                showErrorDialog(
                    ProcessHelper.getContext().getString(R.string.email_send_error),
                    ProcessHelper.getContext().getString(R.string.email_please_check_network)
                )
                return@prepareMailImgToSend
            }

            val title = email.title
            if (title.isNullOrEmpty() && !sendNoTitleMail) {
                showNoTitleMailErrorDialog(
                    ProcessHelper.getContext().getString(R.string.email_whether_send_no_title_mail),
                    "",
                    ProcessHelper.getContext().getString(
                        R.string.email_cancel
                    )
                ) {
                    checkMailAddressStatus(email, scheduleSend, block)
                }
                return@prepareMailImgToSend
            }

            checkMailAddressStatus(email, scheduleSend, block)
        }
    }

    private fun checkMailAddressStatus(
        email: EmailInfo,
        scheduleSend: Boolean,
        block: ((EmailInfo, Boolean) -> Unit)?
    ): Boolean {
        if (!email.id.isNullOrEmpty() && scheduleSend.not()) {
            MailPoint.pointClickSendMail(
                email.id,
                !(ServiceManager.getInstance().emailService.currentCacheAccountInfo
                    ?.toAccountBean()?.isPersonAccount ?: true),
                !fileListAdapter.data.isNullOrEmpty(),
                viewModel.imgUploadAll.isNotEmpty()
            )
        }

        val hasInvalidMailAddress = email.receiverList.existsInvalidAddress() || email.carbonCopyList.existsInvalidAddress() || email.encryptCopyList.existsInvalidAddress()

        if (hasInvalidMailAddress) {
            showErrorDialog(
                "提示",
                ProcessHelper.getContext().getString(R.string.email_receive_mail_address_error)
            )
            return true
        }

        block?.invoke(email, true)
        return false
    }

    private fun List<MailPeople>.existsInvalidAddress() =
        any { !EmailUtil.validMailAddress(it.mailAddress) }


    private fun realSendMail(email: EmailInfo) {
        viewModel.sendMail(email)
        viewModel.sendMailEnable.postValue(0)
        handler.postDelayed(sendMailProgress, 500)
    }

    private fun realSendMailSchedule(email: EmailInfo, startDate: String, startTime: String) {
        viewModel.sendMailSchedule(email, startDate, startTime) { code, errorInfo: String? ->
            TLog.info(TAG, "realSendMailSchedule result $code $errorInfo")
            handler.removeCallbacks(sendMailProgress)
            dismissProgressDialog()
            if (code == MailConstants.Code.SUCCESS) {
                clickedSendMail = true
                QMUIKeyboardHelper.hideKeyboard(dataBinding.mailContent)
                val window = activity
                DecorToast.toastSuccessClickAble(
                    window,
                    ProcessHelper.getContext().getString(R.string.email_send_success), "查看", 5000
                ) {
                    // 切换发件账号并定位至草稿箱
                    switchShowMailFolderWhenClick(window, FolderType.DRAFTS)
                }
                dialog?.cancel()
            } else {
                clickedSendMail = false
                if (code == TIME_ERROR) {
                    ToastUtils.failure(ProcessHelper.getContext().getString(R.string.email_schedule_time_invalid))
                } else {
                    if (errorInfo.isNullOrEmpty()) {
                        if (isAdded) showErrorDialog(
                            ProcessHelper.getContext().getString(R.string.email_schedule_send_failed),
                            ProcessHelper.getContext()
                                .getString(R.string.email_please_check_network)
                        )
                    } else {
                        ToastUtils.failure(errorInfo)
                    }
                }
            }
        }
        viewModel.sendMailEnable.postValue(0)
        handler.postDelayed(sendMailProgress, 500)
    }

    private val sendMailProgress = Runnable {
        showProgressDialog(ProcessHelper.getContext().getString(R.string.email_sending))
    }

    private fun showAddMailPeopleFailedToast(source: String? = "", threshold: Int) {
        val fixed = if (source.isNullOrEmpty().not()) {
            source?.subSequence(0, source.length - 1)
        } else source
        ToastUtils.ss("${fixed}最多" + threshold + "个邮箱地址")
    }

    /**
     * 发送无主题邮件提示框
     */
    private fun showNoTitleMailErrorDialog(title: String, content: String, cancel: String? = "", block: (() -> Unit)?) {
        if (mSendNoTitleMailDialog == null) {
            val builder = CommonDialog.Builder(context)
            builder.setCancelable(false)
                .setLayoutId(R.layout.common_dialog_with_title_layout)
                .add(R.id.tv_sure_common)
                .add(R.id.tv_cancel_common)
                .setDisplayTextById(
                    R.id.tv_content,
                    content
                )
                .setDisplayTextById(
                    R.id.tv_sure_common,
                    ProcessHelper.getContext().getString(R.string.sure)
                )
                .setDisplayTextById(
                    R.id.tv_title,
                    title
                ).setDisplayTextById(
                    R.id.tv_cancel_common,
                    cancel
                )
                .setGravity(Gravity.CENTER)
                .setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .setOnItemClickListener { dialog, v ->
                    dialog.dismiss()
                    if (R.id.tv_sure_common == v.id) {
                        sendNoTitleMail = true
                        block?.invoke()
                    }
                    mSendNoTitleMailDialog = null
                }
            mSendNoTitleMailDialog = builder.create()
        }
        mSendNoTitleMailDialog?.show()
    }

    private fun showErrorDialog(title: String, content: String, confirm: String? = "", cancel: String? = "", block: (() -> Unit)? = null) {
        if (errorMailAddressDialog == null) {
            val builder = CommonDialog.Builder(context)
            builder.setCancelable(false)
                .setLayoutId(R.layout.common_dialog_with_title_layout)
                .add(R.id.tv_sure_common)
                .add(R.id.tv_cancel_common)
                .setDisplayTextById(
                    R.id.tv_content,
                    content
                )
                .setDisplayTextById(
                    R.id.tv_sure_common,
                    if (confirm.isNullOrEmpty()) ProcessHelper.getContext().getString(R.string.sure) else confirm
                )
                .setDisplayTextById(
                    R.id.tv_title,
                    title
                ).setDisplayTextById(
                    R.id.tv_cancel_common,
                    cancel
                )
                .setGravity(Gravity.CENTER)
                .setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .setOnItemClickListener { dialog, v ->
                    dialog.dismiss()
                    errorMailAddressDialog = null
                    viewModel.updateSendMailEnableStatus()
                    if (v.id == R.id.tv_sure_common && block != null) {
                        block()
                    }
                }
            errorMailAddressDialog = builder.create()
        }
        errorMailAddressDialog?.show()
    }

    override fun onClickCancel() {
        val uploading = viewModel.uploadingNumber.get()
        val imgUploadingNumber = viewModel.imgUploadingNumber.get()
        TLog.info(TAG, "onClickCancel uploadingNumber ${uploading}")
        if (uploading > 0) {
            ToastUtils.sl(if (imgUploadingNumber != 0) R.string.email_insert_pic else R.string.email_insert_attach_file)
            return
        }
        QMUIKeyboardHelper.hideKeyboard(dataBinding.editorRoot)
        dialog?.cancel()
    }

    override fun onClickIcon(view: View) {
        QMUIKeyboardHelper.hideKeyboard(dataBinding.mailContent)
        val mailPeople = viewModel.getUserMailPeople()
        fragmentManager?.showEmailEditMoreActionDialog(
            viewModel.sendMailEnable.value?.run { this >= 3 } ?: false,
            mDataBinding.receive.mailEditTitle.contentText,
            mailPeople.formatSender(),
            viewModel.mailSendTypeLiveData.value,
            object : OnEditMoreActionListener {
                override fun onScheduleSend() {
                    showScheduleSendMailDialog()
                }

                override fun onSeparatelySend() {
                    switchMailSendType()
                }
            })
    }

    private fun switchMailSendType(type: Int? = null) {
        viewModel.switchMailSendType(type)
        mailContentAnyModify = true
    }

    private fun showScheduleSendMailDialog() {
        mMinutePickerView = DateTimePickerBuilder(activity) { datePickerModel: DatePickerModel, timePickerModel: TimePickerModel ->
            mDealLaterTargetDate.setLength(0)
            mDealLaterTargetDate.append(datePickerModel.toString())
            mDealLaterEndTime.setLength(0)
            mDealLaterEndTime.append(timePickerModel.pickerViewText)
        }.setLayoutRes(R.layout.email_pickerview_change_minute) { v: View ->
            val tvJump = v.findViewById<View>(R.id.tv_jump)
            val tvCancel = v.findViewById<View>(R.id.tv_cancel)
            val tvSubmit = v.findViewById<View>(R.id.tv_submit)
            tvSubmit.setBackgroundResource(if (ThemeUtils.useNewTheme) R.drawable.bg_selector_common_button_primary else R.drawable.bg_corner_8_color_5d68e8)
            tvJump.setOnClickListener { jumpTv: View? ->
                mMinutePickerView?.dismiss()
            }
            tvCancel.setOnClickListener { cancelTv: View? -> mMinutePickerView?.dismiss() }
            tvSubmit.setOnClickListener { submitTv: View? ->
                checkMailSendCondition(true) { email, checkResult ->
                    if (checkResult) {
                        mMinutePickerView?.dismiss()
                        realSendMailSchedule(email, mDealLaterTargetDate.toString(), mDealLaterEndTime.toString())
                    }
                }
            }
        }.isDialog(true)
            .setItemVisibleCount(5)
            .setLayoutGravity(Gravity.BOTTOM)
            .setDividerColor(Color.TRANSPARENT)
            .setTextColorCenter(Color.BLACK) //设置选中项文字颜色
            .setContentTextSize(18)
            .build()
        mMinutePickerView?.show()
    }

    override fun onClickTitle() {
    }

    override fun cancelUpload(uploadBean: UploadFileBean) {
        viewModel.cancelUploadFile(uploadBean)
        mailContentAnyModify = true
    }

    override fun previewAttachFile(uploadBean: UploadFileBean) {
        TLog.info(EmailDetailsFragment.TAG, "previewAttachFile $uploadBean")
        // 本地存在直接打开
        if (uploadBean.filePath.isNotEmpty() && File(uploadBean.filePath).exists()) {
            FilePreviewUtil.openAttachFile(activity, File(uploadBean.filePath))
            return
        }

        if (uploadBean.isBigFile && uploadBean.expireTime > 0 && System.currentTimeMillis() > uploadBean.expireTime) {
            ToastUtils.sl("附件已失效")
        }

        // 大附件特殊处理。根据下载地址打开下载页面
        if (uploadBean.isBigFile) {
            viewModel.queryLocalBigFile(uploadBean.fileUrl) { exist: Boolean, fileLocal: File? ->
                if (exist) {
                    FilePreviewUtil.openAttachFile(activity, fileLocal)
                } else {
                    if (!uploadBean.showOutputProgress) {
                        val filePreviewBean = FilePreviewBean()
                        filePreviewBean.fileId = uploadBean.fileId
                        filePreviewBean.name = uploadBean.titleName
                        filePreviewBean.fileInfo =
                            MessageForFile.FileInfo(
                                uploadBean.fileUrl,
                                uploadBean.titleName,
                                uploadBean.fileSize
                            )
                        filePreviewBean.url = uploadBean.fileUrl
                        filePreviewBean.from = 2
                        FilePreviewUtil.openFilePreviewByFilePreviewBean(
                            activity,
                            filePreviewBean,
                            null
                        )
                    } else {
                        ToastUtils.ss("附件加载中，请稍后查看")
                    }
                }
            }
            return
        }

        // 邮件附件走sdk接口下载后打开
        viewModel.previewFileWhenDownloaded(
            uploadBean.titleName,
            uploadBean.fileId,
            uploadBean.cid,
            viewModel.getMailId(),
            {
                ToastUtils.ss("附件加载中，请稍后查看")
                MailPoint.pointMailAttachFileDownloadClick(viewModel.getMailId(), EmailBusinessCentre.getCurrentMailAccount()?.mailAddress?:"", 1, uploadBean.fileId)
                viewModel.autoDownload(uploadBean) { file ->
                    uploadBean.filePath = file?.absolutePath ?: ""
                    ExecutorFactory.execMainTask {
                        if (file == null || !file.exists()) {
                            ToastUtils.failure("文件下载失败")
                            return@execMainTask
                        }
                    }
                }
            },
            { fileExist ->
                FilePreviewUtil.openAttachFile(activity, fileExist)
            })
    }

    override fun onSelectFileUpload() {
        if (viewModel.allAttachFiles.getNormalFiles().size >= viewModel.serverMailOption.attachmentMaxNum) {
            ToastUtils.ss("单个邮件最多${viewModel.serverMailOption.attachmentMaxNum}个附件")
            return
        }
        //请求权限
        val manager = PermissionAvoidManager((activity as FragmentActivity?)!!)
        manager.requestPermission(
            HiPermissionUtil.storagePermissions
        ) { hasPermission, _ ->
            if (hasPermission) {
                mailContentAnyModify = true
                createMailIfNeed {
                    if (it.result == MailConstants.Code.FAILED) {
                        return@createMailIfNeed
                    }
                    val activityLocal = activity
                    //打开文件浏览器兼容逻辑
                    if (activityLocal != null && getOpenDocumentIntent(true).resolveActivity(
                            activityLocal.packageManager
                        ) != null
                    ) {
                        startActivityForResult(getOpenDocumentIntent(true), FILE_REQUEST_CODE)
                    } else if (activityLocal != null && getOpenDocumentIntent(false).resolveActivity(
                            activityLocal.packageManager
                        ) != null
                    ) {
                        startActivityForResult(getOpenDocumentIntent(false), FILE_REQUEST_CODE)
                    }
                }
            } else {
                ToastUtils.failure("没有获得SD卡权限")
            }
        }
    }

    override fun onClickExpandSenderList(accountInfoBean: AccountInfoBean) {
        MailAccountSelector(accountInfoBean).show(EmailBusinessCentre.getCacheMailAccountList())
    }

    override fun detachSchedule() {
        detachScheduleDialogUtils = DialogUtils.Builder(activity)
            .setContent("是否确认删除已填会议邀请？")
            .setNegative(R.string.cancel)
            .setPositive(R.string.delete)
            .setPositiveListener {
                detachScheduleDialogUtils?.dismiss()
                viewModel.detachSchedule()
                mailContentAnyModify = true
            }
            .setNegativeListener {
                detachScheduleDialogUtils?.dismiss()
            }
            .build();
    }

    override fun editDraftSchedule() {
        onClickSchedule()
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        toastSaveDraftInfo()
    }

    private fun toastSaveDraftInfo() {
        val uploading = viewModel.uploadingNumber.get()
        val imgUploadingNumber = viewModel.imgUploadingNumber.get()
        TLog.info(TAG, "toastSaveDraftInfo uploadingNumber ${uploading}")
        if (uploading > 0) {
            ToastUtils.sl(if (imgUploadingNumber != 0) R.string.email_insert_pic else R.string.email_insert_attach_file)
            return
        }
        val emptyDraftMail = isEmptyMail()
        listener?.onEditExit(hasUpdateMail, clickedSendMail || emptyDraftMail)
        if (hasUpdateMail && emptyDraftMail) {
            handler.removeCallbacks(updateMailRunnable)
            viewModel.deleteMailComplete(viewModel.getMailId())
            dismissProgressDialog()
            return
        }
        val saveDraftCondition = fun(): Boolean {
            return (mailContentAnyModify || currentType != TYPE_EDIT) && !clickedSendMail
        }
        if (saveDraftCondition()) {
            TimeDifferenceUtil.getInstance().start(TimeTag.MAIL_SAVE_DRAFT)
            val window = activity
            createMailIfNeed {
                if (it.result == MailConstants.Code.SUCCESS) {
                    prepareMailImgToSend { html, text ->
                        viewModel.updateMail(getMailInfoFromEditor(html)) {
                            ExecutorFactory.execMainTask {
                                destroyWebView()
                                viewModel.mailInfoLiveData.value?.id?.let {
                                    TLog.info(TAG, "toast -> updateMail $isAdded")
                                    DecorToast.toastSuccessClickAble(
                                        window,
                                        ProcessHelper.getContext().getString(R.string.email_saved_as_draft), "删除", 3000
                                    ) {
                                        TLog.info(TAG, "click -> delete $it ")
                                        viewModel.deleteMail(it, EmailBusinessCentre.obtainFolderIdFromBoxType(Mailbox.Draft)) {
                                            if (it == 0) {
                                                viewModel.mailInfoLiveData.value?.run {
                                                    viewModel.fakeDelMailInfo(this)
                                                }
                                                ToastUtils.success("邮件已移至\"已删除\"")
                                                listener?.onEditExit(hasUpdateMail, true)
                                            } else {
                                                ToastUtils.failure("邮件删除失败")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        mailContentAnyModify = false
                    }
                } else {
                    destroyWebView()
                }
            }
        } else {
            destroyWebView()
        }
        dismissProgressDialog()
    }

    /**
     * 空邮件
     */
    private fun isEmptyMail(): Boolean {
        return dataBinding.mailContent.html.isNullOrEmpty() &&
                dataBinding.receive.mailEditTitle.contentText.isNullOrEmpty() &&
                dataBinding.receive.mailEditReceive.mailPeople.isNullOrEmpty() &&
                dataBinding.receive.mailEditCc.mailPeople.isNullOrEmpty() &&
                dataBinding.receive.mailEditBsc.mailPeople.isNullOrEmpty() &&
                dataBinding.receive.mailEditBsc.mailPeople.isNullOrEmpty() &&
                fileListAdapter.data.isNullOrEmpty()
    }

    /**
     * 更新编辑框内所有的img标签图片本地路径为cid
     * 便于兼容其他端的图片展示
     */
    private fun prepareMailImgToSend(afterUpdateHtml: (html: String, text: String) -> Unit) {
        val validBigAttachFiles = viewModel.allAttachFiles.filter {
            it.isBigAttach && (it.expireTime > System.currentTimeMillis() || it.expireTime < 0)
        }
        val inValidBigAttachFiles = viewModel.allAttachFiles.filter {
            it.isBigAttach && (it.expireTime < System.currentTimeMillis() && it.expireTime > 0)
        }
        inValidBigAttachFiles.forEach {
            viewModel.deleteUnusedAttachFile(it?.id ?: "", true)
        }
        val uploadImgBeanList = Vector<UploadImgBean>(viewModel.imgInMailsAll)
        TLog.info(TAG, "uploadImgBeanList -> ${uploadImgBeanList.size}  $hasDownload ")
        JavaScript.exportHtmlFromEditor(
            dataBinding.mailContent,
            GsonUtils.getGson().toJsonTree(uploadImgBeanList), hasDownload,
            GsonUtils.getGson().toJsonTree(validBigAttachFiles)
        ) { html: String, text: String ->
            if (html.isEmpty()) {
                ToastUtils.sl("解析出错了~ 内容为空")
                return@exportHtmlFromEditor
            }
            TLog.info(TAG, "update mail size is ${getMailSize()}")
            afterUpdateHtml(html, text)
        }
    }

    override fun getDialogHeight(): Int {
        return ViewGroup.LayoutParams.MATCH_PARENT
    }

    companion object {
        // 创建
        const val TYPE_CREATE = 0

        // 回复
        const val TYPE_REPLY_TO = 1

        // 回复全部
        const val TYPE_REPLY_ALL = 2

        // 转发
        const val TYPE_REPLY_FORWARD = 3

        const val TYPE_COPY_EDIT = 4

        // 编辑草稿
        const val TYPE_EDIT = 100

        // eml文件创建的编辑
        const val TYPE_REPLY_EML_FILE = 101

        /**
         * 新建草稿邮件
         */
        @JvmStatic
        fun instance(type: Int?, emailId: String?, replyToId: String? = "") =
            EmailEditFragment().apply {
                val bundle = Bundle()
                if (type != null) {
                    bundle.putInt(BundleConstants.BUNDLE_EMAIL_CREATE_TYPE, type)
                }
                bundle.putString(BundleConstants.BUNDLE_EMAIL_ID, emailId)
                bundle.putString(BundleConstants.BUNDLE_EMAIL_REPLY_ID, replyToId)
                arguments = bundle
                initDialogStyle(type, this)
            }

        @JvmStatic
        fun instance(type: Int?, bundle: Bundle?) = EmailEditFragment().apply {
            bundle?.let {
                if (type != null) {
                    bundle.putInt(BundleConstants.BUNDLE_EMAIL_CREATE_TYPE, type)
                }
                arguments = bundle
            }
            initDialogStyle(type, this)
        }

        /**
         * 编辑已有邮件
         */
        @JvmStatic
        fun instance(
            type: Int?,
            emailId: String?,
            previewType: Int,
            title: String? = "",
            replyToId: String = ""
        ) = EmailEditFragment().apply {
            val bundle = Bundle()
            if (type != null) {
                bundle.putInt(BundleConstants.BUNDLE_EMAIL_CREATE_TYPE, type)
                bundle.putInt(BundleConstants.BUNDLE_EMAIL_PREVIEW_MAIL_TYPE, previewType)
            }
            bundle.putString(BundleConstants.BUNDLE_EMAIL_REPLY_ID, replyToId)
            bundle.putString(BundleConstants.BUNDLE_EMAIL_ID, emailId)
            val mailInfo = MailInfoBean()
            mailInfo.title = title
            bundle.putParcelable(BundleConstants.BUNDLE_EMAIL_INFO, mailInfo)
            arguments = bundle
            initDialogStyle(type, this)
        }

        private fun initDialogStyle(type: Int?, emailEditFragment: EmailEditFragment) {
            if (type == TYPE_CREATE) {
                emailEditFragment.setDialogFullScreenTransparent()
            } else {
                emailEditFragment.setDialogTransparent()
            }
            emailEditFragment.isCancelable = true
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        dataBinding.receive.mailEditReceive.setMailEditListener(null)
        dataBinding.mailContent.setOnTextChangeListener(null)
        dataBinding.receive.mailEditTitle.removeTextChangedListener(this)
        dataBinding.receive.mailEditReceive.removeTextChangedListener(this)
        dataBinding.receive.mailEditCc.removeTextChangedListener(this)
        dataBinding.receive.mailEditBsc.removeTextChangedListener(this)
    }

    /**
     * 延迟. 获取编辑器内容保存后再销毁
     */
    private fun destroyWebView() {
        handler.removeCallbacksAndMessages(null)
        dataBinding.mailContent.removeView(null)
        dataBinding.mailContent.destroyDrawingCache()
        dataBinding.mailContent.destroy()
        JavaScript.unRegisterCallback()
    }

    override fun onPause() {
        super.onPause()
        updateMailInfoDelay(-1)
    }

    /**
     * 获取用户输入的邮件详情
     * 用于创建或者发送邮件
     */
    private fun getMailInfoFromEditor(html: String): EmailInfo? {
        val email = viewModel.mailInfoLiveData.value
        if (email != null) {
            email.receiverList = dataBinding.receive.mailEditReceive.mailPeople
            email.carbonCopyList = dataBinding.receive.mailEditCc.mailPeople
            email.encryptCopyList = dataBinding.receive.mailEditBsc.mailPeople
            email.title = dataBinding.receive.mailEditTitle.contentText
            email.body = html
            email.sender = viewModel.getUserMailPeople()
            email.replyToId = replyToId
            email.attachedFiles = null
            email.bigAttachedFiles = null
            email.mailScheduleInfo = viewModel.mailScheduleLiveData.value?.toMailScheduleInfo()
            email.isSeparatelySend = viewModel.mailSendTypeLiveData.value == MAIL_SEND_TYPE_SEPARATE
        }
        return email
    }

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
    }

    /**
     * 正文外的内容编辑变化回调
     */
    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        // 数据没有增减时，直接返回
        if (before == 0 && count == 0) {
            return
        }
        mailContentAnyModify = true
        updateMailInfoDelay(12_000)
    }

    override fun afterTextChanged(s: Editable?) {
    }

    /**
     * 正文编辑变化回调
     */
    override fun onTextChange(text: String?) {
        checkInputHintVisible(text.isNullOrEmpty())
        mailContentAnyModify = true
        updateMailInfoDelay(12_000)
    }

    private fun checkInputHintVisible(visible: Boolean) {
        dataBinding.tvHint.isVisible = visible
    }

    override fun onEditBodyClick() {
        dataBinding.funcPanel.isGone = true
    }

    /**
     * 保存更新的邮件
     */
    private fun updateMailInfoDelay(delay: Long) {
        // 更新邮件前，需要先创建邮件
        createMailIfNeed()
        // 未修改内容, 已点击发送时，无需更新
        if (!mailContentAnyModify || clickedSendMail) {
            TimeDifferenceUtil.getInstance().finish(TimeTag.MAIL_SAVE_DRAFT)
            return
        }
        hasUpdateMail = true
        handler.removeCallbacks(updateMailRunnable)
        handler.postDelayed(updateMailRunnable, delay)
    }

    /**
     * 邮件更新
     */
    private val updateMailRunnable = Runnable {
        prepareMailImgToSend { html, text ->
            viewModel.updateMail(getMailInfoFromEditor(html))
            mailContentAnyModify = false
        }
    }

    private fun createMailIfNeed(createEmpty: Boolean? = null, createCallback: ((MailUpdateResponse) -> Unit)? = null) {
        if (viewModel.getMailId().isNotEmpty()) {
            val response = MailUpdateResponse()
            response.result = MailConstants.Code.SUCCESS
            createCallback?.invoke(response)
            return
        }
        if (createEmpty == false && !mailContentAnyModify) {
            return
        }
        viewModel.createMailByType(createCallback)
    }

    fun setEditListener(listener: IEmailEditExitCallback): Unit {
        this.listener = listener
    }

    inner class MailAccountSelector(private val accountInfoBean: AccountInfoBean) : IEmailAccountSelectorCallback {

        private val accountAdapter = MyMultiTypeAdapter()

        init {
            initView(accountInfoBean)
        }

        private val _mailAccountSelector: HiPopupWindow by lazy {
            val contentView = LayoutInflater.from(context)
                .inflate(R.layout.email_send_window_mail_account_selection, view as? ViewGroup, false).also {
                    it.findViewById<View>(R.id.mask)
                        .setOnClickListener { _mailAccountSelector.dismiss() }
                    it.findViewById<RecyclerView>(R.id.mail_account_list).run {
                        layoutManager = LinearLayoutManager(context)
                        adapter = accountAdapter
                        itemAnimator = null
                    }
                }

            val popupWindow = HiPopupWindow.Builder(context)
                .setContentView(contentView)
                .setWidth(QMUIDisplayHelper.getScreenWidth(context))
                .setAnimationStyle(R.style.pop_anim_style_alpha).build()
            popupWindow.setCallBack {
                dataBinding.receive.mailIcArrowDown.setImageResource(R.drawable.email_arrow_down)
                _mailAccountSelector.dismiss()
            }
            popupWindow
        }

        private fun initView(accountInfoBean: AccountInfoBean) {
            accountAdapter.register(
                AccountInfoBean::class.java,
                R.layout.email_send_item_mail_account_item,
                object :
                    MyMultiTypeAdapter.ItemViewBinder<EmailSendItemMailAccountItemBinding, AccountInfoBean> {
                    override fun bind(
                        vdb: EmailSendItemMailAccountItemBinding?,
                        item: AccountInfoBean?,
                        linkIndex: Int
                    ) {
                        vdb?.setVariable(BR.item, item)
                        vdb?.setVariable(BR.callback, this@MailAccountSelector)
                        vdb?.setVariable(BR.viewModel, viewModel)
                        vdb?.setVariable(BR.currentSendMail, accountInfoBean)
                    }
                })
        }

        fun show(list: MutableList<AccountInfoBean>) {
            dataBinding.mailContent.clearFocus()
            dataBinding.receive.mailIcArrowDown.setImageResource(R.drawable.email_arrow_up)
            accountAdapter.data = list.toList()
            _mailAccountSelector.showAsDropDown(dataBinding.receive.mailSendAccount)
            QMUIKeyboardHelper.hideKeyboard(dataBinding.receive.sendMailPeople)
        }

        override fun onSwitchMailAccount(item: AccountInfoBean) {
            if (item.indexKey == accountInfoBean.indexKey) {
                _mailAccountSelector.dismiss()
                return
            }
            val uploading = viewModel.uploadingNumber.get()
            if (uploading > 0) {
                ToastUtils.sl(getString(R.string.email_cannot_switch_sender_while_uploading))
                _mailAccountSelector.dismiss()
                return
            }
            showProgressDialog("", false)
            mailContentAnyModify = true
            createMailIfNeed {
                if (it.result == MailConstants.Code.FAILED) {
                    dismissProgressDialog()
                    _mailAccountSelector.dismiss()
                    return@createMailIfNeed
                }
                if (it.emailInfo != null && it.emailInfo.id.isNotEmpty()) {
                    TLog.info(TAG, "bindMessageIdForHtml -> ${it.emailInfo.id} ")
                    JavaScript.bindMessageIdForHtml(dataBinding.mailContent, it.emailInfo.id) {
                        ExecutorFactory.execMainTask {
                            replaceHtmlSignatureWhenSwitchSender(item, accountInfoBean, _mailAccountSelector)
                        }
                    }
                } else {
                    TLog.info(TAG, "replace -> ${viewModel.getMailId()} ")
                    replaceHtmlSignatureWhenSwitchSender(item, accountInfoBean, _mailAccountSelector)
                }
            }
        }
    }

    private fun replaceHtmlSignatureWhenSwitchSender(item: AccountInfoBean, accountInfoBean: AccountInfoBean, mailAccountSelector: HiPopupWindow) {
        handler.removeCallbacks(updateMailRunnable)
        prepareMailImgToSend { html, text ->
            viewModel.updateMail(getMailInfoFromEditor(html)) {
                if (it == MailConstants.Code.SUCCESS) {
                    viewModel.switchSendMail(item, accountInfoBean) { code, newMailId ->
                        ExecutorFactory.execMainTask {
                            dataBinding.receive.mailIcArrowDown.setImageResource(R.drawable.email_arrow_down)
                            mailAccountSelector.dismiss()

                            if (code == MailConstants.Code.SUCCESS) {
                                // 非编辑草稿的时支持切换签名
                                signatureSwitch = !(initType == TYPE_EDIT && initPreviewType == TYPE_EDIT)
                                currentType = TYPE_EDIT
                                currentPreviewMailType = TYPE_EDIT
                                viewModel.initMailType(TYPE_EDIT, newMailId, "", null, TYPE_EDIT)
                            } else {
                                dismissProgressDialog()
                            }
                        }
                    }
                } else {
                    ExecutorFactory.execMainTask {
                        dataBinding.receive.mailIcArrowDown.setImageResource(R.drawable.email_arrow_down)
                        mailAccountSelector.dismiss()
                        dismissProgressDialog()
                    }
                }
            }
        }
    }

    private fun replaceSignature(accountInfoBean: AccountInfoBean?, function: () -> Unit) {
        accountInfoBean?.run {
            viewModel.getAccountSignatureInfo (accountInfoBean) {
                ExecutorFactory.execMainTask {
                    it?.run {
                        JavaScript.replaceSignature(dataBinding.mailContent, signature) {
                            function.invoke()
                        }
                    } ?: run {
                        JavaScript.removeSignature(dataBinding.mailContent) {
                            function.invoke()
                        }
                    }
                }
            }
        }
    }

}