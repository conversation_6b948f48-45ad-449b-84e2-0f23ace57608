package com.twl.hi.module_email.common

import com.techwolf.lib.tlog.TLog
import com.twl.hi.email.pb.receive.BshResponse
import com.twl.hi.foundation.model.email.constants.MailConstants
import lib.twl.common.util.ToastUtils

const val TAG = "MailCommonError"

fun BshResponse.showMailCommonFailure() {
    if (codeValue != MailConstants.Code.SUCCESS) {
        if (shown && errorInfo?.errorMsg?.isNotEmpty() == true) {
            ToastUtils.failure(errorInfo?.errorMsg)
        } else {
            ToastUtils.failure("邮箱请求失败")
        }
        TLog.info(TAG, "showMailCommonFailure code=${errorInfo?.errorCode}, ${errorInfo?.errorMsg}")
    }
}