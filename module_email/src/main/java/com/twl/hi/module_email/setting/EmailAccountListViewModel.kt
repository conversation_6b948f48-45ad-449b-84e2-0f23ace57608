package com.twl.hi.module_email.setting

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.techwolf.lib.tlog.TLog
import com.twl.hi.email.pb.receive.BshMailboxListResponse
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.email.constants.MailConstants
import com.twl.hi.module_email.domain.AccountInfoBean
import com.twl.hi.module_email.domain.toAccountBean
import lib.twl.common.base.BaseViewModel
import lib.twl.common.util.ExecutorFactory

/**
 * <AUTHOR>
 * @Date   2023/11/8 10:17 AM
 * @Desc   别删这行注释啊，删了程序就崩了
 */
class EmailAccountListViewModel(app: Application): BaseViewModel(app) {

    val TAG = "EmailAccountListViewModel"

    val accountListLiveData = MutableLiveData<MutableList<AccountInfoBean>>()

    fun queryAccountList() {
        ExecutorFactory.execWorkTask {
            val list = mutableListOf<AccountInfoBean>()
            ServiceManager.getInstance().emailService.getMailAccountList { code: Int, response: BshMailboxListResponse ->
                if (code == MailConstants.Code.SUCCESS) {
                    val listMailBox = response.mailboxListList
                    listMailBox?.run {
                        forEach { item ->
                            item?.let {
                                val account = item.toAccountBean()
                                account.initAvatar()
                                list.add(account)
                                TLog.info(TAG, "item -> ${item.toAccountBean()} ")
                            }
                        }
                    }
                }
                TLog.info(TAG, "queryAccountList size ${list.size}")
                accountListLiveData.postValue(list)
            }
        }
    }

}