package com.twl.hi.module_email.main.base

import androidx.lifecycle.ViewModel
import com.twl.hi.foundation.model.email.bean.receive.AllReadResponse
import com.twl.hi.module_email.domain.EmailBusinessCentre
import com.twl.hi.module_email.domain.Mailbox

open abstract class EmailFilterUnReadViewModel : ViewModel() {

    private val businessCentre = EmailBusinessCentre

    abstract fun getMailBox() : Mailbox

    fun markAllRead(mailbox: Mailbox, callback: (AllReadResponse) -> Unit) {
        businessCentre.markAllRead(businessCentre.obtainFolderIdFromBoxType(mailbox), callback)
    }


}