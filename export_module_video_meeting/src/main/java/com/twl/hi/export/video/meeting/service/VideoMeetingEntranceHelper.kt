package com.twl.hi.export.video.meeting.service

import android.content.Context

/**
 *@author: musa on 2023/3/2
 *@e-mail: yang<PERSON><EMAIL>
 *@desc: 音视频入口开放接口
 */

interface VideoMeetingEntranceHelper {
    companion object{
        val TAG get() = "VideoMeetingEntranceHelper"
    }

    /**
     * 开始多人视频会议
     * @param chatId: 在群聊中开启会议时需要传，将当前群加入邀请列表中
     * */
    fun startGroupVideoMeeting(context: Context, chatId: String = "")

    /**
     * 从日程开启视频会议
     *
     * @param roomId: 预约会议的会议 ID
     * @param roomName: 预约会议的名称
     */
    fun startScheduleVideoMeeting(context: Context, roomId: String, roomName: String , scheduleId: String)

    /**加入多人视频会议
     * @param meetingNumberCode 会议号
     * */
    fun joinMeeting(context: Context, meetingNumberCode: String?)

    /**开始单人视频会议
     * @param peerId: 指的是 接听方的userId
     * @param type: 单聊可以选 视频会议还是 音频会议
     * */
    fun startSingleVideoMeeting(context: Context, peerId: String, type: Int)

    /**加入视频会议 包括 接听单聊接听和群聊入会
     * @param roomId: 加入会议的会议Id
     * @param senderId: 拨打方的Id，仅用于单聊接听
     * @param callback: 回调 用来通知入会的结果和时机
     * */

    fun joinVideoMeeting(context: Context, roomId: String, senderId: String = "", callback: BootCallback? = null)

    /**
     * 从日程加入视频会议
     */
    fun joinVideoMeetingFromSchedule(context: Context, roomId: String, scheduleId: String, callback: BootCallback? = null)

    /**
     * @param audioStatus: 是否打开麦克风
     * @param videoStats: 是否打开摄像头
     */
    fun joinVideoMeeting(context: Context, roomId: String, senderId: String = "", audioStatus:Boolean, videoStats:Boolean, callback: BootCallback? = null)

    /**打开视频会议页面回调*/
    interface BootCallback {
        /**成功*/
        fun onSuccess(){}

        /**失败*/
        fun onFail(){}

        /**完成，成功失败都会走到这*/
        fun onComplete(){}
    }

}