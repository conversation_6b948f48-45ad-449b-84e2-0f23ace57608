package com.twl.hi.export.video.meeting.router;

import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.sankuai.waimai.router.Router;
import com.twl.hi.export.video.meeting.model.bean.MeetingInfoBean;
import com.twl.hi.export.video.meeting.service.VideoMeetingAbilityService;

import lib.twl.common.callback.BaseCallback;

public class VideoAndAudioPageRouter {
    public static final String MODULE_NAME_VIDEO_CHAT = "/video_and_video";

    public static final String MEETING_ACTIVITY = MODULE_NAME_VIDEO_CHAT + "/meeting_activity";

    public static final String SERVICE_VIDEO_MEETING_ENTRANCE = "service_video_meeting_entrance";

    public static final String SERVICE_VIDEO_MEETING_ABILITY = MODULE_NAME_VIDEO_CHAT + "service_video_meeting_ability";


    /**
     * 记录当前真正参会状态
     */
    private static final MutableLiveData<MeetingInfoBean> curMeetingInfo = new MutableLiveData<>();


    /**
     * 检查当前的参会状态
     */
    public static void checkMeetingInfo() {
        queryMeetingInfo(new BaseCallback<MeetingInfoBean>() {
            @Override
            public void onFailure(@Nullable Exception exception) {
                //nothing
            }

            @Override
            public void onSuccess(@Nullable MeetingInfoBean meetingInfoBean) {
                curMeetingInfo.postValue(meetingInfoBean);
            }
        });
    }

    public static LiveData<MeetingInfoBean> getCurMeetingInfo() {
        return curMeetingInfo;
    }

    public static void clearCurMeetingInfo() {
        curMeetingInfo.postValue(null);
    }

    /**
     * 获取参会信息
     */
    public static void queryMeetingInfo(BaseCallback<MeetingInfoBean> callback) {
        Router.getService(VideoMeetingAbilityService.class).queryMeetingInfo(callback);
    }
}
