package com.twl.hi.login.api.request;

import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.login.api.response.RegionInfoResponse;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class LoginRegionInfoRequest extends BaseApiRequest<RegionInfoResponse> {
    public LoginRegionInfoRequest(AbsRequestCallback<RegionInfoResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_LOGIN_REGION;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
