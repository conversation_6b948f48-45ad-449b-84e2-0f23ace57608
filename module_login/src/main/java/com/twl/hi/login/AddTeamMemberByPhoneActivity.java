package com.twl.hi.login;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;

import com.twl.hi.login.databinding.LoginActivityAddTeamMemberBinding;
import com.twl.hi.login.databinding.LoginItemTeamMemberEditByPhoneBinding;
import com.twl.hi.foundation.model.Company;
import com.twl.hi.login.callback.AddTeamMemberByPhoneCallback;
import com.twl.hi.login.model.TeamMemberEditModel;
import com.twl.hi.login.viewmodel.AddTeamMemberByPhoneViewModel;

import hi.kernel.BundleConstants;

public class AddTeamMemberByPhoneActivity extends AddTeamMemberBaseActivity<LoginActivityAddTeamMemberBinding, AddTeamMemberByPhoneViewModel> implements AddTeamMemberByPhoneCallback {

    public static Intent createIntent(Context context, Company companiesBean, String departmentId) {
        Intent intent = new Intent(context, AddTeamMemberByPhoneActivity.class);
        intent.putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE, companiesBean);
        intent.putExtra(BundleConstants.BUNDLE_DATA_LONG, departmentId);
        return intent;
    }

    @Override
    public void next() {
        getViewModel().addTeamMembers(companiesBean.comId,departmentId);

    }

    @Override
    public void addTeamMemberEdit() {
        LoginItemTeamMemberEditByPhoneBinding itemTeamMemberEditBinding = LoginItemTeamMemberEditByPhoneBinding.inflate(LayoutInflater.from(this), getDataBinding().llAddTeamMembers, true);
        TeamMemberEditModel teamMemberEditModel = new TeamMemberEditModel();
        getViewModel().addMemberEditModel(teamMemberEditModel);
        itemTeamMemberEditBinding.setModel(teamMemberEditModel);
        itemTeamMemberEditBinding.etName.addTextChangedListener(this);
        itemTeamMemberEditBinding.etPhone.addTextChangedListener(this);
    }

}
