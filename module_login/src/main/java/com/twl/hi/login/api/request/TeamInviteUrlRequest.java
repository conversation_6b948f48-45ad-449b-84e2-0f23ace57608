package com.twl.hi.login.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.login.api.response.TeamUrlResponse;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class TeamInviteUrlRequest extends BaseApiRequest<TeamUrlResponse> {
    @Expose
    public String companyId;
    @Expose
    public String deptId;

    public TeamInviteUrlRequest(BaseApiRequestCallback<TeamUrlResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_GET_INVITE_URL;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
