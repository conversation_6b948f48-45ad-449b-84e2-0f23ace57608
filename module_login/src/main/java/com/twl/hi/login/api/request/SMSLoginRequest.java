package com.twl.hi.login.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.login.api.response.SMSLoginResponse;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestHeader;
import com.twl.http.config.RequestMethod;

public class SMSLoginRequest extends BaseApiRequest<SMSLoginResponse> {

    @Expose
    public int regionCode;
    @Expose
    public long phone;
    @Expose
    public int smsCode;
    @Expose
    public int platform = 2;
    @Expose
    public String gps;
    @Expose
    public String duId;
    @Expose
    public String clientIp;
    @Expose
    public String challenge;
    @Expose
    public String validate;
    @Expose
    public String seccode;

    public SMSLoginRequest(BaseApiRequestCallback<SMSLoginResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_SMS_LOGIN;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }


    @Override
    public RequestHeader getHeaders() {
        RequestHeader header = super.getHeaders();
        header.put("Content-Type", "application/x-www-form-urlencoded");
        return header;
    }
}
