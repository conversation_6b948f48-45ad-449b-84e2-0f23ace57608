package com.twl.hi.login;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import com.twl.hi.basic.dialog.DialogUtils;
import hi.kernel.BundleConstants;

import com.twl.hi.login.databinding.LoginActivityInviteTeamMembersBinding;
import com.twl.hi.foundation.model.Company;
import com.twl.hi.login.callback.InviteTeamMembersCallback;
import com.twl.hi.login.viewmodel.InviteTeamMembersViewModel;

public class InviteTeamMembersActivity extends InviteTeamMembersBaseActivity<LoginActivityInviteTeamMembersBinding, InviteTeamMembersViewModel> implements InviteTeamMembersCallback {
    DialogUtils dialogUtils;

    public static Intent createIntent(Context context, Company companiesBean) {
        Intent intent = new Intent(context, InviteTeamMembersActivity.class);
        intent.putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE, companiesBean);
        return intent;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.login_activity_invite_team_members;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Override
    protected void initExtra(Intent intent) {
        super.initExtra(intent);
    }

    @Override
    public void done() {
        getViewModel().setDefaultTeam(companiesBean);
    }

    @Override
    public void onBackPressed() {

        if (dialogUtils == null) {
            dialogUtils = new DialogUtils.Builder(this)
                    .setContent(getResources().getString(R.string.login_skip_team_invite))
                    .setPositive(getResources().getString(R.string.sure))
                    .setPositiveListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            done();
                        }
                    })
                    .setNegative(getResources().getString(R.string.cancel))
                    .setNegativeListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            dialogUtils.dismiss();
                        }
                    })
                    .build();
        }

        dialogUtils.show();
    }
}
