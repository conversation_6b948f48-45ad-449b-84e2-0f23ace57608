package com.twl.hi.login;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.SpannedString;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.LinkMovementMethod;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ClickableSpan;
import android.util.ArrayMap;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.Observer;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.BottomListDialog;
import com.twl.hi.basic.model.SelectBottomBean;
import com.twl.hi.basic.model.WebViewBean;
import com.twl.hi.export.main.router.AppPageRouter;
import com.twl.hi.export.webview.WebViewPageRouter;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.base.fragment.FoundationVMShareFragment;
import com.twl.hi.login.api.request.LoginRegionInfoRequest;
import com.twl.hi.login.api.response.RegionInfoResponse;
import com.twl.hi.login.callback.LoginNumberCallback;
import com.twl.hi.login.databinding.LoginFragmentLoginInputNumberBinding;
import com.twl.hi.login.databinding.LoginPopupListBinding;
import com.twl.hi.login.viewmodel.LoginInputNumberViewModel;
import com.twl.hi.login.viewmodel.LoginViewModel;
import com.twl.hi.switchsetting.SwitchSettingActivity;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import hi.kernel.Constants;
import lib.twl.common.adapter.CommonAdapter;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.permission.PermissionAvoidManager;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.HiPermissionUtil;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.util.QMUIKeyboardHelper;
import lib.twl.common.util.widget.HiPopupWindow;

/**
 * 登录-输入手机号页面
 */
public class LoginInputNumberFragment extends FoundationVMShareFragment<LoginFragmentLoginInputNumberBinding, LoginInputNumberViewModel, LoginViewModel> implements LoginNumberCallback {
    private final String TAG = "LoginInputNumberFragment";
    private BottomListDialog mBottomDialog;
    private ArrayList<SelectBottomBean> mBottomBeans = null;
    /**
     * regionCode和对应正则表达式的集合
     */
    private ArrayMap<String, String> mRegexMap = new ArrayMap<>();
    private Pattern mCurrentPattern;

    private int mKeyBoardOpenHeight;
    private int mInterval;
    private HiPopupWindow mUsedNumbersPopupWindow;
    private CommonAdapter<String> adapter;
    PermissionAvoidManager manager;

    private int clickSettingCount = 0;

    @Override
    public int getContentLayoutId() {
        return R.layout.login_fragment_login_input_number;
    }


    @Override
    protected void initFragment() {
        super.initFragment();
        setEditHintTextSize();
        initProtocol();
        getDataBinding().edit.requestFocus();
        manager = new PermissionAvoidManager(this);
        QMUIKeyboardHelper.setVisibilityEventListener(activity, new QMUIKeyboardHelper.KeyboardVisibilityEventListener() {
            @Override
            public boolean onVisibilityChanged(boolean isOpen, int heightDiff) {
                if (isOpen && mKeyBoardOpenHeight <= 0) {
                    mKeyBoardOpenHeight = heightDiff;
                    mInterval = (int) (QMUIDisplayHelper.getScreenHeight(activity) + QMUIDisplayHelper.getNavMenuHeight(activity) - (mKeyBoardOpenHeight + getDataBinding().layoutNumber.getMeasuredHeight() + getDataBinding().layoutNumber.getY()));
                }
                if (mInterval <= 0) {
                    RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getDataBinding().llRoot.getLayoutParams();
                    if (isOpen) {
                        params.topMargin = mInterval;
                    } else {
                        params.topMargin = 0;
                    }
                    getDataBinding().llRoot.setLayoutParams(params);
                }
                return false;
            }
        });
        if (BuildConfig.DEBUG) {
            adapter = new CommonAdapter<String>();
            adapter.addCallback(BR.callback, this);
            adapter.setVariableId(BR.bean);
            adapter.setLayoutId(R.layout.login_item_layout_used_number);
            getViewModel().getUsedNumbersLiveData().observe(this, new Observer<List<String>>() {
                @Override
                public void onChanged(@Nullable List<String> usedNumbers) {
                    if (usedNumbers == null || usedNumbers.isEmpty()) {
                        return;
                    }
                    showUsedNumberDialog(usedNumbers);
                }
            });
            getViewModel().loadUsedNumbers();
            getDataBinding().tvConfig.setVisibility(View.VISIBLE);
            getDataBinding().tvConfig.setOnClickListener(v -> {
                AppUtil.startUri(getContext(), AppPageRouter.CUSTOM_CONFIG_ACTIVITY);
            });
        }
        getDataBinding().edit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                setBtnNextStatue(s.toString());

            }
        });

        getDataBinding().tvTips.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clickSettingCount++;
                if (clickSettingCount % 10 == 0) {
                    startActivity(new Intent(getContext(), SwitchSettingActivity.class));
                }
            }
        });

        initDialogData();
        getRegionInfo(); // 获取登录区号信息，拿到返回值后直接刷新兜底的数据，进入当前页面只请求一次，不用考虑无网的case
        getActivityViewModel().getPhoneModel().get().areaCode.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                String regex = mRegexMap.get(s);
                mCurrentPattern = TextUtils.isEmpty(regex) ? null : Pattern.compile(regex);
            }
        });
    }

    private void getRegionInfo() {
        LoginRegionInfoRequest request = new LoginRegionInfoRequest(new BaseApiRequestCallback<RegionInfoResponse>() {
            @Override
            public void onSuccess(ApiData<RegionInfoResponse> data) {
                transformDataAndRefresh(data);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (reason != null) {
                    TLog.error(TAG, reason.getErrReason());
                }
            }
        });
        HttpExecutor.execute(request);
    }

    /**
     * 获取服务端的数据转换成本地数据模型，并覆盖兜底数据
     * @param data
     */
    private void transformDataAndRefresh(ApiData<RegionInfoResponse> data) {
        if (data != null && data.resp != null && data.resp.regionInfoVOList != null && data.resp.regionInfoVOList.size() > 0) {
            ArrayList<SelectBottomBean> beans = new ArrayList<>();
            for (RegionInfoResponse.RegionBean regionBean : data.resp.regionInfoVOList) {
                SelectBottomBean bean = new SelectBottomBean(regionBean.name, regionBean.regionCode);
                beans.add(bean);
                mRegexMap.put(regionBean.regionCode, regionBean.regex);
            }

            if (beans.size() > 0) {
                mBottomBeans.clear();
                mBottomBeans.addAll(beans);
                if (mBottomDialog == null) {
                    initBottomDialog();
                }

                if (mBottomDialog.isShowing()) {
                    mBottomDialog.getAdapter().setNewData(mBottomBeans);
                }
            }
        }
    }


    private void setBtnNextStatue(String phoneNum) {
        if (TextUtils.isEmpty(phoneNum)) {
            getDataBinding().btnPhoneNext.setEnabled(false);
            return;
        }

        String destNum = phoneNum.replace(" ", "");
        if (mCurrentPattern != null) {
            getDataBinding().btnPhoneNext.setEnabled(mCurrentPattern.matcher(destNum).matches());
        } else {
            getDataBinding().btnPhoneNext.setEnabled(true);
        }
    }

    /**
     * 设置hint字体大小
     */
    private void setEditHintTextSize() {
        StringBuilder stringBuilder = new StringBuilder(getResources().getString(R.string.login_tips_input_phone_number));
        stringBuilder.insert(0, '\0');
        SpannableString ss = new SpannableString(stringBuilder.toString());//定义hint的值
        ss.setSpan(new AbsoluteSizeSpan(20, true), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);//适配小米手机光标大小会变化
        ss.setSpan(new AbsoluteSizeSpan(16, true), 1, ss.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        getDataBinding().edit.setHint(new SpannedString(ss));
    }

    /**
     * 初始化协议显示
     */
    private void initProtocol() {
        String protocol = getString(R.string.login_login_protocol);
        SpannableStringBuilder spannableBuilder = new SpannableStringBuilder(protocol);
        spannableBuilder.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                WebViewBean webViewBean = new WebViewBean();
                webViewBean.setUrl("https://hi.zhipin.com/protocol");
                webViewBean.setTitle(getResources().getString(R.string.login_protocol));
                webViewBean.setStyle(Constants.WEB_STYLE_HAS_NO_SHARE);
                Bundle bundle = new Bundle();
                bundle.putSerializable(Constants.DATA_WEB_BEAN, webViewBean);
                AppUtil.startUri(activity, WebViewPageRouter.WEB_VIEW_ACTIVITY, bundle);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(BaseApplication.getApplication().getColor(getViewModel().isUseNewTheme() ? R.color.color_text_primary : R.color.color_5D68E8));
                ds.setUnderlineText(false);
            }
        }, 10, 14, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        spannableBuilder.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                WebViewBean webViewBean = new WebViewBean();
                webViewBean.setUrl("https://hi.zhipin.com/policy");
                webViewBean.setTitle(getResources().getString(R.string.login_policy));
                webViewBean.setStyle(Constants.WEB_STYLE_HAS_NO_SHARE);
                Bundle bundle = new Bundle();
                bundle.putSerializable(Constants.DATA_WEB_BEAN, webViewBean);
                AppUtil.startUri(activity, WebViewPageRouter.WEB_VIEW_ACTIVITY, bundle);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(getResources().getColor(getViewModel().isUseNewTheme() ? R.color.color_text_primary : R.color.color_5D68E8));
                ds.setUnderlineText(false);
            }
        }, 15, protocol.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // 不设置点击不生效
        getDataBinding().tvProtocol.setMovementMethod(LinkMovementMethod.getInstance());
        getDataBinding().tvProtocol.setText(spannableBuilder);
        getDataBinding().tvProtocol.setHighlightColor(Color.parseColor("#00000000"));
    }


    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    @Override
    public void showAreaCodePop() {
        QMUIKeyboardHelper.hideKeyboard(activity);
        if (mBottomDialog == null) {
            initBottomDialog();
        }
        mBottomDialog.show(true);
    }

    /**
     * 初始化本地的兜底数据
     */
    private void initDialogData() {
        mBottomBeans = new ArrayList<>();
        mBottomBeans.add(new SelectBottomBean("中国大陆", "86"));
        mRegexMap.put("86", "^1[0-9]{10}$");
        mBottomBeans.add(new SelectBottomBean("中国香港", "852"));
        mRegexMap.put("852", "^\\d{8}$");
        mBottomBeans.add(new SelectBottomBean("美国", "1"));
        mRegexMap.put("1", "^\\d{6,11}$");
        mBottomBeans.add(new SelectBottomBean("澳大利亚", "61"));
        mRegexMap.put("61", "^\\d{6,11}$");
        mBottomBeans.add(new SelectBottomBean("英国", "44"));
        mRegexMap.put("44", "^\\d{6,11}$");
    }

    private void initBottomDialog() {
        mBottomDialog = new BottomListDialog.Builder(activity)
                .setItemLayout(R.layout.item_layout_area_code)
                .setLayoutId(R.layout.dialog_show_area_code)
                .setShowTitle(true)
                .setData(mBottomBeans)
                .setOnBottomItemClickListener(new BottomListDialog.OnBottomItemClickListener() {
                    @Override
                    public void onBottomItemClick(View view, int pos, SelectBottomBean bottomBean) {
                        if (!TextUtils.isEmpty(bottomBean.content1)) {
                            getDataBinding().tvAreaCode.setText(getResources().getString(R.string.add) + bottomBean.content1);
//                            getActivityViewModel().getPhoneModel().get().areaCode.setValue(bottomBean.content1);
                            changeArea(bottomBean.content1);
                        }
                    }
                }).create();
    }

    private void showUsedNumberDialog(List<String> usedNumbers) {
        if (mUsedNumbersPopupWindow == null) {
            LoginPopupListBinding binding = DataBindingUtil.inflate(LayoutInflater.from(activity), R.layout.login_popup_list, null, false);
            mUsedNumbersPopupWindow = new HiPopupWindow.Builder(activity).setContentView(binding.getRoot()).setShadow(activity.getWindow(), 0.6f).build();
            binding.rvUsedNumbers.setAdapter(adapter);
        }
        adapter.submitList(usedNumbers);
        mUsedNumbersPopupWindow.showAtLocation(getDataBinding().getRoot(), Gravity.CENTER, 0, 0);
    }

    @Override
    public void dismissPop() {
        mBottomDialog.dismiss();
    }

    @Override
    public void changeArea(String areaCode) {
        getActivityViewModel().getPhoneModel().get().areaCode.setValue(areaCode);
        setBtnNextStatue(getDataBinding().edit.getText().toString());
    }

    @Override
    public void toCodeFragment() {
        getActivityViewModel().toCodeFragment();
        if (BuildConfig.DEBUG) {
            manager.requestPermission(HiPermissionUtil.getAccessSharedFilePermissions(), new PermissionAvoidManager.OnCommonPermissionCallBack() {
                @Override
                public void onRequestPermissionsResult(boolean hasPermission, boolean shouldShowAllRequestPermissionRationale) {
                    if (hasPermission) {
                        getViewModel().saveUsedNumber(getActivityViewModel().getPhoneModel().get().phoneNumber.get(),
                                getActivityViewModel().getPhoneModel().get().areaCode.getValue());
                    }
                }
            });
        }
    }

    @Override
    public void onUsedNumbersItemClick(String number) {
        getActivityViewModel().getPhoneModel().get().phoneNumber.set(number);
        getDataBinding().edit.postDelayed(new Runnable() {
            @Override
            public void run() {
                getDataBinding().edit.setSelection(getDataBinding().edit.getText().length());
            }
        }, 200);
        if (mUsedNumbersPopupWindow.isShowing()) {
            mUsedNumbersPopupWindow.dismiss();
        }
    }

    @Override
    public void onShowUsedNumbersClick() {
        if (BuildConfig.DEBUG) {
            getViewModel().postUsedNumbers();
        }
    }
}
