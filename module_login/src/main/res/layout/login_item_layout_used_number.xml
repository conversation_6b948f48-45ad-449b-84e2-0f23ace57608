<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="String" />

        <variable
            name="callback"
            type="com.twl.hi.login.callback.LoginNumberCallback" />
    </data>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center_vertical"
        android:onClick="@{()->callback.onUsedNumbersItemClick(bean)}"
        android:paddingLeft="20dp"
        android:singleLine="true"
        android:text="@{bean}"
        android:textColor="@color/color_212121"
        android:textSize="17sp"
        tools:text="13100018401" />
</layout>