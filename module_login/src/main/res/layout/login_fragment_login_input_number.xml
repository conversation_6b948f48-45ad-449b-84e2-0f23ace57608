<layout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools">

	<data>

		<import type="android.text.TextUtils" />

		<import type="com.twl.hi.basic.util.ThemeUtils" />

		<variable
			name="viewModel"
			type="com.twl.hi.login.viewmodel.LoginInputNumberViewModel" />

		<variable
			name="activityViewModel"
			type="com.twl.hi.login.viewmodel.LoginViewModel" />

		<variable
			name="callback"
			type="com.twl.hi.login.callback.LoginNumberCallback" />

	</data>

	<RelativeLayout
		android:layout_width="match_parent"
		android:layout_height="match_parent">

		<LinearLayout
			android:id="@+id/ll_root"
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:background="@android:color/transparent"
			android:clickable="true"
			android:focusable="true"
			android:orientation="vertical"
			android:paddingLeft="30dp"
			android:paddingTop="108dp"
			android:paddingRight="30dp">

			<TextView
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_gravity="center_vertical"
				android:onClick="@{()->callback.onShowUsedNumbersClick()}"
				android:text="@string/login_hi_boss_hi"
				android:textColor="@color/color_0D0D1A"
				android:textSize="30sp" />

			<TextView
				android:id="@+id/tv_tips"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_gravity="center_vertical"
				android:layout_marginTop="3dp"
				android:text="@string/login_hi_login_notice"
				android:textColor="@color/color_B1B1B8"
				android:textSize="14sp" />

			<LinearLayout
				android:id="@+id/layout_number"
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:orientation="vertical">

				<androidx.constraintlayout.widget.ConstraintLayout
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:layout_marginTop="62dp"
					android:orientation="horizontal">

					<TextView
						android:id="@+id/tv_area_code"
						android:layout_width="wrap_content"
						android:layout_height="25dp"
						android:layout_marginRight="16dp"
						android:layout_marginBottom="12dp"
						android:drawableRight="@drawable/login_ic_down"
						android:drawablePadding="14dp"
						android:gravity="bottom"
						android:onClick="@{()->callback.showAreaCodePop()}"
						android:paddingRight="3dp"
						android:text="@{@string/add + activityViewModel.phoneModel.areaCode}"
						android:textColor="@color/color_0D0D1A"
						android:textSize="18sp"
						app:layout_constraintBottom_toTopOf="@+id/v_area_code_line"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintTop_toTopOf="parent" />

					<com.twl.hi.basic.views.PhoneNumberEditText
						android:id="@+id/edit"
						android:layout_width="0dp"
						android:layout_height="wrap_content"
						android:layout_marginLeft="16dp"
						android:layout_marginBottom="12dp"
						android:background="@null"
						android:gravity="center_vertical"
						android:hint="@string/login_tips_input_phone_number"
						android:maxLength="13"
						android:singleLine="true"
						android:text="@={activityViewModel.phoneModel.phoneNumber}"
						android:textColor="@color/color_0D0D1A"
						android:textColorHint="@color/color_B1B1B8"
						android:textCursorDrawable="@drawable/login_color_636dea_text_cursor"
						android:textSize="18sp"
						app:layout_constraintBottom_toTopOf="@+id/v_num_line"
						app:layout_constraintLeft_toRightOf="@+id/tv_area_code"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent" />

					<View
						android:id="@+id/v_area_code_line"
						android:layout_width="0dp"
						android:layout_height="0.5dp"
						android:background="@drawable/login_bg_login_bottom_line"
						android:enabled="@{TextUtils.isEmpty(activityViewModel.phoneModel.areaCode) ? false : true}"
						app:layout_constraintBottom_toBottomOf="parent"
						app:layout_constraintLeft_toLeftOf="@+id/tv_area_code"
						app:layout_constraintRight_toRightOf="@+id/tv_area_code" />

					<View
						android:id="@+id/v_num_line"
						android:layout_width="0dp"
						android:layout_height="0.5dp"
						android:background="@drawable/login_bg_login_bottom_line"
						android:enabled="@{TextUtils.isEmpty(activityViewModel.phoneModel.phoneNumber) ? false : true}"
						app:layout_constraintBottom_toBottomOf="parent"
						app:layout_constraintLeft_toLeftOf="@+id/edit"
						app:layout_constraintRight_toRightOf="@+id/edit" />
				</androidx.constraintlayout.widget.ConstraintLayout>

				<lib.twl.common.views.MButton
					android:id="@+id/btn_phone_next"
					android:layout_width="match_parent"
					android:layout_height="44dp"
					android:layout_marginTop="44dp"
					android:background="@{viewModel.useNewTheme?@drawable/bg_selector_common_button_primary:@drawable/bg_selector_common_button_primary_old}"
					tools:background="@drawable/bg_selector_common_button_primary"
					android:enabled="false"
					android:gravity="center"
					android:onClick="@{()->callback.toCodeFragment()}"
					android:text="@string/login_next"
					android:textColor="@color/app_white"
					android:textSize="17sp" />

				<TextView
					android:id="@+id/tv_protocol"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:layout_marginTop="10dp"
					android:textColor="@color/color_B1B1B8"
					android:textSize="12sp"
					android:visibility="visible"
					tools:text="@string/login_login_protocol"
					tools:visibility="visible" />
			</LinearLayout>
		</LinearLayout>

		<TextView
			android:id="@+id/tv_config"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_alignParentEnd="true"
			android:layout_margin="40dp"
			android:background="@{viewModel.useNewTheme?@drawable/bg_selector_common_button_primary:@drawable/bg_selector_common_button_primary_old}"
			android:padding="5dp"
			android:text="调试配置"
			android:textColor="@color/app_white"
			android:visibility="gone"
			tools:background="@drawable/bg_selector_common_button_primary"
			tools:visibility="visible" />
	</RelativeLayout>
</layout>