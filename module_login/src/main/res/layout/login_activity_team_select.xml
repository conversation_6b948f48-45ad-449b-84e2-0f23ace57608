<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.login.viewmodel.TeamSelectViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.login.callback.TeamSelectCallback" />
    </data>

    <LinearLayout
        android:id="@+id/view_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white"
        android:orientation="vertical"
        tools:context=".login.TeamSelectActivity">

        <ImageButton
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@null"
            android:onClick="@{(view)->callback.clickLeft(view)}"
            android:src="@drawable/ic_icon_black_back" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="51dp"
            android:paddingLeft="30dp"
            android:paddingRight="30dp"
            android:text="@string/login_your_team"
            android:textColor="@color/color_0D0D1A"
            android:textSize="29sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="30dp"
            android:paddingRight="30dp"
            android:text="@string/login_team_select_notice"
            android:textColor="@color/color_B1B1B8"
            android:textSize="14sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_teams"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="44dp"
            android:layout_weight="1"
            android:fadeScrollbars="false"
            android:scrollbarAlwaysDrawVerticalTrack="true"
            android:scrollbars="vertical" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:layout_marginBottom="25dp"
            android:background="@drawable/bg_corner_7_color_5d68e8"
            android:gravity="center"
            android:onClick="@{()->callback.createTeam()}"
            android:text="@string/create_new_company"
            android:textColor="@color/app_white"
            android:textSize="17sp" />


    </LinearLayout>
</layout>