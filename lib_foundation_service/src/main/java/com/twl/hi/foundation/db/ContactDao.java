package com.twl.hi.foundation.db;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.RoomWarnings;
import androidx.room.Update;

import com.twl.hi.foundation.api.response.bean.SimpleContactBean;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.ContactCachedDTO;
import com.twl.hi.foundation.model.ConversationSelectBean;
import com.twl.hi.foundation.model.GroupMember;
import com.twl.hi.foundation.model.GroupSearchMemberContact;

import java.util.List;

import hi.kernel.Constants;
import kotlinx.coroutines.flow.Flow;

@Dao
public interface ContactDao {
    @Query("SELECT * FROM TB_CONTACT WHERE status < 2 AND (visible = 1 or relation = 1 ) AND userType = 1 AND "
            + "(instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0 or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != ''")
    List<Contact> findContactByName(String name);

    @Query("SELECT * FROM TB_CONTACT WHERE status < 2 AND (((visible = 1 or relation = 1 )  AND userType = 1) or (userId = '" + Constants.CHAT_ID_MSG_SCHEDULE_ROBOT + "')) AND "
            + " (instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0  or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != '' LIMIT 7")
    List<Contact> findContactByNameForLimit(String name);

    @Query("SELECT c.userId FROM TB_CONTACT c,TB_CONVERSATION co WHERE "
            + "(c.status < 2 AND co.type = 1 AND userType = 1 AND c.userId = co.chatId ) AND "
            + "(instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0  or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != '' order by co.sortVersion desc")
    List<String> findContactIdsByNameWithChat(String name);

    @Query("SELECT c.userId,c.userName,c.nickName,c.remark FROM TB_CONTACT c,TB_CONVERSATION co WHERE "
            + "(c.status < 2 AND co.type = 1 AND userType = 1 AND c.userId = co.chatId ) AND "
            + "(instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0  or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != '' order by co.sortVersion desc")
    List<SimpleContactBean> findContactsByNameWithChat(String name);

    @Query("SELECT userId FROM TB_CONTACT WHERE "
            + "((status < 2 AND userType = 1 AND (visible = 1 ))) AND "
            + "(userId NOT IN (SELECT chatId FROM tb_conversation WHERE type = 1)) AND "
            + "(instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0  or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != '' order by userNamePy")
    List<String> findContactIdsByNameWithNoChat(String name);

    @Query("SELECT userId,userName,nickName,remark  FROM TB_CONTACT WHERE "
            + "((status < 2 AND userType = 1 AND (visible = 1 ))) AND "
            + "(userId NOT IN (SELECT chatId FROM tb_conversation WHERE type = 1)) AND "
            + "(instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0  or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != '' order by userNamePy")
    List<SimpleContactBean> findContactsByNameWithNoChat(String name);

    @Query("SELECT userId FROM TB_CONTACT WHERE "
            + "(status < 2 AND userType = 1) AND "
            + "(userId NOT IN (SELECT chatId FROM tb_conversation WHERE type = 1)) AND "
            + "(instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0  or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != '' order by userNamePy")
    List<String> findAtAllContactIdsByNameWithNoChat(String name);

    @Query("SELECT c.userId as chatId, 1 as type,c.avatar as avatar, c.userName as userName, c.nickName as nickName , c.remark as remark,-1 as deptId,0 as groupMemberCount, c.aiRobot " +
            "FROM TB_CONTACT c " +
            "WHERE status < 2 AND chatVisible = 1 AND (userType = 1 or c.userId = '" + Constants.CHAT_ID_FILE_HELPER + "') AND "
            + "(instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0 or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != ''")
    List<ConversationSelectBean> findChatVisibleContactByName(String name);

    @SuppressWarnings(RoomWarnings.CURSOR_MISMATCH)
    @Query("SELECT c.userId as chatId, 1 as type,c.avatar as avatar, c.userName as userName, c.nickName as nickName , c.remark as remark,-1 as deptId,0 as groupMemberCount, c.aiRobot FROM TB_CONTACT c,TB_CONVERSATION co WHERE "
            + "(c.status < 2 AND co.type = 1 AND (userType = 1 or c.userId = '" + Constants.CHAT_ID_FILE_HELPER + "') AND c.userId = co.chatId ) AND "
            + "(instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0  or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != '' order by co.sortVersion desc")
    List<ConversationSelectBean> findSingleConversationByNameWithChat(String name);

    @SuppressWarnings(RoomWarnings.CURSOR_MISMATCH)
    @Query("SELECT c.userId as chatId, 1 as type,c.avatar as avatar, c.userName as userName, c.nickName as nickName, c.remark as remark ,-1 as deptId,0 as groupMemberCount, c.aiRobot FROM TB_CONTACT c WHERE "
            + "((status < 2 AND ((userType = 1 AND (visible = 1 )) or c.userId = '" + Constants.CHAT_ID_FILE_HELPER + "'))) AND "
            + "(userId NOT IN (SELECT chatId FROM tb_conversation WHERE type = 1)) AND "
            + "(instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0  or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != '' order by userNamePy")
    List<ConversationSelectBean> findSingleConversationByNameWithNoChat(String name);

    @Query("SELECT c.* FROM TB_CONTACT c, TB_GROUP_MEMBER m "
            + "WHERE m.groupId =:groupId AND m.status = 0 AND c.userType = 1 AND c.userId = m.userId AND c.status < 2 AND (instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0 or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != ''")
    List<Contact> findGroupContactByName(String name, String groupId);

    @Query("SELECT c.userId FROM TB_CONTACT c, TB_GROUP_MEMBER m WHERE m.groupId =:groupId AND m.status = 0 AND c.userType = 1 AND c.userId = m.userId AND c.status < 2 AND (instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0 or instr(remarkParticiples, :name) > 0) AND userName is not null and userName != ''")
    List<String> findGroupContactIdByName(String name, String groupId);

    @Query("SELECT c.* FROM TB_CONTACT c, TB_GROUP_MEMBER m WHERE m.groupId=:groupId AND m.status = 0 AND c.userType = 1 AND c.userId = m.userId AND c.status < 2 AND userName is not null and userName != '' order by createTime")
    List<Contact> findGroupContacts(String groupId);

    /**
     * 联系人 基础数据和动态数据 不是同一时间下发, 需要判断userName是否为空
     * @param id
     * @return
     */
    @Query("SELECT * FROM TB_CONTACT WHERE userId = :id AND userName is not null and userName != '' LIMIT 1")
    Contact findContactById(String id);

    @Query("SELECT * FROM TB_CONTACT WHERE userId IN (:ids) AND userName is not null and userName != ''")
    List<Contact> findContactByIds(List<String> ids);

    @Query("SELECT userName || remark FROM TB_CONTACT WHERE userId IN (:ids)")
    Flow<List<String>> listenContactNameChange(List<String> ids);

    @Query("SELECT * FROM TB_CONTACT WHERE status < 2 AND (visible = 1 or relation = 1 ) AND userType = 1 AND userName is not null and userName != '' order by userNamePy COLLATE NOCASE ASC")
    List<Contact> getContacts();

    @Query("SELECT userId, userName, userNamePy, nickName, remark, remarkPy, chatVisible, title, status, avatar, tinyAvatar, visible, deptIds, " +
            "userType, email, relation, gender, introduce, tag, showDeptIds, avatarDecoration, aiRobot " +
            "FROM TB_CONTACT a where userName is not null and userName != '' order by userNamePy COLLATE NOCASE ASC")
    List<ContactCachedDTO> getAllContactsCached();

    @Query("SELECT userId FROM TB_CONTACT WHERE instr(deptIds, :depId) > 0 AND status < 2 AND visible = 1 AND userType = 1 AND userName is not null and userName != '' order by userNamePy")
    List<String> findContactsByDepId(String depId);

    @SuppressWarnings(RoomWarnings.CURSOR_MISMATCH)
    @Query("SELECT c.userId as chatId, 1 as type,c.avatar as avatar, c.userName as userName, c.nickName as nickName ,-1 as deptId,',0,' as chatGroupIdList,0 as groupMemberCount, c.aiRobot " +
            "FROM TB_CONTACT c " +
            "WHERE instr(c.deptIds, :depId) > 0 AND c.status < 2 AND (c.visible = 1 or c.relation = 1) AND c.userType = 1 AND userName is not null and userName != '' order by c.userNamePy")
    List<ConversationSelectBean> findConversationByDepId(String depId);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Contact contact);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(List<Contact> contact);

    @Update
    void update(Contact contact);

    @Update
    void update(List<Contact> contact);

    @Query("UPDATE TB_CONTACT SET avatarDecoration = :avatarDecoration WHERE userId = :userId")
    int updateAvatarDecoration(String userId, String avatarDecoration);

    @Query("SELECT g.userId FROM TB_GROUP_MEMBER g ,TB_CONTACT c WHERE g.groupId=:groupId AND g.status = 0 AND c.userId = g.userId AND userName is not null and userName != '' order by c.userNamePy COLLATE NOCASE ASC")
    List<String> getGroupMembersIds(String groupId);

    @Query("SELECT g.userId FROM TB_GROUP_MEMBER g ,TB_CONTACT c WHERE g.groupId=:groupId AND g.status = 0 AND c.userId = g.userId AND userName is not null and userName != '' order by g.createTime ASC, c.userNamePy COLLATE NOCASE ASC")
    List<String> getGroupMembersIdsOrderByTime(String groupId);

    @Query("SELECT g.userId FROM TB_GROUP_MEMBER g ,TB_CONTACT c WHERE g.groupId=:groupId AND g.status = 0 AND c.userId = g.userId AND (instr(c.userNameParticiples, :searchKey) > 0 or instr(c.nickNameParticiples, :searchKey) > 0 or instr(remarkParticiples, :searchKey) > 0) AND userName is not null and userName != '' order by g.createTime ASC, c.userNamePy COLLATE NOCASE ASC")
    List<String> searchGroupMembersIdsByNameOrderByTime(String groupId, String searchKey);

    @Query("SELECT g.userId FROM TB_GROUP_MEMBER g ,TB_CONTACT c WHERE instr(c.aboveDeptIds, :deptId) = 0 AND g.groupId=:groupId AND g.status = 0 AND c.userId = g.userId AND userName is not null and userName != '' order by g.createTime COLLATE NOCASE ASC")
    List<String> getGroupMembersIdsOutDepartmentByTime(String groupId, String deptId);

    @Query("SELECT" +
            "    COUNT(*)" +
            "FROM" +
            "    TB_GROUP_MEMBER g" +
            "    JOIN TB_CONTACT c ON c.userId = g.userId AND userName is not null and userName != ''" +
            "    JOIN tb_group gp ON g.groupId = gp.groupId " +
            "WHERE" +
            "    g.groupId = :groupId" +
            "    AND g.status = 0" +
            "    AND instr(c.aboveDeptIds,  gp.deptId) = 0")
    int getMemberCountOutDepartment(String groupId);

    @Query("SELECT count(*) FROM TB_GROUP_MEMBER g ,TB_CONTACT c WHERE g.groupId=:groupId AND g.status = 0 AND c.userId = g.userId AND userName is not null and userName != ''")
    int getGroupMemberCount(String groupId);

    @Query("SELECT" +
            "    m.userId " +
            "FROM" +
            "    TB_GROUP_MEMBER m" +
            "    JOIN TB_CONTACT c ON c.userId = m.userId AND userName is not null and userName != ''" +
            "    JOIN tb_group gp ON m.groupId = gp.groupId " +
            "WHERE" +
            "    instr(c.aboveDeptIds,  gp.deptId) = 0" +
            "    AND m.groupId =:groupId " +
            "    AND m.status = 0" +
            "    AND c.userType = 1" +
            "    AND c.status < 2" +
            "    AND (instr(c.userNameParticiples,:name) > 0 or instr(c.nickNameParticiples,:name) > 0 or instr(remarkParticiples, :name) > 0)")
    List<String> findGroupMembersIdsOutDepartmentByName(String name, String groupId);

    @Query("SELECT c.* FROM TB_GROUP_MEMBER g ,TB_CONTACT c  WHERE g.groupId=:groupId AND g.status = 0 AND c.userId IN (:ids) AND g.userId = c.userId AND userName is not null and userName != '' order by g.createTime")
    List<Contact> getGroupContactInIds(String groupId, List<String> ids);

    @Query("SELECT c.* FROM TB_GROUP_MEMBER g ,TB_CONTACT c  WHERE g.groupId=:groupId AND g.status = 0 AND c.userId NOT IN (:ids) AND g.userId = c.userId AND userName is not null and userName != '' order by g.createTime limit :limit")
    List<Contact> getGroupContactNotInIds(String groupId, List<String> ids, int limit);


    @Query("SELECT * FROM TB_GROUP_MEMBER WHERE groupId=:groupId")
    List<GroupMember> getHistoryGroupMembers(String groupId);

    @Query("SELECT c.status, c.avatar, c.userId, c.userName, c.remark, c.nickName, c.deptIds, m.status as groupStatus "
            + "FROM TB_CONTACT c, tb_group_member m WHERE m.groupId = :groupId AND m.status = 0 AND c.userId = m.userId AND c.status < 2 AND c.userId in (:ids) AND userName is not null and userName != '' ORDER BY c.userNamePy")
    List<GroupSearchMemberContact> findContactByIdsWithDataSource(List<String> ids, String groupId);

    @Query("SELECT * FROM TB_CONTACT WHERE instr(deptIds, :depId) > 0 AND status < 2 AND visible = 1 AND userType = 1 AND userName is not null and userName != '' ORDER BY userNamePy")
    LiveData<List<Contact>> findContactsLiveDataByDepId(String depId);

    @Query("SELECT * FROM TB_CONTACT WHERE status < 2 AND userType = 1 AND "
            + "(instr(userNameParticiples, :name) > 0 or instr(nickNameParticiples, :name) > 0 or instr(remarkParticiples, :name) > 0 ) AND userName is not null and userName != ''")
    List<Contact> searchGroupContactsByName(String name);

    @Query("SELECT * FROM TB_CONTACT WHERE userType = 1 and status < 2 and relation = 1 AND userName is not null and userName != ''")
    List<Contact> getFriendsOrChatRelation();

    @Query("SELECT * FROM TB_CONTACT WHERE aiRobot = 1 and visible = 1 and status < 2 AND userName is not null and userName != ''")
    List<Contact> getAIFriends();

    @Query("SELECT count(userId) FROM TB_CONTACT WHERE userType = 1 and status < 2 and relation = 1 AND userName is not null and userName != ''")
    LiveData<Integer> getFriendsCount();

    @Query("SELECT * FROM TB_CONTACT WHERE userId = :uid AND userName is not null and userName != ''")
    LiveData<Contact> getContactLiveDataById(String uid);

    @Query("SELECT * FROM TB_CONTACT WHERE status < 2 AND userType = 1 AND userName is not null and userName != '' order by userNamePy COLLATE NOCASE ASC")
    List<Contact> getAtAllContacts();

    @Query("SELECT * FROM TB_GROUP_MEMBER WHERE groupId=:groupId AND userId=:userId AND status = 0 order by createTime")
    GroupMember getGroupMember(String groupId, String userId);

    @Query("SELECT * FROM TB_CONTACT WHERE email=:mailAddress AND userName is not null and userName != ''")
    Contact getContactByMailAddr(String mailAddress);

    @Query("SELECT " +
            "    g.userId " +
            "FROM " +
            "    TB_GROUP_MEMBER g " +
            "    JOIN TB_CONTACT c ON c.userId = g.userId " +
            "\tJOIN tb_group gp ON g.groupId = gp.groupId " +
            "WHERE " +
            "    instr(c.aboveDeptIds,  gp.deptId) = 0 " +
            "    AND g.groupId =:groupId " +
            "    AND g.status = 0 " +
            "    AND userName is not null and userName != '' " +
            "ORDER BY " +
            "    g.createTime COLLATE NOCASE ASC " +
            "LIMIT " +
            "    (:currentPage - 1) * :pageSize, :pageSize")
    List<String> getGroupMembersIdsOutDepartmentByTimeByPage(String groupId, int currentPage, int pageSize);

    @Query("SELECT g.userId FROM TB_GROUP_MEMBER g ,TB_CONTACT c WHERE g.groupId=:groupId AND g.status = 0 AND c.userId = g.userId AND userName is not null and userName != '' order by g.createTime ASC, c.userNamePy COLLATE NOCASE ASC limit (:currentPage - 1) * :pageSize, :pageSize")
    List<String> getGroupMembersIdsOrderByTimeByPage(String groupId, int currentPage, int pageSize);

    @Query("SELECT * FROM TB_CONTACT order by userNamePy COLLATE NOCASE ASC limit (:currentPage - 1) * :pageSize, :pageSize")
    List<Contact> getAllContactsByPage(int currentPage, int pageSize);

    @Query("SELECT c.* FROM tb_contact c, tb_group_robot r WHERE r.groupId=:groupId AND r.status != 0 AND r.type = 1 AND c.userId = r.robotId AND userName is not null and userName != '' ORDER BY r.createTime DESC")
    List<Contact> getGroupRobotContact(String groupId);

    @Query("DELETE FROM tb_contact")
    void deleteAll();

    @Query("DELETE FROM tb_contact WHERE userId = :userId")
    void deleteContact(String userId);

    @Insert(onConflict = OnConflictStrategy.ABORT)
    long insertAbortWhenConflict(Contact contact);

    @Query("UPDATE tb_contact set relation = 1 where userId = :chatId")
    Integer updateContactRelation(String chatId);

    @Query("UPDATE tb_contact set relation = 1 where userId in (:chatIds)")
    void updateContactRelationList(List<String> chatIds);

    /**
     * 仅查询id存在的Contact实体数据
     * @param id
     * @return
     */
    @Query("SELECT * FROM TB_CONTACT WHERE userId = :id")
    Contact isContactIdExist(String id);

    @Query("UPDATE tb_contact set relation=1 WHERE relation!=1 and userId in(SELECT chatId from tb_conversation where type=1)")
    void updateContactRelationCorrect();
}
