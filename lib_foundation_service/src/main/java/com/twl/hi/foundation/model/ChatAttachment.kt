package com.twl.hi.foundation.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import lib_twl_db_config.DBConstants

/**
 * 聊天附件实体类
 * 用于存储聊天中的附件信息到数据库
 */
@Entity(tableName = DBConstants.TAB_CHAT_ATTACHMENT)
data class ChatAttachment(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,                                 // 唯一标识 (文件MD5_chatId)

    @ColumnInfo(name = "chat_id")
    val chatId: String,                             // 聊天ID

    //ChatType
    @ColumnInfo(name = "chat_type")
    val chatType: Int,                              // 聊天类型

    @ColumnInfo(name = "file_name")
    val fileName: String,                           // 文件名

    @ColumnInfo(name = "file_path")
    val filePath: String? = null,                   // 上传后的文件地址

    @ColumnInfo(name = "local_path")
    val localPath: String? = null,                  // 本地文件路径（Uri字符串）

    @ColumnInfo(name = "local_file_path")
    val localFilePath: String? = null,              // 本地实际文件路径（如果可获取）

    @ColumnInfo(name = "original_uri")
    val originalUri: String? = null,                // 原始选择的Uri（用于重试）

    @ColumnInfo(name = "file_size")
    val fileSize: Long = 0,                         // 文件大小（字节）

    @ColumnInfo(name = "mime_type")
    val mimeType: String = "",                      // MIME类型

    @ColumnInfo(name = "file_type")
    val fileType: Int = 0,                          // 文件类型 0:文档 1:图片 2:视频

    @ColumnInfo(name = "upload_status")
    val uploadStatus: Int = 0,                      // 上传状态 0:待上传 1:上传中 2:上传成功 3:上传失败

    @ColumnInfo(name = "upload_progress")
    val uploadProgress: Int = 0,                    // 上传进度 (0-100)

    @ColumnInfo(name = "error_message")
    val errorMessage: String? = null,               // 错误信息

    @ColumnInfo(name = "thumbnail_path")
    val thumbnailPath: String? = null,              // 缩略图路径（图片/视频）

    // 图片相关字段
    @ColumnInfo(name = "origin_url")
    val originUrl: String? = null,                  // 原图URL

    @ColumnInfo(name = "origin_width")
    val originWidth: Int = 0,                       // 原图宽度

    @ColumnInfo(name = "origin_height")
    val originHeight: Int = 0,                      // 原图高度

    @ColumnInfo(name = "tiny_url")
    val tinyUrl: String? = null,                    // 缩略图URL

    @ColumnInfo(name = "tiny_width")
    val tinyWidth: Int = 0,                         // 缩略图宽度

    @ColumnInfo(name = "tiny_height")
    val tinyHeight: Int = 0,                        // 缩略图高度

    // 视频相关字段
    @ColumnInfo(name = "video_url")
    val videoUrl: String? = null,                   // 视频URL

    @ColumnInfo(name = "video_cover_url")
    val videoCoverUrl: String? = null,              // 视频封面URL

    @ColumnInfo(name = "video_width")
    val videoWidth: Int = 0,                        // 视频宽度

    @ColumnInfo(name = "video_height")
    val videoHeight: Int = 0,                       // 视频高度

    @ColumnInfo(name = "video_duration")
    val videoDuration: Int = 0,                    // 视频时长（毫秒）

    @ColumnInfo(name = "video_v_url")
    val videoVUrl: String? = null,                  //其实就是 fileId

    @ColumnInfo(name = "video_url_source")          //0 老版本视频，下载再播；1 新版本 V 组视频，点播
    val videoUrlSource: Int = 0,

    // 消息相关字段
    @ColumnInfo(name = "message_type")
    val messageType: Int = 2,                       // 最终发送的消息类型 0:图片消息 1:视频消息 2:文件消息

    @ColumnInfo(name = "message_id")
    val messageId: Long? = null,                    // 关联的消息ID（发送成功后）

    // 扩展信息
    @ColumnInfo(name = "ext_info")
    val extInfo: String? = null,                    // 扩展信息JSON

    // 时间戳
    @ColumnInfo(name = "create_time")
    val createTime: Long = System.currentTimeMillis(),  // 创建时间

    @ColumnInfo(name = "update_time")
    val updateTime: Long = System.currentTimeMillis()   // 更新时间
) {

    companion object {
        // 文件类型常量
        const val FILE_TYPE_DOCUMENT = 0
        const val FILE_TYPE_IMAGE = 1
        const val FILE_TYPE_VIDEO = 2

        // 上传状态常量
        const val UPLOAD_STATUS_PENDING = 0
        const val UPLOAD_STATUS_UPLOADING = 1
        const val UPLOAD_STATUS_SUCCESS = 2
        const val UPLOAD_STATUS_FAILED = 3

        // 消息类型常量
        const val MESSAGE_TYPE_IMAGE = 0
        const val MESSAGE_TYPE_VIDEO = 1
        const val MESSAGE_TYPE_FILE = 2
    }

    /**
     * 是否为图片类型
     */
    fun isImage(): Boolean = fileType == FILE_TYPE_IMAGE

    /**
     * 是否为视频类型
     */
    fun isVideo(): Boolean = fileType == FILE_TYPE_VIDEO

    /**
     * 是否为文档类型
     */
    fun isDocument(): Boolean = fileType == FILE_TYPE_DOCUMENT

    /**
     * 是否上传中
     */
    fun isUploading(): Boolean = uploadStatus == UPLOAD_STATUS_UPLOADING

    /**
     * 是否上传成功
     * 修复：只检查状态和文件路径，不依赖错误信息
     * 因为错误信息可能在状态更新后没有及时清空
     */
    fun isUploadSuccess(): Boolean = uploadStatus == UPLOAD_STATUS_SUCCESS && !filePath.isNullOrEmpty()

    /**
     * 是否上传失败
     */
    fun isUploadFailed(): Boolean = uploadStatus == UPLOAD_STATUS_FAILED

    /**
     * 是否待上传
     */
    fun isPending(): Boolean = uploadStatus == UPLOAD_STATUS_PENDING

    /**
     * 获取文件扩展名
     */
    fun getFileExtension(): String {
        return if (fileName.contains(".")) {
            fileName.substring(fileName.lastIndexOf(".") + 1).lowercase()
        } else {
            ""
        }
    }

    /**
     * 格式化文件大小
     */
    fun getFormattedFileSize(): String {
        return when {
            fileSize < 1024 -> "${fileSize}B"
            fileSize < 1024 * 1024 -> String.format("%.1fKB", fileSize / 1024.0)
            fileSize < 1024 * 1024 * 1024 -> String.format("%.1fMB", fileSize / (1024.0 * 1024.0))
            else -> String.format("%.1fGB", fileSize / (1024.0 * 1024.0 * 1024.0))
        }
    }

    /**
     * 获取最终的文件URL
     */
    fun getFinalUrl(): String? {
        return when (messageType) {
            MESSAGE_TYPE_IMAGE -> originUrl ?: filePath
            MESSAGE_TYPE_VIDEO -> videoUrl ?: filePath
            MESSAGE_TYPE_FILE -> filePath
            else -> filePath
        }
    }

    /**
     * 获取本地文件Uri（用于重试上传）
     */
    fun getLocalUri(): android.net.Uri? {
        return try {
            when {
                originalUri != null -> android.net.Uri.parse(originalUri)
                localPath != null -> android.net.Uri.parse(localPath)
                else -> null
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 获取本地文件File对象（如果可能）
     */
    fun getLocalFile(): java.io.File? {
        return try {
            when {
                localFilePath != null -> java.io.File(localFilePath)
                localPath?.startsWith("file://") == true -> {
                    java.io.File(android.net.Uri.parse(localPath).path ?: return null)
                }
                else -> null
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 检查本地文件是否存在
     */
    fun isLocalFileAvailable(): Boolean {
        val file = getLocalFile()
        return file?.exists() == true
    }

    /**
     * 检查是否可以重试上传
     */
    fun canRetryUpload(): Boolean {
        return when {
            isUploadSuccess() -> false // 已成功，无需重试
            isUploading() -> false // 正在上传，不能重试
            getLocalUri() != null -> true // 有Uri可以尝试
            isLocalFileAvailable() -> true // 本地文件存在
            else -> false
        }
    }

    // ================================ UI 扩展方法 ================================

    /**
     * 获取上传状态描述
     */
    fun getUploadStatusText(): String = when (uploadStatus) {
        UPLOAD_STATUS_PENDING -> "待上传"
        UPLOAD_STATUS_UPLOADING -> "上传中 $uploadProgress%"
        UPLOAD_STATUS_SUCCESS -> "上传成功"
        UPLOAD_STATUS_FAILED -> "上传失败"
        else -> "未知状态"
    }

    /**
     * 获取文件类型描述
     */
    fun getFileTypeText(): String = when (fileType) {
        FILE_TYPE_IMAGE -> "图片"
        FILE_TYPE_VIDEO -> "视频"
        FILE_TYPE_DOCUMENT -> "文档"
        else -> "未知类型"
    }

    /**
     * 获取进度百分比文本
     */
    fun getProgressText(): String {
        return when (uploadStatus) {
            UPLOAD_STATUS_UPLOADING -> "$uploadProgress%"
            UPLOAD_STATUS_SUCCESS -> "100%"
            else -> "0%"
        }
    }

    /**
     * 获取显示名称（用于UI显示）
     */
    fun getDisplayName(): String {
        return if (fileName.isNotEmpty()) {
            fileName
        } else {
            "未知文件"
        }
    }

    /**
     * 是否可以删除
     */
    fun canDelete(): Boolean {
        return uploadStatus != UPLOAD_STATUS_UPLOADING
    }

    /**
     * 获取视频尺寸
     */
    fun getVideoSize(): Pair<Int, Int>? {
        return if (videoWidth > 0 && videoHeight > 0) {
            Pair(videoWidth, videoHeight)
        } else null
    }

    /**
     * 获取图片尺寸
     */
    fun getImageSize(): Pair<Int, Int>? {
        return if (originWidth > 0 && originHeight > 0) {
            Pair(originWidth, originHeight)
        } else null
    }

    /**
     * 是否有上传结果
     */
    fun hasUploadResult(): Boolean {
        return when (messageType) {
            MESSAGE_TYPE_IMAGE -> !originUrl.isNullOrEmpty()
            MESSAGE_TYPE_VIDEO -> !videoUrl.isNullOrEmpty()
            MESSAGE_TYPE_FILE -> !filePath.isNullOrEmpty()
            else -> !filePath.isNullOrEmpty()
        }
    }

    /**
     * 获取消息类型描述
     */
    fun getMessageTypeText(): String {
        return when (messageType) {
            MESSAGE_TYPE_IMAGE -> "图片消息"
            MESSAGE_TYPE_VIDEO -> "视频消息"
            MESSAGE_TYPE_FILE -> "文件消息"
            else -> "文件消息"
        }
    }
}