package com.twl.hi.foundation.api.response.bean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/13.
 */
public class ShiningSendBean extends ShiningBaseBean<ShiningSendBean> {
    public List<ShiningAtUser> users;//@列表
    public ShiningSendExtBean ext;

    @Override
    public int getShiningType() {
        return ShiningBaseBean.SHINING_TYPE_SEND;
    }

    public static class ShiningAtUser {
        public String userId;// 用户id
        public int status;// 状态，0-未处理，1-已处理，2-稍后处理

        public ShiningAtUser() {
        }

        public ShiningAtUser(String userId, int status) {
            this.userId = userId;
            this.status = status;
        }
    }

    public String getTaskId() {
        if (ext != null) {
            return ext.taskId;
        }
        return "";
    }

    /**
     * 获取好友申请的会话id
     *
     * @return
     */
    public String getAddChatId() {
        if (ext != null) {
            return ext.shiningToId;
        }
        return "";
    }
}
