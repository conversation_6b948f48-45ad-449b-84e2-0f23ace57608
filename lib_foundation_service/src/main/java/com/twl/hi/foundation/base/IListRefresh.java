package com.twl.hi.foundation.base;

import android.annotation.SuppressLint;

import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;

/**
 * <AUTHOR>
 * 刷新页面能力，需要手动调用 registerHeadUrlLiveData 注册
 *
 */
public interface IListRefresh {

    /**
     * 是否强制全量刷新可见的item列表，不做是否包含的判断
     * 如部分无法根据itemId判断包含关系的页面，可重写此方法为true强制刷新
     * @return
     */
    default boolean forceUpdateVisibleItem() {
        return false;
    }

    /**
     * 获取list
     * @return
     */
    default RecyclerView getRecyclerView() {
        return null;
    }

    /**
     * 页面为联系人头像时打开监听
     * @return
     */
    default boolean listenContactChange() {
        return false;
    }

    /**
     * 通知联系人更新
     * 非recyclerview需要刷新时通知
     * @param contact
     */
    void onContactAvatarUpdate(Contact contact);

    /**
     * 获取owner
     * @return
     */
    default LifecycleOwner getRefreshPageOwner() {
        return null;
    }

    /**
     * 主动注册页面list中头像数据的刷新监听
     */
    default void registerHeadUrlLiveData() {
        if (getRefreshPageOwner() == null) {
            return;
        }

        // 注册liveData监听
        ServiceManager.getInstance().getProfileService().getHeadUrlLiveData().observe(getRefreshPageOwner(), new Observer<Contact>() {
            @SuppressLint("NotifyDataSetChanged")
            @Override
            public void onChanged(Contact contact) {
                if (contact == null) {
                    return;
                }

                TLog.info("IListRefresh", "IListRefresh -> notify avatar change ");
                // 不做数据判断，强制刷新可见item
                if (forceUpdateVisibleItem()) {
                    onContactAvatarUpdate(contact);
                    RecyclerView recyclerView = getRecyclerView();
                    if (recyclerView != null && recyclerView.getAdapter() != null) {
                        recyclerView.getAdapter().notifyDataSetChanged();
                    }
                    return;
                }

                if (listenContactChange()) {
                    onContactAvatarUpdate(contact);
                    RecyclerView recyclerView = getRecyclerView();
                    if (recyclerView != null && recyclerView.getAdapter() != null) {
                        recyclerView.getAdapter().notifyDataSetChanged();
                    }
                }
            }
        });
    }

    /**
     * 是否包含item, 包含则刷新
     * 通过adapter的getItemId判断界面中可见item是否存在变更
     * 刷新单个or可见item
     * @param itemId
     */
    default void updateVisibleItem() {
        if (!checkViewValidAndExist()) {
            return;
        }

        RecyclerView recyclerView = getRecyclerView();
        if (recyclerView == null) {
            return;
        }
        int first = findFirstVisibleItemPosition(recyclerView);
        int last = findLastVisibleItemPosition(recyclerView);
        RecyclerView.Adapter adapter = recyclerView.getAdapter();
        if (first < 0 || last < 0 || first > adapter.getItemCount() || last > adapter.getItemCount()) {
            return;
        }

        // 变化多个，直接更新可见item，非可见item在滑动到界面时dataBinding会重新绑定新的item值，自动刷新界面
        if (last < recyclerView.getAdapter().getItemCount()) {
            int itemCount = last - first + 1;
            adapter.notifyItemRangeChanged(first, itemCount);
        }
    }

    default int findLastVisibleItemPosition(RecyclerView recyclerView) {
        if (recyclerView.getLayoutManager() instanceof LinearLayoutManager) {
            return ((LinearLayoutManager) recyclerView.getLayoutManager()).findLastVisibleItemPosition();
        }
        return -1;
    }

    default int findFirstVisibleItemPosition(RecyclerView recyclerView) {
        if (recyclerView.getLayoutManager() instanceof LinearLayoutManager) {
            return ((LinearLayoutManager) recyclerView.getLayoutManager()).findFirstVisibleItemPosition();
        }
        return -1;
    }

    default boolean checkViewValidAndExist() {
        RecyclerView recyclerView = getRecyclerView();
        if (recyclerView == null) {
            return false;
        }
        RecyclerView.Adapter adapter = recyclerView.getAdapter();
        if (adapter == null) {
            return false;
        }
        if (adapter.getItemCount() <= 0) {
            return false;
        }

        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
        if (layoutManager == null) {
            return false;
        }
        return true;
    }
}
