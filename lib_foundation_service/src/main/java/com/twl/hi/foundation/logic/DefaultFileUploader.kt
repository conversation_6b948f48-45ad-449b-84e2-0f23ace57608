package com.twl.hi.foundation.logic

import com.techwolf.lib.tlog.TLog

import com.twl.hi.foundation.api.base.BaseUpdateRequestCallback
import com.twl.hi.foundation.api.request.ImageUploadRequestV2
import com.twl.hi.foundation.api.response.ImageUploadResponse
import com.twl.hi.foundation.api.response.VideoUploadResult
import com.twl.hi.foundation.model.ChatAttachment
import com.twl.hi.foundation.utils.AttachmentFileUtils
import com.twl.hi.basic.download.OkRenewalUpload
import com.twl.hi.basic.download.RenewalUploadTask
import com.twl.hi.basic.download.VideoRenewalUploadTask
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.ProgressRequestBody
import com.twl.http.error.ErrorReason
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import lib.twl.common.base.BaseApplication
import lib.twl.common.model.ImageInfo
import lib.twl.common.util.launchIO
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 默认文件上传器实现
 * 基于队列的单文件上传，参考 FileUploadManager 的实现
 */
class DefaultFileUploader(val scope: CoroutineScope) : IFileUploader {

    companion object {
        private const val TAG = "DefaultFileUploader"
    }

    // 视频上传器（通过依赖注入设置）
    private var videoUploader: IVideoUploader? = null

    // 文档上传器（通过依赖注入设置）
    private var documentUploader: IDocumentUploader? = null
    
    // 上传队列
    private val uploadQueue = LinkedBlockingQueue<IFileUploader.UploadTask>()
    
    // 当前正在上传的任务
    @Volatile
    private var currentTask: IFileUploader.UploadTask? = null
    
    // 是否暂停
    private val isPaused = AtomicBoolean(false)
    
    // 是否已销毁
    private val isDestroyed = AtomicBoolean(false)
    
    // 任务映射表（用于快速查找和取消）
    private val taskMap = ConcurrentHashMap<String, IFileUploader.UploadTask>()

    // 保存活跃的上传任务引用（用于取消RenewalUploadTask）
    private val activeRenewalTasks = ConcurrentHashMap<String, RenewalUploadTask>()
    
    override fun addUploadTask(
        attachment: ChatAttachment,
        file: File,
        callback: IFileUploader.UploadProgressCallback
    ) {
        if (isDestroyed.get()) {
            TLog.error(TAG, "Uploader is destroyed, cannot add task")
            return
        }
        
        TLog.info(TAG, "addUploadTask: ${attachment.id}")
        
        val task = IFileUploader.UploadTask(attachment, file, callback)
        taskMap[attachment.id] = task
        uploadQueue.offer(task)
        
        processQueue()
    }
    
    override fun cancelUpload(attachmentId: String): Boolean {
        TLog.info(TAG, "cancelUpload: $attachmentId")

        val task = taskMap.remove(attachmentId) ?: return false

        // 标记任务为已取消
        task.cancel()

        // 从队列中移除
        uploadQueue.remove(task)

        // ✅ 关键修复：取消实际的上传任务
        val renewalTask = activeRenewalTasks.remove(attachmentId)
        if (renewalTask != null) {
            RenewalUploadTask.cancel(attachmentId)
            TLog.info(TAG, "成功取消RenewalUploadTask: $attachmentId")
        }

        // 如果是当前正在上传的任务
        if (currentTask?.attachment?.id == attachmentId) {
            currentTask = null
            // 通知取消
            task.callback.onUploadCancelled(attachmentId)
            // 继续处理下一个任务
            processQueue()
        } else {
            // 通知取消
            task.callback.onUploadCancelled(attachmentId)
        }

        return true
    }
    
    override fun retryUpload(
        attachmentId: String,
        file: File,
        callback: IFileUploader.UploadProgressCallback
    ): Boolean {
        TLog.info(TAG, "retryUpload: $attachmentId - This method should not be used directly")
        TLog.info(TAG, "Please use ChatAttachmentService.retryUpload() instead for proper data consistency")

        // 这个方法不应该被直接调用，应该通过 ChatAttachmentService 来重试上传
        // 因为 ChatAttachmentService 能确保从数据库获取正确的附件信息
        // 这里保留实现是为了接口兼容性，但建议使用 addUploadTask 替代

        if (isDestroyed.get()) {
            TLog.error(TAG, "Uploader is destroyed, cannot retry upload")
            return false
        }

        // 先确保完全取消原有任务
        val existingTask = taskMap[attachmentId]
        if (existingTask != null) {
            TLog.info(TAG, "Cancelling existing task before retry: $attachmentId")
            // 同步取消，确保任务完全移除
            cancelUpload(attachmentId)
            // 取消操作是同步的，无需等待
        }

        // 由于无法从这里获取正确的附件信息，返回 false
        // 调用方应该使用 ChatAttachmentService.retryUpload() 或直接调用 addUploadTask()
        TLog.error(TAG, "retryUpload failed: Please use ChatAttachmentService.retryUpload() for proper data handling")
        return false
    }
    
    override fun getQueueStatus(): IFileUploader.QueueStatus {
        return IFileUploader.QueueStatus(
            queueSize = uploadQueue.size,
            isUploading = currentTask != null,
            currentUploadingId = currentTask?.attachment?.id
        )
    }
    
    override fun hasActiveUpload(): Boolean {
        return currentTask != null
    }
    
    override fun getCurrentUploadingId(): String? {
        return currentTask?.attachment?.id
    }
    
    override fun clearQueue() {
        TLog.info(TAG, "clearQueue")
        
        // 取消当前任务
        currentTask?.let { task ->
            task.cancel()
            task.callback.onUploadCancelled(task.attachment.id)
            currentTask = null
        }
        
        // 清空队列并通知所有任务被取消
        while (uploadQueue.isNotEmpty()) {
            val task = uploadQueue.poll()
            task?.let {
                it.cancel()
                it.callback.onUploadCancelled(it.attachment.id)
            }
        }
        
        taskMap.clear()
    }
    
    override fun pauseUpload() {
        TLog.info(TAG, "pauseUpload")
        isPaused.set(true)
    }
    
    override fun resumeUpload() {
        TLog.info(TAG, "resumeUpload")
        isPaused.set(false)
        processQueue()
    }
    
    override fun isPaused(): Boolean {
        return isPaused.get()
    }
    
    override fun destroy() {
        TLog.info(TAG, "destroy")
        isDestroyed.set(true)
        clearQueue()
    }

    /**
     * 设置视频上传器
     */
    fun setVideoUploader(uploader: IVideoUploader?) {
        this.videoUploader = uploader
    }

    /**
     * 设置文档上传器
     */
    fun setDocumentUploader(uploader: IDocumentUploader?) {
        this.documentUploader = uploader
    }
    
    /**
     * 处理上传队列
     */
    private fun processQueue() {
        if (isDestroyed.get() || isPaused.get()) {
            return
        }
        
        // 如果当前有任务在上传，等待完成
        if (currentTask != null) {
            return
        }
        
        val task = uploadQueue.poll() ?: return
        
        // 检查任务是否已被取消
        if (task.isCancelled) {
            processQueue() // 继续处理下一个
            return
        }
        
        currentTask = task
        TLog.info(TAG, "processQueue: start upload ${task.attachment.id}, fileType: ${task.attachment.fileType}")

        // 通知开始上传
        task.callback.onUploadStarted(task.attachment.id)
        TLog.info(TAG, "onUploadStarted called for ${task.attachment.id}")

        // 重置进度为0，确保UI显示正确的初始状态
        task.callback.onProgressUpdate(task.attachment.id, 0)

        // 根据文件类型选择上传方式
        when (task.attachment.fileType) {
            ChatAttachment.FILE_TYPE_IMAGE -> {
                TLog.info(TAG, "Starting image upload for ${task.attachment.id}")
                uploadImage(task)
            }
            ChatAttachment.FILE_TYPE_VIDEO -> {
                TLog.info(TAG, "Starting video upload for ${task.attachment.id}")
                uploadVideo(task)
            }
            ChatAttachment.FILE_TYPE_DOCUMENT -> {
                TLog.info(TAG, "Starting document upload for ${task.attachment.id}")
                uploadFile(task)
            }
            else -> {
                TLog.info(TAG, "Starting default file upload for ${task.attachment.id}")
                uploadFile(task) // 默认按文件处理
            }
        }
    }
    
    /**
     * 上传图片
     */
    private fun uploadImage(task: IFileUploader.UploadTask) {
        TLog.info(TAG, "uploadImage: ${task.attachment.fileName}")
        
        val request = ImageUploadRequestV2(object : BaseUpdateRequestCallback<ImageUploadResponse>() {
            override fun handleInChildThread(data: ApiData<ImageUploadResponse>) {
                if (task.isCancelled) return
                
                val response = data.resp
                if (response?.originImage != null && response.tinyImage != null) {
                    // 处理GIF图片
                    if (response.originImage.url?.contains(".gif") == true) {
                        response.tinyImage.url = response.originImage.url
                    }
                    handleImageUploadSuccess(task, response.originImage, response.tinyImage)
                } else {
                    handleUploadFailed(task, "图片上传失败：响应数据异常")
                }
            }
            
            override fun handleErrorInChildThread(reason: ErrorReason) {
                if (task.isCancelled) return
                handleUploadFailed(task, reason.errReason ?: "图片上传失败")
            }
        })
        
        request.file = task.file
        HttpExecutor.uploadWithProgress(request, object : ProgressRequestBody.UploadProgressListener {
            override fun onProgress(url: String?, progress: Float, bytesWritten: Long, totalBytes: Long) {
                if (task.isCancelled) return
                
                // 修复进度精度问题：只有真正达到1.0才算100%
                val progressPercent = if (progress >= 1.0f) {
                    100
                } else {
                    (progress * 100).toInt().coerceAtMost(99) // 确保未完成时不会显示100%
                }
                TLog.debug(TAG, "image upload progress: $progressPercent% (raw: $progress)")

                task.callback.onProgressUpdate(task.attachment.id, progressPercent)
            }
        })
    }
    
    /**
     * 上传视频 - 重构版：使用 VideoRenewalUploadTask
     */
    private fun uploadVideo(task: IFileUploader.UploadTask) {
        TLog.info(TAG, "uploadVideo (重构版): ${task.attachment.fileName}")

        try {
            val context = BaseApplication.getApplication().applicationContext
            val fileResult = AttachmentFileUtils.getFileFromAttachment(context, task.attachment)

            if (fileResult.file == null || !fileResult.file.exists()) {
                TLog.error(TAG, "Video file not found: ${fileResult.errorMessage}")
                handleUploadFailed(task, fileResult.errorMessage ?: "视频文件不存在")
                return
            }

            val file = fileResult.file
            TLog.info(TAG, "Starting video upload with VideoRenewalUploadTask: ${file.absolutePath}")

            // 创建视频上传任务
            val renewalTask = OkRenewalUpload.createVideoUploadTask(
                task.attachment.id,
                file,
                task.attachment
            )

            // 设置进度回调
            renewalTask.setProgressCallback { uploadBytes, totalBytes ->
                if (task.isCancelled) return@setProgressCallback

                val progress = if (totalBytes > 0) (uploadBytes * 100 / totalBytes).toInt() else 0
                TLog.debug(TAG, "Video upload progress: $progress% ($uploadBytes/$totalBytes)")
                task.callback.onProgressUpdate(task.attachment.id, progress)
            }

            // 设置完成回调
            renewalTask.register(object : BaseUpdateRequestCallback<com.twl.hi.foundation.api.response.FileUploadResponse>() {
                override fun handleInChildThread(data: ApiData<com.twl.hi.foundation.api.response.FileUploadResponse>) {
                    if (task.isCancelled) return

                    // 从VideoRenewalUploadTask获取VideoUploadResult
                    val videoResult = renewalTask.progress.videoResult
                    if (videoResult != null) {
                        handleVideoUploadSuccess(task, videoResult)
                    } else {
                        handleUploadFailed(task, "视频上传成功但结果为空")
                    }

                    handleTaskComplete(task)
                }

                override fun handleErrorInChildThread(reason: ErrorReason) {
                    if (task.isCancelled) return

                    TLog.error(TAG, "Video upload failed: ${task.attachment.id}, reason: ${reason.errReason}")
                    handleUploadFailed(task, reason.errReason ?: "视频上传失败")
                    handleTaskComplete(task)
                }
            })

            // 保存任务引用
            activeRenewalTasks[task.attachment.id] = renewalTask

            // 启动上传
            renewalTask.start()

        } catch (e: Exception) {
            TLog.error(TAG, "Error in uploadVideo (重构版)", e)
            handleUploadFailed(task, "视频上传异常：${e.message}")
        }
    }
    
    /**
     * 上传文件 - 重构版：使用 RenewalUploadTask
     */
    private fun uploadFile(task: IFileUploader.UploadTask) {
        TLog.info(TAG, "uploadFile (重构版): ${task.attachment.fileName}")

        try {
            val context = BaseApplication.getApplication().applicationContext
            val fileResult = AttachmentFileUtils.getFileFromAttachment(context, task.attachment)

            if (fileResult.file == null || !fileResult.file.exists()) {
                TLog.error(TAG, "Document file not found: ${fileResult.errorMessage}")
                handleUploadFailed(task, fileResult.errorMessage ?: "文档文件不存在")
                return
            }

            val file = fileResult.file
            TLog.info(TAG, "Starting document upload with RenewalUploadTask: ${file.absolutePath}")

            // 创建文件上传任务
            val renewalTask = OkRenewalUpload.createFileUploadTask(
                task.attachment.id,
                file
            )

            // 设置分片进度回调
            renewalTask.setChunkProgressCallback { completedChunks, totalChunks, progress ->
                if (task.isCancelled) return@setChunkProgressCallback

                val progressPercent = (progress * 100).toInt()
                TLog.debug(TAG, "File chunk progress: $progressPercent% ($completedChunks/$totalChunks)")
                task.callback.onProgressUpdate(task.attachment.id, progressPercent)
            }

            // 设置完成回调
            renewalTask.register(object : BaseUpdateRequestCallback<com.twl.hi.foundation.api.response.FileUploadResponse>() {
                override fun handleInChildThread(data: ApiData<com.twl.hi.foundation.api.response.FileUploadResponse>) {
                    if (task.isCancelled) return

                    val response = data.resp
                    if (response != null && !response.url.isNullOrEmpty()) {
                        TLog.info(TAG, "File upload success: ${task.attachment.id}, url: ${response.url}")
                        handleFileUploadSuccess(task, response.url)
                    } else {
                        handleUploadFailed(task, "文件上传失败：服务器返回空结果")
                    }

                    handleTaskComplete(task)
                }

                override fun handleErrorInChildThread(reason: ErrorReason) {
                    if (task.isCancelled) return

                    TLog.error(TAG, "File upload failed: ${task.attachment.id}, reason: ${reason.errReason}")
                    handleUploadFailed(task, reason.errReason ?: "文件上传失败")
                    handleTaskComplete(task)
                }
            })

            // 保存任务引用
            activeRenewalTasks[task.attachment.id] = renewalTask

            // 启动上传
            renewalTask.start()

        } catch (e: Exception) {
            TLog.error(TAG, "Error in uploadFile (重构版)", e)
            handleUploadFailed(task, "文件上传异常：${e.message}")
        }
    }
    
    /**
     * 处理图片上传成功
     */
    private fun handleImageUploadSuccess(
        task: IFileUploader.UploadTask,
        originImage: ImageInfo,
        tinyImage: ImageInfo
    ) {
        // 检查任务是否已被取消
        if (task.isCancelled) {
            TLog.info(TAG, "任务已取消，忽略图片上传成功回调: ${task.attachment.id}")
            return
        }

        TLog.info(TAG, "handleImageUploadSuccess: ${task.attachment.id}")

        val updatedAttachment = task.attachment.copy(
            uploadStatus = ChatAttachment.UPLOAD_STATUS_SUCCESS,
            uploadProgress = 100,
            filePath = originImage.url,
            originUrl = originImage.url,
            originWidth = originImage.width,
            originHeight = originImage.height,
            tinyUrl = tinyImage.url,
            tinyWidth = tinyImage.width,
            tinyHeight = tinyImage.height,
            updateTime = System.currentTimeMillis()
        )

        finishUpload(task, updatedAttachment)
    }
    
    /**
     * 处理视频上传成功
     */
    private fun handleVideoUploadSuccess(task: IFileUploader.UploadTask, result: VideoUploadResult?) {
        // 检查任务是否已被取消
        if (task.isCancelled) {
            TLog.info(TAG, "任务已取消，忽略视频上传成功回调: ${task.attachment.id}")
            return
        }

        scope.launchIO{
            val updatedAttachment = ServiceManager.getInstance().chatAttachmentService.getAttachmentById(task.attachment.id)?.copy(
                uploadStatus = ChatAttachment.UPLOAD_STATUS_SUCCESS,
                uploadProgress = 100,
                filePath = result?.url,
                videoUrl = result?.url,
                videoCoverUrl = result?.coverUrl,
                updateTime = System.currentTimeMillis(),
                videoVUrl = result?.fileId,
                videoUrlSource = result?.urlSource?:1
            )
            withContext(Dispatchers.Main){
                if(updatedAttachment==null){
                    TLog.info(TAG, "视频附件已被删除，忽略上传成功回调: ${task.attachment.id}")
                    handleUploadFailed(task,"上传任务已删除")
                }else{
                    finishUpload(task, updatedAttachment)
                }

            }
        }
        TLog.info(TAG, "handleVideoUploadSuccess: ${task.attachment.id}")



    }
    
    /**
     * 处理文件上传成功
     */
    private fun handleFileUploadSuccess(task: IFileUploader.UploadTask, url: String?) {
        // 检查任务是否已被取消
        if (task.isCancelled) {
            TLog.info(TAG, "任务已取消，忽略文件上传成功回调: ${task.attachment.id}")
            return
        }

        TLog.info(TAG, "handleFileUploadSuccess: ${task.attachment.id}, url: $url")

        val updatedAttachment = task.attachment.copy(
            uploadStatus = ChatAttachment.UPLOAD_STATUS_SUCCESS,
            uploadProgress = 100,
            filePath = url,
            updateTime = System.currentTimeMillis()
        )

        TLog.info(TAG, "Created updated attachment with status: ${updatedAttachment.uploadStatus}, progress: ${updatedAttachment.uploadProgress}")
        finishUpload(task, updatedAttachment)
    }
    
    /**
     * 处理上传失败
     */
    private fun handleUploadFailed(task: IFileUploader.UploadTask, errorMessage: String) {
        TLog.error(TAG, "handleUploadFailed: ${task.attachment.id}, error=$errorMessage")
        
        task.callback.onUploadFailed(task.attachment.id, errorMessage)
        
        // 清理并继续下一个任务
        taskMap.remove(task.attachment.id)
        currentTask = null
        processQueue()
    }
    
    /**
     * 统一的任务完成处理
     */
    private fun handleTaskComplete(task: IFileUploader.UploadTask) {
        // 清理任务引用
        activeRenewalTasks.remove(task.attachment.id)

        // 重置当前任务
        if (currentTask == task) {
            currentTask = null
        }

        // 处理下一个任务
        processQueue()
    }

    /**
     * 完成上传
     */
    private fun finishUpload(task: IFileUploader.UploadTask, updatedAttachment: ChatAttachment) {
        TLog.info(TAG, "finishUpload: calling onUploadSuccess for ${updatedAttachment.id}")
        task.callback.onUploadSuccess(updatedAttachment)

        // 清理并继续下一个任务
        taskMap.remove(task.attachment.id)
        currentTask = null
        processQueue()
    }
}
