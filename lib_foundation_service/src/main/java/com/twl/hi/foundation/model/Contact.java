package com.twl.hi.foundation.model;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.twl.hi.foundation.api.response.bean.CompanySetting;
import com.twl.hi.foundation.converter.ContactConverters;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.utils.ContactUtils;
import com.twl.utils.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Stack;

import hi.kernel.Constants;
import hi.kernel.HiKernel;
import lib_twl_db_config.DBConstants;

@Entity(tableName = DBConstants.TAB_CONTACT)
@TypeConverters(ContactConverters.class)
public class Contact implements Serializable {

    public static final int SHOW_NAME_SCENE_NAME_OR_NICK = 0;

    public static final int SHOW_NAME_SCENE_REMARK_OR_NAME_WITH_NICK = 1;

    public static final int SHOW_NAME_SCENE_NAME_WITH_NICK = 2;

    public static final int SHOW_NAME_SCENE_NAME = 3;

    public static final int SHOW_NAME_SCENE_NAME_WITH_NICK_AND_REMARK = 4;
    private static final long serialVersionUID = -8535111054051335643L;
    public static final String DELIMITER = "|";
    @Ignore
    public static final int USER_TYPE_NORMAL = 1;
    @Ignore
    public static final int USER_TYPE_SYSTEM = 2;
    @Ignore
    public static final int USER_STATUS_INIT = 0;
    @Ignore
    public static final int USER_STATUS_NORMAL = 1;
    @Ignore
    public static final int USER_STATUS_RESIGN = 2;

    /********************用户基础数据字段分割线***********************/
    @PrimaryKey
    @NonNull
    private String userId = ""; //人员id,机器人id为小于等于10000
    private String userName; //名字
    private String userNamePy; //全拼
    private List<String> userNamePyList; //  ["zeng lin hong", "ceng lin hong"] 姓名拼音，多音字列表
    private String title;
    private String email; // 邮箱
    private int gender; // 性别，1-男，2-女
    private List<String> deptIds;
    private int status; // 状态：0-初始化，1-正常，2-离职
    private int userType; // 用户类型，1-普通用户，2-系统用户
    private List<String> aboveDeptIds; //用户所在部门以及上级部门id列表


    /********************用户动态数据字段分割线***********************/
    private List<String> nickNamePyList; //  ["zeng lin hong", "ceng lin hong"] 昵称拼音，多音字列表
    private String nickName;
    private String nickNamePy;

    // 用户备注
    private String remark;
    private String remarkPy;
    private List<String> remarkPyList;

    private String avatarDecoration; // 头像挂件
    private String avatar;
    private String tinyAvatar;
    private List<String> showDeptIds; //展示部门id列表
    private CompanySetting companySetting;


    /********************系统用户/机器人的独有字段分割线***********************/
    private String introduce; //bot的介绍,当userType=2时才有
    private String tag; // 机器人服务号提示，当该字段不为空时，显示在聊天页顶部
    @ColumnInfo(defaultValue = "0")
    private int aiRobot = 0; //0:默认，1:ai助理 （3.38新增）

    /********************可见性字段分割线***********************/
    private int visible; // 是否可见部门内用户。1-是，0-否。当为0时，只返回用户id、姓名、姓名拼音、昵称、昵称拼音、头像、状态字段
    @ColumnInfo(defaultValue = "1")
    private int chatVisible = 1; // 是否可以直接开聊 0 - 不可， 1 - 可以

    /********************本地维护字段分割线***********************/
    private int relation; // 用户聊天关系本地维护, 已经开聊或者加好友 (加好友有系统自动发送消息)
    @Ignore
    private String showDeptNames; //展示的所属部门名字列表
    // 搜索分词
    private String userNameParticiples;
    // 搜索分词
    private String nickNameParticiples;

    private String remarkParticiples; // 备注分词

    private String remarkPysString; // 备注拼音
    private String nickNamePysString; //昵称拼音 ",zeng lin hong,ceng lin hong,"
    private String userNamePysString; //姓名拼音 ",zeng lin hong,ceng lin hong,"

    public Contact() {

    }

    public int getChatVisible() {
        return chatVisible;
    }

    public void setChatVisible(int chatVisible) {
        this.chatVisible = chatVisible;
    }

    public String getRemarkPysString() {
        return remarkPysString;
    }

    public void setRemarkPysString(String remarkPysString) {
        this.remarkPysString = remarkPysString;
    }

    public String getRemarkParticiples() {
        return remarkParticiples;
    }

    public void setRemarkParticiples(String remarkParticiples) {
        this.remarkParticiples = remarkParticiples;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemarkPy() {
        return remarkPy;
    }

    public void setRemarkPy(String remarkPy) {
        this.remarkPy = remarkPy;
    }

    public List<String> getRemarkPyList() {
        return remarkPyList;
    }

    public void setRemarkPyList(List<String> remarkPyList) {
        this.remarkPyList = remarkPyList;
    }

    public String getAvatarDecoration() {
        return avatarDecoration;
    }

    public void setAvatarDecoration(String avatarDecoration) {
        this.avatarDecoration = avatarDecoration;
    }

    public String getWorkStatus() {
        if (companySetting == null) {
            return "";
        }
        return companySetting.getWorkStatus();
    }

    public int getWorkStatusId() {
        if (companySetting == null) {
            return 0;
        }
        return companySetting.workStatusId;
    }

    public String getSignature() {
        if (companySetting == null) {
            return "";
        }
        return companySetting.signature;
    }

    public String getFirstChar() {
        if (TextUtils.isEmpty(userNamePy) && TextUtils.isEmpty(remarkPy)) {
            return "#";
        } else {
            char c;
            if (!TextUtils.isEmpty(remarkPy)) {
                c = remarkPy.toUpperCase().charAt(0);
            } else {
                c = userNamePy.toUpperCase().charAt(0);
            }
            if ('A' <= c && c <= 'Z') {
                return String.valueOf(c);
            } else {
                return "#";
            }
        }
    }

    /**
     * 获取名字首字母索引
     * 若名字前包含特殊字符，数字和表情等，直接返回#
     *
     * @return
     */
    public String getFullNameFirstChar() {
        if (TextUtils.isEmpty(userNamePy)) {
            return "#";
        } else {
            char c = userNamePy.toUpperCase().charAt(0);
            if ('A' <= c && c <= 'Z') {
                return String.valueOf(c);
            } else {
                return "#";
            }
        }
    }

    public String getUserNameParticiples() {
        return userNameParticiples;
    }

    public void setUserNameParticiples(String userNameParticiples) {
        this.userNameParticiples = userNameParticiples;
    }

    public String getNickNameParticiples() {
        return nickNameParticiples;
    }

    public void setNickNameParticiples(String nickNameParticiples) {
        this.nickNameParticiples = nickNameParticiples;
    }

    /**
     * 默认 showName
     *
     * @return 存在备注则展示备注否则展示 姓名（昵称）
     */
    public String getShowName() {
        return getShowName(SHOW_NAME_SCENE_REMARK_OR_NAME_WITH_NICK);
    }

    /**
     * 根据场景值返回 showName
     * @param scene 场景
     * @return showName
     */
    public String getShowName(int scene) {
        String showName = "";
        switch (scene) {
            case SHOW_NAME_SCENE_NAME_WITH_NICK:
                showName = nameWithNick();
                break;
            case SHOW_NAME_SCENE_REMARK_OR_NAME_WITH_NICK:
                showName = remarkOrNameWithNick();
                break;
            case SHOW_NAME_SCENE_NAME_OR_NICK:
                showName = nameOrNick();
                break;
            case SHOW_NAME_SCENE_NAME:
                showName = userName;
                break;
            case SHOW_NAME_SCENE_NAME_WITH_NICK_AND_REMARK:
                showName = nameWithNickAndRemark();
                break;
        }
        return showName;
    }

    private String nameWithNickAndRemark() {
        if (!TextUtils.isEmpty(remark)) {
            return nameWithNick() + "（" + remark + "）";
        } else {
            return nameWithNick();
        }
    }

    private String nameWithNick() {
        Stack<String> stack = new Stack<>();
        if (!TextUtils.isEmpty(userName)) {
            stack.push(userName);
        }
        if (!TextUtils.isEmpty(nickName)) {
            stack.push(nickName);
        }
        StringBuilder sb = new StringBuilder();
        while(!stack.isEmpty()) {
            String item = stack.pop();
            if (sb.length() == 0) {
                sb.append(item);
            } else {
                String preAppend = item + "(";
                sb.insert(0, preAppend);
                sb.append(")");
            }
        }
        return sb.toString();
    }

    private String nameOrNick() {
        return TextUtils.isEmpty(getUserName()) ? getNickName() : getUserName();
    }

    private String remarkOrNameWithNick() {
        if (!TextUtils.isEmpty(remark)) {
            return remark;
        } else {
            return nameWithNick();
        }
    }

    public String getChatShowName() {
        return TextUtils.isEmpty(getRemark()) ? getUserName() : getRemark();
    }

    public String getStrAvatar() {
        return ContactUtils.getDisplayAvatarStr(userName);
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getShowDeptNames() {
        if (!TextUtils.isEmpty(showDeptNames)) {
            return showDeptNames;
        }
        StringBuilder sb = new StringBuilder("");
        if (showDeptIds != null && showDeptIds.size() > 0) {
            for (String dept : showDeptIds) {
                String name = null;
                if (StringUtils.isNotEmpty(dept)) {
                    name = ServiceManager.getInstance().getDepartmentService().getDepName(dept);
                } else if (TextUtils.equals(dept, Constants.All_STAFF_GROUP)) {
                    name = HiKernel.getHikernel().getAccount().getCompanyName();
                }

                if (!TextUtils.isEmpty(name)) {
                    sb.append(name);
                    sb.append("|");
                }
            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }
            showDeptNames = sb.toString();
        }
        if (TextUtils.isEmpty(showDeptNames)) {
            return "";
        }
        return showDeptNames;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserNamePy() {
        if (!TextUtils.isEmpty(userNamePy)) {
            return userNamePy.replaceAll(" ", "");
        } else {
            return userNamePy;
        }
    }

    public void setUserNamePy(String userNamePy) {
        if (!TextUtils.isEmpty(userNamePy)) {
            this.userNamePy = userNamePy.replaceAll(" ", "");
        } else {
            this.userNamePy = userNamePy;
        }
    }

    public String getNickNamePy() {
        if (!TextUtils.isEmpty(nickNamePy)) {
            return nickNamePy.replaceAll(" ", "");
        } else {
            return nickNamePy;
        }
    }

    public void setNickNamePy(String nickNamePy) {
        if (!TextUtils.isEmpty(nickNamePy)) {
            this.nickNamePy = nickNamePy.replaceAll(" ", "");
        } else {
            this.nickNamePy = nickNamePy;
        }
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public List<String> getDeptIds() {
        return deptIds;
    }

    public void setDeptIds(List<String> deptIds) {
        this.deptIds = deptIds;
    }

    public int getVisible() {
        return visible;
    }

    public void setVisible(int visible) {
        this.visible = visible;
    }

    public void setShowDeptNames(String showDeptNames) {
        this.showDeptNames = showDeptNames;
    }

    public String getTinyAvatar() {
        return tinyAvatar;
    }

    public void setTinyAvatar(String tinyAvatar) {
        this.tinyAvatar = tinyAvatar;
    }

    public int getUserType() {
        return userType;
    }

    public void setUserType(int userType) {
        this.userType = userType;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public CompanySetting getCompanySetting() {
        return companySetting;
    }

    public void setCompanySetting(CompanySetting companySetting) {
        this.companySetting = companySetting;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getRelation() {
        return relation;
    }

    public void setRelation(int relation) {
        this.relation = relation;
    }

    public boolean isUserVisible() {
        return visible == Constants.CONTACT_DEP_VISIBLE || relation == 1;
    }

    public String getIntroduce() {
        if (introduce == null) {
            return "";
        }
        return introduce;
    }

    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public List<String> getShowDeptIds() {
        return showDeptIds;
    }

    public void setShowDeptIds(List<String> showDeptIds) {
        this.showDeptIds = showDeptIds;
    }

    public List<String> getNickNamePyList() {
        return nickNamePyList;
    }

    public void setNickNamePyList(List<String> nickNamePyList) {
        this.nickNamePyList = nickNamePyList;
    }

    public List<String> getUserNamePyList() {
        return userNamePyList;
    }

    public void setUserNamePyList(List<String> userNamePyList) {
        this.userNamePyList = userNamePyList;
    }

    public String getNickNamePysString() {
        return nickNamePysString;
    }

    public void setNickNamePysString(String nickNamePysString) {
        this.nickNamePysString = nickNamePysString;
    }

    public String getUserNamePysString() {
        return userNamePysString;
    }

    public void setUserNamePysString(String userNamePysString) {
        this.userNamePysString = userNamePysString;
    }

    public List<String> getAboveDeptIds() {
        return aboveDeptIds;
    }

    public void setAboveDeptIds(List<String> aboveDeptIds) {
        this.aboveDeptIds = aboveDeptIds;
    }

    public int getAiRobot() {
        return aiRobot;
    }

    public void setAiRobot(int aiRobot) {
        this.aiRobot = aiRobot;
    }
}
