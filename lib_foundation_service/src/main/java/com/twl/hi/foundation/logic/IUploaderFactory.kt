package com.twl.hi.foundation.logic

/**
 * 上传器工厂接口
 * 用于解决依赖倒置问题：lib_foundation_service 不能直接依赖 module_foundation_business
 */
interface IUploaderFactory {

    /**
     * 创建视频上传器
     */
    fun createVideoUploader(): IVideoUploader?

    /**
     * 创建文档上传器
     */
    fun createDocumentUploader(): IDocumentUploader?

    /**
     * 取消指定的上传任务
     * @param attachmentId 附件ID
     * @return 是否成功取消
     */
    fun cancelUpload(attachmentId: String): Bo<PERSON>an

    /**
     * 清理所有上传任务
     */
    fun clearAllTasks()
}
