package com.twl.hi.foundation.api.response

import com.twl.http.client.HttpResponse

/**
 * 打卡关怀配置的响应
 * <p>
 * Created by tanshicheng on 2024/01/09
 */
class AttendanceCareConfigResponse : HttpResponse() {

    /**
     * 打卡后关怀操作类型 1:跳转,3:发送关怀语,4:微调研
     */
    var careType: String? = null

    /**
     * 内容 关怀语 或者 跳转的链接
     */
    var careContent: String? = null

    /**
     * 规则名称
     */
    var ruleName: String? = null

    /**
     * 微调研问题标题
     */
    var microResearchQuestion: String? = null

    /**
     * 微调研问题形式,1:表情+文字,2:仅表情,3:仅文字
     */
    var microResearchType: Int? = null

    /**
     * 微调研问题选项
     */
    var microResearchItemVOS: List<MicroResearchItem>? = null
}

data class MicroResearchItem(
    val expression: String?, // 表情
    val text: String?, // 表情描述
)
