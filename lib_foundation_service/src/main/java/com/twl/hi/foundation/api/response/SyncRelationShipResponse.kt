package com.twl.hi.foundation.api.response

import com.twl.http.client.HttpResponse

/**
 * Author : <PERSON><PERSON><PERSON><PERSON> .
 * Date   : On 2024/2/27
 * Email  : Contact <EMAIL>
 * Desc   : 同步联系人开聊关系
 *
 */

data class SyncRelationShipResponse(
    // 数据版本号
    val version: Long,
    val hasMore: Int,
    val relationShipList: List<RelationShip>
) : HttpResponse()

data class RelationShip(
    val userId: String,
    // 是否可以直接开聊
    val chatVisible: Int
)