package com.twl.hi.foundation.api.request.chattab;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

/**
 * 排序聊天标签页
 * <p>
 * Created by tanshi<PERSON> on 2022/11/17
 */
public class ChatTabSortRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String chatId;
    @Expose
    public int chatType = 1;
    @Expose
    public String idsStr = "";

    public ChatTabSortRequest(BaseApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_CHAT_TAB_SORT;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}

