package com.twl.hi.foundation.model.message

import java.io.Serializable
import lib.twl.common.model.ImageInfo

/**
 * 图片系统卡片消息
 */
class MessageForImageCard : ChatMessage() {
    var imageCardInfo: ImageCardInfo? = null

    init {
        mediaType = MessageConstants.MSG_IMAGE_CARD
    }

    override fun accept(visitor: Visitor) {
        visitor.visit(this)
    }

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as MessageForImageCard

        if (imageCardInfo != other.imageCardInfo) return false

        return true
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + (imageCardInfo?.hashCode() ?: 0)
        return result
    }

    data class ImageCardInfo(
        val original: ImageInfo?,
        val protocol: String?,
        val text: String?,
    ) : Serializable
}