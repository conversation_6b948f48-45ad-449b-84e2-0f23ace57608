package com.twl.hi.foundation.facade

import androidx.annotation.Keep
import androidx.lifecycle.LiveData
import com.google.gson.Gson
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.request.chattab.ChatTabAddRequest
import com.twl.hi.foundation.api.request.chattab.ChatTabDeleteRequest
import com.twl.hi.foundation.api.request.chattab.ChatTabSortRequest
import com.twl.hi.foundation.api.request.chattab.ChatTabUpdateRequest
import com.twl.hi.foundation.db.ChatPropsDao
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.chatprops.ChatTabModel
import com.twl.hi.foundation.model.chatprops.ChatTabRequestModel
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.client.HttpResponse
import com.twl.http.error.ErrorReason
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * 聊天属性数据操作Repository
 *
 * Created by tanshicheng on 2022/11/17
 */
class ChatPropsRepository : BaseRepository(listOf()) {

    val chatPropsDao: ChatPropsDao by lazy {
        ServiceManager.getInstance().databaseService.chatPropsDao
    }

    override fun onUpdate(event: String?) {
        super.onUpdate(event)
    }

    override fun onUpdate(event: String?, obj: Any?) {
        super.onUpdate(event, obj)
    }

    suspend fun deleteTab(tabId: String?, chatId: String?):TabOptResult {
        return suspendCancellableCoroutine {
            val request =
                ChatTabDeleteRequest(object : BaseApiRequestCallback<HttpResponse?>() {

                    override fun handleInChildThread(data: ApiData<HttpResponse?>?) {
                        super.handleInChildThread(data)
                        if (data?.resp?.isSuccess == false) {
                            chatPropsDao.deleteOneChatTab(tabId.orEmpty(), chatId)
                        }
                    }

                    override fun onSuccess(data: ApiData<HttpResponse?>?) {
                        it.resume(TabOptResult())
                    }

                    override fun onFailed(reason: ErrorReason?) {
                        it.resume(TabOptResult(false, reason?.errReason))
                    }

                })
            request.id = tabId.orEmpty()
            HttpExecutor.execute(request)
        }
    }

    suspend fun addTab(tab: ChatTabRequestModel?):TabOptResult {
        return suspendCancellableCoroutine {
            val request =
                ChatTabAddRequest(object : BaseApiRequestCallback<HttpResponse?>() {
                    override fun onSuccess(data: ApiData<HttpResponse?>?) {
                        it.resume(TabOptResult())
                    }

                    override fun onFailed(reason: ErrorReason?) {
                        it.resume(TabOptResult(false, reason?.errReason))
                    }

                })
            request.chatTabStr = Gson().toJson(tab)
            HttpExecutor.execute(request)
        }
    }

    suspend fun updateTab(tab: ChatTabRequestModel?): TabOptResult {
        return suspendCancellableCoroutine {
            val request = ChatTabUpdateRequest(object : BaseApiRequestCallback<HttpResponse?>() {
                override fun onSuccess(data: ApiData<HttpResponse?>?) {
                    it.resume(TabOptResult())
                }

                override fun onFailed(reason: ErrorReason?) {
                    it.resume(TabOptResult(false, reason?.errReason))
                }
            })
            request.chatTabStr = Gson().toJson(tab)
            HttpExecutor.execute(request)
        }
    }

    suspend fun sortTabs(chatId: String?, chatType: Int, chatTabIds: String?): TabOptResult {
        return suspendCancellableCoroutine {
            val request = ChatTabSortRequest(object : BaseApiRequestCallback<HttpResponse?>() {
                override fun onSuccess(data: ApiData<HttpResponse?>?) {
                    it.resume(TabOptResult())
                }

                override fun onFailed(reason: ErrorReason?) {
                    it.resume(TabOptResult(false, reason?.errReason))
                }
            })
            request.chatId = chatId.orEmpty()
            request.chatType = chatType
            request.idsStr = chatTabIds
            HttpExecutor.execute(request)
        }
    }

    fun queryTabs(chatId: String?, chatType: Int): LiveData<List<ChatTabModel>> {
        return chatPropsDao.queryAllTabs(chatId, chatType)
    }

    fun queryAllBotMenus(chatId: String) = chatPropsDao.queryChatRobotQuickMenu(chatId)
}

@Keep
data class TabOptResult(
    var isSuccess: Boolean = true,
    var msg: String? = ""
)
