package com.twl.hi.foundation.api.request;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.response.HypertextDetailResponse;
import com.twl.http.HttpUtils;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

import java.util.List;

import okhttp3.MediaType;

/**
 * QE链接或者CRM号详情查询请求
 */
public class HypertextDetailRequest extends BaseApiRequest<HypertextDetailResponse> {
    /**
     * "requestList":[{"elementIds":["https://qe.kanzhun-inc.com/issue?projectKey\u003dBH\u0026num\u003d109792"],"elementType":6}]
     */
    @Expose
    public List<Bean> requestList;

    public HypertextDetailRequest(AbsRequestCallback<HypertextDetailResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_HYPERTEXT_RESOURCE_DETAIL;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }

    @Nullable
    @Override
    public MediaType getMediaType() {
        return MediaType.parse(HttpUtils.MEDIA_TYPE_JSON);
    }

    @Keep
    public static class Bean {
        public int elementType; // 文本类型，5-CRM，6-QE
        public List<String> elementIds; // 资源 ID
    }
}
