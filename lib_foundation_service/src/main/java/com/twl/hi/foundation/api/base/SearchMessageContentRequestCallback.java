package com.twl.hi.foundation.api.base;

import com.twl.hi.foundation.api.response.SearchChatContentRecordResponse;

import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2021/8/5.
 */
public class SearchMessageContentRequestCallback<R extends SearchChatContentRecordResponse> extends BasePbApiRequestCallback<R> {
    @Override
    protected void parseWithJson(JSONObject jsonObject, R searchChatContentRecordResponse) {
        super.parseWithJson(jsonObject, searchChatContentRecordResponse);
        if (jsonObject.has("hasMore")) {
            searchChatContentRecordResponse.hasMore = jsonObject.optInt("hasMore");
        }
        if (jsonObject.has("offsetMsgId")) {
            searchChatContentRecordResponse.offsetMsgId = jsonObject.optLong("offsetMsgId");
        }
    }
}
