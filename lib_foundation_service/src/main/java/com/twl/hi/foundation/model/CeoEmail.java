package com.twl.hi.foundation.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

import java.io.Serializable;

import lib_twl_db_config.DBConstants;

@Entity(tableName = DBConstants.TAB_CEO_EMAIL)
public class CeoEmail implements Serializable {

    private static final long serialVersionUID = 4928052218851335251L;
    @PrimaryKey
    private long threadId;
    private String draft;

    public long getThreadId() {
        return threadId;
    }

    public void setThreadId(long threadId) {
        this.threadId = threadId;
    }

    public String getDraft() {
        return draft;
    }

    public void setDraft(String draft) {
        this.draft = draft;
    }
}
