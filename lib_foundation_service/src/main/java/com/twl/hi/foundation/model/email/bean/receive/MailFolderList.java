package com.twl.hi.foundation.model.email.bean.receive;

import java.util.List;

public class MailFolderList extends BaseMailResponse {

    private int code;
    private List<MailFolder> mailFolderList;

    private List<MailFolder> mailLabelList;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public List<MailFolder> getMailFolderList() {
        return mailFolderList;
    }

    public void setMailFolderList(List<MailFolder> mailFolderList) {
        this.mailFolderList = mailFolderList;
    }

    public List<MailFolder> getMailLabelList() {
        return mailLabelList;
    }

    public void setMailLabelList(List<MailFolder> mailLabelList) {
        this.mailLabelList = mailLabelList;
    }

    @Override
    public String toString() {
        return "MailFolderList{" +
                "code=" + code +
                ", mailFolderList=" + mailFolderList +
                ", mailLabelList=" + mailLabelList +
                '}';
    }
}
