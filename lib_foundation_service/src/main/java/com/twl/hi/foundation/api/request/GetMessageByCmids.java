package com.twl.hi.foundation.api.request;

import androidx.annotation.Nullable;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BasePbApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.callback.ChatMessgaePbApiRequestCallback;
import com.twl.http.HttpUtils;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

import java.util.List;

import okhttp3.MediaType;

public class GetMessageByCmids extends BaseApiRequest<BasePbApiRequestCallback.MessageResponse> {

    @Expose
    private List<Long> cmids;

    public GetMessageByCmids(ChatMessgaePbApiRequestCallback<BasePbApiRequestCallback.MessageResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_QUERY_MESSAGE_BY_CMID;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }

    @Nullable
    @Override
    public MediaType getMediaType() {
        return MediaType.parse(HttpUtils.MEDIA_TYPE_JSON);
    }

    public List<Long> getCmids() {
        return cmids;
    }

    public void setCmids(List<Long> cmids) {
        this.cmids = cmids;
    }
}
