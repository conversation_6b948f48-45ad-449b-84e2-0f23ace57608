package com.twl.hi.foundation.model.message.extensioncard.data.modules;

import com.twl.hi.foundation.model.message.extensioncard.data.element.ElementForText;
import com.twl.utils.StringUtils;
import com.zhipin.bosshi.mqtt.codec.ChatProtocolStr;

import java.io.Serializable;
import java.util.List;

/**
 * 消息卡片-Markdown模块
 * <p>
 * Created by <PERSON><PERSON><PERSON> on 2023/5/15
 */
public class ModuleForMarkdown extends Module {

    private MarkdownModuleData markdown;

    public ModuleForMarkdown() {
        setTag(MODULE_TAG_MARKDOWN);
    }

    public MarkdownModuleData getMarkdown() {
        return markdown;
    }

    public void setMarkdown(MarkdownModuleData markdown) {
        this.markdown = markdown;
    }

    public ElementForText getMDContentTextElement() {
        if (markdown != null) {
            ElementForText elementForText = markdown.content;
            if (elementForText != null) {
                elementForText.setUrl(markdown.url);
                return elementForText;
            }
        }
        return null;
    }

    @Override
    public Module parseFromHiModule(ChatProtocolStr.HiCardModuleStr hiCardModule) {
        super.parseFromHiModule(hiCardModule);
        return parseFromHiMarkdownModule(hiCardModule.getMarkdown());
    }

    @Override
    public boolean needRefresh() {
        return isSupport() && markdown == null;
    }

    @Override
    public long getDefaultSupportVersion() {
        return Module.VERSION_311;
    }

    @Override
    public List<String> getTextAtIds() {
        if (markdown != null && markdown.content != null) {
            return markdown.content.getAtIds();
        }
        return null;
    }

    @Override
    public ChatProtocolStr.HiCardModuleStr convertToHiModule() {
        return getBaseHiModuleBuilder()
                .setMarkdown(
                        ChatProtocolStr.HiCardMarkdownModuleStr.newBuilder()
                                .setContent(markdown.content.buildHiCardTextElement())
                                .setTextAlign(this.markdown.textAlign)
                                .setUrl(StringUtils.getStringOrEmpty(this.markdown.url))
                                .setSmId(this.markdown.smId)
                                .setPcUrl(StringUtils.getStringOrEmpty(this.markdown.picUrl))
                                .setIosUrl(StringUtils.getStringOrEmpty(this.markdown.iosUrl))
                                .setAndroidUrl(StringUtils.getStringOrEmpty(this.markdown.androidUrl))
                                .build()
                )
                .build();
    }

    public ModuleForMarkdown parseFromHiMarkdownModule(ChatProtocolStr.HiCardMarkdownModuleStr mdModule) {

        this.markdown = new MarkdownModuleData();

        if (mdModule.hasContent()) {
            this.markdown.content = new ElementForText(mdModule.getContent().getType(), mdModule.getContent().getText(), mdModule.getContent().getAtsList());
        }
        if (mdModule.hasTextAlign()) {
            this.markdown.textAlign = mdModule.getTextAlign();
        }
        if (mdModule.hasUrl()) {
            this.markdown.url = mdModule.getUrl();
        }
        if(mdModule.hasPcUrl()){
            this.markdown.picUrl = mdModule.getPcUrl();
        }
        if(mdModule.hasIosUrl()){
            this.markdown.iosUrl = mdModule.getIosUrl();
        }
        if (mdModule.hasAndroidUrl()) {
            this.markdown.androidUrl = mdModule.getAndroidUrl();
        }
        if(mdModule.hasSmId()){
            this.markdown.smId = mdModule.getSmId();
        }
        return this;
    }

    public static class MarkdownModuleData implements Serializable {

        private ElementForText content;

        private int textAlign = 1;

        private String url;

        private String picUrl;

        private String androidUrl;

        private String iosUrl;

        private int smId;

        private int fromIndex = 0;

        public ElementForText getContent() {
            return content;
        }

        public void setContent(ElementForText content) {
            this.content = content;
        }

        public int getTextAlign() {
            return textAlign;
        }

        public void setTextAlign(int textAlign) {
            this.textAlign = textAlign;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getPicUrl() {
            return picUrl;
        }

        public void setPicUrl(String picUrl) {
            this.picUrl = picUrl;
        }

        public String getAndroidUrl() {
            return androidUrl;
        }

        public void setAndroidUrl(String androidUrl) {
            this.androidUrl = androidUrl;
        }

        public String getIosUrl() {
            return iosUrl;
        }

        public void setIosUrl(String iosUrl) {
            this.iosUrl = iosUrl;
        }

        public int getSmId() {
            return smId;
        }

        public void setSmId(int smId) {
            this.smId = smId;
        }

        public int getFromIndex() {
            return fromIndex;
        }

        public void setFromIndex(int fromIndex) {
            this.fromIndex = fromIndex;
        }
    }
}
