package com.twl.hi.foundation.model;

import java.util.ArrayList;
import java.util.List;

import hi.kernel.Constants;
import lib.twl.common.views.adapter.entity.MultiItemEntity;

/**
 * Created by ChaiJiangpeng on 2020-04-13
 * Describe:
 */
public class ScheduleMonthItem implements MultiItemEntity {
    private String title;
    private List<ScheduleWeekItem> mSubItems;
    private long beginTime;
    private long endTime;
    private int totalSize;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public long getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(long beginTime) {
        this.beginTime = beginTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    @Override
    public int getItemType() {
        return Constants.TYPE_LEVEL_MONTH;
    }

    public List<ScheduleWeekItem> getSubItems() {
        return mSubItems;
    }

    public void addSubItem(ScheduleWeekItem subItem) {
        if (mSubItems == null) {
            mSubItems = new ArrayList<>();
        }
        mSubItems.add(subItem);
    }

    public void addSizeNum(int num) {
        totalSize = totalSize + num;
    }

    public int getTotalSize() {
        return totalSize;
    }
}
