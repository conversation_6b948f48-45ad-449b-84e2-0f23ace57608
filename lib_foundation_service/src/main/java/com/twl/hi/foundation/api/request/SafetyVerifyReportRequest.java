package com.twl.hi.foundation.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

public class SafetyVerifyReportRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String safeId = "";
    @Expose
    public int verifyStatus = 0;
    @Expose
    public String safeToken = "";
    @Expose
    public String bizId = "";
    @Expose
    public String scene = "";

    public SafetyVerifyReportRequest(BaseApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_SAFETY_VERIFY_RESULT_REPORT;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}

