package com.twl.hi.foundation.model.message

import com.twl.hi.foundation.model.Agreement

/**
 * 灰条消息
 */
class MessageForHint : ChatMessage() {
    var hintInfo: HintInfo? = null

    init {
        mediaType = MessageConstants.MSG_HINT
    }

    override fun accept(visitor: Visitor) {
        visitor.visit(this)
    }

    override var content: String?
        get() = if (System.currentTimeMillis() - time >= ONE_DAY_IN_MILLIS) {
            super.content?.replace("\u200b(撤回|撤销添加|撤销邀请)\u2060$".toRegex(), "") ?: ""
        } else super.content
        set(value) {
            super.content = value
        }

    val targetUserId: String?
        get() = hintInfo?.extension?.shotTargetUserId

    val agreements: List<Agreement>
        get() = hintInfo?.agreements ?: emptyList()

    val textColor: Int
        get() = hintInfo?.textColor ?: 0

    val bgColor: Int
        get() = hintInfo?.bgColor ?: 0

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as MessageForHint

        if (hintInfo != other.hintInfo) return false

        return true
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + (hintInfo?.hashCode() ?: 0)
        return result
    }

    data class HintInfo(
        val agreements: List<Agreement>?,
        val textColor: Int,
        val bgColor: Int,
        val contentType: Int, //灰条消息内容类型 304 拍一拍灰条
        val extension: Extension? = null,
    ) {
        data class Extension(
            val shotTargetUserId: String?
        )
    }

    companion object {
        const val TAG = "MessageForHint"

        const val TYPE_HINT_TAP = 304 //拍一拍灰条消息

        const val ONE_DAY_IN_MILLIS = 24L * 60 * 60 * 1000
    }
}