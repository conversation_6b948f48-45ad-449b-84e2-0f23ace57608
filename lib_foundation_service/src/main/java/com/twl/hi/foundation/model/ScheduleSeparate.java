package com.twl.hi.foundation.model;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/29
 * Describe: 重复日程中单独编辑分离的日程
 */
public class ScheduleSeparate {
    /**
     * "targetDate": "2019-07-10", // 日期
     * "scheduleId": 1000234 // 日程id
     */
    private String targetDate;
    private String scheduleId;

    public String getTargetDate() {
        return targetDate;
    }

    public void setTargetDate(String targetDate) {
        this.targetDate = targetDate;
    }

    public String getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(String scheduleId) {
        this.scheduleId = scheduleId;
    }
}
