package com.twl.hi.foundation.api.base;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class URLConfig {

    public static final String URL_SMS_LOGIN = "login/v1/smsLogin_v2"; //通过手机验证码登录

    public static final String URL_LOGIN_REGION = "login/auth/loginRegionInfo"; // 获取登录区号信息

    public static final String URL_CHECK_SAFE = "safe/v2/checkSafe"; // 验证是否安全

    public static final String URL_DEVICE_REPORT = "login/v1/report"; // 设备信息上报接口，用于安全设备校验

    public static final String URL_SMS_LOGOUT = "login/v1/logout"; //通过手机验证码登录

    public static final String URL_QR_SCAN_LOGIN = "login/v1/app/qrScan"; //通过手机扫码授权登录

    public static final String URL_QR_SCAN_LOGIN_CANCEL = "login/v1/cancelQrLogin"; //取消扫码登录(桌面端返回/手机端取消)

    public static final String URL_QR_SCAN_LOGIN_QUIT = "login/v1/kickPc"; //踢出桌面端登录
    public static final String URL_CONTACT_SYNC_GROUPS = "im/group/v1/sync/syncGroups_v2"; //查询群聊列表

    public static final String URL_CONVERSTAION_PROP_SYNC = "im/chat/v1/sync/relation"; //同步消息列表属性

    public static final String URL_USER_INFO = "user/v1/user/info"; //查询个人信息

    public static final String URL_USER_CARD_INFO = "user/front/v2/user/business/carde/info"; // 新版个人名片信息

    public static final String URL_UPDATE_USER_REMARK = "user/front/v1/user/updateContactRemark"; // 更新用户备注

    public static final String URL_USER_UPDATE = "user/v1/user/update"; //更新我的信息

    public static final String URL_UPDATE_PERSONAL_CARD = "user/v1/user/updatePersonalCard"; //更新我的信息

    public static final String URL_USER_ME = "user/v1/user/me"; //我的信息（F4）

    public static final String URL_UPLOAD_AVATAR = "media/upload/avatar"; //更新用户头像

    public static final String URL_REPLACE_IMAGE_URL = "media/v2/listNewUrl"; //更新富文本中的图片

    public static final String URL_SETTING_CHAT = "im/chat/v1/settings"; //设置聊天免打扰、置顶

    public static final String URL_READ_LABEL_ADD = "im/readlabel/v2/add"; // 会话标为未读

    public static final String URL_READ_LABEL_DELETE = "im/readlabel/v2/delete"; // 会话标为已读

    public static final String URL_READ_LABEL_LIST = "im/readlabel/v2/list"; // 未读标记会话列表

    public static final String URL_UPLOAD_IMAGE_V2 = "media/v2/upload/image"; //上传图片V2

    public static final String URL_UPLOAD_FILE = "media/upload/file"; //上传文件

    public static final String URL_UPLOAD_MAIL_ATTACH_FILE = "mail/attachment/upload/part"; //大附件分片上传

    public static final String URL_PRE_UPLOAD_MAIL_ATTACH_FILE = "mail/attachment/upload/preFast"; //判断大附件是否上传过

    public static final String URL_UPLOAD_VIDEO = "media/upload/video"; //上传视频

    public static final String URL_GET_EMOTION = "im/sticker/v1/get"; //查询所有表情

    public static final String URL_EMOJI_SYNC = "im/chat/v2/sticker/emoji/sync"; //查询所有黄豆表情

    public static final String URL_EMOTION_FAVORITE = "im/sticker/v1/favorite"; //收藏到自定义表情

    public static final String URL_CHAT_WITHDRAW = "im/chat/v1/message/withdraw"; //撤回消息

    public static final String URL_ADD_EMOTION = "im/sticker/v1/add"; //添加自定义表情

    public static final String URL_UPLOAD_EMOTION = "media/upload/sticker"; //上传动画表情

    public static final String URL_DELETE_EMOTION = "im/sticker/v1/delete"; //删除自定义表情

    public static final String URL_GET_CHAT_SHARE = "im/chat/v1/message/getShareV2"; //查询转发消息内容(加密版本)

    public static final String URL_GROUP_CREATE = "im/group/v1/create"; //新建群聊

    public static final String URL_GET_GROUP_QR = "im/group/v1/getQrInfo"; //获取群二维码地址

    public static final String URL_GET_GROUP_INFO_FROM_QR = "im/group/v1/info"; //从二维码中获取群消息

    public static final String URL_GROUP_INFO_UPDATE = "im/group/v1/update"; //更新群信息

    public static final String URL_GROUP_REMARK_UPDATE = "im/group/v1/groupRemark"; // 更新群备注

    public static final String URL_GROUP_ADD_USERS = "im/group/v1/addUsers"; //添加群成员

    public static final String URL_GROUP_ROBOT_SEARCH = "open/app-center/v1/robotList"; //搜索机器人

    public static final String URL_GROUP_ROBOT_PROFILE = "open/app-center/v1/robotDetail"; //查询群聊机器人名片

    public static final String URL_GROUP_ROBOT_INFO = "im/group/v1/getRobotInfo"; //查询群聊机器人详情

    public static final String URL_GROUP_ROBOT_CHECK = "im/group/v1/checkAddRobot"; //添加群聊机器人的前置检查

    public static final String URL_GROUP_ROBOT_ADD = "im/group/v1/addRobot"; //添加群聊机器人

    public static final String URL_GROUP_ROBOT_REMOVE = "im/group/v1/removeRobot"; //移除群聊机器人

    public static final String URL_GROUP_ROBOT_UPDATE = "im/group/v1/updateRobot"; //更新群聊机器人

    public static final String URL_GROUP_ROBOT_CANCEL_INVITE = "im/group/v1/cancelInviteRobot"; //取消邀请群聊机器人

    public static final String URL_GROUP_JOIN = "im/group/v1/join"; //加群

    public static final String URL_GROUP_QUIT = "im/group/v1/quit";  //退群

    public static final String URL_GROUP_OPEN_ID = "open/app-center/v1/getOpenGroupId"; //置换开放平台中存储的加密群ID

    public static final String URL_CHAT_LAST_INFO = "im/reconciliation/v2/chatLastInfo"; //客户端本地会话信息上报（最后一条消息、红点数等）

    public static final String URL_CHAT_LATEST_GROUP = "im/reconciliation/v2/latestGroup"; //客户端本地最新的N（默认N=10）条群聊信息

    public static final String URL_WALLET_BALANCE = "wallet/balance"; //查询账户余额

    public static final String URL_TICKET_HISTORY = "ticket/v1/history"; //红包记录

    public static final String URL_CLOCK_GET = "clock/v1/get"; //查询当前打卡信息

    public static final String URL_CLOCK_SURVEY_SUBMIT = "clock/v1/survey/submit"; //提交打卡微调研结果

    public static final String URL_CLOCK_UPDATE = "clock/v1/update"; //提交打卡

    public static final String URL_CLOCK_GET_CARE_CONFIG = "clock/v1/getCareConfig"; //获取关怀配置

    public static final String URL_TICKET_INFO = "ticket/v1/info"; //查询红包详情

    public static final String URL_TICKET_WATCH = "ticket/v1/alloc"; //领取红包

    public static final String URL_CREATE_TICKET = "ticket/v1/create"; //创建支付订单

    public static final String URL_TICKET_STATUS = "ticket/v1/status"; //查询支付结果

    public static final String URL_UPDATE_TOKEN = "token/update"; //更新推送令牌

    public static final String URL_GET_WX_ACCOUNT = "ticket/v1/wxAccount"; //查询绑定的微信账号

    public static final String URL_GET_WX_BIND = "ticket/v1/wxBind"; //绑定或更换微信账号

    public static final String URL_ALIPAY_BIND = "ticket/ali/bind"; //绑定支付宝账号

    public static final String URL_CASH_WITHDRAW = "ticket/v1/withdraw"; //余额提现

    public static final String URL_PAY_CHANNEL_INFO = "ticket/channel/info/get"; // 查询支付渠道信息

    public static final String URL_ALIPAY_AUTH_INFO = "ticket/ali/auth/code/get"; // 支付宝认证授权请求参数

    public static final String URL_CALENDAR_SYNC = "schedule/v1/sync"; //同步日历变更

    public static final String URL_CALENDAR_QUERY = "schedule/v1/query"; //查询日历数据

    public static final String URL_CALENDAR_ADD = "schedule/v1/add"; //添加日程&待办

    public static final String URL_CALENDAR_UPDATE = "schedule/v1/update"; //编辑日程

    public static final String URL_CALENDAR_DEL = "schedule/v1/del"; //删除日程

    public static final String URL_QUIT_SHARED_CALENDAR = "schedule/v1/group/exit"; //退出共享日程

    public static final String URL_VERSION_QUERY_V2 = "version/query_v2"; //查询版本信息

    public static final String URL_DOWNLOAD_REQUIRE = "flow/version/download/require"; //资源下载许可

    public static final String URL_WORK_GETHOLIDAY = "s/work/v1/getHolidays"; //查询假期余额


    public static final String URL_CEO_EMAIL_DSEND = "bbs/create"; //发送匿名信

    public static final String URL_WORK_GETWORKLIST = "work/v1/getWorkList"; //查询考勤异常

    public static final String URL_WORK_GETTIMES = "work/v1/getTimes"; //查询考勤时间

    public static final String URL_WORK_NOTICES = "work/v1/settings"; //查询考勤设置列表

    public static final String URL_WORK_NOTICE_ADD = "work/v1/addSetting"; //添加考勤设置

    public static final String URL_WORK_NOTICE_DELETE = "work/v1/delSetting"; //删除考勤设置

    public static final String URL_WORK_NOTICE_UPDATE = "work/v1/updateSetting"; //更新考勤设置

    public static final String URL_CHAT_MSGSTATUS = "im/chat/v1/message/msgStatus"; //查询群聊消息未读用户列表

    public static final String URL_ZHISHU_PERMIT = "zhishu/v1/permit"; // 编辑直书链接权限信息

    public static final String URL_ZHISHU_PREVIEW = "zhishu/v1/preview/detail"; // 获取直书链接预览信息

    public static final String URL_ZHISHU_PREVIEW_PERMISSION = "zhishu/v1/preview/otherPermission"; // 获取直书链接预览信息及权限

    public static final String URL_HYPERTEXT_PREVIEW_TITLE = "im/chat/v1/richElement/enhance"; // 获取消息超链接标题

    public static final String URL_HYPERTEXT_RESOURCE_DETAIL = "im/chat/v1/richElement/queryDetails/v2"; // 获取消息超链接详情

    public static final String URL_GROUP_ATTENDANCE_DAILY = "work/v1/team/today"; //查询今日实时考勤

    public static final String URL_GROUP_ATTENDANCE_WEEKLY = "work/v1/team/week"; //查询周考勤统计

    public static final String URL_GROUP_ATTENDANCE_MONTHLY = "work/v1/team/month"; //查询月考勤统计

    public static final String URL_SYSTEM_PLUGINS_V3 = "open/app-center/v1/workbench"; //工作台请求插件信息

    public static final String URL_WORK_STATION_OTHER = "system/v1/otherInfo"; // 工作台其他信息
    public static final String URL_WORK_STATION_WIDGET_TAB = "data-element/datasource/list"; // 工作台小组件数据源列表
    public static final String URL_WORK_STATION_WIDGET_TAB_DETAIL = "data-element/datasource/detail"; // 工作台小组件数据源详情

    public static final String URL_WORK_STATION_WIDGET_TAB_RECORD = "data-element/datasource/saveLastActive"; // 记录上次选中数据源
    public static final String URL_FUN_VIEWS = "system/v1/funcViews"; //新查询功能信息

    public static final String URL_DATA_POINT_V3 = "common/v1/data/point_v3"; //2.37版本修改打点

    public static final String URL_DATA_POINT_V2 = "common/data/point_v2"; //2.17版本修改打点

    public static final String URL_PATCH_LOG = "common/point/batchLog";//批量埋点上传

    public static final String URL_GROUP_UPDATESETTINGS = "im/group/v1/updateSettings"; //更新群管理设置

    public static final String URL_CHANGE_PHONE = "user/v1/user/changePhone"; //更改手机号

    public static final String URL_WORK_APPROVAL_LIST = "s/work/flow/getListInfo"; //查询审批列表信息

    public static final String URL_GROUP_KICKUSERS = "im/group/v1/kickUsers"; //群主踢人

    public static final String URL_NOAH_UPLOAD_FILE = "media/noah/uploadFiles"; //上传附件

    public static final String URL_SCHEDULE_BOOKINS = "meeting/v1/myBookings"; //查询我的预订

    public static final String URL_CANCEL_SCHEDULE_BOOKING = "meeting/v1/cancel"; //取消预订

    public static final String URL_H5_APPLY_DETAIL_LEAVE = "apply/detail/leave"; //请假详情

    public static final String URL_H5_APPLY_DETAIL_ATTENDANCE = "apply/detail/attendance"; //补卡详情

    public static final String URL_H5_APPLY_DETAIL_TRAVEL = "apply/detail/travel"; //公出详情

    public static final String URL_H5_APPLY_LEAVE = "apply/leave"; //请假

    public static final String URL_H5_APPLY_ATTENDANCE = "apply/attendance"; //补卡

    public static final String URL_H5_APPLY_TRAVEL = "apply/travel"; //公出

    public static final String URL_MSG_EMOJI = "im/chat/v1/message/msgEmoji"; //消息增删表情

    public static final String URL_CREATE_SCHEDULED_MSG = "im/scheduledmsg/v2/create"; // 创建定时消息

    public static final String URL_DELETE_SCHEDULED_MSG = "im/scheduledmsg/v2/delete"; // 删除定时消息

    public static final String URL_CHAT_MESSAGE_V2 = "im/chat/v1/message/messages_v2"; //查询消息详情（可查出不可见消息，本地标记为已删除）

    public static final String URL_QUERY_MESSAGE_BY_CMID = "im/chat/v2/encrypt/message/queryByCmids"; //根据客户端生成的 cmid 查询消息详情

    public static final String URL_CHAT_REPLY_LIST = "im/chat/v1/message/replyList"; //查询消息回复列表

    public static final String URL_CHAT_UNREAD_INFO = "im/chat/v1/unreadImpInfos"; //进入聊天时候，获取的置顶信息

    public static final String URL_VIDEO_CHAT_CANCEL = "link/room/inviteCancel"; //取消视频语音聊天

    public static final String URL_VIDEO_CHAT_NO_ANSWER = "link/noAnswer"; //语音视频聊天无应答

    public static final String URL_VIDEO_CHAT_GET_ACTIVE_ROOMS = "link/room/info"; //语音视频聊天查询房间存活

    public static final String URL_GET_MEETING_FLOOR_LIST = "meeting/v1/getFloorList"; //查询可预订的楼层

    public static final String URL_GET_MEETING_ROOM_LIST = "meeting/v1/getRoomList"; //查询楼层中会议室和预定信息

    public static final String URL_GET_GROUP_AVATAR_LIST = "im/group/v1/projectAvatars"; //查询项目组默认头像

    public static final String URL_CALENDAR_AVATAR_LIST = "schedule/v1/avatar/list"; //查询日历默认头像

    public static final String URL_GET_TEMPORARY_TOKEN = "login/v1/openToken"; //获取临时token

    public static final String URL_MEETING_TYPES = "meeting/v1/meetingTypes"; //查询会议室预定用途

    public static final String URL_GET_SERVER_TIME = "clock/v1/time"; //查询noah服务端时间

    public static final String URL_GROUP_CHANGE_OWNER = "im/group/v1/changeOwner"; //转让群主

    public static final String URL_GROUP_UPDATE_MANAGER = "im/group/v1/updateManagers"; //更新群管理员

    public static final String URL_GROUP_GET_MANAGER = "im/group/v1/getManagers"; //查询群管理员

    public static final String URL_URL_INFO = "common/urlInfo"; //解析url概要内容

    public static final String URL_H5_WECHAT_NOTIFY = "notify"; //微信公众号H5页面地址

    public static final String URL_SEARCH_CHAT_CONTENT = "search/v1/message/content"; //查询聊天记录内容

    public static final String URL_SEARCH_CHAT_SUMMARY = "search/v1/message/summary"; //查询聊天记录汇总

    public static final String URL_SEARCH_SCHEDULE_CONTENT = "search/v1/schedule/content"; //查询聊天记录汇总

    public static final String URL_SEARCH_TASK_CONTENT = "search/v1/task/content_v2"; //搜索任务

    public static final String URL_CHAT_FAVORITE = "im/chat/v1/favorites/list"; //收藏列表

    public static final String URL_CHAT_LATER_CREATE = "im/later/v1/create"; //创建稍后处理

    public static final String URL_CHAT_FAVORITE_CANCEL = "im/chat/v1/favorites/delete"; //取消收藏

    public static final String URL_CHAT_AT_ME_NUM = "im/chat/v2/atMe/num"; //获取群@我消息数量

    public static final String URL_CALENDAR_QUERY_WITH_CALENDAR = "schedule/v1/queryByIdAndCalendarId"; //根据id查询日历数据

    public static final String URL_SCHEDULE_CREATE_CALENDAR = "schedule/v1/group/add"; // 新增日历分组

    public static final String URL_SCHEDULE_UPDATE_CALENDAR = "schedule/v1/group/update"; // 更新日历分组

    public static final String URL_SCHEDULE_DELETE_CALENDAR = "schedule/v1/group/del"; // 删除日历分组

    public static final String URL_SCHEDULE_CALENDAR_LIST = "schedule/v1/group/list"; // 获取日程分组列表

    public static final String URL_SHARE_CALENDAR = "calendar/v1/group/forward"; //分享日历

    public static final String URL_QUERY_CALENDAR_DETAIL = "calendar/v1/group/share/detail/get"; //日历详情查询

    public static final String URL_COMPANY_ALL = "user/v1/company/getAll"; //获取用户所有团队信息

    public static final String URL_GET_TEAM_QR = "user/v1/company/getInviteQr"; //获取邀请二维码

    public static final String URL_GET_ADD_TEAM_MEMBER_BY_PHONE = "user/v1/company/inviteByPhone"; //通过手机号邀请成员

    public static final String URL_GET_ADD_TEAM_MEMBER_BY_EMAIL = "user/v1/company/inviteByEmail"; //通过邮箱邀请成员

    public static final String URL_GET_CREATE_TEAM = "user/v1/company/create"; //创建团队

    public static final String URL_GET_SET_COMPANY = "user/v1/company/user/setCompany"; //切换团队

    public static final String URL_GET_INVITE_URL = "user/v1/company/getInviteLink"; //获取邀请链接

    public static final String URL_GET_ADMIN_URL = "user/v1/company/getAdminUrl"; //获取管理后台地址

    public static final String URL_CHAT_ARCHIVE_ADD = "im/chat/v1/box/add"; //会话折叠

    public static final String URL_CHAT_ARCHIVE_REMOVE = "im/chat/v1/box/remove"; //取消会话折叠

    public static final String URL_SYSTEM_INFOS = "common/system/v1/infos"; //获取系统配置

    public static final String URL_CHANNEL_ADD = "chat/channel/add"; //频道添加

    public static final String URL_CHANNEL_DELETE = "chat/channel/remove"; //频道删除

    public static final String URL_CHANNEL_UPDATE = "chat/channel/update"; //频道更新

    public static final String URL_CALENDAR_INIT = "schedule/v1/init"; //初始化日程数据

    public static final String URL_CONTACT_MANAGER_INFO = "user/v1/contact/manager/info"; //查询通讯录管理页信息

    public static final String URL_CONTACT_MANAGER_SETTING = "user/v1/contact/manager/setting"; //通讯录管理页设置按钮

    public static final String URL_COMMON_ICONS = "common/icons"; //获取所有图标

    public static final String URL_GROUP_MATCH = "im/group/v1/match"; //查找用户Id完全匹配的群组列表

    public static final String URL_GROUP_FEEDBACK = "im/group/v1/feedback"; //用户反馈群信息获取

    public static final String URL_COM_SETTING = "user/v1/user/comSetting/update"; //工作状态设置

    public static final String URL_FAVORITES_ADD = "im/chat/v1/favorites/add"; //添加收藏

    public static final String URL_FAVORITES_BATCHADD = "im/chat/v1/favorites/batchAdd"; //添加合并收藏消息

    public static final String URL_SEARCH_RECORD_CHAT = "search/v1/recent/recordChat"; //记录最近搜索点击会话

    public static final String URL_SEARCH_RECORD_CHAT_CLEAR = "search/recent/clearChat"; //清空最近搜索点击会话

    public static final String URL_CALENDAR_SHARE = "calendar/v1/share"; //转发日程

    public static final String URL_CALENDAR_JOIN = "calendar/v1/join"; //参与日程

    public static final String URL_CALENDAR_QUERY_SHARE = "calendar/v1/queryShare"; //根据id查询日历数据

    public static final String URL_SEARCH_CONTENT = "search/v1/facade/content_v3"; //搜索全部  需要改成v3

    public static final String URL_RECENT_RECORDS = "search/v1/recent/records"; //最近搜索会话列表

    public static final String URL_SEARCH_HISTORY = "search/v1/recent/content"; //历史搜索列表

    public static final String URL_SEARCH_HISTORY_REPORT = "search/recent/result/click"; //搜索历史记录上送

    public static final String URL_SEARCH_HISTORY_CLEAR = "search/recent/content/del"; //清空搜索历史

    public static final String URL_SEARCH_CHAT_RECORD_USER = "search/v1/user/name"; //搜索联系人

    public static final String URL_SEARCH_CHAT_RECORD_GROUP = "search/v1/group/name"; //搜索群组/项目组

    public static final String URL_CONTACT_FRIEND_APPLIES = "im/contact/friend/v1/applies_v2"; //查询好友申请列表

    public static final String URL_ZHISHU_RESOURCE = "zhishu/bosshi/resource/child"; //查看文件夹内容

    public static final String URL_ZHISHU_SAVE_FILE = "zhishu/bosshi/resource/save"; // 保存到直书

    public static final String URL_ZHISHU_SPACE = "zhishu/bosshi/resource/space"; //获取云盘根文件夹

    public static final String URL_ZHISHU_TRANSFER = "zhishu/bosshi/resource/transfer"; //直书文档/表格转换

    public static final String URL_CONTACT_FRIEND_HANDLE = "im/contact/friend/v1/handle"; //处理好友申请

    public static final String URL_CLOUD_FOLDER_CREATE = "cloud/v1/folder/create"; //新建文件夹

    public static final String URL_CLOUD_RENAME_FOLDER_FILE = "cloud/v1/file/rename"; //重命名文件

    public static final String URL_CLOUD_RENAME_FOLDER = "cloud/v1/folder/rename"; //重命名文件夹

    public static final String URL_CLOUD_FILE_ADD = "cloud/v1/file/add"; //上传文件

    public static final String URL_CLOUD_FOLDER_DELETE = "cloud/v1/folder/delete"; //删除文件夹

    public static final String URL_CLOUD_FOLDER_FILE_DELETE = "cloud/v1/file/delete"; //删除文件

    public static final String URL_CLOUD_FOLDER_FILE_BATCH_DELETE = "cloud/v1/file/batchDelete"; //删除多个云盘文件

    public static final String URL_CLOUD_FOLDER_MEMBER = "cloud/v1/folder/member/list"; //查询文件夹用户列表

    public static final String URL_CONTACT_FRIEND_APPLY_COUNT = "im/contact/friend/v1/num_v2"; //查询申请中的好友数量, 改成v2

    public static final String URL_CLOUD_FOLDER_MEMBER_UPDATE = "cloud/v1/folder/member/update"; //修改文件夹用户

    public static final String URL_CLOUD_FOLDER_MEMBER_DELETE = "cloud/v1/folder/member/delete"; //删除文件夹用户

    public static final String URL_CLOUD_FOLDER_MEMBER_ADD = "cloud/v1/folder/member/add"; //增加文件夹用户

    public static final String URL_CONTACT_FRIEND_APPLY = "im/contact/friend/v1/apply"; //申请添加好友

    public static final String URL_CALENDAR_WORK_QUERY = "calendar/v1/system/query"; //查询日历数据

    public static final String URL_CALENDAR_USER_BUSY_TIME = "schedule/v1/user/busyTimeV2"; //查询其他人日程闲忙状态

    public static final String URL_CALENDAR_GROUP_USER_BUSY_TIME = "schedule/v1/listGroupCalendar"; //查询群日程闲忙状态

    public static final String URL_CALENDAR_MOVE_USER_TOP = "schedule/v1/topGroupCalendarSeq"; //人员置顶

    public static final String URL_CALENDAR_USER_BUSY_ORDER = "schedule/v1/addGroupCalendarSeq"; //查看闲忙人员排序

    public static final String URL_QUERY_SHARED_SCHEDULE_SUMMARY = "schedule/v1/calendar/list"; //查询日历日程（共享或订阅日历）

    public static final String URL_TICKET_FEE = "ticket/v1/fee"; //查询提现费率和收费

    public static final String URL_SYSTEM_SETTING = "system/v1/settings"; //查询用户系统设置

    public static final String URL_SYSTEM_SETTING_SET = "system/v1/setting/set"; //保存用户系统配置

    public static final String URL_TASK_LIST_MY = "task/v1/list/my"; //我的任务列表

    public static final String URL_TASK_ADD = "task/v1/add"; //添加任务

    public static final String URL_TASK_LOGS = "task/v1/logs"; //查询任务动态

    public static final String URL_TASK_QUERY_BY_ID = "task/v1/queryById"; //查询任务详情

    public static final String URL_BEST_MEETING_ROOM = "meeting/v1/recommend"; //查询推荐的会议室

    public static final String URL_RECOMMEND_MEETING_ROOM = "meeting/v1/recommend_v2"; //查询推荐的会议室

    public static final String URL_TASK_UPDATE_CONTENT = "task/v1/update/content"; //编辑内容

    public static final String URL_TASK_UPDATE_DESC = "task/v1/update/desc"; //编辑描述

    public static final String URL_TASK_UPDATE_PARTNER = "task/v1/update/partner"; //更新参与人

    public static final String URL_TASK_UPDATE_MARK_TYPE = "task/v1/update/markType"; //编辑优先级

    public static final String URL_TASK_UPDATE_EXECUTOR = "task/v1/update/executor"; //编辑执行人

    public static final String URL_TASK_UPDATE_DEADLINE = "task/v1/update/deadline"; //编辑截止时间

    public static final String URL_TASK_UPDATE_TASK_GROUP = "task/v1/update/taskGroup"; //编辑任务所属工作流

    public static final String URL_TASK_UPDATE_TASK_GROUP_SUB = "task/v1/update/taskGroupSub"; //编辑任务所属工作流分组

    public static final String URL_TASK_UPDATE_ADD_FILES = "task/v1/update/addFiles"; //增加文件

    public static final String URL_TASK_UPDATE_ADD_PICS = "task/v1/update/addPics"; //增加图片

    public static final String URL_TASK_UPDATE_DEL_ATTACH = "task/v1/update/delAttach"; //删除附件

    public static final String URL_TASK_GROUP_LIST = "task/v1/group/list"; //工作流列表

    public static final String URL_TASK_GROUP_SUB_LIST = "task/v1/group/subs"; //查询工作流分组

    public static final String URL_TASK_COMMENT_ADD = "task/v1/comment"; //添加任务评论

    public static final String URL_TASK_OPERATE = "task/v1/operate"; //操作任务(认领/完成/重置/参与)

    public static final String URL_TASK_DELETE = "task/v1/del"; //删除任务

    public static final String URL_CALENDAR_ECHO = "schedule/v1/echo"; //响应日程

    public static final String URL_SURVEY_QUERY = "survey/query"; //查询是否需要调研

    public static final String URL_SURVEY_ANSWER = "survey/answer"; //调研结果

    public static final String URL_TASK_DEL_LOG = "task/v1/delLog"; //删除任务评论

    public static final String URL_TASK_UPDATE_FIELD = "task/v1/update/field"; //编辑任务自定义字段

    public static final String URL_TASK_ALERT = "task/v1/alert"; //催办任务

    public static final String URL_TASK_SHARE = "task/v1/share"; //转发任务

    public static final String URL_TASK_GROUP_TAGS = "task/v1/group/tags"; //查询工作流标签列表

    public static final String URL_TASK_GROUP_ADD_TAG = "task/v1/group/createTag"; //添加工作流标签

    public static final String URL_TASK_UPDATE_TAG = "task/v1/update/tags"; //更新任务标签

    public static final String URL_USER_OUT_EMAIL = "user/v1/user/outer/email"; //根据邮箱获取用户信息

    public static final String URL_VIDEO_AUDIO_CALL_CREATE_ROOM = "link/room/create"; //视频语音通话创建房间

    public static final String URL_VIDEO_AUDIO_CALL_INVITE_ROOM = "link/room/invite"; //视频语音通话邀请通话

    public static final String URL_VIDEO_AUDIO_CALL_INVITE_REFUSE_ROOM = "link/room/inviteRefuse"; //拒绝语音视频通话

    public static final String URL_VIDEO_AUDIO_CALL_JOIN = "link/room/join"; //加入房间

    public static final String URL_VIDEO_AUDIO_CALL_LEAVE = "link/room/leave"; //离开房间

    public static final String URL_VIDEO_ENTER_MULTIMEDIA_ROOM = "link/meeting/v1/enter"; //后端加入房间

    public static final String URL_VIDEO_LEAVE_MULTIMEDIA_ROOM = "link/meeting/v1/quit"; //离开房间

    public static final String URL_VIDEO_MEETING_MUTE_MEMBER = "link/meeting/v1/mute"; //主持人静音群成员

    public static final String URL_VIDEO_MEETING_UNMUTE_MEMBER = "link/meeting/v1/unmute"; //主持人静音群成员

    public static final String URL_VIDEO_AUDIO_CALL_HEARTBEAT = "link/heartbeat/report"; //音视频通话心跳

    public static final String URL_ONLINE_FILE_INFO = "s/shimo/getFileInfo"; //获取在线文档信息

    public static final String URL_TASK_GROUP_SET = "task/v1/group/settings"; //查询工作流权限设置

    public static final String URL_GROUP_DISMISS = "im/group/v1/dismiss"; //解散群聊

    public static final String URL_SEARCH_CHAT_RECORD_DEPARTMENT = "search/v1/dept/name"; //搜索部门

    public static final String URL_SHINING_RECEIVE_LIST = "im/chat/v1/shining/receiveList"; //加急消息收到列表

    public static final String URL_GEE_CAPTCHA_REGISTER = "common/geeCaptchaRegister"; //极验验证初始化接口

    public static final String URL_GEE_CAPTCHA_VALIDATE = "common/geeSecondValidate_v2"; //极验验证二次验证接口

    public static final String URL_SHINING_ADD = "im/chat/v1/shining/add"; //设置消息加急

    public static final String URL_SHINING_OPERATE = "im/chat/v1/shining/operate"; //加急消息被处理

    public static final String URL_CHAT_SHIELD = "im/chat/v1/message/shield"; //群消息屏蔽

    public static final String URL_AT_REC_LIST = "im/group/v1/atRec/list"; //查询用户群聊 @ 人推荐

    public static final String URL_AT_REC_ADD = "im/group/v1/atRec/add"; //新增或更新用户 @ 人推荐 列表信息

    public static final String URL_PLUGIN_FAVORITE_SAVE = "open/app-center/v1/favorite/save_v2"; //保存常用应用列表

    public static final String URL_MEETING_WORKPLACE = "meeting/v1/workplace"; //查询员工的工作地点

    public static final String URL_QRCODE_PROTOCOL = "login/v1/qrCode/protocol"; //获取短链协议内容

    public static final String URL_SYSTEM_RAINS = "common/system/v1/rains"; //查看表情雨配置

    public static final String URL_LOG_UPLOAD = "log/v1/app/saveLog"; //上传本地tlog
    public static final String URL_TASK_GROUP_QUERY = "task/v1/group/setting/query"; //任务页面查询工作流

    public static final String URL_SCHEDULE_SEARCH_CALENDAR = "calendar/v1/public/recommend"; //搜索查询订阅日历（推荐）

    public static final String URL_SCHEDULE_CALENDAR_SUBCRIBE = "calendar/v1/public/subscribe"; //订阅日历

    public static final String URL_SCHEDULE_SEARCH_USER = "schedule/v1/subscribe/recommend"; //搜索查询订阅联系人（推荐）

    public static final String URL_SCHEDULE_SUBSCRIBE_USER = "schedule/v1/subscribe/user"; //查询用户已订阅列表

    public static final String URL_SCHEDULE_SUBSCRIBE_UPDATE = "schedule/v1/subscribe/update"; //更新订阅接口

    public static final String URL_SCHEDULE_PUBLIC_SUBSCRIBE = "calendar/v1/public/subscribe"; //更新订阅接口

    public static final String URL_SCHEDULE_UPDATE_COLOR = "schedule/v1/subscribe/updateColor"; //更新订阅日历颜色

    public static final String URL_SCHEDULE_MEETING_INFO = "meeting/v1/info"; //查询会议室信息

    public static final String URL_GET_GROUP_USER_COUNT = "im/group/v1/userCount"; //查询群成员数量

    public static final String URL_SINGLE_GROUP_SYNC = "im/group/v1/sync/syncGroup"; //查询单个群的详细信息

    public static final String URL_TICKET_THEME_CHOOSE = "ticket/v1/theme/choose"; //查询红包选中主题

    public static final String URL_TICKET_THEME_LIST = "ticket/v1/theme/list"; //查询红包主题列表

    public static final String URL_TASK_GROUP_TASK_AUTH_USER = "task/v1/group/taskAuth/user"; //查询用户工作流任务权限

    public static final String URL_ROOM_CHANGE = "link/room/change"; //切换语音聊天

    public static final String URL_CHAT_DELETE = "im/chat/v1/delete"; //删除会话

    public static final String URL_DELETE_MSGS = "im/chat/v1/message/delMsgs"; //批量删除消息

    public static final String URL_MARK_ADD = "im/chat/v1/mark/add"; //mark消息

    public static final String URL_MARK_CANCEL = "im/chat/v1/mark/cancel"; //取消mark消息

    public static final String URL_MARK_IS_READ = "im/chat/v1/mark/isRead"; //查询是否存在未读 mark

    public static final String URL_MARK_LIST = "im/chat/v1/mark/list"; //获取mark列表

    public static final String URL_GET_DEPT_LIST = "user/v1/user/dept/showStatus"; //获取用户部门列表状态

    public static final String URL_UPDATE_DEPT_LIST_STATUS = "user/v1/user/dept/updateShow"; //更新用户部门列表状态

    public static final String URL_TIEBI_UPLOAD_CHEAT = "tiebi/uploadCheat"; //上传铁壁作弊数据

    public static final String URL_SHINING_CANCEL = "im/chat/v1/shining/cancel"; //加急消息取消

    public static final String URL_TASK_LAST_WATCH = "user/v1/user/lastWatchTask"; //查询用户最后一次查看任务

    public static final String URL_TASK_ADD_LAST_WATCH = "user/v1/user/addLastWatchTask"; //添加用户最后一次查看任务

    public static final String URL_SET_SIGNATURE = "user/v1/user/updateSignature"; //设置用户个人签名

    public static final String URL_SYSTEM_WORK_STATUS = "common/system/v1/workStatus"; //查询工作状态配置

    public static final String URL_H5_CHECK_IN_RULES = "checkin/rules"; //考勤规则地址

    public static final String URL_GROUP_FOCUS_ADD = "im/group/v1/focus/add"; //添加群聊特别关心用户

    public static final String URL_GROUP_FOCUS_QUERY = "im/group/v1/focus/query"; //查询群聊特别关心

    public static final String URL_GROUP_FOCUS_DEL = "im/group/v1/focus/del"; //移除群聊特别关心用户

    public static final String URL_WORKFLOW_TASK_LIST_GROUP = "task/v1/list/groupSub"; //工作流任务列表

    public static final String URL_CHAT_ENTER = "im/chat/v1/enter"; //进入会话

    public static final String URL_SCHEDULE_MEETING_SIGN_STATUS = "meeting/v1/querySignStatus"; //查询会议室签到状态

    public static final String URL_SCHEDULE_MEETING_SIGN_OUT = "meeting/v1/signOut"; //会议室签退

    public static final String URL_COMMON_WORDS_LIST = "comWords/list"; //常用语列表

    public static final String URL_COMMON_WORDS_CHANGE_ORDER = "comWords/app/changeOrder"; //常用语列表排序

    public static final String URL_COMMON_WORDS_DELETE = "comWords/app/drop"; //常用语删除

    public static final String URL_COMMON_WORDS_PUT = "comWords/app/put"; //常用语-添加/编辑

    public static final String URL_CHAT_TRANS_TEXT = "im/asr/v1/transText"; //语音消息转文字

    public static final String URL_SHINING_CHECK = "im/chat/v1/shining/check"; //判断加急消息+处理异常加急消息

    public static final String URL_MESSAGE_TRANSLATE = "im/chat/v2/message/translate/start"; //消息翻译

    public static final String URL_MESSAGE_TRANSLATE_QUERY = "im/chat/v2/message/translate/queryResult"; //消息翻译结果查询

    public static final String URL_MESSAGE_TRANSLATE_CANCEL = "im/chat/v2/message/translate/cancel"; //取消消息翻译

    public static final String URL_TASK_SON_ADD = "task/v1/son/add"; //添加子任务

    public static final String URL_MEETING_CREATE = "link/meeting/v1/create";//创建会议

    public static final String URL_MEETING_INVITE = "link/meeting/v1/invite";//点对点发起呼叫

    public static final String URL_MEETING_CHECK = "link/meeting/v1/check";//获取房间信息

    public static final String URL_MEETING_REJECT = "link/meeting/v1/notJoin";//会议暂不加入

    public static final String URL_MEETING_INFO = "link/meeting/v1/info/get";//查询房间信息（仅查询不创建）

    public static final String URL_MEETING_REFUSE = "link/meeting/v1/inviteRefuse";//单聊拒绝呼叫

    public static final String URL_MEETING_CANCEL = "link/meeting/v1/inviteCancel";//单聊取消呼叫

    public static final String URL_MEETING_CLOSE = "link/meeting/v1/close";//结束会议

    public static final String URL_MEETING_ACCEPT = "link/meeting/v1/inviteAccept";//接受邀请

    public static final String URL_MEETING_CHECK_IN_MEETING = "link/meeting/v1/checkInMeeting";//用户会议状态判断

    public static final String URL_MEETING_FORWARD = "link/meeting/v1/forward";//转发会议邀请

    public static final String URL_MEETING_MUTE_ALL = "link/meeting/v1/muteAll";//全员静音

    public static final String URL_MEETING_UN_MUTE_ALL = "link/meeting/v1/unmuteAll";//取消全员静音

    public static final String URL_MEETING_KICK = "link/meeting/v1/kick";//异常退出后，新建会议，需要退出之前的会议

    public static final String URL_MEETING_WHITE_USER_CHECK = "link/meeting/v1/white/user/check"; // 查询用户是否会议白名单

    public static final String URL_USER_CLOCK_TIME = "clock/v1/clock/time"; //截止时间列表


    public static final String URL_WORK_STATUS_SYNC_SETTINGS_LIST = "user/v1/user/comSetting/auto/setting/list"; //工作状态自动同步设置列表

    public static final String URL_WORK_STATUS_SYNC_SETTINGS_UPDATE = "user/v1/user/comSetting/auto/setting/update"; //工作状态自动同步设置更新

    public static final String URL_GET_MSG_NOTIFY = "user/v1/user/msgNotify/get"; //查询用户消息通知设置

    public static final String URL_GET_MSG_TRANSLATE = "im/chat/v1/getTranslateConfig"; //查询可翻译语种列表 & 已选择语种

    public static final String URL_UPDATE_MSG_NOTIFY = "user/v1/user/msgNotify/update"; //更新用户消息通知设置

    public static final String URL_DEVICE_QUERY = "safe/v2/device/list"; // 获取用户设备列表

    public static final String URL_DEL_DEVICE = "safe/v2/device/delete"; // 删除或退出设备

    public static final String URL_TASK_SUB_LIST = "task/v1/sub/list"; //查询子任务列表
    public static final String URL_SEARCH_PLUGIN = "open/app-center/v1/search"; //搜索工作台应用

    public static final String URL_SEARCH_FAVOR = "search/v1/favor/content_v2"; //搜索收藏消息

    public static final String URL_SEARCH_SHI_MO = "search/shimo/content"; //搜索石墨文档

    public static final String URL_SCHEDULE_ICS = "schedule/v1/ics/url/get"; //获取日历ICS订阅链接

    public static final String URL_GROUP_SHARE_CHECK = "im/group/v1/share/check"; //群聊分享校验

    public static final String URL_GET_GROUP_DETAIL = "im/group/v1/groupCard/info"; //查询群名片信息

    public static final String URL_GROUP_CARD_JOIN = "im/group/v1/groupCard/join"; //通过群名片加群

    public static final String URL_SPLASH_PIC_QUERY = "user/v1/company/showWelcomePage"; //查询开屏页图片
    public static final String URL_GROUP_PUBLIC_JOIN = "im/group/v1/public/join"; //加入公开群

    public static final String URL_CHAT_FINISHED_UPDATE = "im/chat/v1/finished/update"; //会话移入或移出"已完成"tab

    public static final String URL_CONTACT_FRIEND_CLICK = "im/contact/friend/v1/click"; // 点击新的联系人列表，用于消除红点

    public static final String URL_CHAT_GROUP_SYNC = "im/chat/grouping/v1/sync"; // 2.39 会话分组同步（全量）

    public static final String URL_CHAT_GROUP_UPDATE = "im/chat/grouping/v1/update"; // 新增、编辑会话分组

    public static final String URL_CHAT_GROUP_DELETE = "im/chat/grouping/v1/del"; // 删除会话分组

    public static final String URL_CHAT_GROUP_SORT = "im/chat/grouping/v1/manager"; // 会话排序

    public static final String URL_CHAT_GROUP_UPDATE_BY_CHAT_ID = "im/chat/grouping/v1/updateByChatId"; // 更新会话所属标签（分组）

    public static final String URL_GROUP_CANCEL_INVITE = "im/group/v1/cancelInvite"; // 取消入群邀请

    public static final String URL_CHAT_BOT_NLP_FEEDBACK = "im/chat/v1/bot/nlp/feedBack"; // nlp问答反馈

    public static final String URL_SAFETY_VERIFY_RESULT_REPORT = "safe/v2/reportFaceVerify"; // 移动端可信设备认证 结果上报

    public static final String URL_SAFETY_MOBILE_SAFE_CONFIRM = "safe/v2/mobileSafeConfirm"; // pc触发可信设备认证 登录确认

    public static final String URL_KZMP_NEWEST_VERSION = "open/app-center/v1/base/resource/get"; // 小程序基础库最新版本获取

    public static final String URL_KZMP_VERSIONS = "open/app-center/v1/base/resource/getList"; // 小程序基础库历史版本获取

    public static final String URL_SYNC_RELATION_FOR_PRESENCE = "im/chat/v1/sync/relationForPresence"; // 出席时会话拉取接口（Pull）

    public static final String URL_HISTORY_VISIBLE_MESSAGE = "im/chat/v1/message/history_visible_message"; // 指定会话历史消息拉取（保证返回一定数量可见消息）

    public static final String URL_CHAT_PROPS_SYNC = "im/chat/v1/sync/property"; // 会话属性同步

    public static final String URL_CHAT_TAB_ADD = "im/chat/v1/tab/add"; // 会话标签页新增

    public static final String URL_CHAT_TAB_UPDATE = "im/chat/v1/tab/update"; // 会话标签页更新

    public static final String URL_CHAT_TAB_DELETE = "im/chat/v1/tab/delete"; // 会话标签页删除

    public static final String URL_CHAT_TAB_SORT = "im/chat/v1/tab/sort"; // 会话标签页排序

    public static final String URL_CHAT_STICKY_TOP_MSG_CREATE = "im/chat/v1/top/message/create"; // 聊天消息置顶

    public static final String URL_CHAT_STICKY_TOP_MSG_CANCEL = "im/chat/v1/top/message/cancel"; // 聊天置顶消息取消

    public static final String URL_CHAT_STICKY_TOP_MSG_CANCEL_SELF = "im/chat/v1/top/message/cancelSelf"; // 聊天置顶消息仅对自己取消

    public static final String URL_SLASH_COMMAND_SYNC_ALL = "im/command/sync/all"; // 斜杠指令全量同步

    public static final String URL_SLASH_COMMAND_SYNC_PART = "im/command/sync/part"; // 斜杠指令增量同步

    public static final String URL_SLASH_COMMAND_SYNC_RECENT = "im/command/sync/recent"; // 最近使用斜杠指令列表

    public static final String URL_FEED_SORT_UPDATE = "im/feedsort/v2/update"; // 更新feed列表排序

    public static final String URL_FEED_SORT_QUERY = "im/feedsort/v2/get"; // 获取feed列表排序

    // 开放平台网页免登录 code 获取
    public static final String URL_PLATFORM_TMP_CODE = "third/tmpCode/v2";

    // 小程序 login 接口
    public static final String URL_KZMP_TMP_CODE = "third/tmpCode";

    public static final String URL_OPEN_ID_TRANSFER = "open/app-center/v1/applinkTrans"; //开放体系下的id解密与还原

    public static final String URL_OPEN_APP_GET_LASTED = "open/app-center/v1/openApp/getLasted"; //获取开放应用最新详情

    public static final String URL_ID_ENCRYPT = "system/v1/idEncrypt"; //业务id加密

    public static final String URL_MEDIA_CHECK_FILE = "media/v2/checkExist/file"; // 检测文件是否已存在

    public static final String URL_MEDIA_CHECK_VIDEO = "media/v2/checkExist/video"; // 检测视频是否已存在

    public static final String URL_IM_MSG_MONITOR = "common/v1/reportMetric"; // IM消息监控数据上报

    // jsapi 鉴权接口
    public static final String URL_JS_API_CONFIG = "third/jsapi/auth";

    // 搜索反馈
    public static final String URL_SEARCH_FEED_BACK = "search/feedback/add";

    public static final String URL_SYNC_SIGNAL = "im/signal/v2/sync";

    public static final String URL_MEETING_CHECK_MOBILE_TYPE = "link/meeting/v1/checkMobileType"; // 判断机型是否适配

    public static final String URL_MEETING_STOP_RECORD = "link/meeting/v1/stopRecord"; // 结束录制

    public static final String URL_SEARCH_FEEDBACK_BATCH_PROBLEM = "search/feedback/batch/problem"; // 批量获取问题类型

    public static final String URL_RECENT_EMOJI_QUERY = "im/sticker/v1/sort/get"; // 获取最近表情
    public static final String URL_RECENT_EMOJI_UPDATE = "im/sticker/v1/sort/update"; // 更新最近表情

    public static final String URL_CHAT_AT_ME_V2 = "im/chat/v2/atMe/list"; // 新版@me 列表查询接口

    public static final String URL_CHAT_MARK_ADD = "im/chat/mark/v1/add"; // 添加标记

    public static final String URL_CHAT_MARK_CANCEL = "im/chat/mark/v1/cancel"; // 取消标记

    // 灰度配置获取
    public static final String URL_SYSTEM_GRAY_CONFIG = "system/v1/gray/config";

    public static final String URL_GET_USER_INFO = "open/app-center/v1/jsapi/user/get";

    // 用 bosshi 字段置换 开放平台字段
    public static final String URL_EXCHANGE_BH_TO_OPEN = "open/app-center/v1/batch_user_info/bosshi_to_open";

    // 用 开放平台字段置换 bosshi 字段
    public static final String URL_EXCHANGE_OPEN_TO_BH = "open/app-center/v1/batch_user_info/open_to_bosshi";

    public static final String URL_EXCHANGE_CARD_CONTENT = "open/app-center/v1/compressive_card/content_transfer";
    public static final String URL_CHECK_SESSION = "open/app-center/v1/jsapi/check/session";

    public static final String URL_MEETING_SDK_SIGN_INFO = "link/meeting/v1/getSdkSignInfo"; // 客户端进入会议前，获取SDK签名信息

    public static final String URL_MESSAGE_CALLBACK_NEW_CARD = "im/chat/v1/message/callback/newCard"; // 消息卡片回传参数

    public static final String URL_START_RECORD_MEETING = "link/meeting/v1/startRecord"; //开启录制
    public static final String URL_START_AI_SUMMARY = "link/meeting/v1/startSummary"; //主持人同意开启Ai总结

    public static final String URL_APPLY_RECORD_MEETING = "link/meeting/v1/applyRecord";//请求主持人录制

    public static final String URL_REJECT_RECORD_MEETING = "link/meeting/v1/rejectRecord"; //拒绝录制

    public static final String URL_REJECT_AI_SUMMARY_REQUEST = "link/meeting/v1/rejectSummary"; //拒绝开启AI总结
    // 搜索联系人
    public static final String URL_SEARCH_CONTACT_V2 = "search/v1/user/name/choose";

    // 同步联系人开聊关系
    public static final String URL_SYNC_RELATIONSHIP = "im/chat/v2/sync/relationShip";

    public static final String URL_SYNC_CONTACT = "user/front/v1/contact/users";

    public static final String URL_SEARCH_EMAIL = "search/v1/mail/content";

    public static final String URL_ID_DECRYPTION = "system/v1/idDecryptForJsApi";

    public static final String URL_SCHEDULE_CREATE_GROUP = "schedule/v1/createScheduleGroup"; //日程创建群聊

    public static final String URL_JOIN_SCHEDULE_GROUP = "schedule/v1/joinScheduleGroup"; //根据日程ID加入日程创建的群聊

    public static final String URL_MEETING_INVITATION_LIST = "link/meeting/v1/notMeetList"; //未入会列表

    public static final String URL_MEETING_CALL_INVITE = "link/meeting/v1/callInvite"; //发起会议呼叫邀请

    public static final String URL_MEETING_CALL_INVITE_ACCEPT = "link/meeting/v1/callInviteAccept"; //取消接受

    public static final String URL_MEETING_CALL_INVITE_REJECT = "link/meeting/v1/callInviteReject"; //取消拒绝

    public static final String URL_MEETING_CALL_INVITE_CANCEL = "link/meeting/v1/callInviteCancel"; //取消呼叫

    public static final String URL_MEETING_ENTER_FAIL = "link/meeting/v1/enterFail"; //入会失败

    public static final String URL_MEETING_GET_ROOM_ID = "link/meeting/v1/getRoomId"; //会议号转换

    public static final String URL_MEETING_SUMMERY_OPERATE = "link/meeting/v1/summaryOperate"; //开启/关闭AI总结


    public static final String URL_MEETING_SUMMERY_SETTING = "link/meeting/v1/summarySetting"; //查看/点击移动端红点


    public static final String URL_MEETING_SUMMERY_HINT = "link/meeting/v1/summaryHint"; //收到运营弹窗回调
    public static final String URL_MEETING_BOARD_VERSION = "link/meeting/v1/getBoardVersion"; //获取白板version
    public static final String URL_MEETING_UPDATE_BOARD_VERSION = "link/meeting/v1/updateBoardVersion"; //更新白板version

    public static final String URL_MEETING_SYNC_BOARD_DATA = "link/meeting/v1/syncBoardData"; //同步白板data

    public static final String URL_MEETING_GET_USER_VERSION = "link/meeting/v1/getUserVersion"; //获取用户版本

    public static final String URL_MEETING_USER_STATUS_ADD = "link/meeting/v1/user/status/add"; //新增会议用户状态

    public static final String URL_MEETING_USER_STATUS_DELETE = "link/meeting/v1/user/status/delete"; //删除会议用户状态
    public static final String URL_MEETING_USER_STATUS_CLEAR = "link/meeting/v1/user/status/clear"; //清除会议用户状态

    public static final String URL_MEETING_USER_STATUS_LIST = "link/meeting/v1/user/status/list"; //查询会议用户的状态

    // 邮件总结
    public static final String URL_EMAIL_SUMMARY = "mail/ai/summary";

    // 生成邮件
    public static final String URL_EMAIL_GENERATE_SUMMARY = "mail/ai/reGenerate";

    // 邮件签名图片上传
    public static final String URL_EMAIL_SIGNATURE_IMG_UPLOAD = "image/mail/upload";

    // 检查是否展示会话ai总结
    public static final String URL_CHAT_MSG_AI_SUMMARY_CHECK = "im/chat/v2/summary/check";

    // 轮训获取ai总结结果
    public static final String URL_CHAT_MSG_AI_SUMMARY_CREATE = "im/chat/v2/summary/create";

    public static final String URL_MEETING_HOST_OPEN_CAMERA = "link/meeting/v1/openCamera"; //请求成员开启摄像头

    public static final String URL_MEETING_HOST_CLOSE_CAMERA = "link/meeting/v1/closeCamera"; //关闭成员摄像头

    public static final String URL_MEETING_HOST_KICK_USER = "link/meeting/v1/kickUser"; //会议踢人

    public static final String URL_MEETING_UPDATE_HOST = "link/meeting/v1/updateHost"; //更新主持人

    public static final String URL_MEETING_GET_BOX_TYPE = "link/meeting/v1/getMeetingBoxType"; //获取盒子类型

    // 直书文档预览权限查询
    public static final String URL_ZHISHU_PERMISSION_CHECK = "zhishu/service/bosshi/preview/check";

    // 获取下载链接
    public static final String URL_ZHISHU_FETCH_DOWNLOAD_URL = "zhishu/transfer/bosshi/prepare";

    public static final String URL_CHECK_MEETING_INFO = "link/meeting/v1/checkMeetingInfo"; //获取参会信息

    public static final String URL_CONTACT_BASIC_INFO = "user/front/v1/contact/syncStaffDeptBasic"; //通讯录基础信息

    public static final String URL_CONTACT_DEPTMENT_VISIABLE_INFO = "user/front/v1/contact/syncStaffDeptVisible"; //同步团队组织架构员工和部门可见性数据

    public static final String URL_CONTACT_DYNAMIC_INFO = "user/front/v1/contact/syncStaffDynamic"; //同步团队组织架构用户动态数据

    public static final String URL_CONTACT_SYNC_ROBOT_INFO = "user/front/v1/contact/syncBot"; //同步团队机器人+系统用户数据

    public static final String URL_MEETING_MESSAGE_HISTORY = "link/meeting/v1/message/history"; //获取音视频会议间消息记录

    public static final String URL_THIRD_PARTY_LOGIN = "open/app-center/v1/third/login"; //第三方登录鉴权

    public static final String URL_DEFAULT_TAB_ORDER = "open/app-center/v1/defaultTabOrder";//获取固定的tab默认排序&应用
    public static final String URL_UPDATE_DEFAULT_TAB_ORDER = "open/app-center/v1/updateTabOrder";//更改导航栏排序
    public static final String URL_MEETING_UPDATE_SETTING = "link/meeting/v1/updateSetting";//更新会议配置
    public static final String URL_MEETING_UPDATE_SUBJECT = "link/meeting/v1/updateSubject";//更新会议主题类型
    public static final String URL_BOT_CHECK_QUICK_MENU = "im/chat/v1/bot/checkQuickMenu";//查询快捷菜单是否可用
    public static final String URL_BOT_NEW_CONVERSATION = "im/chat/v2/bot/newConversation";//查询快捷菜单是否可用
    public static final String URL_BOT_QUERY = "im/chat/v2/bot/query";//查询流式消息

    public static final String URL_SHORTHAND_BEGIN = "application/transcription/begin";//新开启一个速记

    public static final String URL_GET_SHORTHAND_TYPE = "application/transcription/type";//获取速记类型
    public static final String QUERY_SHORTHAND_BY_ID = "application/transcription/queryById";//通过速记ID查询速记
    public static final String QUERY_SHORTHAND_BY_USER_ID = "application/transcription/queryByUser";//通过userId查询速记
    public static final String URL_SHORTHAND_EDIT = "application/transcription/update";//速记编辑
    public static final String URL_SHORTHAND_VISIBLE = "application/transcription/visible";//速记是否可见
    public static final String URL_SHORTHAND_END = "application/transcription/end";//结束速记

    public static final String URL_V_UPLOAD_INFO = "media/v/upload/info";//文件上传v组信息
    public static final String URL_V_UPLOAD_QUICK = "media/v/upload/quick";//文件上传v组秒传
    public static final String URL_V_UPLOAD_CALLBACK = "media/v/upload/callback";//文件上传v组成功回调
    public static final String URL_V_VIDEO_PLAY = "media/v/upload/play";//文件上传v组成功回调

    public static List<String> FILE_UPLOAD_LIST = Arrays.asList(
            URL_UPLOAD_AVATAR,
            URL_UPLOAD_IMAGE_V2,
            URL_UPLOAD_FILE,
            URL_MEDIA_CHECK_FILE,
            URL_MEDIA_CHECK_VIDEO,
            URL_UPLOAD_VIDEO,
            URL_UPLOAD_EMOTION,
            URL_NOAH_UPLOAD_FILE,
            URL_REPLACE_IMAGE_URL,
            URL_EMAIL_SIGNATURE_IMG_UPLOAD,
            URL_V_UPLOAD_INFO,
            URL_V_UPLOAD_QUICK,
            URL_V_UPLOAD_CALLBACK,
            URL_V_VIDEO_PLAY
    );

    public static List<String> ZHISHU_LIST = Arrays.asList(URL_ZHISHU_PERMISSION_CHECK, URL_ZHISHU_FETCH_DOWNLOAD_URL, URL_ZHISHU_RESOURCE, URL_ZHISHU_SPACE, URL_ZHISHU_SAVE_FILE, URL_ZHISHU_TRANSFER);

    public static List<String> FILE_H5_LIST = Arrays.asList(URL_H5_APPLY_DETAIL_LEAVE, URL_H5_APPLY_DETAIL_ATTENDANCE, URL_H5_APPLY_DETAIL_TRAVEL, URL_H5_WECHAT_NOTIFY, URL_H5_APPLY_LEAVE, URL_H5_APPLY_ATTENDANCE, URL_H5_APPLY_TRAVEL, URL_H5_CHECK_IN_RULES);

    public static List<String> LOG_UPLOAD_LIST = Collections.singletonList(URL_LOG_UPLOAD);
}
