package com.twl.hi.foundation.utils.emotion

import android.content.Context
import android.graphics.drawable.Drawable
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.sankuai.waimai.router.Router
import com.techwolf.lib.tlog.TLog
import com.twl.hi.foundation.model.emotion.EmojiModel
import com.twl.hi.foundation.model.emotion.EmotionItem
import com.twl.hi.foundation.model.emotion.ServerEmotionCategoryBean
import lib.twl.common.ext.dp
import lib.twl.common.ext.getResourceDrawable
import lib.twl.common.views.EmotionTextIndex

/**
 * emotion模块全局工具类
 *
 * Created by tan<PERSON><PERSON> on 2023/8/15
 */
object EmotionGlobalHelper {

    const val EMOTION_SERVICE_KEY = "emotion_global_service"

    private const val TAG = "EmotionGlobalHelper"

    @JvmStatic
    fun updateAllEmotionData(userId: String?, emotionList: MutableList<ServerEmotionCategoryBean>?) {
        getEmotionGlobalService()?.updateAllEmotionData(userId, emotionList)
    }

    @JvmStatic
    fun updateAllEmojiData(userId: String, emojiList: MutableList<EmojiModel>) {
        getEmotionGlobalService()?.updateAllEmojiData(userId, emojiList)
    }

    @JvmStatic
    fun getEmojiItemByName(text: String?, filterNotActive: Boolean = true): EmotionItem? {
        return getEmotionGlobalService()?.getEmojiItemByName(text, filterNotActive)
    }

    @JvmStatic
    fun getEmojiItemBySid(sid: Long): EmotionItem? {
        return getEmotionGlobalService()?.getEmojiItemBySid(sid)
    }

    @JvmStatic
    fun getAllEmojiItems(): List<EmotionItem>? {
        return getEmotionGlobalService()?.getAllEmojiItems()
    }

    private fun getEmotionGlobalService(): IEmotionGlobalService? {
        return Router.getService(IEmotionGlobalService::class.java, EMOTION_SERVICE_KEY)
    }

    @JvmStatic
    fun getEmojiItemDrawable(
        context: Context?,
        emojiTextIndexItem: EmotionTextIndex?,
        callback: OnEmojiDrawableGetCallback?,
        size: Int = 24.dp.toInt()
    ) {
        emojiTextIndexItem?.let {
            if (it.emojiResId > 0) {
                it.emojiResId.getResourceDrawable()?.run {
                    setBounds(0, 0, size, size)
                    callback?.onEmojiDrawableGet(this)
                }
            } else {
                context ?: return
                Glide.with(context)
                    .load(it.emojiUrl)
                    .override(size, size)
                    .into(object : CustomTarget<Drawable>() {
                        override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                            resource.run {
                                setBounds(0, 0, size, size)
                                callback?.onEmojiDrawableGet(this)
                            }
                        }

                        override fun onLoadCleared(placeholder: Drawable?) {
                        }

                        override fun onLoadFailed(errorDrawable: Drawable?) {
                            super.onLoadFailed(errorDrawable)
                            callback?.onEmojiDrawableGet(null)
                            TLog.error(TAG, "getEmojiItemDrawable error,url:" + it.emojiUrl)
                        }
                    })
            }
        }
    }
}

interface OnEmojiDrawableGetCallback {
    fun onEmojiDrawableGet(drawable: Drawable?)
}