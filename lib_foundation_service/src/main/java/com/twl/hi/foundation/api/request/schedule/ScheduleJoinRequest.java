package com.twl.hi.foundation.api.request.schedule;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

public class ScheduleJoinRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String scheduleId;
    @Expose
    public String msgId;

    public ScheduleJoinRequest(AbsRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_CALENDAR_JOIN;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
