package com.twl.hi.foundation.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.response.ZhiShuResourcesResponse;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

/**
 * Author : Xuweixiang .
 * Date   : On 2023/10/17
 * Email  : Contact <EMAIL>
 * Desc   :
 */

public class ZhiShuResourceRequest extends BaseApiRequest<ZhiShuResourcesResponse> {

    @Expose
    public int pageNum;

    @Expose
    public int pageSize;

    @Expose
    public String visitCode;

    // 1-发送直书文件 2-保存到直书
    @Expose
    public int type;

    public ZhiShuResourceRequest(AbsRequestCallback<ZhiShuResourcesResponse> mCallback, int pageNum, int pageSize, String visitCode, int type) {
        super(mCallback);
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.visitCode = visitCode;
        this.type = type;
    }

    public ZhiShuResourceRequest(AbsRequestCallback<ZhiShuResourcesResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_ZHISHU_RESOURCE;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
