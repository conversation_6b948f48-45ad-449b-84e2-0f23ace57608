package com.twl.hi.foundation.model.email.bean.receive;

/**
 * <AUTHOR>
 */
public class AttachFileDownloadResult extends BaseMailResponse{
    // 结果 1 失败， 0成功
    private int result;
    // 邮件id
    private String mailId;
    // cid
    private String cid;

    private String requestId;
    // 保存路径
    private String savePath;

    private String buryInfo;

    public String getBuryInfo() {
        return buryInfo;
    }

    public void setBuryInfo(String buryInfo) {
        this.buryInfo = buryInfo;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public String getMailId() {
        return mailId;
    }

    public void setMailId(String mailId) {
        this.mailId = mailId;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getSavePath() {
        return savePath;
    }

    public void setSavePath(String savePath) {
        this.savePath = savePath;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("AttachFileDownloadResult{");
        sb.append("result=").append(result);
        sb.append(", mailId='").append(mailId).append('\'');
        sb.append(", cid='").append(cid).append('\'');
        sb.append(", requestId='").append(requestId).append('\'');
        sb.append(", savePath='").append(savePath).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
