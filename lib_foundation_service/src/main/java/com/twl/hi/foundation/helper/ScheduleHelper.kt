package com.twl.hi.foundation.helper

import com.techwolf.lib.tlog.TLog
import com.twl.hi.foundation.api.request.schedule.CalendarGroupListRequest
import com.twl.hi.foundation.api.response.CalendarGroupListResponse
import com.twl.hi.foundation.api.response.bean.CalendarBean
import com.twl.hi.foundation.api.response.bean.CalendarItemBean
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.callback.ApiRequestCallback
import com.twl.http.error.ErrorReason

/**
 *@author: musa on 2022/11/17
 *@e-mail: <EMAIL>
 *@desc:日程辅助类
 */
object ScheduleHelper {
    private const val TAG = "ScheduleHelper"

    /**
     * 查询 日历列表
     * 合并两个请求的结果
     */
    @JvmStatic
    fun queryCalendarList(resultCallback: QueryCalendarListCallback) {

        CalendarGroupListRequest(
            object : ApiRequestCallback<CalendarGroupListResponse>() {

                override fun handleInChildThread(data: ApiData<CalendarGroupListResponse>?) {
                    data?.resp?.data?.let {
                        it.mapNotNull { bean ->
                            CalendarItemBean.convertToViewBean(bean)
                        }.let { beans ->
                            resultCallback.onGetCalendarList(beans, false)
                        }
                        //获取过一次日历列表信息后，选中状态肯定已经存储了，这里把标记置为true
                        ServiceManager.getInstance().scheduleService.signalCacheCalendarCheckState()
//                    removeInvalidCalendarCache(it)
                    } ?: let {
                        TLog.error(TAG, "请求日程分组返回空")
                    }
                }

                override fun onSuccess(data: ApiData<CalendarGroupListResponse>?) {

                }

                override fun onComplete() {
                }

                override fun onFailed(reason: ErrorReason?) {
                    resultCallback.onGetCalendarList(emptyList(), true)
                }
            }).let {
            HttpExecutor.execute(it)
        }
    }

}

/**返回结果的回调*/
interface QueryCalendarListCallback{
    fun onGetCalendarList(calendarList: List<CalendarItemBean>, occurException: Boolean)
}

