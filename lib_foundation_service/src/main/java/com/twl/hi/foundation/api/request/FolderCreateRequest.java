package com.twl.hi.foundation.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.response.FolderCreateResponse;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class FolderCreateRequest extends BaseApiRequest<FolderCreateResponse> {

    @Expose
    public String parentId;//父文件夹id，根目录默认为空，已加密
    @Expose
    public String name;//文件夹名称

    public FolderCreateRequest(BaseApiRequestCallback<FolderCreateResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_CLOUD_FOLDER_CREATE;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}

