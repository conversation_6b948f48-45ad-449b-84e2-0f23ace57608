package com.twl.hi.foundation.model;

import android.text.TextUtils;

import androidx.room.Ignore;

import com.twl.hi.foundation.utils.ContactUtils;

import java.io.Serializable;

import hi.kernel.Constants;

/**
 * <AUTHOR>
 * @date 2020/5/11.
 */
public class ConversationSelectBean extends ConversationSelectBaseBean implements Serializable {
    private static final long serialVersionUID = 5599633226888505937L;

    /**
     * 头像
     */
    private String avatar;
    private String userName;
    /**
     * 单选时用户类型
     */
    @Ignore
    private int userType;
    /**
     * 群备注
     */
    private String groupRemark;
    private String nickName;
    /**
     * A3jqnkmSbjNY/kWGU1HM2g==：普通群、8E49nwtSsYoQPQdeL4d0zg==：全员群、其它：部门群 ，常量存储在 Constants
     */
    private String deptId = Constants.NOT_DEPARTMENT_GROUP;
    @Ignore
    private String groupMemberName;
    /**
     * 搜索关键字
     */
    @Ignore
    private String searchKey;
    /**
     * 小头像 对应Group表中的tinyAvatar字段
     */
    @Ignore
    private String tinyAvatar;
    /**
     * 群公告 对应Group表中的bulletinr字段
     */
    @Ignore
    private String bulletin;
    /**
     * 群成员数
     */
    @Ignore
    private int memberCount;

    /**
     * 是否是公开群
     */
    @Ignore
    private boolean isPublic;

    @Ignore
    private int pageFrom; // 具体业务场景才有的概念，0代表推荐页面，1代表搜索页面，用于埋点; -1表示不需要上传埋点的场景

    @Ignore
    private int position; // 具体业务场景才有的概念, 代表当前Bean在列表(recyclerview)中显示的位置，从1开始计算

    /**
     * 单聊时，用户的备注名
     */
    private String remark;

    private int groupMemberCount = 0;
    // 1:AI助理, 0:非AI助理
    private int aiRobot = 0;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getAvatar() {
        return avatar;
    }

    public String getUserName() {
        return userName;
    }

    public String getStrAvatar() {
        return ContactUtils.getDisplayAvatarStr(userName);
    }


    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public int getUserType() {
        return userType;
    }

    public void setUserType(int userType) {
        this.userType = userType;
    }

    public String getGroupRemark() {
        return groupRemark;
    }

    public void setGroupRemark(String groupRemark) {
        this.groupRemark = groupRemark;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getGroupMemberName() {
        return groupMemberName;
    }

    public void setGroupMemberName(String groupMemberName) {
        this.groupMemberName = groupMemberName;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    /**
     * 是否展示公司名称
     */
    @Ignore
    public boolean showGroupName() {
        if (TextUtils.isEmpty(searchKey)) {
            return false;
        }
        if (TextUtils.isEmpty(groupRemark)) {
            return false;
        }
        return !TextUtils.isEmpty(userName) && userName.contains(searchKey);
    }

    public String getTinyAvatar() {
        return tinyAvatar;
    }

    public void setTinyAvatar(String tinyAvatar) {
        this.tinyAvatar = tinyAvatar;
    }

    public String getBulletin() {
        return bulletin;
    }

    public void setBulletin(String bulletin) {
        this.bulletin = bulletin;
    }

    public int getMemberCount() {
        return memberCount;
    }

    public void setMemberCount(int memberCount) {
        this.memberCount = memberCount;
    }

    public boolean isPublic() {
        return isPublic;
    }

    public void setPublic(boolean aPublic) {
        isPublic = aPublic;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public void setPageFrom(int value) {
        pageFrom = value;
    }

    public int getPageFrom() {
        return pageFrom;
    }

    public void setPosition(int pos) {
        position = pos;
    }

    public int getPosition() {
        return position;
    }

    public int getGroupMemberCount() {
        return groupMemberCount;
    }

    public void setGroupMemberCount(int groupMemberCount) {
        this.groupMemberCount = groupMemberCount;
    }

    public int getAiRobot() {
        return aiRobot;
    }

    public void setAiRobot(int aiRobot) {
        this.aiRobot = aiRobot;
    }
}
