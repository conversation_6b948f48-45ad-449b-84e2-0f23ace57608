package com.twl.hi.foundation.utils.monitor

import com.twl.hi.foundation.api.request.ReportMsgTimeRequest
import com.twl.hi.foundation.model.message.ChatMessage
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.callback.ApiRequestCallback
import com.twl.http.client.HttpResponse
import com.twl.http.error.ErrorReason
import com.twl.mms.ServerResponse

/**
 * <AUTHOR>
 * @desc   IM消息链路监控上报
 *
 * IM消息监控文档说明
 *
 * https://zhishu.zhipin.com/preview?id=Bej5lSOEMSj
 */
object MessageMonitor {

    /**
     * 对发送成功对消息，进行数据上报
     */
    fun reportMessageSend(messageWrapper: MessageWrapper) {
        reportPreSendMessage(messageWrapper)
        reportAfterSendMessage(messageWrapper)
    }

    /**
     * 上传端侧消息生成处理耗时
     */
    private fun reportPreSendMessage(messageWrapper: MessageWrapper) {
        val chatMessage = messageWrapper.chatMessage
        val request = ReportMsgTimeRequest(object : ApiRequestCallback<HttpResponse>() {
            override fun onSuccess(data: ApiData<HttpResponse>?) {

            }
            override fun onComplete() {
            }
            override fun onFailed(reason: ErrorReason?) {
            }
        })
        request.cmid = chatMessage.cmid.toString()
        request.msgId = messageWrapper.serverResponse.serverId
        request.chatType = chatMessage.type
        request.chatId = chatMessage.chatId
        request.phase = "clientPreProcess"
        request.retry = 0
        request.operateTime = System.currentTimeMillis()
        request.executeTime = (messageWrapper.recordTime - messageWrapper.chatMessage.cmid).toInt()
        HttpExecutor.execute(request)
    }

    /**
     * 上传端侧上报耗时
     */
    private fun reportAfterSendMessage(messageWrapper: MessageWrapper) {
        val chatMessage = messageWrapper.chatMessage
        val current = System.currentTimeMillis()
        val request = ReportMsgTimeRequest(object : ApiRequestCallback<HttpResponse>() {
            override fun onSuccess(data: ApiData<HttpResponse>?) {

            }
            override fun onComplete() {
            }
            override fun onFailed(reason: ErrorReason?) {
            }
        })
        request.cmid = chatMessage.cmid.toString()
        request.msgId = messageWrapper.serverResponse.serverId
        request.chatType = chatMessage.type
        request.chatId = chatMessage.chatId
        request.phase = "uplink"
        request.retry = messageWrapper.serverResponse.reTryCount
        request.operateTime = current
        request.executeTime = ((current - messageWrapper.recordTime) / 2).toInt()
        HttpExecutor.execute(request)
    }

    /**
     * 上传端侧消息保存耗时
     */
    fun reportAfterReceiveMessage(
        startTime: Long,
        chatMessage: ChatMessage
    ) {
        val current = System.currentTimeMillis()
        val request = ReportMsgTimeRequest(object : ApiRequestCallback<HttpResponse>() {
            override fun onSuccess(data: ApiData<HttpResponse>?) {

            }
            override fun onComplete() {
            }
            override fun onFailed(reason: ErrorReason?) {
            }
        })
        request.cmid = chatMessage.cmid.toString()
        request.msgId = chatMessage.mid
        request.chatType = chatMessage.type
        request.chatId = chatMessage.chatId
        request.phase = "clientPostProcess"
        request.retry = 0
        request.operateTime = current
        request.executeTime = (current - startTime).toInt()
        HttpExecutor.execute(request)
    }

}

data class MessageWrapper(val chatMessage: ChatMessage, val serverResponse: ServerResponse, val recordTime : Long)