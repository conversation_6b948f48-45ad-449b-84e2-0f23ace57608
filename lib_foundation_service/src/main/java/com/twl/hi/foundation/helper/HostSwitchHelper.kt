package com.twl.hi.foundation.helper

import com.twl.hi.foundation.utils.PointUtils
import com.twl.http.httpswitch.HttpSwitchManager
import com.twl.utils.GsonUtils

/**
 * <AUTHOR>
 * @date 2024/1/29
 * description:
 */
object HostSwitchHelper {
    const val REASON_SWITCH_LOGIN = "1"
    const val REASON_SWITCH_CHECK = "2"
    const val REASON_SWITCH_DATA_SYNC = "3"
    const val REASON_SWITCH_MQTT = "4"


    /**
     * 参数具体意义见数星后台
     * https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/152?showdetail=307280
     */
    @JvmOverloads
    @JvmStatic
    fun pointHostSwitch(
        reason: String,
        oldDomain: String,
        newDomain: String,
        strategy: HttpSwitchManager.Strategy? = null,
        reasonDesc: HttpSwitchManager.Strategy? = null
    ) {
        val params = PointUtils.BuilderV4()
            .name("bosshi-domain-change")
            .params("reason", reason)
            .params("oldDomain", oldDomain)
            .params("newDomain", newDomain)
        strategy?.run {
            params.params("strategy", GsonUtils.getGson().toJson(this))
        }
        reasonDesc?.run {
            params.params("reasonDesc", GsonUtils.getGson().toJson(this))
        }
        params.point()
    }
}