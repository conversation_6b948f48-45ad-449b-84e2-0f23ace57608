package com.twl.hi.foundation.model.emotion;

import java.util.ArrayList;
import java.util.List;

/**
 * 表情类别数据模块
 * <p>
 * Created by jia on 2018/6/12.
 */
public class EmotionCategory {

    private long id = 0;             //最近，内置黄脸，自定义，内置GIF 四种，可以用0 1 2 3标识
    private String name = "";       //最近，内置黄脸，自定义，内置GIF =>可以和type共用一个属性，删掉type或者name
    private String type = "";       //最近，内置黄脸，自定义，内置GIF
    private int icon = 0;           //目前都是种类图标都是drawable ID
    private String iconUrl = "";   //以后可能需要扩展都是种类图标都是drawable URL
    private String desc;
    private int status;
    private int spanX;
    private List<EmotionItem> itemList;

    public EmotionCategory() {
        itemList = new ArrayList<>();
    }

    @Override
    public String toString() {
        return "EmotionCategory{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", icon=" + icon +
                ", iconUrl='" + iconUrl + '\'' +
                ", desc='" + desc + '\'' +
                ", status=" + status +
                ", spanX=" + spanX +
                ", itemList=" + itemList.size() +
                '}';
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<EmotionItem> getEmotionItemList() {
        return itemList;
    }

    public void setEmotionItemList(List<EmotionItem> itemList) {
        this.itemList = itemList;
    }

    public int getSpanX() {
        return spanX;
    }

    public void setSpanX(int spanX) {
        this.spanX = spanX;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getIcon() {
        return icon;
    }

    public void setIcon(int icon) {
        this.icon = icon;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        if (iconUrl == null) {
            return;
        }
        this.iconUrl = iconUrl;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    /**
     * 业务层Bean转化为数据库Bean对象保存
     */
    public ServerEmotionCategoryBean toServerEmotionBean() {
        ServerEmotionCategoryBean serverEmotionCategoryBean = new ServerEmotionCategoryBean();
        serverEmotionCategoryBean.packId = getId();
        serverEmotionCategoryBean.name = getName();
        serverEmotionCategoryBean.description = getDesc();
        serverEmotionCategoryBean.tinyUrl = getIconUrl();
        List<EmotionItem> bossEmotionItemList = getEmotionItemList();
        if (bossEmotionItemList != null) {
            serverEmotionCategoryBean.items = new ArrayList<>();
            for (EmotionItem item : bossEmotionItemList) {
                if (item == null) continue;
                serverEmotionCategoryBean.items.add(item.transferToServerBean());
            }
        }
        return serverEmotionCategoryBean;
    }
}
