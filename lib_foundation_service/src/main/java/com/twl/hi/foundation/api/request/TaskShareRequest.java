package com.twl.hi.foundation.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

public class TaskShareRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String singleChatIds;
    @Expose
    public String groupChatIds;
    @Expose
    public String taskId;
    @Expose
    public String msgId;
    @Expose
    public String message;
    @Expose
    public String shiningId;
    @Override
    public String getUrl() {
        return URLConfig.URL_TASK_SHARE;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
