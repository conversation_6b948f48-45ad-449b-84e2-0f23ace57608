package com.twl.hi.foundation.facade;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.request.slashcmd.SlashCommandSyncAllRequest;
import com.twl.hi.foundation.api.request.slashcmd.SlashCommandSyncPartRequest;
import com.twl.hi.foundation.api.response.SlashCommandSyncAllResponse;
import com.twl.hi.foundation.api.response.SlashCommandSyncPartResponse;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import lib.twl.common.base.BaseApplication;
import lib.twl.common.util.LList;
import lib.twl.common.util.ProcessHelper;

/**
 * 指令同步处理器
 * <p>
 * Created by tanshi<PERSON> on 2023/7/3
 */
public class SlashCommandSyncHandler extends BaseHandler<SlashCommandSyncAllResponse> {

    public static final String KEY_FINISH_SYNC_ALL = "slash_command_has_sync_all";

    private static final String TAG = "SlashCommandSyncHandler";

    private static final int TYPE_SYNC_BY_APP_ID = 1;
    private static final int TYPE_SYNC_BY_COMMAND_ID = 2;

    private boolean mHasMore;
    private long mVersion;

    public SlashCommandSyncHandler() {
    }

    @Override
    protected void syncData() {
        SlashCommandSyncAllRequest slashCommandSyncAllRequest = new SlashCommandSyncAllRequest(this);
        slashCommandSyncAllRequest.version = mVersion;
        HttpExecutor.execute(slashCommandSyncAllRequest);
        TLog.info(TAG, "SlashCommandSyncHandler -> syncAll start " + mVersion);
    }

    public void handleSyncAll() {
        boolean hasSyncAll = ProcessHelper.getUserCompanyPreferences().getBoolean(KEY_FINISH_SYNC_ALL, false);
        if (!hasSyncAll) {
            syncAll();
        }
    }

    public void syncAll() {
        mVersion = 0;
        startUpdate();
    }

    public void syncPart(int type, String appId, String cmdId) {
        SlashCommandSyncPartRequest slashCommandSyncPartRequest = new SlashCommandSyncPartRequest(new BaseApiRequestCallback<SlashCommandSyncPartResponse>() {

            @Override
            public void handleInChildThread(ApiData<SlashCommandSyncPartResponse> data) {
                super.handleInChildThread(data);
                if (data != null && data.resp != null) {
                    TLog.info(TAG, "SlashCommandSyncHandler -> syncPart success,appId:" + appId + " cmdId:" + cmdId + "size:" + data.resp.data.size());
                    if (type == TYPE_SYNC_BY_APP_ID) {
                        ServiceManager.getInstance().getSlashCommandService().deleteAllCommandsByAppId(appId);
                    }
                    ServiceManager.getInstance().getSlashCommandService().insertCommands(data.resp.data);
                }
            }

            @Override
            public void onSuccess(ApiData<SlashCommandSyncPartResponse> data) {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        if (type == TYPE_SYNC_BY_APP_ID) {
            slashCommandSyncPartRequest.appId = appId;
        } else if (type == TYPE_SYNC_BY_COMMAND_ID) {
            slashCommandSyncPartRequest.commandId = cmdId;
        }
        HttpExecutor.execute(slashCommandSyncPartRequest);
    }

    @Override
    public void handleInChildThread(ApiData<SlashCommandSyncAllResponse> data) {
        super.handleInChildThread(data);
        if (mVersion == 0) {
            ServiceManager.getInstance().getSlashCommandService().deleteAllCommands();
        }
        mVersion = data.resp.nextVersion;
        mHasMore = data.resp.hasMore;
        if (LList.isNotEmpty(data.resp.functionCommandList)) {
            ServiceManager.getInstance().getSlashCommandService().insertCommands(data.resp.functionCommandList);
        }
        if (LList.isNotEmpty(data.resp.operateCommandList)) {
            ServiceManager.getInstance().getSlashCommandService().insertCommands(data.resp.operateCommandList);
        }
        if (!mHasMore) {
            ProcessHelper.getUserCompanyPreferences().putBoolean(KEY_FINISH_SYNC_ALL, true);
            TLog.info(TAG, "SlashCommandSyncHandler <- syncAll data,finish");
        }
    }

    @Override
    public void onCompleteInWorkThread() {
        super.onCompleteInWorkThread();
        if (mHasMore) {
            startUpdate();
        }
    }

    @Override
    public void handleErrorInChildThread(ErrorReason reason) {
        super.handleErrorInChildThread(reason);
        TLog.error(TAG, reason.getErrReason());
        mHasMore = false;
    }

    @Override
    public void showFailed(ErrorReason reason) {
    }
}
