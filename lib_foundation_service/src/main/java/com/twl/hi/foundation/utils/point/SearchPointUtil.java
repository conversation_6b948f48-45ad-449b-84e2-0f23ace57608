package com.twl.hi.foundation.utils.point;

import static androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_IDLE;

import android.text.TextUtils;
import android.util.Range;
import android.view.ViewTreeObserver;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.utils.PointUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import lib.twl.common.util.SearchAdapterInterface;
import lib.twl.common.util.SearchResultInterface;

/**
 * 搜索结果曝光埋点工具类
 */

public class SearchPointUtil {
    private static final String TAG = "SearchPointUtil";
    private ViewTreeObserver.OnGlobalLayoutListener mOnGlobalLayoutListener;
    private String mSearchId;
    private RecyclerView.OnScrollListener mOnScrollListener;
    private boolean mEnable = true;

    /**
     * 给需要曝光的recyclerview item追加相关的listener
     * @param recyclerView 待曝光的recyclerview
     * @param adapterImpl 对应的adapter
     */
    public void setExposeListener(RecyclerView recyclerView, SearchAdapterInterface adapterImpl) {
        if (adapterImpl == null) {
            return;
        }
        adapterImpl.setEnableOnAttachStateChangeListener(true);

        if (mOnScrollListener == null) {
            mOnScrollListener = new RecyclerView.OnScrollListener() {
                @Override
                public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                    super.onScrollStateChanged(recyclerView, newState);
                    if (newState == SCROLL_STATE_IDLE) {
                        exposePoint(adapterImpl);
                    }
                }
            };
            recyclerView.addOnScrollListener(mOnScrollListener);
        }

        if (mOnGlobalLayoutListener == null) {
            mOnGlobalLayoutListener = new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    exposePoint(adapterImpl);
                }
            };
            recyclerView.getViewTreeObserver().addOnGlobalLayoutListener(mOnGlobalLayoutListener);
        }
    }

    public void updateSearchId(String searchId) {
        mSearchId = searchId;
    }

    public String getSearchId() {
        return mSearchId;
    }

    public void exposeInstantly(RecyclerView rv) {
        RecyclerView.Adapter adapter = rv.getAdapter();
        if (!(adapter instanceof SearchAdapterInterface)) {
            TLog.error(TAG, "错误的曝光适配器");
            return;
        }
        SearchAdapterInterface<?> exposeAdapter = (SearchAdapterInterface<?>) adapter;
        List<?> itemData = exposeAdapter.getData();
        if (mEnable && itemData != null && itemData.size() > 0) {
            Range<Integer> showingRange = getShowingRange(rv);
            if (showingRange.getLower() < 0 || showingRange.getUpper() >= itemData.size()) {
                //防止数组越界
                return;
            }

            int index = 0;
            List<? super Map<?,?>> paramGroup = new ArrayList<>();
            for (int i = showingRange.getLower(); i <= showingRange.getUpper(); i++) {
                Object object = itemData.get(i);
                if (object instanceof SearchResultInterface) {
                    index++;
                    SearchResultInterface searchResultInterface = (SearchResultInterface)object;
                    Map<String, String> params = new HashMap<>();
                    String pointId = searchResultInterface.getPointId();
                    if (TextUtils.isEmpty(pointId)) {
                        // 不符合规范的item跳过不曝光，比如全部搜索tab未输入query时，联系人分组的折叠按钮item
                        continue;
                    }
                    params.put("id", pointId);
                    params.put("rank", String.valueOf(index));
                    params.put("cnt", "1");
                    params.put("info", searchResultInterface.getPointId());
                    String level = TextUtils.isEmpty(searchResultInterface.getPointLevel()) ? searchResultInterface.getPointLevel() : searchResultInterface.getPointLevel();
                    params.put("level", exposeAdapter.isAllTab() ? "全部_" + level : "" + level);
                    if (exposeAdapter.isAllTabWithOutQuery()) {
                        params.put("tab", "全部");
                    }
                    paramGroup.add(params);
                }
            }
            new PointUtils.BuilderV4()
                    .name("search-result-expose")
                    .params("content", paramGroup)
                    .params("search_id", mSearchId)
                    .point();
            HashMap<Integer, Integer> exposedItems = exposeAdapter.getExposedItems();
            if (exposedItems != null) {
                exposedItems.clear();
            }
        }
    }

    private Range<Integer> getShowingRange(RecyclerView rv) {
        RecyclerView.LayoutManager layoutManager = rv.getLayoutManager();
        if (layoutManager instanceof GridLayoutManager) {
            return new Range<>(
                    ((GridLayoutManager) layoutManager).findFirstCompletelyVisibleItemPosition(),
                    ((GridLayoutManager) layoutManager).findLastCompletelyVisibleItemPosition()
            );
        } else if (layoutManager instanceof LinearLayoutManager) {
            return new Range<>(
                    ((LinearLayoutManager) layoutManager).findFirstCompletelyVisibleItemPosition(),
                    ((LinearLayoutManager) layoutManager).findLastCompletelyVisibleItemPosition()
            );
        } else if (layoutManager instanceof StaggeredGridLayoutManager) {
            StaggeredGridLayoutManager staggeredGridLayoutManager = (StaggeredGridLayoutManager) layoutManager;
            return new Range<>(
                    staggeredGridLayoutManager.findFirstVisibleItemPositions(null)[staggeredGridLayoutManager.getSpanCount()],
                    staggeredGridLayoutManager.findLastVisibleItemPositions(null)[staggeredGridLayoutManager.getSpanCount()]
            );
        } else if (layoutManager instanceof LayoutManagerPositionCallback) {
            LayoutManagerPositionCallback callback = (LayoutManagerPositionCallback) layoutManager;
            return new Range<>(callback.findFirstCompletelyVisibleItemPosition(), callback.findLastCompletelyVisibleItemPosition());
        }
        return new Range<>(0, 0);
    }

    /**
     * 待曝光的数据转化成json上报埋点
     * @param adapterImpl
     * @return
     */
    private List<?> convertJson(SearchAdapterInterface adapterImpl) {
        List<Object> datas = adapterImpl.getData();
        HashMap<Integer, Integer> items = adapterImpl.getExposedItems();
        Iterator<Integer> iterator = items.keySet().iterator();
        List<? super Map<?,?>> paramGroup = new ArrayList<>();
        int index = 0;
        while (iterator.hasNext()) {
            Integer pos = iterator.next();
            Object object = datas.get(pos);
            if (object instanceof SearchResultInterface) {
                index++;
                SearchResultInterface searchResultInterface = (SearchResultInterface)object;
                Integer cnt = items.get(pos);
                Map<String, String> params = new HashMap<>();
                String pointId = searchResultInterface.getPointId();
                if (TextUtils.isEmpty(pointId)) {
                    // 不符合规范的item跳过不曝光，比如全部搜索tab未输入query时，联系人分组的折叠按钮item
                    continue;
                }
                params.put("id", pointId);
                params.put("rank", String.valueOf(index));
                params.put("cnt", String.valueOf(cnt));
                params.put("info", searchResultInterface.getPointId());
                String level = TextUtils.isEmpty(adapterImpl.getPointLevel()) ? searchResultInterface.getPointLevel() : adapterImpl.getPointLevel();
                params.put("level", adapterImpl.isAllTab() ? "全部_" + level : "" + level);
                if (adapterImpl.isAllTabWithOutQuery()) {
                    params.put("tab", "全部");
                }
                paramGroup.add(params);
                // 单独曝光搜索卡片埋点
                exposeSearchCard(searchResultInterface);
            }
        }
        return paramGroup;
    }

    /**
     * 对外提供单独的曝光接口给外部调用，当OnScrollListener和OnGlobalLayoutListener没有触发却又需要曝光时
     * 可以考虑使用这个接口
     * @param adapterImpl
     */
    public void exposePoint(SearchAdapterInterface adapterImpl) {
        if (mEnable && adapterImpl != null && adapterImpl.getExposedItems() != null && adapterImpl.getData() != null) {
            int exposeSize = adapterImpl.getExposedItems().size();
            int adapterSize = adapterImpl.getData().size();
            if (exposeSize > 0 && adapterSize > 0 && adapterSize >= exposeSize) {
                HashMap<Integer, Integer> items = adapterImpl.getExposedItems();
                Iterator<Integer> iterator = items.keySet().iterator();
                while (iterator.hasNext()) {
                    // 防止数组越界
                    Integer pos = iterator.next();
                    if (pos >= adapterSize) {
                        return;
                    }
                }
                new PointUtils.BuilderV4()
                        .name("search-result-expose")
                        .params("content", convertJson(adapterImpl))
                        .params("search_id", mSearchId)
                        .point();
                HashMap<Integer, Integer> exposedItems = adapterImpl.getExposedItems();
                if (exposedItems != null) {
                    exposedItems.clear();
                }
            }
        }
    }

    /**
     * 搜索卡片曝光
     * @param object
     */
    private void exposeSearchCard(SearchResultInterface object) {
        if (((SearchResultInterface) object).getPointId().equals("SearchCard")) {
            new PointUtils.BuilderV4()
                    .name("search-card-expose")
                    .params("search_id", mSearchId)
                    .point();
        }
    }

    public void setExposeEnable(boolean enable) {
        mEnable = enable;
    }


    public interface LayoutManagerPositionCallback {

        int findFirstCompletelyVisibleItemPosition();
        int findLastCompletelyVisibleItemPosition();
    }

}
