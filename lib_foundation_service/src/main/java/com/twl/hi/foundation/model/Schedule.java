package com.twl.hi.foundation.model;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.twl.hi.foundation.converter.ScheduleConverters;
import com.twl.hi.foundation.utils.ScheduleUtils;

import java.io.Serializable;
import java.util.Calendar;
import java.util.List;

import lib.twl.common.util.LList;
import lib_twl_db_config.DBConstants;

@Entity(tableName = DBConstants.TAB_SCHEDULE)
@TypeConverters(ScheduleConverters.class)
public class Schedule implements Serializable {
    private static final long serialVersionUID = 5648087425346687547L;
    @PrimaryKey
    @NonNull
    private String scheduleId = "";//日程&待办id
    private String creatorId;//创建者用户id
    private String content;//标题
    private int contentEmpty;// 标题是否为空 0-否 1-是
    private String targetDate;//日期
    private int type = 1;//类型：1-日程，2-待办
    private int status;//状态：0-未完成，1-完成
    private int deleted;// 是否已删除，0-未删除，1-删除
    private String beginTime;// 开始时间
    private String endTime;//结束时间
    private int markType;// 标识类型 0-无优先级 1-重要 2-紧急
    private long createTime;// 创建时间
    private long finishTime;// 完成时间

    /**
     * 仅保存接受拒绝的人员
     */
    @Ignore
    private List<ScheduleFriend> friends;
    /**
     * 参与者 复合群和个人
     */
    @Ignore
    private List<ScheduleParticipant> partners;
    ScheduleMeeting meetingInfo;
    ScheduleLastOpeLog lastOpeLog;

    /**
     * repeatType 重复类型，0-不重复，1-每天，2-每周，3-每月，4-每年
     * "frequent": 2, // 重复频率，如repeatType为2时，frequent为2表示每两周
     * "repeatDays": "1,2,3,4,5", // 指定重复时间，英文逗号分隔。每周时，表示星期几；每月时表示几号；
     * "repeatEnd": "2019-07-30", // 日程重复截止日期
     * "alertMins": "0,15", // 提醒时间，-1: 不提醒，0: 实时提醒，5: 五分钟前，7200: 一天前，以分钟计数，默认为15
     * "parentId": 0, // 分离自的父日程id，0表示无。有该id时不能创建重复日程
     * "separateList": [{ // 重复日程中单独编辑分离的日程
     * "target_date": "2019-07-10", // 日期
     * "scheduleId": 1000234 // 日程id，本地删除重复日程时可根据该id同步删除分离出的日程
     * }],
     */
    private int repeatType;
    private int frequent;
    private String repeatDays;
    private String repeatEnd;
    private String alertMins;
    private String parentId;
    private List<ScheduleSeparate> separateList;

    private List<ScheduleFile> files;//附件
    @ColumnInfo(name = "schedule_desc")
    private String desc;//描述

    @Ignore
    private boolean lastSchedule;// 标记是否是当天最后一个日程
    @Ignore
    private boolean firstSchedule;// 标记是否是当天第一个日程
    @Ignore
    private boolean isAllDay;//是否是全天的日程
    @Ignore
    private Calendar beginCalendar;// 开始时间
    @Ignore
    private Calendar endCalendar;//结束时间
    @Ignore
    private String groupName;//项目组日程的项目组名称
    @Ignore
    private int echoType;//对于我自己 日程响应类型

    private int partnerModify;//是否允许参与人修改日程，1-允许   0-不允许
    private int partnerShare;// 是否允许参与者分享日程 1-是 0-否

    private ScheduleLocation location;

    private int friendNum;

    private String calenderGroupId;//日历Id

    private String scheduleCreateGroupId;//日程群Id

    private int totalFriendCount; //去重日程总人数(无论用户否选择参与)
    /**
     * "linkMeetingType": 1 ,// 默认 0 ，1- bosshi 视频会议 ，2- 三方视频会议
     * "linkMeetingTag":"BossHi音视频会议",
     * "linkMeetingUri":"sdffxxxxaa", //  可空 bosshi 视频会议 为对应 roomId，三方会议为链接地址
     * "linkMeetingStatus": 1 , // 0 不可加入 1 可加入
     */
    private int linkMeetingType;
    private String linkMeetingTag;
    private String linkMeetingUri;
    private int linkMeetingStatus;

    private boolean canShare;  //是否可分享
    private boolean canEdit;  //是否可编辑
    private boolean canDelete; //是否可删除

    private boolean showCreator = true;// 是否展示创建者
    private boolean showPartner = true; // 是否展示参与者
    private boolean showButton = true; // 是否展示按钮

    private boolean showLinkMeeting = true; // 是否展示视频会议入口

    @Ignore
    private int isManager; // 是否有共享日历的管理权限，是则具有完全编辑权限

    @Ignore
    private boolean changed;

    public Schedule() {
    }

    public boolean isChanged() {
        return changed;
    }

    public void setChanged(boolean changed) {
        this.changed = changed;
    }

    /**
     * 是否有效日程
     *
     * @return 既未删除也未更改
     */
    public boolean valid() {
        return deleted == 0 && !changed;
    }

    public int getLinkMeetingType() {
        return linkMeetingType;
    }

    public void setLinkMeetingType(int linkMeetingType) {
        this.linkMeetingType = linkMeetingType;
    }

    public String getLinkMeetingTag() {
        return linkMeetingTag;
    }

    public void setLinkMeetingTag(String linkMeetingTag) {
        this.linkMeetingTag = linkMeetingTag;
    }

    public String getLinkMeetingUri() {
        return linkMeetingUri;
    }

    public void setLinkMeetingUri(String linkMeetingUri) {
        this.linkMeetingUri = linkMeetingUri;
    }

    public int getLinkMeetingStatus() {
        return linkMeetingStatus;
    }

    public void setLinkMeetingStatus(int linkMeetingStatus) {
        this.linkMeetingStatus = linkMeetingStatus;
    }

    public ScheduleLocation getLocation() {
        return location;
    }

    public void setLocation(ScheduleLocation location) {
        this.location = location;
    }

    public int getPartnerModify() {
        return partnerModify;
    }

    public void setPartnerModify(int partnerModify) {
        this.partnerModify = partnerModify;
    }

    public int getEchoType() {
        return echoType;
    }

    public void setEchoType(int echoType) {
        this.echoType = echoType;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public int getRepeatType() {
        return repeatType;
    }

    public void setRepeatType(int repeatType) {
        this.repeatType = repeatType;
    }

    public int getFrequent() {
        return frequent;
    }

    public void setFrequent(int frequent) {
        this.frequent = frequent;
    }

    public String getRepeatDays() {
        return repeatDays;
    }

    public void setRepeatDays(String repeatDays) {
        this.repeatDays = repeatDays;
    }

    public String getRepeatEnd() {
        return repeatEnd;
    }

    public void setRepeatEnd(String repeatEnd) {
        this.repeatEnd = repeatEnd;
    }

    public String getAlertMins() {
        return alertMins;
    }

    public void setAlertMins(String alertMins) {
        this.alertMins = alertMins;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public List<ScheduleSeparate> getSeparateList() {
        return separateList;
    }

    public void setSeparateList(List<ScheduleSeparate> separateList) {
        this.separateList = separateList;
    }

    public Calendar getBeginCalendar() {
        return beginCalendar;
    }

    public void setBeginCalendar(Calendar beginCalendar) {
        this.beginCalendar = beginCalendar;
    }

    public Calendar getEndCalendar() {
        return endCalendar;
    }

    public void setEndCalendar(Calendar endCalendar) {
        this.endCalendar = endCalendar;
    }

    public boolean isFirstSchedule() {
        return firstSchedule;
    }

    public void setFirstSchedule(boolean firstSchedule) {
        this.firstSchedule = firstSchedule;
    }

    public boolean isLastSchedule() {
        return lastSchedule;
    }

    public void setLastSchedule(boolean lastSchedule) {
        this.lastSchedule = lastSchedule;
    }

    public String getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(String scheduleId) {
        this.scheduleId = scheduleId;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public int getMarkType() {
        return markType;
    }

    public void setMarkType(int markType) {
        this.markType = markType;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public List<ScheduleFriend> getFriends() {
        return friends;
    }

    public void setFriends(List<ScheduleFriend> friends) {
        this.friends = friends;
    }

    public List<ScheduleParticipant> getPartners() {
        return partners;
    }

    public void setPartners(List<ScheduleParticipant> partners) {
        this.partners = partners;
    }

    public String getTargetDate() {
        return targetDate;
    }

    public void setTargetDate(String targetDate) {
        this.targetDate = targetDate;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public ScheduleMeeting getMeetingInfo() {
        return meetingInfo;
    }

    public String getRoomId() {
        if (meetingInfo == null) {
            return "";
        }
        return meetingInfo.getRoomId();
    }

    public String getBookingId() {
        if (meetingInfo == null) {
            return "";
        }
        return meetingInfo.getBookingId();
    }

    public void setMeetingInfo(ScheduleMeeting meetingInfo) {
        this.meetingInfo = meetingInfo;
    }

    public boolean isAllDay() {
        if (isAllDay) {
            return true;
        }
        return TextUtils.equals(beginTime, ScheduleUtils.DAY_BEGIN_TIME) && TextUtils.equals(endTime, ScheduleUtils.DAY_END_TIME);
    }

    public void setAllDay(boolean allDay) {
        isAllDay = allDay;
    }

    public ScheduleLastOpeLog getLastOpeLog() {
        return lastOpeLog;
    }

    public void setLastOpeLog(ScheduleLastOpeLog lastOpeLog) {
        this.lastOpeLog = lastOpeLog;
    }

    public int getDeleted() {
        return deleted;
    }

    public void setDeleted(int deleted) {
        this.deleted = deleted;
    }

    public int getFriendNum() {
        return friendNum;
    }

    public void setFriendNum(int friendNum) {
        this.friendNum = friendNum;
    }

    public List<ScheduleFile> getFiles() {
        return files;
    }

    public void setFiles(List<ScheduleFile> files) {
        this.files = files;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCalenderGroupId() {
        return calenderGroupId;
    }

    public void setCalenderGroupId(String calenderGroupId) {
        this.calenderGroupId = calenderGroupId;
    }

    public void addFiles(List<ScheduleFile> files) {
        if (!LList.isEmpty(this.files)) {
            this.files.addAll(files);
        }
    }

    public void addFile(ScheduleFile file) {
        if (!LList.isEmpty(this.files)) {
            this.files.add(file);
        }
    }

    public boolean isFinished() {
        return System.currentTimeMillis() > getFinishTime();
    }

    public boolean isCanShare() {
        return canShare;
    }

    public void setCanShare(boolean canShare) {
        this.canShare = canShare;
    }

    public boolean isCanEdit() {
        return canEdit;
    }

    public void setCanEdit(boolean canEdit) {
        this.canEdit = canEdit;
    }

    public boolean isCanDelete() {
        return canDelete;
    }

    public void setCanDelete(boolean canDelete) {
        this.canDelete = canDelete;
    }

    public int getPartnerShare() {
        return partnerShare;
    }

    public void setPartnerShare(int partnerShare) {
        this.partnerShare = partnerShare;
    }

    public int getContentEmpty() {
        return contentEmpty;
    }

    public void setContentEmpty(int contentEmpty) {
        this.contentEmpty = contentEmpty;
    }

    public boolean isShowCreator() {
        return showCreator;
    }

    public void setShowCreator(boolean showCreator) {
        this.showCreator = showCreator;
    }

    public boolean isShowPartner() {
        return showPartner;
    }

    public void setShowPartner(boolean showPartner) {
        this.showPartner = showPartner;
    }

    public boolean isShowButton() {
        return showButton;
    }

    public void setShowButton(boolean showButton) {
        this.showButton = showButton;
    }

    public boolean isShowLinkMeeting() {
        return showLinkMeeting;
    }

    public void setShowLinkMeeting(boolean showLinkMeeting) {
        this.showLinkMeeting = showLinkMeeting;
    }

    public int isManager() {
        return isManager;
    }

    public void setManager(int manager) {
        isManager = manager;
    }

    @Override
    public String toString() {
        return "Schedule{" +
                "scheduleId=" + scheduleId +
                ", creatorId=" + creatorId +
                ", content='" + content + '\'' +
                ", contentEmpty=" + contentEmpty +
                ", targetDate='" + targetDate + '\'' +
                ", type=" + type +
                ", status=" + status +
                ", deleted=" + deleted +
                ", beginTime='" + beginTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", markType=" + markType +
                ", createTime=" + createTime +
                ", finishTime=" + finishTime +
                ", friends=" + friends +
                ", partners=" + partners +
                ", meetingInfo=" + meetingInfo +
                ", lastOpeLog=" + lastOpeLog +
                ", repeatType=" + repeatType +
                ", frequent=" + frequent +
                ", repeatDays='" + repeatDays + '\'' +
                ", repeatEnd='" + repeatEnd + '\'' +
                ", alertMins='" + alertMins + '\'' +
                ", parentId=" + parentId +
                ", separateList=" + separateList +
                ", files=" + files +
                ", desc='" + desc + '\'' +
                ", lastSchedule=" + lastSchedule +
                ", firstSchedule=" + firstSchedule +
                ", isAllDay=" + isAllDay +
                ", beginCalendar=" + beginCalendar +
                ", endCalendar=" + endCalendar +
                ", groupName='" + groupName + '\'' +
                ", echoType=" + echoType +
                ", partnerModify=" + partnerModify +
                ", partnerShare=" + partnerShare +
                ", location=" + location +
                ", friendNum=" + friendNum +
                ", calenderGroupId='" + calenderGroupId + '\'' +
                ", linkMeetingType=" + linkMeetingType +
                ", linkMeetingTag='" + linkMeetingTag + '\'' +
                ", linkMeetingUri='" + linkMeetingUri + '\'' +
                ", linkMeetingStatus=" + linkMeetingStatus +
                '}';
    }

    public String getScheduleCreateGroupId() {
        return scheduleCreateGroupId;
    }

    public void setScheduleCreateGroupId(String scheduleCreateGroupId) {
        this.scheduleCreateGroupId = scheduleCreateGroupId;
    }

    public int getTotalFriendCount() {
        return totalFriendCount;
    }

    public void setTotalFriendCount(int totalFriendCount) {
        this.totalFriendCount = totalFriendCount;
    }
}
