package com.twl.hi.foundation.model.message

import com.twl.hi.foundation.R
import com.twl.hi.foundation.model.AtMethod
import com.twl.hi.foundation.model.AtProperty
import hi.kernel.Constants
import hi.kernel.HiKernel
import lib.twl.common.util.LText

/**
 * At消息聚合类
 * 所有包含At消息的类均派生自这个类
 */
abstract class ChatMessageWithAt : ChatMessage(), AtProperty {

    // replyBar，已处理时不用展示
    var atStatusProcessed = false

    fun updateAtStatusProcessed() {
        // 没有@的普通消息or单聊不用展示
        if (type == MessageConstants.MSG_SINGLE_CHAT || atMeMethod == AtMethod.AT_NONE) {
            atStatusProcessed = true
            return
        }
        val isExpired = expiredTime <= 0
        if (isExpired) {
            atStatusProcessed = true
            return
        }
        if (HiKernel.getHikernel().account.userId in replyUserIds) {
            atStatusProcessed = true
            return
        }
        if (emojiReplies.any { (emojiCode, users) ->
                emojiCode == Constants.EMOTION_ID_OK && HiKernel.getHikernel().account.userId in users
            }
        ) {
            atStatusProcessed = true
            return
        }
    }

    val atQuickReplyText: String
        get() = LText.getString(R.string.chat_received_with_emoji_got)

    val expiredTime: Long
        get() = REPLY_BAR_SHOW_TIME_LIMIT - (System.currentTimeMillis() - time)

    companion object {
        private const val REPLY_BAR_SHOW_TIME_LIMIT = 2L * 60 * 60 * 1000
    }
}