package com.twl.hi.foundation.api.request

import com.google.gson.annotations.Expose
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.hi.foundation.api.response.ContactDeptVisibleDataResponse
import com.twl.http.HttpUtils
import com.twl.http.client.BaseApiRequest
import com.twl.http.config.RequestMethod
import okhttp3.MediaType.Companion.toMediaTypeOrNull

/**
 * <AUTHOR>
 * @Date   2023/11/28 11:08 AM
 * @Desc   别删这行注释啊，删了程序就崩了
 */
class ContactAndDeptVisibleDataRequest(callback: BaseApiRequestCallback<ContactDeptVisibleDataResponse>): BaseApiRequest<ContactDeptVisibleDataResponse>(callback) {

    @Expose
    @JvmField
    var version: Long = 0L

    @Expose
    @JvmField
    var subVersion: Long = 0L
    override fun getUrl() = URLConfig.URL_CONTACT_DEPTMENT_VISIABLE_INFO

    override fun getMethod() = RequestMethod.POST

    override fun getMediaType() = HttpUtils.MEDIA_TYPE_JSON.toMediaTypeOrNull()

}