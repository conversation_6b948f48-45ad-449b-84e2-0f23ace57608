package com.twl.hi.foundation.model.workbench

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import lib_twl_db_config.DBConstants

/**
 *@author: musa on 2022/12/28
 *@e-mail: yangpeng<PERSON>@kanzhun.com
 *@desc: 工作台插件分组关系表
 */
@Entity(tableName = DBConstants.TAB_WORK_PLUGIN_GROUP_RELATION,
    primaryKeys = ["relationPluginGroupId", "relationAppId"],
    indices = [Index("relationPluginGroupId"), Index("relationAppId")])
data class WorkbenchPluginGroupRelation(
    val relationPluginGroupId: String, //插件分组Id
    val relationAppId: String, //应用唯一id
    val relationSort: Int
)