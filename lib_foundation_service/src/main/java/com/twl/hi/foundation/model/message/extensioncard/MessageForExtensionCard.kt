package com.twl.hi.foundation.model.message.extensioncard

import java.io.Serializable
import com.twl.hi.foundation.model.AtMethod
import com.twl.hi.foundation.model.Hypertext
import com.twl.hi.foundation.model.message.ChatMessageWithAt
import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.hi.foundation.model.message.MessageWithHypertext
import com.twl.hi.foundation.model.message.Visitor
import com.twl.hi.foundation.model.message.extensioncard.data.CardHeader
import com.twl.hi.foundation.model.message.extensioncard.data.GlobalConfig
import com.twl.hi.foundation.model.message.extensioncard.data.modules.Module
import com.twl.utils.GsonUtils
import hi.kernel.HiKernel

/**
 * 消息卡片消息
 */
class MessageForExtensionCard : ChatMessageWithAt(), MessageWithHypertext {
    var cardInfo: ExtensionCardInfo? = null

    init {
        mediaType = MessageConstants.MSG_MESSAGE_EXTENSION_CARD
    }

    override val atMeMethod: AtMethod
        get() = if (HiKernel.getHikernel().account.userId in atIds) {
            AtMethod.AT_USER
        } else if (MessageConstants.MSG_AT_ALL in atIds) {
            AtMethod.AT_ALL
        } else {
            AtMethod.AT_NONE
        }

    override val atIds: List<String> = cardInfo?.modules?.let { modules ->
        if (modules.isEmpty()) null
        else modules.mapNotNull { it.textAtIds }.flatten()
    } ?: emptyList()

    val isOriginalMessage: Boolean
        get() = cardInfo?.originId == mid

    fun canForward(): Boolean {
        return cardInfo?.globalConfig?.isForward ?: false
    }

    fun needRefresh(): Boolean {
        return cardInfo?.modules?.any { it.needRefresh() } ?: false
    }

    fun isStreamMessage(): Boolean {
        return cardInfo?.streamStatus == 1 || cardInfo?.streamStatus == 2 ||  cardInfo?.streamStatus == 3
    }

    fun isStreaming(): Boolean {
        return cardInfo?.streamStatus == 1
    }

    fun isStreamCompleted(): Boolean {
        return cardInfo?.streamStatus == 2 ||  cardInfo?.streamStatus == 3
    }

    override fun accept(visitor: Visitor) {
        visitor.visit(this)
    }

    override val unfilteredHypertextList: List<Hypertext>
        get() = cardInfo?.richElements?: emptyList()

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as MessageForExtensionCard

        if (GsonUtils.getGson().toJson(cardInfo) != GsonUtils.getGson().toJson(other.cardInfo)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + (GsonUtils.getGson().toJson(cardInfo)?.hashCode() ?: 0)
        return result
    }

    /**
     * 消息卡片内容
     */
    class ExtensionCardInfo(
        /**
         * 原始Id，用于转发，以及判断是否是转发的消息卡片
         */
        val originId: Long,

        /**
         * 卡片标题信息
         */
        val header: CardHeader?,

        /**
         * 消息卡片点击跳转链接 http/applink 为空则不跳转
         */
        val cardLink: String?,
        /**
         * pc 端消息卡片点击跳转链接 http/applink 为空则不跳转
         */
        val pcUrl: String?,
        /**
         * android 端消息卡片点击跳转链接 http/applink 为空则不跳转
         */
        val androidUrl: String?,
        /**
         * ios 端消息卡片点击跳转链接 http/applink 为空则不跳转
         */
        val iosUrl: String?,

        /**
         * 消息卡片配置信息
         */
        val globalConfig: GlobalConfig?,

        /**
         * 消息卡片配置视图模块
         */
        val modules: List<Module>?,

        /**
         * 富元素
         */
        val richElements: List<Hypertext>?,
        /**
         * 轮询间隔,单位秒
         */
        val pollInterval:Int,
        /**
         * 流式状态，0非流式，1生成中，2 结束  3 异常，不再进行流式
         */
        var streamStatus: Int,
    ) : Serializable
}