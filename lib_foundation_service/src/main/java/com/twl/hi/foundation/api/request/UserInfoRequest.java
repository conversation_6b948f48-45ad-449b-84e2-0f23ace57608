package com.twl.hi.foundation.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.response.UserInfoResponse;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

/**
 * Author: <PERSON>
 */
public class UserInfoRequest extends BaseApiRequest<UserInfoResponse> {

    @Expose
    public String userId;
    @Expose
    public long msgId;
    @Expose
    public String groupId;


    public UserInfoRequest(BaseApiRequestCallback<UserInfoResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_USER_INFO;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
