package com.twl.hi.foundation.api.base;

public class BaseConfigParam {

    /**
     * 使用结束后清空
     */
    private String psKey = "";
    private byte[] clientData = null; // 客户端证书
    private byte[] serverData = null; // 服务端证书
    private byte[] pemData = null;

    public String getPsKey() {
        return psKey;
    }

    public void setPsKey(String psKey) {
        this.psKey = psKey;
    }

    public byte[] getClientData() {
        return clientData;
    }

    public void setClientData(byte[] clientData) {
        this.clientData = clientData;
    }

    public byte[] getServerData() {
        return serverData;
    }

    public void setServerData(byte[] serverData) {
        this.serverData = serverData;
    }

    public byte[] getPemData() {
        return pemData;
    }

    public void setPemData(byte[] pemData) {
        this.pemData = pemData;
    }
}
