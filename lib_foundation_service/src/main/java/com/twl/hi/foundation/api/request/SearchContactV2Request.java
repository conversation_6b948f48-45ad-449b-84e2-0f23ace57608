package com.twl.hi.foundation.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.response.SearchContactV2Response;
import com.twl.http.callback.ApiSyncRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

/**
 * Author : Xuweixiang .
 * Date   : On 2023/4/6
 * Email  : Contact <EMAIL>
 * Desc   : 搜索联系人 v2
 */

public class SearchContactV2Request extends BaseApiRequest<SearchContactV2Response> {

    @Expose
    public
    String content;

    @Expose
    public int page;

    @Expose
    public int size;

    @Expose
    public boolean highlight;

    @Expose
    public boolean userVisible;

    @Expose
    public boolean systemUser;

    @Expose
    public boolean departUser;

    public SearchContactV2Request(BaseApiRequestCallback<SearchContactV2Response> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_SEARCH_CONTACT_V2;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
