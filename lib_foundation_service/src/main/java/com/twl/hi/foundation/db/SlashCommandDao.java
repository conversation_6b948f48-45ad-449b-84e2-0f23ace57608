package com.twl.hi.foundation.db;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.twl.hi.foundation.model.slashcmd.SlashCommandEntity;
import com.twl.hi.foundation.model.slashcmd.SlashCommandVO;

import java.util.List;

/**
 * 斜杠指令数据库操作DAO
 * <p>
 * Created by tanshicheng on 2023/6/28
 */
@Dao
public interface SlashCommandDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertCommand(SlashCommandEntity slashCommandEntity);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertCommands(List<SlashCommandEntity> slashCommandEntities);

    @Query("SELECT * from TB_SLASH_COMMAND")
    LiveData<List<SlashCommandEntity>> queryAllCommands();

    /**
     * 查询指定机器人的所有指令
     *
     * @param robotId 机器人id
     * @return 指令列表
     */
    @Query("SELECT * FROM TB_SLASH_COMMAND " +
            "WHERE robotId = :robotId order by createTime desc")
    LiveData<List<SlashCommandVO>> queryRobotCommands(String robotId);

    /**
     * 根据关键词查询指定机器人的所有指令
     *
     * @param keyword   关键词
     * @param robotId   机器人id
     * @param robotName 机器人名称
     * @return 指令列表
     */
    @Query("SELECT * FROM TB_SLASH_COMMAND " +
            "WHERE robotId = :robotId AND status = 0 AND (instr(lower(name),:keyword)>0 OR instr(lower(`desc`),:keyword)>0 OR instr(lower(tips),:keyword)>0 OR instr(lower(:robotName),:keyword)>0) " +
            "order by createTime desc")
    LiveData<List<SlashCommandVO>> queryRobotCommandsByKeyword(String keyword, String robotId, String robotName);

    /**
     * 匹配指定群里所有机器人的指令
     *
     * @param keyword 关键词
     * @param groupId 群组id
     * @return 指令列表
     */
    @Query("SELECT c.*, r.name AS robotName,r.avatar AS robotAvatar FROM TB_SLASH_COMMAND c LEFT JOIN TB_GROUP_ROBOT r ON r.robotId = c.robotId " +
            "WHERE r.groupId = :groupId AND c.status = 0 AND r.status != 0 AND (instr(lower(c.name),:keyword)>0 OR instr(lower(c.`desc`),:keyword)>0 OR instr(lower(c.tips),:keyword)>0 OR instr(lower(robotName),:keyword)>0) " +
            "order by createTime desc")
    LiveData<List<SlashCommandVO>> queryGroupCommandsByKeyword(String keyword, String groupId);

    /**
     * 查询指定群里所有机器人的所有指令
     *
     * @param groupId 群组id
     * @return 指令列表
     */
    @Query("SELECT c.*, r.name AS robotName,r.avatar AS robotAvatar FROM TB_SLASH_COMMAND c LEFT JOIN TB_GROUP_ROBOT r ON r.robotId = c.robotId " +
            "WHERE r.groupId = :groupId AND c.status = 0 AND r.status != 0 order by createTime desc")
    LiveData<List<SlashCommandVO>> queryGroupCommands(String groupId);

    /**
     * 指定群组里面机器人指令集数量
     *
     * @param groupId 群组id
     * @return 指令列表
     */
    @Query("SELECT COUNT(*) FROM tb_group_robot " +
            "WHERE groupId = :groupId AND status != 0 AND robotId in (SELECT DISTINCT c.robotId FROM TB_SLASH_COMMAND c WHERE c.status = 0)")
    LiveData<Integer> queryGroupCommandCount(String groupId);

    /**
     * 指定机器人指令集数量
     *
     * @param robotId 群组id
     * @return 指令列表
     */
    @Query("SELECT COUNT(*) FROM TB_SLASH_COMMAND " +
            "WHERE robotId = :robotId AND status= 0")
    LiveData<Integer> queryRobotCommandCount(String robotId);

    /**
     * 删除所有指令
     */
    @Query("DELETE FROM TB_SLASH_COMMAND")
    void deleteAllCommands();

    /**
     * 删除指定应用的所有指令
     *
     * @param appId 应用id
     */
    @Query("DELETE FROM TB_SLASH_COMMAND WHERE appId = :appId")
    void deleteCommandsByAppId(String appId);

}
