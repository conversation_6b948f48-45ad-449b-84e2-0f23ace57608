package com.twl.hi.foundation.logic

import android.content.Context
import android.net.ConnectivityManager
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.techwolf.lib.tlog.TLog
import com.twl.hi.foundation.db.ChatAttachmentDao
import com.twl.hi.foundation.model.ChatAttachment
import com.twl.hi.foundation.utils.AttachmentFileUtils
import com.twl.utils.file.FileUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import lib.twl.common.base.BaseApplication
import java.io.File

/**
 * 聊天附件服务（重构版）
 * 核心改进：
 * 1. 移除监听器机制，完全依赖Room LiveData驱动UI更新
 * 2. 简化协程操作，减少回调层级
 * 3. 数据库优先策略，确保数据一致性
 * 4. 与策略模式配合，提高可维护性
 */
class ChatAttachmentService : BaseService() {

    companion object {
        private const val TAG = "ChatAttachmentService"

        // 上传器工厂（由业务模块设置）
        @Volatile
        private var uploaderFactory: IUploaderFactory? = null

        /**
         * 设置上传器工厂（由业务模块调用）
         */
        @JvmStatic
        fun setUploaderFactory(factory: IUploaderFactory?) {
            uploaderFactory = factory
        }
    }

    // 协程作用域
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // 数据库 DAO
    private var chatAttachmentDao: ChatAttachmentDao? = null

    // 文件上传器
    private var fileUploader: IFileUploader = DefaultFileUploader(serviceScope)
    
    override fun onAccountInitialized() {
        // 账号初始化时的处理
    }
    
    override fun onCompanyInitialized() {
        TLog.info(TAG, "onCompanyInitialized")

        // 获取数据库 DAO
        chatAttachmentDao = ServiceManager.getInstance().databaseService.chatAttachmentDao

        // 重新创建文件上传器（清理之前的状态）
        fileUploader.destroy()
        fileUploader = DefaultFileUploader(serviceScope)

        // 重新设置上传器（通过工厂创建）
        initializeUploaders()
    }
    
    override fun onCompanyRelease() {
        TLog.info(TAG, "onCompanyRelease")

        // 清理资源
        fileUploader.destroy()
        chatAttachmentDao = null
    }
    
    override fun onAccountRelease() {
        // 账号释放时的处理
    }

    /**
     * 初始化上传器（通过工厂创建）
     */
    private fun initializeUploaders() {
        val factory = uploaderFactory
        if (factory != null) {
            try {
                // 通过工厂创建上传器
                val videoUploader = factory.createVideoUploader()
                val documentUploader = factory.createDocumentUploader()

                // 设置到文件上传器中
                val uploader = fileUploader
                if (uploader is DefaultFileUploader) {
                    uploader.setVideoUploader(videoUploader)
                    uploader.setDocumentUploader(documentUploader)
                }

                TLog.info(TAG, "上传器初始化成功")
            } catch (e: Exception) {
                TLog.error(TAG, "上传器初始化失败", e)
            }
        } else {
            TLog.info(TAG, "上传器工厂未设置，跳过上传器初始化")
        }
    }
    
    // ================================ 数据库操作方法 ================================
    
    /**
     * 插入附件（重构版 - 数据库优先）
     */
    suspend fun insertAttachment(attachment: ChatAttachment): Long {
        return withContext(Dispatchers.IO) {
            try {
                val result = chatAttachmentDao?.insert(attachment) ?: -1L
                if (result > 0) {
                    // 更新会话的sortVersion，触发会话列表更新
                    updateConversationSortVersion(attachment.chatId, attachment.chatType)
                    TLog.info(TAG, "附件插入成功: ${attachment.fileName}")
                } else {
                    TLog.error(TAG, "附件插入失败: ${attachment.fileName}")
                }
                result
            } catch (e: Exception) {
                TLog.error(TAG, "插入附件异常: ${attachment.fileName}", e)
                -1L
            }
        }
    }
    

    
    /**
     * 更新附件完整信息（用于上传成功后更新）
     * 修复：确保上传成功时清空错误信息，并检查附件是否仍然存在
     */
    suspend fun updateAttachmentAfterUpload(attachment: ChatAttachment): Int {
        return withContext(Dispatchers.IO) {
            try {
                // 再次检查附件是否仍然存在于数据库中
                val existingAttachment = chatAttachmentDao?.getById(attachment.id)
                if (existingAttachment == null) {
                    TLog.info(TAG, "附件已被删除，跳过上传完成更新: ${attachment.fileName}")
                    return@withContext 0
                }

                // 检查是否已被标记为取消
                if (existingAttachment.uploadStatus == ChatAttachment.UPLOAD_STATUS_FAILED &&
                    existingAttachment.errorMessage?.contains("取消") == true) {
                    TLog.info(TAG, "附件已被取消，跳过上传完成更新: ${attachment.fileName}")
                    return@withContext 0
                }

                // 创建一个副本来清空错误信息
                val updatedAttachment = attachment.copy(errorMessage = null)

                val result = chatAttachmentDao?.update(updatedAttachment) ?: 0
                if (result > 0) {
                    TLog.info(TAG, "附件上传完成更新成功: ${attachment.fileName}, 已清空错误信息")
                } else {
                    TLog.error(TAG, "附件上传完成更新失败: ${attachment.fileName}")
                }
                result
            } catch (e: Exception) {
                TLog.error(TAG, "更新附件异常: ${attachment.fileName}", e)
                0
            }
        }
    }
    
    /**
     * 删除附件（重构版 - 简化操作）
     */
    suspend fun deleteAttachment(attachment: ChatAttachment): Int {
        return withContext(Dispatchers.IO) {
            try {
                // 取消上传任务
                fileUploader.cancelUpload(attachment.id)

                val result = chatAttachmentDao?.delete(attachment) ?: 0
                if (result > 0) {
                    // 更新会话的sortVersion，触发会话列表更新
                    updateConversationSortVersion(attachment.chatId, attachment.chatType)
                    TLog.info(TAG, "附件删除成功: ${attachment.fileName}")
                } else {
                    TLog.error(TAG, "附件删除失败: ${attachment.fileName}")
                }
                result
            } catch (e: Exception) {
                TLog.error(TAG, "删除附件异常: ${attachment.fileName}", e)
                0
            }
        }
    }
    
    /**
     * 根据ID删除附件（重构版 - 简化操作）
     */
    suspend fun deleteAttachmentById(id: String): Int {
        return withContext(Dispatchers.IO) {
            try {
                // 先获取附件信息
                val attachment = chatAttachmentDao?.getById(id)

                // 取消上传任务
                fileUploader.cancelUpload(id)

                val result = chatAttachmentDao?.deleteById(id) ?: 0
                if (result > 0 && attachment != null) {
                    // 更新会话的sortVersion，触发会话列表更新
                    updateConversationSortVersion(attachment.chatId, attachment.chatType)
                    TLog.info(TAG, "附件删除成功: ${attachment.fileName}")
                } else {
                    TLog.error(TAG, "附件删除失败: $id")
                }
                result
            } catch (e: Exception) {
                TLog.error(TAG, "删除附件异常: $id", e)
                0
            }
        }
    }
    
    /**
     * 删除指定聊天的所有附件（重构版 - 简化操作）
     */
    suspend fun deleteAttachmentsByChatId(chatId: String): Int {
        return withContext(Dispatchers.IO) {
            try {
                // 先获取所有附件，取消上传任务
                val attachments = chatAttachmentDao?.getByChatId(chatId) ?: emptyList()
                attachments.forEach { fileUploader.cancelUpload(it.id) }

                val result = chatAttachmentDao?.deleteByChatId(chatId) ?: 0
                if (result > 0 && attachments.isNotEmpty()) {
                    // 获取第一个附件的chatType（所有附件的chatType应该相同）
                    val chatType = attachments.firstOrNull()?.chatType ?: 1
                    // 更新会话的sortVersion，触发会话列表更新
                    updateConversationSortVersion(chatId, chatType)
                    TLog.info(TAG, "删除聊天附件成功: $chatId, 数量: $result")
                } else {
                    TLog.info(TAG, "聊天无附件需要删除: $chatId")
                }
                result
            } catch (e: Exception) {
                TLog.error(TAG, "删除聊天附件异常: $chatId", e)
                0
            }
        }
    }
    
    /**
     * 根据ID查询附件
     */
    suspend fun getAttachmentById(id: String): ChatAttachment? {
        return withContext(Dispatchers.IO) {
            chatAttachmentDao?.getById(id)
        }
    }
    
    /**
     * 查询指定聊天的所有附件
     */
    suspend fun getAttachmentsByChatId(chatId: String): List<ChatAttachment> {
        return withContext(Dispatchers.IO) {
            chatAttachmentDao?.getByChatId(chatId) ?: emptyList()
        }
    }
    
    /**
     * 查询指定聊天的所有附件（LiveData）
     */
    fun getAttachmentsByChatIdLiveData(chatId: String): LiveData<List<ChatAttachment>> {
        return chatAttachmentDao?.getByChatIdLiveData(chatId) ?: MutableLiveData(emptyList())
    }
    

    
    /**
     * 更新附件上传状态（重构版 - 数据库驱动）
     */
    suspend fun updateUploadStatus(id: String, status: Int, progress: Int): Int {
        return withContext(Dispatchers.IO) {
            try {
                val result = chatAttachmentDao?.updateUploadStatus(id, status, progress) ?: 0
                if (result > 0) {
                    TLog.debug(TAG, "更新上传状态成功: $id, status: $status, progress: $progress")
                } else {
                    TLog.error(TAG, "更新上传状态失败: $id")
                }
                result
            } catch (e: Exception) {
                TLog.error(TAG, "更新上传状态异常: $id", e)
                0
            }
        }
    }
    
    /**
     * 更新附件上传进度（重构版 - 添加日志）
     */
    suspend fun updateUploadProgress(id: String, progress: Int): Int {
        return withContext(Dispatchers.IO) {
            try {
                val result = chatAttachmentDao?.updateUploadProgress(id, progress) ?: 0
                if (result > 0) {
                    TLog.debug(TAG, "更新上传进度: $id -> $progress%")
                }
                result
            } catch (e: Exception) {
                TLog.error(TAG, "更新上传进度异常: $id", e)
                0
            }
        }
    }
    
    /**
     * 更新附件错误信息（重构版 - 数据库驱动）
     */
    suspend fun updateErrorMessage(id: String, errorMessage: String?): Int {
        return withContext(Dispatchers.IO) {
            try {
                val result = chatAttachmentDao?.updateErrorMessage(id, errorMessage) ?: 0
                if (result > 0) {
                    TLog.debug(TAG, "更新错误信息成功: $id -> $errorMessage")
                } else {
                    TLog.error(TAG, "更新错误信息失败: $id")
                }
                result
            } catch (e: Exception) {
                TLog.error(TAG, "更新错误信息异常: $id", e)
                0
            }
        }
    }
    
    /**
     * 更新附件文件路径（重构版 - 数据库驱动）
     */
    suspend fun updateFilePath(id: String, filePath: String?): Int {
        return withContext(Dispatchers.IO) {
            try {
                val result = chatAttachmentDao?.updateFilePath(id, filePath) ?: 0
                if (result > 0) {
                    TLog.debug(TAG, "更新文件路径成功: $id -> $filePath")
                } else {
                    TLog.error(TAG, "更新文件路径失败: $id")
                }
                result
            } catch (e: Exception) {
                TLog.error(TAG, "更新文件路径异常: $id", e)
                0
            }
        }
    }
    

    




    /**
     * 批量查询多个会话是否有附件
     */
    suspend fun bulkCheckHasAttachments(chatIds: List<String>): Map<String, Boolean> {
        return withContext(Dispatchers.IO) {
            try {
                chatAttachmentDao?.bulkCheckHasAttachments(chatIds)
                    ?.associateBy({ it.chatId }, { it.hasAttachment })
                    ?: emptyMap()
            } catch (e: Exception) {
                TLog.error(TAG, "Failed to bulk check attachments", e)
                emptyMap()
            }
        }
    }

    /**
     * 更新附件的消息ID
     */
    suspend fun updateAttachmentMessageId(attachmentId: String, messageId: Long) {
        withContext(Dispatchers.IO) {
            chatAttachmentDao?.updateMessageId(attachmentId, messageId)
        }
    }
    
    // ================================ 上传相关方法 ================================
    
    /**
     * 添加附件并异步复制文件（重构版 - 简化逻辑）
     * 立即显示附件，在后台复制文件并开始上传
     */
    fun addAttachmentWithAsyncCopy(attachment: ChatAttachment, uri: Uri) {
        TLog.info(TAG, "addAttachmentWithAsyncCopy: ${attachment.id}")

        serviceScope.launch {
            try {
                // 立即保存到数据库，UI会自动更新
                insertAttachment(attachment)

                // 在后台异步复制文件并上传
                asyncCopyAndUpload(attachment, uri)
            } catch (e: Exception) {
                TLog.error(TAG, "添加附件失败: ${attachment.fileName}", e)
                updateUploadStatus(attachment.id, ChatAttachment.UPLOAD_STATUS_FAILED, 0)
                updateErrorMessage(attachment.id, "添加失败: ${e.message}")
            }
        }
    }

    /**
     * 添加上传任务（重构版 - 简化逻辑）
     */
    fun addUploadTask(attachment: ChatAttachment, file: File) {
        TLog.info(TAG, "addUploadTask: ${attachment.id}")

        serviceScope.launch {
            try {
                // 保存到数据库，UI会自动更新
                insertAttachment(attachment)

                // 添加到上传队列
                fileUploader.addUploadTask(attachment, file, createUploadCallback())
            } catch (e: Exception) {
                TLog.error(TAG, "添加上传任务失败: ${attachment.fileName}", e)
                updateUploadStatus(attachment.id, ChatAttachment.UPLOAD_STATUS_FAILED, 0)
                updateErrorMessage(attachment.id, "添加任务失败: ${e.message}")
            }
        }
    }
    
    /**
     * 取消上传
     */
    fun cancelUpload(attachmentId: String): Boolean {
        TLog.info(TAG, "cancelUpload: $attachmentId")
        return fileUploader.cancelUpload(attachmentId)
    }
    
    /**
     * 异步复制文件并开始上传（重构版 - 简化逻辑）
     */
    private suspend fun asyncCopyAndUpload(attachment: ChatAttachment, uri: Uri) {
        try {
            TLog.info(TAG, "开始异步复制文件: ${attachment.fileName}")

            // 更新状态为正在准备
            updateUploadStatus(attachment.id, ChatAttachment.UPLOAD_STATUS_PENDING, 0)
            updateErrorMessage(attachment.id, "正在准备文件...")

            val context = BaseApplication.getApplication().applicationContext

            // 在IO线程中复制文件
            val cacheFile = withContext(Dispatchers.IO) {
                val file = AttachmentFileUtils.getCacheFile(context, attachment)

                // 改进的缓存复用判断：检查文件存在、大小匹配、不为空
                val shouldReuseCache = file.exists() &&
                    file.length() > 0 &&
                    file.length() == attachment.fileSize

                if (shouldReuseCache) {
                    TLog.info(TAG, "复用已存在的缓存文件: ${file.absolutePath}, 大小: ${file.length() / (1024 * 1024)}MB")
                    file
                } else {
                    if (file.exists()) {
                        TLog.info(TAG, "缓存文件存在但无效 (大小: ${file.length()}, 期望: ${attachment.fileSize})，重新复制")
                        file.delete() // 删除无效的缓存文件
                    }

                    // 复制文件到缓存
                    TLog.info(TAG, "开始复制文件到缓存: ${attachment.fileName}, 大小: ${attachment.fileSize / (1024 * 1024)}MB")
                    val copyResult = AttachmentFileUtils.copyUriToCache(context, uri, attachment)

                    if (copyResult.file == null) {
                        throw Exception(copyResult.errorMessage ?: "文件复制失败")
                    }

                    TLog.info(TAG, "文件复制成功: ${copyResult.file.absolutePath}, 实际大小: ${copyResult.file.length() / (1024 * 1024)}MB")
                    copyResult.file
                }
            }

            // 复制完成，更新附件信息
            updateAttachmentAfterCopy(attachment, cacheFile)

            // 开始上传
            TLog.info(TAG, "开始上传文件: ${attachment.fileName}")
            fileUploader.addUploadTask(attachment, cacheFile, createUploadCallback())

        } catch (e: Exception) {
            TLog.error(TAG, "异步复制上传失败: ${attachment.fileName}", e)
            updateUploadStatus(attachment.id, ChatAttachment.UPLOAD_STATUS_FAILED, 0)
            updateErrorMessage(attachment.id, "文件处理失败: ${e.message}")
        }
    }

    /**
     * 重试上传（重构版 - 添加网络检查，避免断网时显示虚假进度）
     */
    fun retryUpload(attachmentId: String): Boolean {
        serviceScope.launch {
            try {
                val attachment = getAttachmentById(attachmentId)
                if (attachment == null) {
                    TLog.error(TAG, "重试上传失败：附件不存在 $attachmentId")
                    return@launch
                }

                if (!attachment.canRetryUpload()) {
                    TLog.error(TAG, "附件不能重试上传: ${attachment.fileName}")
                    return@launch
                }

                // 检查网络状态，避免断网时显示虚假进度
                val context = BaseApplication.getApplication().applicationContext
                if (!isNetworkAvailable(context)) {
                    TLog.error(TAG, "网络不可用，重试上传失败: ${attachment.fileName}")
                    updateUploadStatus(attachmentId, ChatAttachment.UPLOAD_STATUS_FAILED, 0)
                    updateErrorMessage(attachmentId, "网络不可用，请检查网络连接")
                    return@launch
                }

                // 取消可能存在的上传任务
                fileUploader.cancelUpload(attachmentId)

                // 重置状态
                updateUploadStatus(attachmentId, ChatAttachment.UPLOAD_STATUS_PENDING, 0)
                updateErrorMessage(attachmentId, "准备重试...")

                // 获取文件并重试
                val fileResult = AttachmentFileUtils.getFileFromAttachment(context, attachment)

                if (fileResult.file != null) {
                    TLog.info(TAG, "开始重试上传: ${attachment.fileName}")
                    fileUploader.addUploadTask(attachment, fileResult.file, createUploadCallback())
                } else {
                    TLog.error(TAG, "重试上传失败，无法获取文件: ${attachment.fileName}")
                    updateUploadStatus(attachmentId, ChatAttachment.UPLOAD_STATUS_FAILED, 0)
                    updateErrorMessage(attachmentId, fileResult.errorMessage ?: "无法获取文件")
                }
            } catch (e: Exception) {
                TLog.error(TAG, "重试上传异常: $attachmentId", e)
                updateUploadStatus(attachmentId, ChatAttachment.UPLOAD_STATUS_FAILED, 0)
                updateErrorMessage(attachmentId, "重试失败: ${e.message}")
            }
        }
        return true
    }

    /**
     * 检查网络是否可用
     */
    private fun isNetworkAvailable(context: Context): Boolean {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                val activeNetwork = connectivityManager.activeNetwork
                val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
                networkCapabilities?.hasCapability(android.net.NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.isConnected == true
            }
        } catch (e: Exception) {
            TLog.error(TAG, "检查网络状态失败: ${e.message}")
            false
        }
    }



    /**
     * 检查是否有正在上传的任务
     */
    fun hasActiveUpload(): Boolean {
        return fileUploader.hasActiveUpload()
    }



    /**
     * 恢复所有附件的缓存文件并清理未完成的上传状态
     * 应在应用启动时调用，确保重启后能正常显示图片并清理异常状态
     */
    fun restoreAttachmentCaches() {
        serviceScope.launch {
            try {
                val context = BaseApplication.getApplication().applicationContext
                val allAttachments = chatAttachmentDao?.getAll() ?: emptyList()

                TLog.info(TAG, "Processing ${allAttachments.size} attachments on app startup")

                var cacheSuccessCount = 0
                var cacheFailCount = 0
                var statusResetCount = 0

                allAttachments.forEach { attachment ->
                    // 1. 清理未完成的上传状态
                    if (attachment.isUploading() || attachment.uploadStatus == ChatAttachment.UPLOAD_STATUS_PENDING) {
                        // 将未完成的上传设置为失败状态，让用户重新上传
                        updateUploadStatus(
                            attachment.id,
                            ChatAttachment.UPLOAD_STATUS_FAILED,
                            0
                        )
                        updateErrorMessage(attachment.id, "上传失败，请重新上传")
                        statusResetCount++
                        TLog.debug(TAG, "Reset upload status for attachment: ${attachment.fileName}")
                    }

                    // 2. 恢复图片和视频的缓存文件
                    if (attachment.isImage() || attachment.isVideo()) {
                        val success = AttachmentFileUtils.ensureAttachmentCacheExists(context, attachment)
                        if (success) {
                            cacheSuccessCount++
                        } else {
                            cacheFailCount++
                        }
                    }
                }

                TLog.info(TAG, "Startup processing completed - Cache: $cacheSuccessCount success, $cacheFailCount failed; Status reset: $statusResetCount attachments")
            } catch (e: Exception) {
                TLog.error(TAG, "Error processing attachments on startup", e)
            }
        }
    }
    

    
    // ================================ 私有方法 ================================
    
    /**
     * 创建上传回调（重构版 - 简化逻辑，移除监听器）
     */
    private fun createUploadCallback(): IFileUploader.UploadProgressCallback {
        return object : IFileUploader.UploadProgressCallback {
            override fun onUploadStarted(attachmentId: String) {
                // 上传开始时设置状态为上传中
                serviceScope.launch {
                    try {
                        updateUploadStatus(attachmentId, ChatAttachment.UPLOAD_STATUS_UPLOADING, 0)
                        updateErrorMessage(attachmentId, null)
                        TLog.info(TAG, "上传开始: $attachmentId")
                    } catch (e: Exception) {
                        TLog.error(TAG, "设置上传开始状态异常: $attachmentId", e)
                    }
                }
            }

            override fun onProgressUpdate(attachmentId: String, progress: Int) {
                // 验证进度的合理性
                if (progress < 0 || progress > 100) {
                    TLog.error(TAG, "无效的进度值: $progress for attachment: $attachmentId")
                    return
                }

                // ✅ 关键修复：实际更新数据库中的进度
                serviceScope.launch {
                    try {
                        val attachment = getAttachmentById(attachmentId)
                        if (attachment == null) {
                            TLog.info(TAG, "附件已被删除，忽略进度更新: $attachmentId")
                            return@launch
                        }

                        // 检查附件是否已被取消
                        if (attachment.uploadStatus == ChatAttachment.UPLOAD_STATUS_FAILED &&
                            attachment.errorMessage?.contains("已取消") == true) {
                            TLog.info(TAG, "附件已被取消，忽略进度更新: $attachmentId")
                            return@launch
                        }

                        if (attachment.isUploading()) {
                            // ✅ 实际更新进度到数据库
                            updateUploadProgress(attachmentId, progress)
                            TLog.debug(TAG, "进度更新成功: $attachmentId -> $progress%")
                        } else {
                            TLog.error(TAG, "收到非上传中附件的进度更新: $attachmentId, 当前状态: ${attachment.uploadStatus}")
                        }
                    } catch (e: Exception) {
                        TLog.error(TAG, "更新进度异常: $attachmentId", e)
                    }
                }
            }

            override fun onUploadSuccess(attachment: ChatAttachment) {
                serviceScope.launch {
                    try {
                        // 检查附件是否仍然存在于数据库中（防止删除后的上传回调）
                        val existingAttachment = getAttachmentById(attachment.id)
                        if (existingAttachment == null) {
                            TLog.info(TAG, "附件已被删除，忽略上传成功回调: ${attachment.fileName}")
                            return@launch
                        }

                        // 检查附件是否已被取消（防止竞态条件）
                        if (existingAttachment.uploadStatus == ChatAttachment.UPLOAD_STATUS_FAILED &&
                            existingAttachment.errorMessage?.contains("已取消") == true) {
                            TLog.info(TAG, "附件已被取消，忽略上传成功回调: ${attachment.fileName}")
                            return@launch
                        }

                        // 更新附件信息到数据库
                        updateAttachmentAfterUpload(attachment)
                        TLog.info(TAG, "上传成功: ${attachment.fileName}")
                    } catch (e: Exception) {
                        TLog.error(TAG, "处理上传成功异常: ${attachment.fileName}", e)
                    }
                }
            }

            override fun onUploadFailed(attachmentId: String, errorMessage: String) {
                serviceScope.launch {
                    try {
                        updateUploadStatus(attachmentId, ChatAttachment.UPLOAD_STATUS_FAILED, 0)
                        updateErrorMessage(attachmentId, errorMessage)
                        TLog.error(TAG, "上传失败: $attachmentId -> $errorMessage")
                    } catch (e: Exception) {
                        TLog.error(TAG, "处理上传失败异常: $attachmentId", e)
                    }
                }
            }

            override fun onUploadCancelled(attachmentId: String) {
                serviceScope.launch {
                    try {
                        // 检查附件是否仍然存在
                        val attachment = getAttachmentById(attachmentId)
                        if (attachment != null) {
                            updateUploadStatus(attachmentId, ChatAttachment.UPLOAD_STATUS_FAILED, 0)
                            updateErrorMessage(attachmentId, "上传已取消")
                            TLog.info(TAG, "上传已取消: $attachmentId")
                        } else {
                            TLog.info(TAG, "附件已被删除，无需处理取消回调: $attachmentId")
                        }
                    } catch (e: Exception) {
                        TLog.error(TAG, "处理上传取消异常: $attachmentId", e)
                    }
                }
            }
        }
    }
    
    /**
     * 更新会话的sortVersion，触发会话列表更新
     */
    private fun updateConversationSortVersion(chatId: String, chatType: Int) {
        try {
            val conversationDao = ServiceManager.getInstance().databaseService.conversationDao
            val hasConversation = conversationDao?.hasConversation(chatId, chatType) ?: false
            if (hasConversation) {
                conversationDao?.flushModifyTime(chatId, chatType, System.currentTimeMillis())
            }
        } catch (e: Exception) {
            TLog.error(TAG, "Failed to update conversation sortVersion", e)
        }
    }



    /**
     * 复制完成后更新附件信息（重构版 - 简化逻辑）
     */
    private suspend fun updateAttachmentAfterCopy(attachment: ChatAttachment, cacheFile: File) {
        try {
            when (attachment.fileType) {
                ChatAttachment.FILE_TYPE_VIDEO -> {
                    // 获取视频信息
                    val info = IntArray(2)
                    FileUtils.getVideoThumbnail(
                        BaseApplication.getApplication(),
                        cacheFile.absolutePath,
                        info
                    )

                    val duration = FileUtils.getVideoDuration("file://" + cacheFile.absolutePath)

                    TLog.info(TAG, "视频信息 - 宽度: ${info[0]}, 高度: ${info[1]}, 时长: $duration")

                    // 更新视频信息到数据库，UI会自动更新
                    val updateResult = chatAttachmentDao?.updateVideoInfo(attachment.id, info[0], info[1], duration)
                    if (updateResult != null && updateResult > 0) {
                        TLog.info(TAG, "视频信息更新成功: ${attachment.id}")
                    } else {
                        TLog.error(TAG, "视频信息更新失败: ${attachment.id}")
                    }
                }
                ChatAttachment.FILE_TYPE_IMAGE -> {
                    // 图片信息在策略模式中处理，这里不需要额外操作
                    TLog.debug(TAG, "图片文件复制完成: ${attachment.fileName}")
                }
                ChatAttachment.FILE_TYPE_DOCUMENT -> {
                    // 文档文件不需要额外信息
                    TLog.debug(TAG, "文档文件复制完成: ${attachment.fileName}")
                }
            }
        } catch (e: Exception) {
            TLog.error(TAG, "复制后更新附件信息异常: ${attachment.fileName}", e)
        }
    }
}
