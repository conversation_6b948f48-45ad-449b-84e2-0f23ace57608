package com.twl.hi.foundation.model.message.extensioncard.adapter;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.twl.hi.foundation.model.message.extensioncard.data.modules.Module;
import com.twl.hi.foundation.model.message.extensioncard.data.modules.parser.ModuleParser;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2023/3/8
 * description: Gson使用 Module解析适配器
 */
public class ModuleTypeAdapter implements JsonDeserializer<Module> {

    @Override
    public Module deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        JsonObject asJsonObject = json.getAsJsonObject();
        int tag = asJsonObject.get("tag").getAsInt();
        return ModuleParser.parseModule(tag, json);
    }
}
