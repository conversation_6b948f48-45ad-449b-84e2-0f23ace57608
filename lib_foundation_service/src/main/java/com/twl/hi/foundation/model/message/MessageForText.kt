package com.twl.hi.foundation.model.message

import com.twl.hi.foundation.model.Agreement
import com.twl.hi.foundation.model.AtMethod
import com.twl.hi.foundation.model.Hypertext
import hi.kernel.HiKernel

/**
 * 纯文本消息
 */
class MessageForText : ChatMessageWithAt(), MessageWithHypertext, TranslatableMessage {
    var extraInfo: ExtraInfo? = null

    init {
        mediaType = MessageConstants.MSG_TEXT
    }

    private val myUid: String by lazy {
        HiKernel.getHikernel().account.userId
    }

    override val atMeMethod: AtMethod
        get() = when (extraInfo?.atId) {
            MessageConstants.MSG_AT_ALL -> AtMethod.AT_ALL
            myUid -> AtMethod.AT_USER
            else -> AtMethod.AT_NONE
        }

    override val atIds: List<String>
        get() = extraInfo?.atIds ?: emptyList()

    val atReads: List<Boolean>
        get() = extraInfo?.atReads ?: emptyList()

    val agreements: List<Agreement>
        get() = extraInfo?.agreements ?: emptyList()

    override val translateContent: String?
        get() = extraInfo?.translateContent

    override val showTranslate: Boolean
        get() = extraInfo?.showTranslate ?: false

    override val unfilteredHypertextList: List<Hypertext>
        get() = extraInfo?.hypertextList ?: emptyList()

    fun updateTranslate(translateContent: String?, showTranslate: Boolean) {
        extraInfo = extraInfo?.copy(translateContent = translateContent, showTranslate = showTranslate)
    }

    override fun closeTranslate() {
        extraInfo = extraInfo?.copy(showTranslate = false)
    }

    override fun accept(visitor: Visitor) {
        visitor.visit(this)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as MessageForText

        if (extraInfo != other.extraInfo) return false

        return true
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + (extraInfo?.hashCode() ?: 0)
        return result
    }

    //文本消息扩展字段
    data class ExtraInfo(
        val atId: String?,
        val atIds: List<String>?,
        val atReads: List<Boolean>?,
        val agreements: List<Agreement>?,
        val hypertextList: List<Hypertext>?,
        val translateContent: String? = null,
        val showTranslate: Boolean = false,
    )
}