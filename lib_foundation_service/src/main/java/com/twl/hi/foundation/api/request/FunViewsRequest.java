package com.twl.hi.foundation.api.request;

import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.response.FunViewsResponse;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class FunViewsRequest extends BaseApiRequest<FunViewsResponse> {

    public FunViewsRequest(BaseApiRequestCallback<FunViewsResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_FUN_VIEWS;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
