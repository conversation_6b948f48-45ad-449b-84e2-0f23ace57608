package com.twl.hi.foundation.model.email.bean.request;

public class GetMailAttachFileParam {

    private String messageId;

    private String attachmentId;

    private String contentId;

    private String fileName;

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getTraceId(){return TraceId; }
    public void setTraceId(String traceId) { this.TraceId = traceId; }
    private String TraceId;

    public int getMailBoxId(){ return MailBoxId; }
    public void setMailBoxId(int mailBoxId){ this.MailBoxId = mailBoxId; }
    private int MailBoxId;

    public int getPushMethod(){ return this.PushMethod; }
    public void setPushMethod(int pushMethod) {this.PushMethod = pushMethod;}
    private int PushMethod;


    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GetMailAttachFileParam{");
        sb.append("messageId='").append(messageId).append('\'');
        sb.append(", attachmentId='").append(attachmentId).append('\'');
        sb.append(", contentId='").append(contentId).append('\'');
        sb.append(", fileName='").append(fileName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
