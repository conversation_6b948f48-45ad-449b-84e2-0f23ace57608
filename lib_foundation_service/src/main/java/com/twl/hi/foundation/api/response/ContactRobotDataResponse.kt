package com.twl.hi.foundation.api.response

import com.twl.http.client.HttpResponse

/**
 * <AUTHOR>
 * @Date   2023/11/28 11:10 AM
 * @Desc   别删这行注释啊，删了程序就崩了
 */
class ContactRobotDataResponse: HttpResponse() {
    var comId: String? = null
    var version: Long? = null
    var subVersion: Long? = null
    var hasMore: Int? = null  // 1是, 0否
    var botInfoVOList: MutableList<RobotData>? = null
}

class RobotData {
    var userId: String ?= null
    var userName: String ?= null
    var userNamePy: String ?= null
    var userNamePyList: MutableList<String> ?= null
    var nickNamePyList: MutableList<String> ?= null
    var nickName: String ?= null
    var nickNamePy: String ?= null
    var avatar: String ?= null
    var tinyAvatar: String ?= null
    var visible: Int ?= null
    var status: Int ?= null
    var userType: Int ?= null
    var introduce: String ?= null
    var tag: String ?= null
    var aiRobot = 0 //0默认，1ai助理 （3.38新增）
}