package com.twl.hi.foundation.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.response.ZhiShuResourceSaveResponse;
import com.twl.http.HttpUtils;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

import okhttp3.MediaType;

/**
 * Author : Xuweixiang .
 * Date   : On 2023/10/17
 * Email  : Contact <EMAIL>
 * Desc   :
 */

public class ZhiShuResourceSaveRequest extends BaseApiRequest<ZhiShuResourceSaveResponse> {

    @Expose
    public String url;

    @Expose
    public String visitCode;

    @Expose
    public long messageId;

    public ZhiShuResourceSaveRequest(AbsRequestCallback<ZhiShuResourceSaveResponse> mCallback) {
        super(mCallback);
    }

    public ZhiShuResourceSaveRequest(AbsRequestCallback<ZhiShuResourceSaveResponse> mCallback, String url, String visitCode, long messageId) {
        super(mCallback);
        this.url = url;
        this.visitCode = visitCode;
        this.messageId = messageId;
    }

    @Override
    public MediaType getMediaType() {
        return MediaType.parse(HttpUtils.MEDIA_TYPE_JSON);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_ZHISHU_SAVE_FILE;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
