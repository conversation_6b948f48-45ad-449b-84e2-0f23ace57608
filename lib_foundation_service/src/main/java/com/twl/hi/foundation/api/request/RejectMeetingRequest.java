package com.twl.hi.foundation.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.response.MeetingCheckResponse;
import com.twl.http.callback.ApiRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

public class RejectMeetingRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String roomId;

    public RejectMeetingRequest(ApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_MEETING_REJECT;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}

