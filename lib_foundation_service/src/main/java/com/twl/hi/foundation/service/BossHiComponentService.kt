package com.twl.hi.foundation.service

import android.app.Service
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Message
import android.os.Messenger
import android.text.TextUtils
import com.twl.hi.foundation.MessageFactory
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.ConversationWithProfile
import com.twl.hi.foundation.model.message.extensioncard.MessageForExtensionCard.ExtensionCardInfo
import com.twl.hi.foundation.utils.RichTextEscapes
import lib.twl.common.util.ExecutorFactory

/**
 * Author : Xuweixiang .
 * Date   : On 2022/12/16
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

const val SERVICE_ACTION_ROOM_CONVERSATION = 0x00000
const val SERVICE_ACTION_MQTT_SEND_MESSAGE_CARD = 0x00101

class BossHiComponentService : Service() {

    private val messenger = Messenger(RoomHandler())

    override fun onBind(intent: Intent?): IBinder? {
        return messenger.binder
    }

    internal class RoomHandler : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                SERVICE_ACTION_MQTT_SEND_MESSAGE_CARD -> {
                    val bundle = msg.data
                    val chatId = bundle.getString("chatId")
                    val chatType = bundle.getInt("chatType")
                    val chatMessage = bundle.getSerializable("chatMessage") as ExtensionCardInfo
                    val leaveMessageContent = msg.data.getString("leaveMessageContent")
                    val messageForAppCard =
                        MessageFactory.createExtensionCardMessage(chatMessage, chatId, chatType, null)
                    ServiceManager.getInstance().messageService.sendMessage(messageForAppCard)
                    if (!TextUtils.isEmpty(leaveMessageContent)) {
                        val textMessage =
                            MessageFactory.createMarkdownTextMessage(
                                null,
                                RichTextEscapes.escapeKeywords(leaveMessageContent),
                                chatId,
                                chatType,
                                null,
                                emptyList(),
                                1
                            )
                        ServiceManager.getInstance().messageService.sendMessage(textMessage)
                    }
                }
                SERVICE_ACTION_ROOM_CONVERSATION -> {
                    val bundle = msg.data
                    val messenger = msg.replyTo
                    val chatId = bundle.getString("chatId")
                    val chatType = bundle.getInt("chatType")
                    val chatMessage = bundle.getSerializable("chatMessage")
                    val withAdditionalMessage = bundle.getBoolean("withAdditionalMessage")
                    ExecutorFactory.execLocalTask {
                        chatId ?: return@execLocalTask
                        val conversation: ConversationWithProfile? =
                            ServiceManager.getInstance().conversationService.getConversationProfile(
                                chatId,
                                chatType
                            )
                        ExecutorFactory.execMainTask {
                            messenger.send(Message.obtain().apply {
                                data = Bundle().apply {
                                    putString("chatId", chatId)
                                    putInt("chatType", chatType)
                                    putSerializable("chatMessage", chatMessage)
                                    putSerializable("conversation", conversation)
                                    putBoolean("withAdditionalMessage", withAdditionalMessage)
                                }
                            })
                        }
                    }
                }
            }
        }
    }
}