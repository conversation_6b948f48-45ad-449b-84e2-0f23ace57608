package com.twl.hi.foundation.utils;

import java.util.Vector;

public class HiMessageDBObservable {
    private Vector<HiMessageDBObserver> obs;

    public HiMessageDBObservable() {
        obs = new Vector<>();
    }

    public synchronized void addObserver(HiMessageDBObserver o) {
        if (o == null)
            throw new NullPointerException();
        if (!obs.contains(o)) {
            obs.addElement(o);
        }
    }

    public synchronized void deleteObserver(HiMessageDBObserver o) {
        obs.removeElement(o);
    }

    public void notifyObservers(long mid) {
        Object[] arrLocal;
        synchronized (this) {
            arrLocal = obs.toArray();
        }

        for (int i = 0; i < arrLocal.length; i++)
            if (((HiMessageDBObserver) arrLocal[i]).update(this, mid)) {
                return;
            }
    }

    public synchronized void deleteObservers() {
        obs.removeAllElements();
    }


    public synchronized int countObservers() {
        return obs.size();
    }
}
