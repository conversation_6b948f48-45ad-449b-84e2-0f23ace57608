package com.twl.hi.foundation.model.message

import com.twl.utils.GsonUtils

/**
 * 消息补充动作消息
 */
class MessageForAction : ChatMessage() {
    var body: Any? = null

    override fun accept(visitor: Visitor) {
        visitor.visit(this)
    }

    class VideoChatActionInfo(
        val roomId: String?,
        val linkType: Int, //1-语音，2-视频
        val linkTips: String?,
        val isApp: Boolean, //是否是客户端点击接受
    )

    class VideoChatBusyActionInfo(
        val busyId: String?, //占线用户id，他人或自己
        val roomId: String?,
        val linkType: Int //1-语音，2-视频
    )

    class VideoChatOverActionInfo(
        val leaverId: String?, //挂断方id
        val totalTime: String?, //总时长 HH:mm:ss
        val roomId: String?,
        val linkType: Int, //1-语音，2-视频
    )

    class SendLogInfo(
        val date: String?,
        val platform: Int
    )

    class ShiningMessageInfo(
        val shiningId: String?, //加急id
        val msgId: Long, //加急消息id
        val senderId: String?, //发起方
        val userIds: List<String>?, //通知用户
    )

    class ShiningBeDealtInfo(
        val shiningId: String?, //加急id
        val msgId: Long, //加急消息id
        val senderId: String?, //发起方
        val echoId: String?, //处理者id
        val shiningContent: String?, //加急内容
    )

    class MailInfo(
        val bodyPreview: String?,
        val id: String?, // 邮件id
        val folderId: String?, // 文件夹id
        val mailAddress: String?,
        val lastModifiedDateTime: String?, // 更新时间
        val senderEmail: String?,
        val senderName: String?,
        val subject: String?,
    )

    companion object {
        const val TYPE_VIDEO_CHAT_BUSY = "412" //通话占线
        const val TYPE_VIDEO_CHAT_NOT_SUPPORT = "413" //对方版本不支持
        const val TYPE_VIDEO_CHAT_INVITATION = "451" //邀请通话
        const val TYPE_VIDEO_CHAT_ACCEPT = "452" // 接受呼叫
        const val TYPE_VIDEO_CHAT_REFUSE = "453" //拒绝通话
        const val TYPE_VIDEO_CHAT_INVITATION_CANCEL = "454" //取消邀请通话
        const val TYPE_VIDEO_CHAT_NOANSWER = "456" //通话超时
        const val TYPE_SHINING_MESSAGE = "511" //加急消息
        const val TYPE_SHINING_BE_DEALT = "512" //加急消息被处理
        const val SEND_LOG_ACTION = "601" //log上传
        const val TYPE_NEW_EMAIL = "701" //新邮件
        const val TYPE_UPDATE_EMAIL = "702" //更新邮件，包含已读
        const val TYPE_DEL_EMAIL = "703" //删除邮件
        const val TYPE_MAIL_FOLDER_UPDATE = "704" //邮件文件夹更新

        fun createActionBody(type: String, json: String?): Any? {
            var obj: Any? = null
            when (type) {
                TYPE_VIDEO_CHAT_INVITATION_CANCEL,
                TYPE_VIDEO_CHAT_INVITATION,
                TYPE_VIDEO_CHAT_REFUSE,
                TYPE_VIDEO_CHAT_NOT_SUPPORT,
                TYPE_VIDEO_CHAT_NOANSWER,
                TYPE_VIDEO_CHAT_ACCEPT -> obj = GsonUtils.getGson().fromJson(json, VideoChatActionInfo::class.java)
                TYPE_VIDEO_CHAT_BUSY -> obj = GsonUtils.getGson().fromJson(json, VideoChatBusyActionInfo::class.java)
                SEND_LOG_ACTION -> obj = GsonUtils.getGson().fromJson(json, SendLogInfo::class.java)
                TYPE_SHINING_MESSAGE -> obj = GsonUtils.getGson().fromJson(json, ShiningMessageInfo::class.java)
                TYPE_SHINING_BE_DEALT -> obj = GsonUtils.getGson().fromJson(json, ShiningBeDealtInfo::class.java)
                TYPE_NEW_EMAIL,
                TYPE_UPDATE_EMAIL,
                TYPE_DEL_EMAIL -> obj = GsonUtils.getGson().fromJson(json, MailInfo::class.java)
            }
            return obj
        }
    }
}