package com.twl.hi.foundation.api.callback;

import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.model.message.ChatMessage;
import com.twl.hi.foundation.utils.MessageUtils;
import com.twl.http.ApiData;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.AbsRequestException;
import com.twl.http.error.ErrorReason;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import okhttp3.Response;

public class MessageHistoryRequestCallback<R extends MessageHistoryRequestCallback.MessageHistoryResponse> extends BaseApiRequestCallback<R> {
    @Override
    public void onSuccess(ApiData<R> data) {

    }

    @Override
    public ApiData<R> parseResponse(Response resp) throws IOException, AbsRequestException {
        JSONObject jsonObject = buildResult(resp);
        try {
            if (jsonObject.optInt("code") == 0) {
                R chatShareResponse = createClass().newInstance();
                chatShareResponse.minSeq = jsonObject.optLong("minSeq") + 1;
                JSONArray array = jsonObject.optJSONArray("messages");
                if (array == null) {
                    array = jsonObject.optJSONArray("result");
                }
                chatShareResponse.messages = array == null ? Collections.emptyList() : MessageUtils.jsonPb2Message(array);
                parseWithJson(jsonObject, chatShareResponse);
                return buildApiData(chatShareResponse);
            }
        } catch (IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
        }
        return buildApiData(createObj(jsonObject.toString()));
    }

    protected void parseWithJson(JSONObject jsonObject, R r) {

    }

    @Override
    public void onComplete() {

    }

    @Override
    public void onFailed(ErrorReason reason) {

    }

    public static class MessageHistoryResponse extends HttpResponse {
        public long minSeq;
        public List<ChatMessage> messages;
    }
}
