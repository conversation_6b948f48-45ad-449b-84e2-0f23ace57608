package com.twl.hi.foundation.api.request;


import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.api.response.MeetingTypesResponse;
import com.twl.http.callback.ApiRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class MeetingTypesRequest extends BaseApiRequest<MeetingTypesResponse> {

    public MeetingTypesRequest(ApiRequestCallback<MeetingTypesResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_MEETING_TYPES;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
