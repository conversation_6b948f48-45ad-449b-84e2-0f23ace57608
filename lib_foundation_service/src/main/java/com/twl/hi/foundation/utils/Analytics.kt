package com.twl.hi.foundation.utils

/**
 * 数据分析 Ktx 工具类
 */
object Analytics {

    /**
     * 埋点工具 [PointUtils.BuilderV4] 的包装，简化调用
     * 可用于构造复杂参数的埋点
     *
     * @param name 埋点名称
     * @param onSecurity 是否附加安全埋点参数
     * @param builder 埋点参数，以构造器提供
     *
     * ```kotlin
     * point("埋点名称") {
     *    put("参数1", "值1")
     *
     *    if (条件) {
     *        put("参数2", "值2")
     *    }
     * }
     * ```
     */
    inline fun point(name: String, onSecurity: Boolean = false, builder: MutableMap<String, Any?>.() -> Unit) {
        with(PointUtils.BuilderV4().name(name)) {
            if (onSecurity) {
                withSafeParams()
            }
            buildMap { builder() }
                .forEach { (key, value) -> params(key, value) }
            point()
        }
    }

    /**
     * 埋点工具 [PointUtils.BuilderV4] 的包装类，简化参数传递
     *
     * @param name 埋点名称
     * @param params 埋点参数
     * @param onSecurity 是否附加安全埋点参数
     *
     * ```kotlin
     * point(
     *     "埋点名称",
     *     "参数1" to "值1",
     *     "参数2" to "值2"
     * )
     * ```
     */
    fun point(name: String, vararg params: Pair<String, Any?>, onSecurity: Boolean = false) {
        with(PointUtils.BuilderV4().name(name)) {
            if (onSecurity) {
                withSafeParams()
            }
            params.forEach { (key, value) -> params(key, value) }
            point()
        }
    }
}
