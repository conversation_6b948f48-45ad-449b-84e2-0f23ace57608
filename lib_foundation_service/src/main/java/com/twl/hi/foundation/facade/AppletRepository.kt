package com.twl.hi.foundation.facade

import android.content.Context
import android.content.Intent
import com.techwolf.lib.tlog.TLog
import com.twl.hi.foundation.api.request.BaseFileDownloadCallback
import com.twl.hi.foundation.api.request.FileDownloadRequest
import com.twl.hi.foundation.model.workbench.AppBv
import com.twl.hi.foundation.model.workbench.WorkbenchPlugin
import com.twl.http.HttpExecutor
import com.twl.http.error.ErrorReason
import com.twl.kzmp.KZMP
import com.twl.kzmp.manager.getWgtParentPath
import com.twl.kzmp.util.ThreadManager.executeOnIoDispatcher
import hi.kernel.BuildConfig
import lib.twl.common.base.BaseApplication
import org.json.JSONException
import org.json.JSONObject
import java.io.File

/**
 * Author : Xuweixiang .
 * Date   : On 2023/5/31
 * Email  : Contact <EMAIL>
 * Desc   : 小程序更新
 *
 */
private const val TAG = "AppletRepository"
const val FILTER_APPLET_UPDATE = "filter-applet-update"
const val UPDATE_STATE_START = 0
const val UPDATE_STATE_SUCCESS = 1
const val UPDATE_STATE_FAIL = 2
const val UPDATE_EXISTS = 3

class AppletRepository :
    BaseRepository(
        arrayListOf(
            SyncDispatch.EVENT_SYNC_APPLET_UPDATE,
            SyncDispatch.EVENT_SYNC_APPLET_SYNCED
        )
    ) {

    override fun onUpdate(event: String?, obj: Any?) {
        super.onUpdate(event, obj)
        val context = BaseApplication.getApplication()
        if (event == SyncDispatch.EVENT_SYNC_APPLET_UPDATE) {
            TLog.info(TAG, "onUpdate -> EVENT_SYNC_APPLET_UPDATE $obj")
            // 711 更新
            if (obj is JSONObject) {
                val appId = obj.getString("appId")
                if (obj.has("appBv")) {
                    try {
                        val info = obj.getJSONArray("appBv")
                        var updateExists = false
                        for (i in 0 until info.length()) {
                            val obj = info.getJSONObject(i)
                            val appBv = parseAppBv(obj)
                            if (appletVisible(appBv)) {
                                if (checkUpdate(appId, appBv)) {
                                    updateExists = true
                                }
                            }
                        }
                        notifyUpdateExists(context, updateExists)
                    } catch (e: JSONException) {
                        TLog.error(TAG, "parse appBv arr err, %s", e.message)
                        try {
                            val info = obj.getJSONObject("appBv")
                            val appBv = parseAppBv(info)
                            var updateExists = false
                            if (appletVisible(appBv)) {
                                if (checkUpdate(appId, appBv)) {
                                    updateExists = true
                                }
                            }
                            notifyUpdateExists(context, updateExists)
                        } catch (e: JSONException) {
                            TLog.error(TAG, "parse appBv obj err, %s", e.message)
                        }
                    }
                }
            }
        }
        if (event == SyncDispatch.EVENT_SYNC_APPLET_SYNCED) {
            /**
             * WorkbenchPlugin(
             *     appId=bli_jet8y0oysju7mi8s,
             *     name=直行,
             *     icon=http://bosshi-static.oss-cn-beijing.aliyuncs.com/qa/backend/34b36d8f-06a9-4eb4-8dac-a56d02b08f07.png?x-oss-process=image/resize,h_100,m_lfit,
             *     num=0,
             *     description=直行小程序,
             *     appBv=AppBv(
             *         openAppletPath=http://bosshi-static.oss-cn-beijing.aliyuncs.com/qa/backend/637434b3-3da7-4a92-b9d2-3271a73a5653,
             *         openAppletVersion=3.1.1,
             *         openVersion=2.3.1,
             *         min=3250000,
             *         max=99999999999)
             *     )
             */
            TLog.info(TAG, "onUpdate -> EVENT_SYNC_APPLET_SYNCED $obj")
            // 接口同步
            if (obj is Collection<*>) {
                var updateExists = false
                (obj as? Collection<WorkbenchPlugin>)?.forEach {
                    it.appBv?.let { appBv ->
                        if (appletVisible(appBv)) {
                           if (checkUpdate(it.appId, appBv)) {
                               updateExists = true
                           }
                        }
                    }
                }
                notifyUpdateExists(context, updateExists)
            }
        }
    }

    private fun notifyUpdateExists(context: Context, updateExists: Boolean) {
        context.sendBroadcast(Intent(FILTER_APPLET_UPDATE).apply {
            putExtra("state", UPDATE_EXISTS)
            putExtra("hasUpdate", updateExists)
        })
    }

    private fun parseAppBv(obj: JSONObject): AppBv {
        val min = obj.optLong("min")
        val max = obj.optLong("max")
        val openAppletPath = obj.getString("openAppletPath")
        val openAppletVersion = obj.getString("openAppletVersion")
        val openVersion = obj.getString("openVersion")
        return AppBv(openAppletPath, openAppletVersion, openVersion, min, max)
    }

    private fun appletVisible(appBv: AppBv): Boolean {
        val min = appBv.min ?: 0L
        val max = appBv.max ?: 0L
        var visible = false
        //都为空，默认可见
        if (min + max == 0L) {
            visible = true
        }
        //如果仅是max = 0 那就认为它是不做限制就是无穷大版本
        if (max == 0L && BuildConfig.VERSION_CODE >= min) {
            visible = true
        }
        if (BuildConfig.VERSION_CODE in
            min..max
        ) {
            visible = true
        }
        TLog.info(TAG, "checkVisible min = [%s], max = [%s]", min, max)
        return visible
    }

    private fun checkUpdate(appId: String, appBv: AppBv) : Boolean {
        appBv.openAppletVersion ?: return false
        // 只有解压过的小程序才能拿到 version
        val appVersion = KZMP.getCurVersion(appId).takeIf { it.isNotEmpty() } ?: return false
        val context = BaseApplication.getApplication()
        TLog.info(
            TAG,
            "checkUpdate appId = [%s] localVersion = [%s], remoteVersion = [%s]",
            appId,
            appVersion,
            appBv.openAppletVersion
        )
        if (remoteVersionLargeThanLocal(appBv.openAppletVersion, appVersion)) {
            val parentPath = getWgtParentPath(context)
            val parentDir = File(parentPath)
            if (!parentDir.exists()) {
                parentDir.mkdirs()
            }

            val downloadFileDir = "${parentPath}${File.separator}${appBv.openAppletVersion}"
            val downloadFileName = "$appId.wgt"
            HttpExecutor.download(
                FileDownloadRequest(
                    appBv.openAppletPath,
                    downloadFileDir,
                    downloadFileName,
                    object : BaseFileDownloadCallback() {
                        override fun onFail(url: String?, reason: ErrorReason?) {
                            TLog.info(TAG, "downloadFailed, %s", reason?.errReason)
                            val downloadFile = File(downloadFileDir, downloadFileName)
                            if (downloadFile.exists() && downloadFile.isFile) {
                                downloadFile.delete()
                            }
                            context.sendBroadcast(Intent(FILTER_APPLET_UPDATE).apply {
                                putExtra("state", UPDATE_STATE_FAIL)
                            })
                        }

                        override fun onSuccess(url: String?, file: File?) {
                            TLog.info(TAG, "downloadSuccess, path[%s]", file?.absolutePath)
                            deleteOnOldWgtExist(parentPath, appId, appVersion)
                            context.sendBroadcast(Intent(FILTER_APPLET_UPDATE).apply {
                                putExtra("state", UPDATE_STATE_SUCCESS)
                            })
                        }

                    })
            )
            context.sendBroadcast(Intent(FILTER_APPLET_UPDATE).apply {
                putExtra("state", UPDATE_STATE_START)
            })
            return true
        }
        return false
    }

    fun deleteOnOldWgtExist(dirPath: String, appIdLocal: String, oldVersion: String) {
        Runnable {
            val filePath = dirPath + File.separator + oldVersion + File.separator + appIdLocal + ".wgt"
            val oldWgt = File(filePath)
            if (oldWgt.exists() && oldWgt.isFile) {
                oldWgt.delete()
            }
        }.executeOnIoDispatcher()
    }

    /**
     * version 对比， 3.1.2 > 3.1.1
     * version 对比， 3.1.12 > 3.1.8
     * version 对比， 3.1.12.0 > 3.1.12
     */
    private fun remoteVersionLargeThanLocal(remote: String, local: String): Boolean {
        val remoteVersion = remote.split(".");
        val localVersion = local.split(".")
        val minLen = remoteVersion.size.coerceAtMost(localVersion.size)
        for (i in 0 until minLen) {
            if (remoteVersion[i].toInt() > localVersion[i].toInt()) {
                return true
            } else if (remoteVersion[i].toInt() < localVersion[i].toInt()) {
                return false
            } else {
                // 相等继续对比下一位
            }
        }
        // 版本一致， 判断谁更长，谁版本多谁更新 "3.1.2", "3.1.2.1"
        return remoteVersion.size > localVersion.size
    }

}