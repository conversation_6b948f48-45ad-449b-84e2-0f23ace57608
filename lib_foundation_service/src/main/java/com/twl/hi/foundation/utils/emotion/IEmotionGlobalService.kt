package com.twl.hi.foundation.utils.emotion

import com.twl.hi.foundation.model.emotion.EmojiModel
import com.twl.hi.foundation.model.emotion.EmotionItem
import com.twl.hi.foundation.model.emotion.ServerEmotionCategoryBean

/**
 * emotion跨模块调用service
 *
 * Created by tan<PERSON>cheng on 2023/8/15
 */
interface IEmotionGlobalService {

    /**
     * 初始化所有表情
     */
    fun updateAllEmotionData(userId: String?, emotionList: List<ServerEmotionCategoryBean>?)

    /**
     * 更新所有黄豆表情
     */
    fun updateAllEmojiData(userId: String, emojiList: MutableList<EmojiModel>)

    /**
     * 根据关键词匹配返回本地黄豆表情资源
     */
    fun getEmojiItemByName(text: String?, filterNotActive: Boolean = true): EmotionItem?

    /**
     * 根据sid匹配返回本地黄豆表情资源
     */
    fun getEmojiItemBySid(sid: Long): EmotionItem?

    /**
     * 获取所有黄豆表情
     */
    fun getAllEmojiItems(): List<EmotionItem>
}