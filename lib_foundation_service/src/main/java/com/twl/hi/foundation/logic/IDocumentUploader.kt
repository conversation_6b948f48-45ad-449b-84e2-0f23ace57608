package com.twl.hi.foundation.logic

import com.twl.http.error.ErrorReason
import java.io.File

/**
 * 文档文件上传接口
 * 用于解耦DefaultFileUploader和MediaUploadHelper之间的依赖
 */
interface IDocumentUploader {
    
    /**
     * 上传文档文件（包含秒传检查和进度监听）
     * 
     * @param file 文档文件
     * @param callback 上传回调
     * @param progressListener 进度监听器
     * @param attachmentId 附件ID
     */
    fun uploadDocumentWithProgress(
        file: File,
        callback: DocumentUploadCallback,
        progressListener: ProgressListener?,
        attachmentId: String?
    )
    
    /**
     * 文档上传回调
     */
    interface DocumentUploadCallback {
        /**
         * 上传成功
         */
        fun onUploadSuccess(url: String?)
        
        /**
         * 上传失败
         */
        fun onUploadFailed(reason: ErrorReason?)
    }
    
    /**
     * 进度监听器
     */
    interface ProgressListener {
        /**
         * 进度更新
         * 
         * @param progress 进度 0.0-1.0
         * @param bytesWritten 已写入字节数
         * @param totalBytes 总字节数
         */
        fun onProgress(progress: Float, bytesWritten: Long, totalBytes: Long)
    }
}
