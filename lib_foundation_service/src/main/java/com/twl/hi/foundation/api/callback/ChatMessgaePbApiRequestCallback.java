package com.twl.hi.foundation.api.callback;

import com.twl.hi.foundation.api.base.BasePbApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.foundation.utils.MessageUtils;
import com.twl.http.ApiData;
import com.twl.http.error.AbsRequestException;
import com.twl.http.error.ErrorReason;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import okhttp3.Response;

public class ChatMessgaePbApiRequestCallback<R extends BasePbApiRequestCallback.MessageResponse> extends BasePbApiRequestCallback<R> {
    @Override
    public ApiData parseResponse(Response resp) throws IOException, AbsRequestException {
        JSONObject jsonObject =  buildResult(resp);
        try {
            R chatShareResponse = createClass().newInstance();
            JSONArray array = jsonObject.getJSONArray("result");
            chatShareResponse.messages = MessageUtils.jsonPb2Message(array);
            parseWithJson(jsonObject, chatShareResponse);
            return buildApiData(chatShareResponse);
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return buildApiData(createObj(jsonObject.toString()));
    }

    @Override
    public void showFailed(ErrorReason reason) {
        if (reason.getErrCode() == 1101 && getUrl().contains(URLConfig.URL_CHAT_MESSAGE_V2)) { //必填参数为空
            return;
        }
        super.showFailed(reason);
    }
}
