package com.twl.hi.foundation.model;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

import com.twl.hi.foundation.api.response.VideoUploadResult;

import java.io.Serializable;

import lib_twl_db_config.DBConstants;

@Entity(tableName = DBConstants.TAB_RENEWAL_PROGRESS)
public class RenewalProgress implements Serializable {
    private static final long serialVersionUID = -663259277514878527L;

    public static final int NONE = 0;         //无状态
    public static final int LOADING = 1;      //下载中
    public static final int PAUSE = 2;        //暂停
    public static final int ERROR = 3;        //错误
    public static final int FINISH = 4;       //完成

    @PrimaryKey
    @NonNull
    private String tag;                              //下载的标识键
    private String url;                              //网址
    private String folder;                           //保存文件夹
    private String fileName;                         //保存的文件名
    private long totalSize;                          //总字节长度, byte
    private long currentSize;                        //本次下载的大小, byte
    private int status;                              //当前状态
    @Ignore
    private int errorTime = 0;

    public RenewalProgress() {
        tag = "";
        totalSize = -1;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFolder() {
        return folder;
    }

    public void setFolder(String folder) {
        this.folder = folder;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public long getTotalSize() {
        return totalSize;
    }

    public void setTotalSize(long totalSize) {
        this.totalSize = totalSize;
    }

    public long getCurrentSize() {
        return currentSize;
    }

    public void setCurrentSize(long currentSize) {
        this.currentSize = currentSize;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    /**
     * @return 进度 百分比
     */
    public float getFraction() {
        return currentSize * 1.0f / totalSize;
    }

    /**
     * 记录失败次数
     */
    public int recordError() {
        return ++errorTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("RenewalProgress{");
        sb.append(", url='").append(url).append('\'');
        sb.append(", folder='").append(folder).append('\'');
        sb.append(", fileName='").append(fileName).append('\'');
        sb.append(", totalSize=").append(totalSize);
        sb.append(", currentSize=").append(currentSize);
        sb.append(", status=").append(status);
        sb.append('}');
        return sb.toString();
    }
}
