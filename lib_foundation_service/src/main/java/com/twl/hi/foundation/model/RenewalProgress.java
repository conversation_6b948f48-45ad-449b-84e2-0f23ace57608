package com.twl.hi.foundation.model;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

import com.twl.hi.foundation.api.response.VideoUploadResult;

import java.io.Serializable;

import lib_twl_db_config.DBConstants;

@Entity(tableName = DBConstants.TAB_RENEWAL_PROGRESS)
public class RenewalProgress implements Serializable {
    private static final long serialVersionUID = -663259277514878527L;

    public static final int NONE = 0;         //无状态
    public static final int LOADING = 1;      //下载中
    public static final int PAUSE = 2;        //暂停
    public static final int ERROR = 3;        //错误
    public static final int FINISH = 4;       //完成

    @PrimaryKey
    @NonNull
    private String tag;                              //下载的标识键
    private String url;                              //网址
    private String folder;                           //保存文件夹
    private String fileName;                         //保存的文件名
    private long totalSize;                          //总字节长度, byte
    private long currentSize;                        //本次下载的大小, byte
    private int status;                              //当前状态
    @Ignore
    private int errorTime = 0;

    // 扩展字段 - 支持视频上传
    @Ignore
    private String uploadType = "file"; // "file" 或 "video"

    @Ignore
    private VideoUploadResult videoResult; // 视频上传结果

    @Ignore
    private long lastUpdateTime = System.currentTimeMillis();

    @Ignore
    private String filePath; // 文件路径（用于视频上传）

    public RenewalProgress() {
        tag = "";
        totalSize = -1;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFolder() {
        return folder;
    }

    public void setFolder(String folder) {
        this.folder = folder;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public long getTotalSize() {
        return totalSize;
    }

    public void setTotalSize(long totalSize) {
        this.totalSize = totalSize;
    }

    public long getCurrentSize() {
        return currentSize;
    }

    public void setCurrentSize(long currentSize) {
        this.currentSize = currentSize;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    /**
     * @return 进度 百分比
     */
    public float getFraction() {
        return currentSize * 1.0f / totalSize;
    }

    /**
     * 记录失败次数
     */
    public int recordError() {
        return ++errorTime;
    }

    /**
     * 设置为视频上传
     */
    public void setAsVideoUpload() {
        this.uploadType = "video";
    }

    /**
     * 判断是否为视频上传
     */
    public boolean isVideoUpload() {
        return "video".equals(uploadType);
    }

    /**
     * 设置视频上传结果
     */
    public void setVideoResult(VideoUploadResult result) {
        this.videoResult = result;
    }

    /**
     * 获取视频上传结果
     */
    public VideoUploadResult getVideoResult() {
        return videoResult;
    }

    /**
     * 获取进度百分比
     */
    public int getProgressPercent() {
        if (totalSize <= 0) return 0;
        return (int) ((currentSize * 100) / totalSize);
    }

    /**
     * 判断是否应该更新进度
     */
    public boolean shouldUpdateProgress() {
        long now = System.currentTimeMillis();
        boolean shouldUpdate = now - lastUpdateTime > 500; // 500ms更新一次
        if (shouldUpdate) {
            lastUpdateTime = now;
        }
        return shouldUpdate;
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        switch (status) {
            case NONE: return "等待中";
            case LOADING: return "上传中 " + getProgressPercent() + "%";
            case PAUSE: return "已暂停";
            case ERROR: return "上传失败";
            case FINISH: return "上传完成";
            default: return "未知状态";
        }
    }

    /**
     * 设置文件路径
     */
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    /**
     * 获取文件路径
     */
    public String getFilePath() {
        return filePath;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("RenewalProgress{");
        sb.append(", url='").append(url).append('\'');
        sb.append(", folder='").append(folder).append('\'');
        sb.append(", fileName='").append(fileName).append('\'');
        sb.append(", totalSize=").append(totalSize);
        sb.append(", currentSize=").append(currentSize);
        sb.append(", status=").append(status);
        sb.append('}');
        return sb.toString();
    }
}
