apply plugin: 'com.bzl.hi.plugin.pmanager'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

apply from: rootProject.file('bzl-push.gradle')
apply from: rootProject.file('ksp_config.gradle')

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion build_versions.build_tools

    defaultConfig {
        minSdkVersion build_versions.min_sdk
        targetSdkVersion build_versions.target_sdk
        resourcePrefix 'meeting_'
    }

    dataBinding {
        enabled = true
    }

    if (project.ext.runAsApp) {
        signingConfigs {
            release {
                storeFile file("${rootDir}/hpbrbosszhipin.keystore")
                keyAlias "bosszhipin"
                keyPassword "techwolf2014"
                storePassword "techwolf2014"
            }
        }

        packagingOptions {
            pickFirst 'lib/x86_64/libc++_shared.so'
            pickFirst 'lib/arm64-v8a/libc++_shared.so'
            pickFirst 'lib/x86/libc++_shared.so'
            pickFirst 'lib/armeabi-v7a/libc++_shared.so'
            pickFirst 'lib/armeabi-v7a/libpl_droidsonroids_gif.so'
            pickFirst 'lib/arm64-v8a/libpl_droidsonroids_gif.so'
            pickFirst 'lib/x86_64/libpl_droidsonroids_gif.so'
            pickFirst 'lib/x86/libpl_droidsonroids_gif.so'
        }


        buildTypes {
            debug {
                minifyEnabled false
                proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                signingConfig signingConfigs.release
            }

            release {
                minifyEnabled true
                proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                signingConfig signingConfigs.release
            }
        }
    }


    compileOptions {
        if (!project.ext.runAsApp) {
            coreLibraryDesugaringEnabled true
        }
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
        freeCompilerArgs += "-Xjvm-default=all"
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }

}

dependenciesForImplementation(project, ":lib_mobile_mqtt_service", deps.commonLibs.module_foundation_business)
dependenciesForImplementation(project, ":lib_emotion", deps.commonLibs.lib_emotion)
dependenciesForImplementation(project, ":lib_secret", deps.commonLibs.lib_secret)
dependenciesForImplementation(project, ":lib_richtext", deps.commonLibs.lib_richtext)
dependenciesForImplementation(project, ":module_foundation_business", deps.commonLibs.module_foundation_business)
dependenciesForImplementation(project, ":module_login", deps.commonLibs.module_login)


dependenciesForImplementation(project, ":export_module_select", deps.commonLibs.export_module_select)
dependenciesForImplementation(project, ":export_module_video_meeting", deps.commonLibs.export_module_video_meeting)
dependenciesForImplementation(project, ":export_module_audio_shorthand", deps.commonLibs.export_module_audio_shorthand)

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    implementation deps.liteav


    implementation 'com.twl.sdk.file:zp-hybrid-biz-sdk:conf-3.24.2@aar'
    implementation 'com.twl.sdk.file:zp-biz-sdk:conf-3.24.1@aar'
    implementation 'com.twl.sdk.file:zp-sdk-eagle:10.23.1@aar'
    implementation 'com.twl.sdk.file:zp-sdk-rtc:10.10.1@aar'
    implementation 'com.twl.sdk.file:zp-sdk-matrix:10.10.1@aar'
    implementation 'com.twl.sdk.file:zp-sdk-support:10.10.2@aar'

    implementation 'androidx.lifecycle:lifecycle-extensions:2.1.0'

    implementation 'com.tencent.liteav:LiteAVSDK_Professional:8.6.10097'
    implementation 'jp.wasabeef:glide-transformations:4.1.0'
    coreLibraryDesugaring deps.desugar_jdk_libs
    implementation deps.androidx.design

}