<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.text.TextUtils" />

        <variable
            name="viewModel"
            type="com.twl.hi.videomeeting.viewmodel.MeetingViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.videomeeting.callback.MeetingMemberListFragmentCallback" />

        <variable
            name="showHandsDownAll"
            type="Boolean" />

        <variable
            name="isHost"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white">

        <LinearLayout
            android:id="@+id/ll_user_handsup"
            android:layout_width="0dp"
            android:layout_height="38dp"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/meeting_shape_user_handsup"
            android:gravity="center_vertical"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_user_number"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:textColor="@color/color_15181D"
                android:textSize="14sp"
                tools:text="2位参会者举手" />

            <TextView
                android:id="@+id/tv_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="@{()->callback.handsUpClick()}"
                android:paddingHorizontal="16dp"
                android:text="查看"
                android:textColor="#5055EB"
                android:textSize="14sp" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_meeting_participants"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="5dp"
            android:scrollbars="vertical"
            app:layout_constraintBottom_toTopOf="@+id/cl_bottom_menu"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_user_handsup" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_bottom_menu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:visibleGone="@{isHost}">

            <CheckedTextView
                android:id="@+id/tv_silence_all"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/meeting_selector_silence_all"
                android:gravity="center"
                android:onClick="@{()->callback.silenceAll()}"
                android:text="@string/video_silence_all"
                android:textAlignment="center"
                android:textColor="@color/meeting_selector_silence_all_text"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/tv_cancel_silence_all"
                app:layout_constraintTop_toTopOf="parent"
                app:visibleGone="@{!showHandsDownAll}" />

            <TextView
                android:id="@+id/tv_cancel_silence_all"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/bg_corner_7_color_33363b"
                android:gravity="center"
                android:onClick="@{()->callback.cancelSilenceAll()}"
                android:text="@string/video_cancel_silence_all"
                android:textColor="#424A57"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tv_silence_all"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:visibleGone="@{!showHandsDownAll}" />

            <TextView
                android:id="@+id/tv_hands_down_all"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/bg_corner_7_color_33363b"
                android:gravity="center"
                android:onClick="@{()->callback.handsDownAllClick()}"
                android:text="@string/video_hands_down_all"
                android:textColor="#424A57"
                android:textSize="16sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:visibleGone="@{showHandsDownAll}" />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>