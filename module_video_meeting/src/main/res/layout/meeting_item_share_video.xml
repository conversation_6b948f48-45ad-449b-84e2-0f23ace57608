<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.twl.hi.videomeeting.bean.MeetingParticipantBean" />

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <import type="com.twl.hi.foundation.media.MediaConstants" />

        <import type="hi.kernel.HiKernel" />

        <import type="com.twl.hi.basic.util.ThemeUtils" />

        <variable
            name="bean"
            type="com.twl.hi.videomeeting.bean.MeetingParticipantBean" />

        <variable
            name="engine"
            type="com.twl.hi.videomeeting.callback.MeetingEngine" />

        <variable
            name="callback"
            type="com.twl.hi.videomeeting.callback.MeetingItemCallback" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rootView"
        android:layout_width="88dp"
        android:layout_height="88dp"
        android:layout_marginHorizontal="4dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="8dp"
        android:background="@{bean.isSpeaker? @drawable/meeting_bg_corner_12_f3f4f5_with_20e5b4 : @drawable/bg_corner_12_f3f4f5}"
        android:padding="1dp">

        <FrameLayout
            android:id="@+id/fl_video_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:engine="@{engine}"
            app:shareVideoStatus="@{bean.isVideoOpen}"
            app:userId="@{bean.userId}" />

        <FrameLayout
            android:id="@+id/fl_info_container"
            visibleGone="@{!bean.isVideoOpen()}"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <include
                android:id="@+id/layout_avatar"
                layout="@layout/item_avatar2"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:visibility="@{bean.isVideoOpen ? View.GONE : View.VISIBLE}"
                app:avatar="@{bean.userId}" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_top_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_gravity="bottom"
                android:layout_margin="2dp"
                android:background="@drawable/meeting_bg_corner_6_white_whit_border"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="4dp">

                <ImageView
                    visibleGone="@{bean.isHost}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="2dp"
                    android:src="@{ThemeUtils.useNewTheme?@drawable/meeting_ic_member_host_new:@drawable/meeting_ic_member_host}"
                    tools:src="@drawable/meeting_ic_member_host_new"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <com.twl.hi.videomeeting.view.VolumeView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="2dp"
                    app:audioStrokeWidth="1dp"
                    app:volumeModel="@{!bean.audioOpen}"
                    app:volumeSize="@{bean.audioVolume}"
                    tools:src="@drawable/meeting_ic_audio_close" />

                <ImageView
                    visibleGone="@{bean.isShare}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="1dp"
                    android:src="@drawable/meeting_ic_member_share"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="2dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@{bean.displayName}"
                    android:textColor="@color/app_black"
                    android:textSize="10sp"
                    tools:text="adfasdfasdfasdf" />
            </LinearLayout>

            <LinearLayout
                visibleGone="@{bean.state == MediaConstants.MEETING_ROOM_STATE_OFFLINE || bean.state == MediaConstants.MEETING_ROOM_STATE_NET_ERROR}"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/meeting_shape_share_top_net_error"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="gone">

                <ImageView
                    android:layout_width="27dp"
                    android:layout_height="23dp"
                    android:layout_marginTop="16dp"
                    android:scaleType="fitXY"
                    android:src="@drawable/meeting_ic_net_error" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="@string/video_meeting_net_error"
                    android:textColor="@color/app_white"
                    android:textSize="10sp" />
            </LinearLayout>
        </FrameLayout>

        <ImageView
            visibleGone="@{bean.isHandsUp}"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="end"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:onClick="@{()->callback.onUserStatusDeleteClick(bean.userId,bean.displayName)}"
            android:src="@mipmap/meeting_im_hands_up"
            android:visibility="gone"
            tools:visibility="visible" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>