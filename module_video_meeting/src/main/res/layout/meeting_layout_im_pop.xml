<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="showMenu"
            type="Boolean" />
    </data>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.twl.hi.videomeeting.view.NoTouchRecyclerView
            android:id="@+id/rv_im_content"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.twl.hi.videomeeting.view.im.MeetingImMenuView
            android:id="@+id/miv_im_menu"
            visibleGone="@{showMenu}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>
</layout>
