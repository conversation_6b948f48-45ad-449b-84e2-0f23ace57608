<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ResourceName">

    <data>

        <import type="com.twl.hi.videomeeting.bean.MeetingParticipantBean" />

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <import type="com.twl.hi.foundation.media.MediaConstants" />

        <import type="hi.kernel.HiKernel" />

        <import type="com.twl.hi.basic.util.ThemeUtils" />

        <variable
            name="bean"
            type="com.twl.hi.videomeeting.bean.MeetingParticipantBean" />

        <variable
            name="engine"
            type="com.twl.hi.videomeeting.callback.MeetingEngine" />

        <variable
            name="callback"
            type="com.twl.hi.videomeeting.callback.MeetingItemCallback" />

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:layout_height="300dp"
        tools:layout_width="300dp">

        <FrameLayout
            android:id="@+id/fl_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="4dp"
            android:background="@drawable/bg_corner_12_f3f4f5">

            <FrameLayout
                android:id="@+id/fl_video_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:engine="@{engine}"
                app:useBigStream="@{bean.isUseBigStream}"
                app:userId="@{bean.userId}"
                app:videoStatus="@{bean.isVideoOpen}" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="26dp"
                android:layout_gravity="bottom|left"
                android:layout_margin="4dp"
                android:background="@drawable/meeting_bg_corner_6_white_whit_border"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="4dp">

                <ImageView
                    visibleGone="@{bean.isHost}"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginStart="2dp"
                    android:src="@{ThemeUtils.useNewTheme?@drawable/meeting_ic_member_host_new:@drawable/meeting_ic_member_host}"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <com.twl.hi.videomeeting.view.VolumeView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    app:audioStrokeWidth="1dp"
                    app:audioTubeColor="#858A99"
                    app:volumeModel="@{!bean.audioOpen}"
                    app:volumeSize="@{bean.audioVolume}"
                    tools:src="@drawable/meeting_ic_audio_close" />

                <TextView
                    android:id="@+id/tv_display_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@{bean.displayName}"
                    android:textColor="@color/app_black"
                    android:textSize="12sp"
                    tools:text="adfasdfasdfasdfadadfasdfasdfadfasdfaasdfafasdfasdfasdfasdfasdfasdfasdfas" />
            </LinearLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                tools:visibility="visible">

                <include
                    android:id="@+id/layout_avatar"
                    layout="@layout/item_avatar2"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="@{bean.isVideoOpen ? View.GONE : View.VISIBLE}"
                    app:avatarBig="@{bean.userId}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1 : 1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHeight_percent="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <ImageView
                    android:id="@+id/iv_hands_up"
                    visibleGone="@{bean.isHandsUp}"
                    android:layout_width="24dp"
                    android:scaleType="fitXY"
                    android:layout_height="24dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="8dp"
                    android:src="@mipmap/meeting_im_hands_up"
                    android:visibility="gone"
                    android:onClick="@{()->callback.onUserStatusDeleteClick(bean.userId,bean.getDisplayName())}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

                <View
                    visibleGone="@{bean.state == MediaConstants.MEETING_ROOM_STATE_OFFLINE || bean.state == MediaConstants.MEETING_ROOM_STATE_NET_ERROR}"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/meeting_bg_corner_6_gray_whit_border"
                    android:visibility="gone"
                    tools:visibility="visible" />


                <ImageView
                    android:id="@+id/iv_offline"
                    visibleGone="@{bean.state == MediaConstants.MEETING_ROOM_STATE_OFFLINE || bean.state == MediaConstants.MEETING_ROOM_STATE_NET_ERROR}"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:src="@drawable/meeting_ic_net_error"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@+id/tv_offline"
                    app:layout_constraintDimensionRatio="27:23"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHeight_percent="0.25"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <TextView
                    android:id="@+id/tv_offline"
                    visibleGone="@{bean.state == MediaConstants.MEETING_ROOM_STATE_OFFLINE || bean.state == MediaConstants.MEETING_ROOM_STATE_NET_ERROR}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/video_meeting_net_error"
                    android:textColor="@color/app_white"
                    android:textSize="13sp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/iv_offline" />


            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>
    </FrameLayout>
</layout>