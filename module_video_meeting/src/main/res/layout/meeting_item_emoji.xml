<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="data"
            type="com.twl.hi.foundation.model.emotion.EmotionItem" />

        <variable
            name="callback"
            type="com.twl.hi.videomeeting.callback.MeetingImEmojiCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:onClick="@{()->callback.onEmotionClick(data)}">

        <ImageView
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginBottom="8dp"
            app:emotion="@{data}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@mipmap/emotion_ic_category_emoji" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>