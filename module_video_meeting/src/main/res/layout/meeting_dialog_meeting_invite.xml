<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.videomeeting.viewmodel.MeetingInviteDialogViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.videomeeting.callback.MeetingInviteFragmentCallback" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="12dp"
            android:background="@drawable/bg_corner_12_color_white"
            android:paddingTop="16dp">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline_start_padding"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:orientation="vertical"
                app:layout_constraintGuide_begin="16dp"
                app:layout_constraintStart_toStartOf="parent" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline_end_padding"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintGuide_end="16dp"/>

            <include
                android:id="@+id/layout_avatar"
                layout="@layout/item_avatar2"
                android:layout_width="24dp"
                android:layout_height="24dp"
                app:avatarContact="@{viewModel.getInvitor()}"
                app:layout_constraintStart_toEndOf="@id/guideline_start_padding"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginTop="0dp" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                app:layout_constraintWidth_default="wrap"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{viewModel.getInvitorTitle()}"
                android:textColor="@color/color_15181D"
                app:layout_constraintBottom_toBottomOf="@+id/layout_avatar"
                app:layout_constraintEnd_toStartOf="@+id/tv_join_tag"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:flow_horizontalBias="0.0"
                app:layout_constraintStart_toEndOf="@+id/layout_avatar"
                app:layout_constraintTop_toTopOf="@+id/layout_avatar"
                tools:text="椰ddd" />

            <TextView
                android:id="@+id/tv_join_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="邀请你加入"
                android:textColor="@color/color_858A99"
                app:layout_constraintBottom_toBottomOf="@+id/layout_avatar"
                app:layout_constraintEnd_toEndOf="@id/guideline_end_padding"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@+id/tv_name"
                app:layout_constraintTop_toTopOf="@+id/layout_avatar" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/color_15181D"
                android:textSize="17sp"
                app:layout_constraintStart_toStartOf="@id/guideline_start_padding"
                app:layout_constraintEnd_toEndOf="@id/guideline_end_padding"
                app:layout_constraintTop_toBottomOf="@+id/layout_avatar"
                tools:text="设计九组BOSS Hi3.三大赛6设计评哇审会三大赛6设计评哇审会三大赛6设计评哇审会三大赛6设计评哇审会" />

            <ImageView
                android:id="@+id/iv_vol"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/meeting_shape_invite_state_bg"
                android:onClick="@{()->callback.onAudioStatusChangeClick()}"
                android:padding="8dp"
                android:src="@{viewModel.audioStatus()?@drawable/meeting_initiate_silence_close:@drawable/meeting_initiate_silence_open}"
                app:layout_constraintStart_toStartOf="@id/guideline_start_padding"
                app:layout_constraintTop_toBottomOf="@+id/tv_title"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintVertical_bias="0"
                android:layout_marginBottom="16dp"
                tools:src="@drawable/meeting_initiate_silence_close" />

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/meeting_shape_invite_state_bg"
                android:onClick="@{()->callback.onVideoStatusChangeClick()}"
                android:padding="8dp"
                android:src="@{viewModel.videoStatus()?@drawable/meeting_ic_video_open:@drawable/meeting_ic_video_close}"
                app:layout_constraintStart_toEndOf="@+id/iv_vol"
                app:layout_constraintTop_toBottomOf="@+id/tv_title"
                tools:src="@drawable/meeting_ic_video_open" />

            <TextView
                android:id="@+id/tv_accept"
                android:layout_width="80dp"
                android:layout_height="32dp"
                android:background="@drawable/meeting_shape_invite_accept_bg"
                android:gravity="center"
                android:onClick="@{()->callback.onJoinClick()}"
                android:text="加入"
                android:textColor="@color/app_white"
                app:layout_constraintEnd_toEndOf="@id/guideline_end_padding"
                app:layout_constraintTop_toTopOf="@+id/iv_vol"
                app:layout_constraintBottom_toBottomOf="@+id/iv_vol" />

            <TextView
                android:layout_width="80dp"
                android:layout_height="32dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/meeting_shape_invite_reject_bg"
                android:gravity="center"
                android:onClick="@{()->callback.onRejectClick()}"
                android:text="拒绝"
                android:textColor="@color/app_white"
                app:layout_constraintBottom_toBottomOf="@+id/tv_accept"
                app:layout_constraintEnd_toStartOf="@+id/tv_accept"
                app:layout_constraintTop_toTopOf="@+id/tv_accept" />

            <LinearLayout
                android:id="@+id/ll_warn"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:background="@drawable/meeting_shape_invite_warn_bg"
                android:gravity="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_accept"
                app:visibleGone="@{viewModel.isInMeeting()||viewModel.isShortHanding()}"
                android:layout_marginTop="20dp"
                tools:visibility="visible">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/meeting_ic_warn" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="@{viewModel.isInMeeting()?@string/join_meeting_exit_curr_meeting:@string/join_meeting_exit_curr_shorthand}"
                    android:textColor="@color/color_0D0D1A" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>