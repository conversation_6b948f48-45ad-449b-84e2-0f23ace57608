package com.twl.hi.videomeeting.dialog

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.recyclerview.widget.GridLayoutManager
import com.twl.hi.basic.BaseBottomDialogFragment
import com.twl.hi.foundation.model.emotion.EmotionItem
import com.twl.hi.videomeeting.BR
import com.twl.hi.videomeeting.R
import com.twl.hi.videomeeting.callback.MeetingImEmojiCallback
import com.twl.hi.videomeeting.databinding.MeetingDialogEmojiBinding
import com.twl.hi.videomeeting.helpers.MeetingImHelper
import com.twl.hi.videomeeting.viewmodel.MeetingEmojiDialogViewModel
import lib.twl.common.adapter.CommonAdapter

/**
 * <AUTHOR>
 * @date 2023/11/30
 * description:
 */
class MeetingEmojiDialog :
    BaseBottomDialogFragment<MeetingDialogEmojiBinding, MeetingEmojiDialogViewModel>(),
    MeetingImEmojiCallback {

    companion object {
        const val TAG = "meeting--->MeetingEmojiDialog"

        private const val ARGUMENT_IS_SCREEN_PORTRAIT = "argumentIsScreenPortrait"
        private const val ARGUMENT_IS_USER_HANDS_UP = "argumentIsUserHandsUp"
        private const val ARGUMENT_ROOM_ID = "argumentRoomId"
        fun newInstance(
            roomId: String,
            isScreenPortrait: Boolean,
            isUserHandsUp: Boolean
        ): MeetingEmojiDialog {
            return MeetingEmojiDialog().apply {
                arguments = Bundle().apply {
                    putBoolean(ARGUMENT_IS_SCREEN_PORTRAIT, isScreenPortrait)
                    putBoolean(ARGUMENT_IS_USER_HANDS_UP, isUserHandsUp)
                    putString(ARGUMENT_ROOM_ID, roomId)
                }
            }
        }
    }

    override fun getContentLayoutId() = R.layout.meeting_dialog_emoji

    override fun getCallbackVariable() = BR.callback
    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel


    @SuppressLint("ClickableViewAccessibility")
    override fun initFragment() {
        dataBinding.userHandsUp = arguments?.getBoolean(ARGUMENT_IS_USER_HANDS_UP) ?: false
        dataBinding.rvEmoji.apply {
            val col = if (arguments?.getBoolean(ARGUMENT_IS_SCREEN_PORTRAIT) != false) {
                7
            } else {
                14
            }
            layoutManager = GridLayoutManager(activity, col)
            adapter = CommonAdapter<EmotionItem>(R.layout.meeting_item_emoji, BR.data).apply {
                addCallback(BR.callback, this@MeetingEmojiDialog)
                submitList(viewModel.getAllEmotionItems())
            }
        }
        dataBinding.tvCancel.setOnClickListener { dismiss() }
    }

    override fun onEmotionClick(item: EmotionItem) {
        MeetingImHelper.sendTextMessage(item.name)
        dismiss()
    }

    override fun onHandsUpClick(oldStatus: Boolean) {
        val newStatus = !oldStatus
        viewModel.sendHandsStatus(arguments?.getString(ARGUMENT_ROOM_ID) ?: "", newStatus)
        dismiss()
    }
}