package com.twl.hi.videomeeting.api.request

import com.google.gson.annotations.Expose
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.hi.videomeeting.api.response.MeetingBoardDataResponse
import com.twl.hi.videomeeting.api.response.MeetingBoardVersionResponse
import com.twl.http.client.BaseApiRequest
import com.twl.http.client.HttpResponse
import com.twl.http.config.RequestMethod

/**
 * <AUTHOR>
 * @date 2023/5/4
 * description: 同步会议状态 入会失败
 */
class MeetingSyncBoardDataRequest(callback: BaseApiRequestCallback<MeetingBoardDataResponse>) :
    BaseApiRequest<MeetingBoardDataResponse>(callback) {

    @Expose
    @JvmField
    var roomId: String? = null

    @Expose
    @JvmField
    var boardId: Long = 0

    @Expose
    @JvmField
    var actionId: Long = 0

    override fun getUrl() = URLConfig.URL_MEETING_SYNC_BOARD_DATA

    override fun getMethod() = RequestMethod.GET
}