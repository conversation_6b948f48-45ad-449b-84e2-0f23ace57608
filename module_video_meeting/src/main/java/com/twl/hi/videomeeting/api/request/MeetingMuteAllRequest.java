package com.twl.hi.videomeeting.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

/**
 * <AUTHOR>
 * @date 2021/12/6.
 */
public class MeetingMuteAllRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String roomId;//房间id
    @Expose
    public Boolean forbid;//是否强制静音

    public MeetingMuteAllRequest(BaseApiRequestCallback<HttpResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_MEETING_MUTE_ALL;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
