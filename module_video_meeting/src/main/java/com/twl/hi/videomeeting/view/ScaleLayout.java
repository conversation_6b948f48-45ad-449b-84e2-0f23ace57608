package com.twl.hi.videomeeting.view;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * @author: musa on 2022/1/25
 * @e-mail: <EMAIL>
 * @desc: 主要用于承载 视频播放器，动态变化横竖比例
 */
public class ScaleLayout extends LinearLayout {
    private static final String TAG = "ScaleLayout";
    /**目标长宽比*/
    private float aspectScale;
    /**区分比例变化的差距*/
    private static final float miniDiff = 0.1f;

    public ScaleLayout(@NonNull Context context) {
        this(context, null);
    }

    public ScaleLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ScaleLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int width = MeasureSpec.getSize(widthMeasureSpec);
        int height = MeasureSpec.getSize(heightMeasureSpec);
        if (aspectScale == 0) {
            //nothing
        } else if (width < height) {
            height = ((int) (width / aspectScale));
        } else {
            width = ((int) (height * aspectScale));
        }
        Log.i(TAG, "aspectScale:"+ aspectScale + " height:"+ height + " width:" + width);
        widthMeasureSpec = MeasureSpec.makeMeasureSpec(width, MeasureSpec.getMode(widthMeasureSpec));
        heightMeasureSpec = MeasureSpec.makeMeasureSpec(height, MeasureSpec.getMode(heightMeasureSpec));
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }
    /**
     * 设置宽高比
     * 直接设置
     */
    public void setAspectScale(float scale, boolean forceRefresh) {
        if (Math.abs(scale - aspectScale) > miniDiff) {
            this.aspectScale = scale;
            if (forceRefresh) {
                requestLayout();
                Log.i(TAG, "request");
            }
        }
    }

    /**
     * 设置宽高比
     * 根据输入宽高设置宽高比
     */
    public void setAspectScale(int width, int height, boolean forceRefresh) {
        float scale = (float) width / height;
        setAspectScale(scale, forceRefresh);
    }

    public float getAspectScale() {
        return aspectScale;
    }
}
