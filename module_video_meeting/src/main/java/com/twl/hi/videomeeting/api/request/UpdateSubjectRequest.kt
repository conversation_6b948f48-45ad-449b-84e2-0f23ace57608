package com.twl.hi.videomeeting.api.request

import com.google.gson.annotations.Expose
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.http.client.BaseApiRequest
import com.twl.http.client.HttpResponse
import com.twl.http.config.RequestMethod

/**
 * author: dubojun
 * date: 2025/5/16
 * description:
 **/
class UpdateSubjectRequest :BaseApiRequest<HttpResponse>() {
    @Expose
    @JvmField
    var roomId = ""

    @Expose
    @JvmField
    var subjectCode = ""

    override fun getUrl(): String {
       return URLConfig.URL_MEETING_UPDATE_SUBJECT
    }

    override fun getMethod(): RequestMethod {
        return RequestMethod.POST
    }
}