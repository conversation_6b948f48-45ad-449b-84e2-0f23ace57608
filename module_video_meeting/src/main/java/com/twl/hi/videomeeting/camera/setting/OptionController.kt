package com.twl.hi.videomeeting.camera.setting

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.twl.hi.videomeeting.bean.MeetingCameraSettingBackgroundBean
import com.twl.hi.videomeeting.utils.MeetingCameraSettingUtils
import com.twl.hi.videomeeting.utils.MeetingCameraSettingUtils.MAIN_OPTION_CACHE_INDEX
import com.twl.hi.videomeeting.utils.MeetingCameraSettingUtils.SUB_OPTION_CACHE_INDEX
import okhttp3.internal.toImmutableMap
import java.util.LinkedList

/**
 *@author: musa on 2024/4/24
 *@e-mail: <EMAIL>
 *@desc: 相机设置选项逻辑类
 */
interface OptionController {
    /**
     * 获取选项
     */
    val optionsLivedata: LiveData<LinkedList<OptionBean>>

    /**
     * 选中选项
     */
    fun chooseOption(optionChosen: OptionBean)

    /**
     * 保存会议信息
     * roomId 和 confCode
     */
    fun saveConfInfo(roomId: String, confCode: String)

}

abstract class BaseOptionController(private val tabId: String, private val cameraOptionsChosen: MutableMap<String, List<String>>) : OptionController {
    /**
     * 用来序列化的，没必要反复创建
     */
    protected val gson by lazy { Gson() }

    /**
     * 选项缓存
     */
    protected val optionChosenIds: MutableList<String> by lazy {
        cameraOptionsChosen.getOrElse(tabId) { emptyList() }.toMutableList()
    }

    /**
     * 选项集合
     */
    protected val _options by lazy { MutableLiveData(createOptions()) }
    override val optionsLivedata: LiveData<LinkedList<OptionBean>>
        get() = _options

    protected var roomId = ""
    protected var confCode = ""

    override fun saveConfInfo(roomId: String, confCode: String) {
        this.roomId = roomId
        this.confCode = confCode
    }

    protected abstract fun createOptions(): LinkedList<OptionBean>

    /**
     * 不支持虚拟背景tab下的使用
     */
    protected fun isChosen(optionId: String) = optionChosenIds.run {
        if (optionChosenIds.isEmpty()) { //没有缓存默认选中空
            optionId == TabBean.TabId.NONE
        } else {
            contains(optionId)
        }
    }

    /**
     * 保存选项
     * 不支持虚拟背景tab下的使用
     */
    protected fun saveOption(optionIds: List<String>) {
        optionChosenIds.clear()
        optionChosenIds.addAll(optionIds)
        cameraOptionsChosen[tabId] = optionChosenIds
        MeetingCameraSettingUtils.saveCameraOptionsChosen(gson, cameraOptionsChosen)
    }
}

/**
 * 虚拟背景选项控制类
 */
class VirtualBackgroundOptionController(tabId: String, cameraOptionsChosen: MutableMap<String, List<String>>) :
    BaseOptionController(tabId, cameraOptionsChosen) {

    override fun createOptions(): LinkedList<OptionBean> = listOf(
        NormalOption.CREATOR.createNone(TabBean.TabId.TAB_VIRTUAL_BACKGROUND, !hasTheBackgroundBlurBeenTurnedOn()),
        NormalOption.CREATOR.createBackgroundBlur(hasTheBackgroundBlurBeenTurnedOn())
    ).run {
        LinkedList(this)
    }

    override fun chooseOption(optionChosen: OptionBean) {
        val newList = LinkedList(_options.value ?: emptyList())
        newList.forEachIndexed { index, it ->
            val isChosen = it.optionId == optionChosen.optionId
            val isChanged = it.isChosen != isChosen
            if (isChanged) {
                newList.updateItemChosenInstance(index, it, !it.isChosen)
            }
            if (isChosen) {
                handleOption(optionChosen)
            }
        }
        _options.value = newList
    }

    private fun hasTheBackgroundBlurBeenTurnedOn() =
        MeetingCameraSettingUtils.getCameraBackgroundType() == MeetingCameraSettingBackgroundBean.BACKGROUND_TYPE_BOKEH

    private fun handleOption(optionChosen: OptionBean) = when (optionChosen.optionId) {
        OptionBean.VirtualBackgroundOptionId.BACKGROUND_BLUR ->
            MeetingCameraSettingUtils.cameraSetting(MeetingCameraSettingBackgroundBean.BACKGROUND_TYPE_BOKEH, roomId, confCode)

        else ->
            MeetingCameraSettingUtils.cameraSetting(MeetingCameraSettingBackgroundBean.BACKGROUND_TYPE_NONE, roomId, confCode)
    }
}

/**
 * 美颜选项控制类
 */
class BeautyFilterOptionController(
    tabId: String,
    cameraOptionsChosen: MutableMap<String, List<String>>,
    val customOptionCallback: (optionId: String, value: Float, name: String) -> Unit
) :
    BaseOptionController(tabId, cameraOptionsChosen) {
    /**
     * 美颜自定义配置缓存
     */
    private val beautyCustomParams by lazy {
        MeetingCameraSettingUtils.getCameraBeautyCustomSetting(gson)
    }

    /**
     * 主要的选项，不会消失
     */
    private val mainOptions: LinkedList<OptionBean> by lazy {
        listOf(
            NormalOption.CREATOR.createNone(TabBean.TabId.TAB_BEAUTY_FILTER, isChosen(TabBean.TabId.NONE)),
            NormalOption.CREATOR.createOriginal(isChosen(OptionBean.BeautyFilterOptionId.ORIGINAL)),
            NormalOption.CREATOR.createNature(isChosen(OptionBean.BeautyFilterOptionId.NATURAL)),
            NormalOption.CREATOR.createFair(isChosen(OptionBean.BeautyFilterOptionId.FAIR)),
            NormalOption.CREATOR.createCustom(isChosen(OptionBean.BeautyFilterOptionId.CUSTOM)),
        ).run {
            //初始化的时候要判断有没有选中自定义，来判断是否展示子选项
            if (firstOrNull { it.isChosen }?.optionId == OptionBean.BeautyFilterOptionId.CUSTOM) {
                shouldShowSubOption = true
            }
            LinkedList(this)
        }
    }

    private var shouldShowSubOption: Boolean = false


    /**
     * 自定义的子选项，未选中自定义时会消失
     */
    private val subOptions: LinkedList<OptionBean> by lazy {
        listOf(
            BeautyFilterSubOption.CREATOR.createSmooth(
                isChosen = isChosen(OptionBean.BeautyFilterOptionId.SMOOTH),
                canShowLabel = beautyCustomParams.smoothLevel > 0
            ),
            BeautyFilterSubOption.CREATOR.createWhiten(
                isChosen = isChosen(OptionBean.BeautyFilterOptionId.WHITEN),
                canShowLabel = beautyCustomParams.whitenLevel > 0
            ),
            BeautyFilterSubOption.CREATOR.createSlimeFace(
                isChosen = isChosen(OptionBean.BeautyFilterOptionId.SLIM_FACE),
                canShowLabel = beautyCustomParams.slimFaceLevel > 0
            ),
            BeautyFilterSubOption.CREATOR.createVFace(
                isChosen = isChosen(OptionBean.BeautyFilterOptionId.V_FACE),
                canShowLabel = beautyCustomParams.vFaceLevel > 0
            ),
            BeautyFilterSubOption.CREATOR.createBigEyes(
                isChosen = isChosen(OptionBean.BeautyFilterOptionId.BIG_EYES),
                canShowLabel = beautyCustomParams.bigEyesLevel > 0
            ),
            BeautyFilterSubOption.CREATOR.createBlush(
                isChosen = isChosen(OptionBean.BeautyFilterOptionId.BLUSH),
                canShowLabel = beautyCustomParams.blushLevel > 0
            ),
        ).run {
            LinkedList(this)
        }
    }

    override fun createOptions(): LinkedList<OptionBean> = LinkedList<OptionBean>().apply {
        addAll(mainOptions)
        if (shouldShowSubOption) {
            addAll(subOptions)
        }
        mainOptions.firstOrNull { it.isChosen }?.let {
            handleOption(it)
        }
        subOptions.firstOrNull { it.isChosen && shouldShowSubOption}?.let {
            handleOption(it)
        }
    }

    override fun chooseOption(optionChosen: OptionBean) {
        if (optionChosenIds.contains(optionChosen.optionId)) { //重复选中无效
            return
        }

        var showSub = false //是否展示子选项的标记，在迭代中确定

        //先判断是不是选中子选项
        subOptions.forEachIndexed { index, it ->
            val isChosen = it.optionId == optionChosen.optionId
            val isChanged = it.isChosen != isChosen
            if (isChanged && optionChosen is BeautyFilterSubOption) { //只有选中其它子选项，选中状态才会变更
                subOptions.updateItemChosenInstance(index, it, !it.isChosen)
            }
            if (isChosen) { //选中项是子选项，继续展示
                showSub = true
            }
        }

        //接着判断是不是选中主选项
        mainOptions.forEachIndexed { index, it ->
            val isChosen = it.optionId == optionChosen.optionId
            val isChanged = it.isChosen != isChosen
            if (isChanged) {
                var newChosenState = !it.isChosen
                if (it.optionId == OptionBean.BeautyFilterOptionId.CUSTOM) {
                    //选中项是子选项 也算 自定义按钮被选中
                    showSub = showSub || newChosenState
                    newChosenState = showSub
                }
                mainOptions.updateItemChosenInstance(index, it, newChosenState)
            }
        }

        //通知UI
        _options.value = LinkedList(mainOptions).apply {
            if (showSub) {
                addAll(subOptions)
            }
            shouldShowSubOption = showSub
        }
        //处理选中逻辑
        handleOption(optionChosen)
    }

    private fun handleOption(optionChosen: OptionBean) {
        when (optionChosen) {
            is BeautyFilterSubOption -> {
                handleSubOption(optionChosen)
            }

            is NormalOption -> {
                handleNormalOption(optionChosen)
            }
        }
        saveOption(optionChosen)
    }

    /**
     * 处理子选项逻辑
     */
    private fun handleSubOption(optionChosen: BeautyFilterSubOption) {
        val value = when(optionChosen.optionId) {
            OptionBean.BeautyFilterOptionId.SMOOTH -> beautyCustomParams.smoothLevel
            OptionBean.BeautyFilterOptionId.WHITEN -> beautyCustomParams.whitenLevel
            OptionBean.BeautyFilterOptionId.SLIM_FACE -> beautyCustomParams.slimFaceLevel
            OptionBean.BeautyFilterOptionId.V_FACE -> beautyCustomParams.vFaceLevel
            OptionBean.BeautyFilterOptionId.BIG_EYES -> beautyCustomParams.bigEyesLevel
            OptionBean.BeautyFilterOptionId.BLUSH -> beautyCustomParams.blushLevel
            else -> return
        }
        MeetingCameraSettingUtils.cameraBeautySetting(beautyCustomParams)
        //回调到UI 展示拖动条
        customOptionCallback(optionChosen.optionId, value, optionChosen.name)
    }

    /**
     * 处理主选项逻辑
     */
    private fun handleNormalOption(optionChosen: NormalOption) {
        val param = when (optionChosen.optionId) {
            OptionBean.BeautyFilterOptionId.ORIGINAL -> BeautyParam.CREATOR.getOriginParam()
            OptionBean.BeautyFilterOptionId.NATURAL -> BeautyParam.CREATOR.getNatureParam()
            OptionBean.BeautyFilterOptionId.FAIR -> BeautyParam.CREATOR.getFairParam()
            OptionBean.BeautyFilterOptionId.CUSTOM -> beautyCustomParams
            else -> BeautyParam.CREATOR.getNoneParam()
        }
        MeetingCameraSettingUtils.cameraBeautySetting(param)

        if (optionChosen.optionId == OptionBean.BeautyFilterOptionId.CUSTOM) {
            subOptions.firstOrNull{it.isChosen }?.run {
                //回调到UI 展示拖动条
                if (this is BeautyFilterSubOption){
                    handleSubOption(this)
                }
            }
        } else {
            //回调到UI 隐藏
            customOptionCallback("", 0f, "")
        }
    }

    /**
     * 安全地从list中获取选中记录
     */
    private fun getChosenIdsPair(): Pair<String, String> =
        optionChosenIds.run {
            val subOptionId = if (size > SUB_OPTION_CACHE_INDEX) get(SUB_OPTION_CACHE_INDEX) else ""
            val mainOptionId = if (size > MAIN_OPTION_CACHE_INDEX) get(MAIN_OPTION_CACHE_INDEX) else ""
            (subOptionId to mainOptionId)
        }

    /**
     * 保存选中记录
     */
    private fun saveOption(optionChosen: OptionBean) {
        var (subOptionId, mainOptionId) = getChosenIdsPair()
        if (optionChosen is BeautyFilterSubOption) {
            subOptionId = optionChosen.optionId
        } else {
            mainOptionId = optionChosen.optionId
        }
        saveOption(listOf(subOptionId, mainOptionId))
    }

    /**
     * 设置自定义美颜
     */
    fun setBeautyCustomParam(optionId: String, value: Float) {
        when(optionId) {
            OptionBean.BeautyFilterOptionId.SMOOTH -> beautyCustomParams.smoothLevel = value
            OptionBean.BeautyFilterOptionId.WHITEN -> beautyCustomParams.whitenLevel = value
            OptionBean.BeautyFilterOptionId.SLIM_FACE -> beautyCustomParams.slimFaceLevel = value
            OptionBean.BeautyFilterOptionId.V_FACE -> beautyCustomParams.vFaceLevel = value
            OptionBean.BeautyFilterOptionId.BIG_EYES -> beautyCustomParams.bigEyesLevel = value
            OptionBean.BeautyFilterOptionId.BLUSH -> beautyCustomParams.blushLevel = value
            else -> return
        }
        MeetingCameraSettingUtils.cameraBeautySetting(beautyCustomParams)
        updateSubOptionLabel(optionId, value)
    }

    /**
     * 更新自定义下选项标展示状态
     */
    private fun updateSubOptionLabel(optionId: String, value: Float) {
        subOptions.updateItemLabelStateInstance(optionId, value > 0)
        _options.value = LinkedList(mainOptions).apply {
            if (shouldShowSubOption) {
                addAll(subOptions)
            }
        }
    }

    /**
     * 保存自定义美颜
     */
    fun saveBeautyCustomParam() {
        MeetingCameraSettingUtils.saveCameraBeautyCustomSetting(gson, beautyCustomParams)
    }

}

/**
 * 使用diffUtil时，必须要进行拷贝，否则无法触发更新
 * 使用老的对象的话，ItemCallback 回调中的 newItem 和 oldItem 是同一个对象，自然不会触发更新
 * 这个方法是基于这个前提下，方便地替换对象
 */
private fun LinkedList<OptionBean>.updateItemChosenInstance(index: Int, expireItem: OptionBean, isChosen: Boolean) {
    set(index, expireItem.getNewChosenStateInstance(isChosen))
}

private fun OptionBean.getNewChosenStateInstance(isChosen: Boolean): OptionBean =
    if (this is BeautyFilterSubOption) {
        BeautyFilterSubOption(this.tabId, this.optionId, this.name, this.iconNormal, this.iconChosen, isChosen, this.canShowLabel)
    } else {
        NormalOption(this.tabId, this.optionId, this.name, this.iconNormal, this.iconChosen, isChosen)
    }

private fun LinkedList<OptionBean>.updateItemLabelStateInstance(optionId: String, canShowLabel: Boolean) {
    forEachIndexed{index, it ->
        if (optionId == it.optionId && it is BeautyFilterSubOption) {
            if (canShowLabel != it.canShowLabel) {
                set(index, BeautyFilterSubOption(it.tabId, it.optionId, it.name, it.iconNormal, it.iconChosen, it.isChosen, canShowLabel))
            }
            return
        }
    }
}
