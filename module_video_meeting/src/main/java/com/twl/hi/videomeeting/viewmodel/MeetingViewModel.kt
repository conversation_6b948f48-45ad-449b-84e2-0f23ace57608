package com.twl.hi.videomeeting.viewmodel

import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.Gravity
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.Transformations
import com.sankuai.waimai.router.Router
import com.sdk.nebulartc.view.NebulaRtcView
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.util.HeadsetManager
import com.twl.hi.basic.util.HeadsetPlugListener
import com.twl.hi.foundation.api.response.MeetingCheckResponse
import com.twl.hi.foundation.api.response.MeetingCheckResponse.SubjectListBean
import com.twl.hi.foundation.media.IMeetingService
import com.twl.hi.foundation.media.MediaConstants
import com.twl.hi.foundation.utils.GroupInfoHelper
import com.twl.hi.foundation.utils.GroupStatusCheckCallback
import com.twl.hi.videomeeting.MeetingRtcSyncInfo
import com.twl.hi.videomeeting.R
import com.twl.hi.videomeeting.api.response.SCREEN_MIRRORING_BOX
import com.twl.hi.videomeeting.bean.MeetingParticipantBean
import com.twl.hi.videomeeting.callback.MeetingEngine
import com.twl.hi.videomeeting.helpers.VideoMeetingEntranceHelperImpl
import com.twl.hi.videomeeting.status.MeetingData
import com.twl.hi.videomeeting.utils.MeetingCameraSettingUtils
import com.twl.hi.videomeeting.utils.MeetingPointUtils
import com.twl.hi.videomeeting.view.HiVideoMeetingView
import hi.kernel.HiKernel
import lib.twl.common.base.BaseApplication
import lib.twl.common.callback.BaseCallback
import lib.twl.common.callback.OuterCallback
import lib.twl.common.ext.getResourceString
import lib.twl.common.util.LList
import lib.twl.common.util.QMUIDeviceHelper
import lib.twl.common.util.ToastUtils
import java.io.Serializable
import kotlin.collections.set

/**
 * <AUTHOR>
 * @date 2022/7/27
 * description:
 */
class MeetingViewModel : Serializable {
    companion object {
        private const val TAG = "Meeting--->MeetingNewViewModel"
        const val PAGE_INDEX_CALL = 1
        const val PAGE_INDEX_WAITING = 2
        const val PAGE_INDEX_CREATE = 3
        const val PAGE_INDEX_MEETING = 4
        const val PAGE_INDEX_JOIN = 5
    }

    private val mEngine = Router.getService<IMeetingService, MeetingEngine>(
        IMeetingService::class.java,
        MediaConstants.MEETING_ENGINE_LISTENER_KEY
    )
    private val meetingData = MeetingData(mEngine)

    private val mShowProgressBar = MutableLiveData<String?>()
    private val mPageIndex = MutableLiveData<Int>()
    private val mHeadsetManager: HeadsetManager = HeadsetManager(BaseApplication.getApplication())

    /**
     * 存在共享屏幕时顶部的成员列表
     */
    private val mShareMemberListData = MediatorLiveData<List<MeetingParticipantBean>>().apply {
        addSource(getMeetingRtcSyncInfo().memberList, Observer {
            if (!LList.isEmpty(value)) {
                value = mergeSortMembersFromSourceForShare(it, value!!)
            }
        })

        addSource(getMeetingRtcSyncInfo().activeUserId, Observer {
            val member = arrayListOf<MeetingParticipantBean>()
            member.addAll(value ?: arrayListOf())
            //是否在[0,3)范围内
            var isInFour = member.isEmpty()
            for (i in member.indices) {
                if (i == 4) {
                    break
                } else {
                    if (member[i].userId == it) {
                        isInFour = true
                        break
                    }
                }
            }
            if (!isInFour) {
                //第四项以后直接插入第二项
                for (i in member.indices) {
                    val meetingParticipantBean = member[i]
                    if (meetingParticipantBean.userId == it) {
                        member.removeAt(i)
                        member.add(2, meetingParticipantBean)
                        break
                    }
                }
                value = member
            }
        })
    }

    fun getShareList(): LiveData<List<MeetingParticipantBean>> = mShareMemberListData

    fun clearShareList() {
        mShareMemberListData.value = arrayListOf()
    }

    fun initShareList() {
        getMeetingRtcSyncInfo().memberList.value?.run {
            mShareMemberListData.value = initSortMembersFromSourceForShare(this)
        }
    }

    /**
     * 没有共享屏幕时成员列表
     */
    val normalMemberList = Transformations.map(getMeetingRtcSyncInfo().memberList) {
        getSortMembersFromSource(it)
    }

    private val mHandler = Handler(Looper.getMainLooper())
    private var mStartCalcTime = false
    private val mTicker: Runnable = object : Runnable {
        override fun run() {
            val lastTime = mEngine.meetingRtcSyncInfo.meetingTimes.value
            if (mStartCalcTime && lastTime != null) {
                mEngine.meetingRtcSyncInfo.setTimes(lastTime + 1)
                mHandler.postDelayed(this, 1000)
            }
        }
    }

    var currentConfCode: String? = null

    fun getEngine(): MeetingEngine = mEngine

    init {
        TLog.info(TAG, "MeetingNewViewModel--->init")
        getMeetingRtcSyncInfo().setHeadsetType(mHeadsetManager.getCurType())
        MeetingCameraSettingUtils.cameraSetting(
            MeetingCameraSettingUtils.getCameraBackgroundType()
        )
    }

    fun checkMeeting(autoEnter: Boolean, isReEnter: Boolean) {
        mEngine.checkMeetingInfo(autoEnter, isReEnter, false)
    }

    fun checkMeeting(autoEnter: Boolean, isReEnter: Boolean, isReCheck: Boolean) {
        mEngine.checkMeetingInfo(autoEnter, isReEnter, isReCheck)
    }

    @JvmOverloads
    fun leaveMeeting(tips: String? = null, tipsId: Int = 0) {
        mEngine.quitMeeting(tips, tipsId)
    }

    fun closeMeeting() {
        mEngine.closeMeeting()
    }

    fun getPageIndex(): LiveData<Int> = mPageIndex

    fun getShowProgressBar(): LiveData<String?> = mShowProgressBar

    fun setShowProgressBar() = mShowProgressBar.postValue("")

    fun hideShowProgressBar() {
        mShowProgressBar.postValue(null)
    }

    fun isSingleChatRoom() =
        mEngine.meetingNetSyncInfo.roomType == MediaConstants.MEETING_ROOM_TYPE_SINGLE

    fun setInitialMeetingInfo(info: VideoMeetingEntranceHelperImpl.Strategy?) {
        mEngine.initialStrategy = info
        mEngine.meetingNetSyncInfo = info?.response

        info?.response?.run {
            setMeetingEnterTitle(theme, HiKernel.getHikernel().account.userName)
        } ?: run {
            setMeetingEnterTitle(null, HiKernel.getHikernel().account.userName)
        }
    }

    fun setPeerId(id: String) {
        mEngine.peerId = id
    }

    fun getPeerId() = mEngine.peerId

    fun setChatId(chatId: String?) {
        mEngine.chatId = chatId
    }

    fun getChatId(): String {
        return mEngine.chatId.orEmpty()
    }

    fun setContactId(id: String) {
        mEngine.setContactId(id)
    }

    fun setSelectIds(selectIds: String) {
        mEngine.selectIds = selectIds
    }

    fun getSelectIds(): String = mEngine.selectIds

    fun setSelectTypes(selectTypes: String) {
        mEngine.selectTypes = selectTypes
    }

    fun getSelectTypes(): String = mEngine.selectTypes

    fun setCreateMeetingBtnText(text: String) {
        getMeetingRtcSyncInfo().setAddParticipantsBtnText(text)
    }

    fun inviteMeetingPerson() {
        mEngine.inviteMeetingPerson()
    }

    fun inviteMeetingPerson(roomId: String?, chatIds: String?, chatTypes: String?) {
        mEngine.inviteMeetingPerson(roomId, chatIds, chatTypes)
    }

    fun getRoomId() = mEngine.meetingNetSyncInfo.roomId

    fun getMeetingRtcSyncInfo(): MeetingRtcSyncInfo = mEngine.meetingRtcSyncInfo

    fun getMeetingNetSyncInfo(): MeetingCheckResponse = mEngine.meetingNetSyncInfo

    fun clearData() {
        mEngine.meetingNetSyncInfo = null
        mEngine.meetingRtcSyncInfo = null
        currentConfCode = null
        mHeadsetManager.unregisterAudioDeviceCallback()
    }

    fun isConnected() = mEngine.isConnected

    fun isMyRoom() = mEngine.isMyRoom

    fun isHost() = mEngine.meetingNetSyncInfo.hostId == HiKernel.getHikernel().account.userId

    fun startCalcTimes() {
        if (mEngine.meetingRtcSyncInfo.meetingTimes.value == null) {
            mEngine.meetingRtcSyncInfo.setTimes(mEngine.meetingNetSyncInfo.duration / 1000)
            mStartCalcTime = true
            mHandler.removeCallbacks(mTicker)
            mHandler.postDelayed(mTicker, 1000)
        }
    }

    fun stopCalcTimes() {
        mStartCalcTime = false
        mHandler.removeCallbacksAndMessages(null)
    }

    fun setAudioSpeaker(boolean: Boolean) {
        mEngine.setAudioSpeaker(boolean)
    }

    fun muteLocalAudio(boolean: Boolean) {
        mEngine.muteLocalAudio(boolean)
        if (boolean) {
            ToastUtils.ssd(R.string.meeting_mike_have_closed.getResourceString())
        } else {
            ToastUtils.ssd(R.string.meeting_mike_have_opened.getResourceString())
        }
    }

    fun shareStreamSwitch(container: ViewGroup, isSharing: Boolean, justUI: Boolean) {
        while (container.childCount > 1){
            container.removeViewAt(0)
        }
        val shareId = getMeetingRtcSyncInfo().conShareId.value
        val view: NebulaRtcView = mEngine.shareVideoView
        if (isSharing) {
            view.layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            ).apply {
                gravity = Gravity.CENTER
            }
            if (view.parent != null) {
                (view.parent as ViewGroup).removeView(view)
            }
            container.addView(view, 0)
            //开始接收视频流
            if (!justUI) {
                mEngine.stopRemoteSubStreamView(getMeetingRtcSyncInfo().tempConShareId)
                mEngine.startRemoteSubStreamView(getMeetingRtcSyncInfo().tempConShareId, view)
            }
        } else {
            //停止接收视频流
            if (!justUI && !TextUtils.isEmpty(shareId)) {
                mEngine.stopRemoteSubStreamView(shareId)
            }
            container.removeView(view)
        }
    }

    fun getUserId() = HiKernel.getHikernel().account.userId

    private fun setMeetingEnterTitle(theme: String?, creatorName: String) {
        if (!TextUtils.isEmpty(theme)) {
            getMeetingRtcSyncInfo().setMeetingThemeTitle(theme)
        } else if (!TextUtils.isEmpty(creatorName)) {
            getMeetingRtcSyncInfo().setMeetingThemeTitle(
                R.string.video_enter_meeting_title.getResourceString(creatorName)
            )
        }
    }

    fun acceptRequest() {
        mEngine.acceptRequest()
    }

    fun tickPreviousMeeting(needCreate: Boolean) {
        mEngine.tickPreviousMeeting(needCreate)
    }

    fun getShareVideoView(): NebulaRtcView = mEngine.shareVideoView

    fun unStashVideoView() {
        mEngine.unStashVideoView()
    }

    fun hostMuteTheMember(userId: String?) {
        mEngine.hostMuteTheMember(userId)
    }

    fun hostUnMuteTheMember(userId: String?, isBoxOperation: Boolean = false) {
        mEngine.hostUnMuteTheMember(userId, isBoxOperation)
    }

    fun hostMuteTheRobot(userId: String) {
        hostMuteTheMember(userId)
    }

    fun hostUnMuteTheRobot(userId: String) {
        mEngine.getBoxType(userId, object : BaseCallback<Int>{
            override fun onSuccess(t: Int?) {
                t?.let {
                    if (it == SCREEN_MIRRORING_BOX) {
                        ToastUtils.failure(R.string.meeting_do_not_support_open_mic.getResourceString())
                    } else {
                        hostUnMuteTheMember(userId, true)
                    }
                }
            }
        })
    }

    fun hostRequestTheMemberOpenCamera(userId: String, isBoxOperation: Boolean = false) {
        mEngine.hostRequestTheMemberOpenCamera(userId, isBoxOperation)
    }

    fun hostCloseTheMemberCamera(userId: String) {
        mEngine.hostCloseTheMemberCamera(userId)
    }

    fun hostRequestTheRobotOpenCamera(userId: String) {
        mEngine.getBoxType(userId, object : BaseCallback<Int>{
            override fun onSuccess(t: Int?) {
                t?.let {
                    if (it == SCREEN_MIRRORING_BOX) {
                        ToastUtils.failure(R.string.meeting_do_not_support_open_camera.getResourceString())
                    } else {
                        hostRequestTheMemberOpenCamera(userId, true)
                    }
                }
            }
        })
    }

    fun hostCloseTheRobotCamera(userId: String) {
        hostCloseTheMemberCamera(userId)
    }

    fun hostKickOutTheMember(kickId: String, callback: OuterCallback<Boolean>) {
        mEngine.hostKickOutTheMember(kickId, callback)
    }

    fun updateHost(newHostId: String, callback: OuterCallback<Boolean>) {
        mEngine.updateHost(newHostId, callback)
    }

    fun stopRecord() {
        mEngine.stopRecord()
    }

    fun applyRecord() {
        mEngine.applyRecord()
    }

    fun startRecord() {
        mEngine.startRecord()
    }

    fun switchCamera() {
        mEngine.meetingRtcSyncInfo.isFrontCamera = !mEngine.meetingRtcSyncInfo.isFrontCamera
        mEngine.switchCamera()
    }

    fun getVideoView(userId: String) = mEngine.getVideoView(userId)


    /**
     * 通过原始数据获取排序好的数据（不能对原始数据列表进行增删）
     * 在没有共享时展示在成员列表中
     */
    fun getSortMembersFromSource(sourceList: List<MeetingParticipantBean>?): MutableList<MeetingParticipantBean> {
        if (sourceList == null) {
            return arrayListOf()
        }
        val memberList = arrayListOf<MeetingParticipantBean>()
        memberList.addAll(sourceList) //copy原始数据副本
        val sortMemberList = arrayListOf<MeetingParticipantBean>()
        val iterator = memberList.iterator()
        while (iterator.hasNext()) {
            if (sortMemberList.size >= 2) {
                break
            }
            val meetingParticipantBean = iterator.next()
            val participantIdUserId = meetingParticipantBean.userId
            if (participantIdUserId == HiKernel.getHikernel().account.userId) { //用户自己
                iterator.remove()
                sortMemberList.add(meetingParticipantBean)
                continue
            }
            if (meetingParticipantBean.isHost.get()) { //主持人
                iterator.remove()
                sortMemberList.add(0, meetingParticipantBean)
                continue
            }
        }
        sortMemberList.addAll(memberList)
        return sortMemberList
    }

    /**
     * 通过原始数据获取排序好的数据（不能对原始数据列表进行增删）
     * 在共享时展示在顶部
     */
    private fun initSortMembersFromSourceForShare(sourceList: List<MeetingParticipantBean>?): MutableList<MeetingParticipantBean> {
        if (sourceList == null) {
            return arrayListOf()
        }
        val memberList = arrayListOf<MeetingParticipantBean>()
        memberList.addAll(sourceList) //copy原始数据副本
        val sortMemberList = arrayListOf<MeetingParticipantBean>()
        val iterator = memberList.iterator()
        while (iterator.hasNext()) {
            if (sortMemberList.size >= 2) {
                break
            }
            val meetingParticipantBean = iterator.next()
            val participantIdUserId = meetingParticipantBean.userId
            if (participantIdUserId == HiKernel.getHikernel().account.userId) { //用户自己
                iterator.remove()
                sortMemberList.add(0, meetingParticipantBean)
                continue
            }
            if (meetingParticipantBean.isShare.get()) { //共享人
                iterator.remove()
                sortMemberList.add(meetingParticipantBean)
                continue
            }
        }
        sortMemberList.addAll(memberList)
        return sortMemberList
    }

    private fun mergeSortMembersFromSourceForShare(
        sourceList: List<MeetingParticipantBean>?,
        shareList: List<MeetingParticipantBean>
    ): MutableList<MeetingParticipantBean> {
        val sortMemberList = initSortMembersFromSourceForShare(sourceList)
        val iterator = sortMemberList.iterator()
        val result = arrayListOf<MeetingParticipantBean>()
        while (iterator.hasNext()) {
            if (result.size < 2) {
                result.add(iterator.next())
                iterator.remove()
                continue
            }
            break
        }
        val leftList = LinkedHashMap<String, MeetingParticipantBean>()
        sortMemberList.forEach {
            leftList[it.userId] = it
        }
        for (i in shareList.indices) {
            val userId = shareList[i].userId
            leftList[userId]?.run {
                result.add(this)
                leftList.remove(userId)
            }
        }
        result.addAll(leftList.values)
        return result
    }


    /**
     * 展示本地视频预览
     */
    fun openLocalPreView(container: ViewGroup) {
        TLog.info(TAG, "openLocalPreView")
        val view: NebulaRtcView = mEngine.localPreviewVideoView
        if (view.parent != null) {
            (view.parent as ViewGroup).removeAllViews()
        }
        container.addView(view)
        mEngine.toLocalPreviewShowingState(view)
    }

    fun toLocalPreviewStopState() {
        mEngine.toLocalPreviewStopState()
    }

    fun toStopRemoteVideoView(userId: String) {
        mEngine.stopRemoteView(userId)
    }

    fun singleChatEndClick() {
        if (isConnected()) {
            MeetingPointUtils.pointMeetingUserLeaveStart(
                MeetingPointUtils.SOURCE_SINGLE,
                mEngine.roomId,
                mEngine.meetingRtcSyncInfo.totalTimes - mEngine.meetingNetSyncInfo.duration,
                getMeetingNetSyncInfo().confCode
            )
            mEngine.quitMeeting(
                "",
                if (isSingleChatRoom()) {
                    R.string.video_chat_over
                } else {
                    R.string.video_meeting_over
                }
            )
        } else {
            if (isMyRoom()) {
                mEngine.cancelVideo(R.string.video_chat_cancel.getResourceString())
            } else {
                mEngine.refuseVideo(R.string.video_chat_refuse.getResourceString())
                MeetingPointUtils.pointMeetingCallCancel(
                    getMeetingNetSyncInfo().linkType,
                    isMyRoom()
                )
            }
        }

    }

    fun videoSwitch() {
        mEngine.videoSwitch()
    }

    fun showShareOnWindow(userId: String, mLayoutShare: FrameLayout) {
        TLog.info(TAG, "showShareOnWindow onConfShareStart userid:${userId}")
        val view: NebulaRtcView = mEngine.shareVideoView
        if (view.parent != null) {
            (view.parent as ViewGroup).removeAllViews()
        }
        mLayoutShare.removeAllViews()
        mLayoutShare.addView(view)
        mEngine.stopRemoteSubStreamView(userId)
        mEngine.startRemoteSubStreamView(userId, view)
    }

    fun onRTCUserVideoAvailableInWindow(
        userId: String,
        available: Boolean,
        flVideoContainer: FrameLayout
    ) {
        TLog.info(TAG, "onRTCUserVideoAvailableInWindow userId = $userId available = $available ")
        try {
            val videoView: NebulaRtcView = mEngine.getVideoView(userId)
            if (available) {
                if (flVideoContainer.childCount > 0 && flVideoContainer.getChildAt(0) != videoView) {
                    flVideoContainer.removeAllViews()
                }
                if (flVideoContainer.childCount == 0) {
                    flVideoContainer.addView(videoView)
                }
                if (videoView is HiVideoMeetingView) {
                    videoView.startStream()
                }
            } else {
                flVideoContainer.removeAllViews()
                if (videoView is HiVideoMeetingView) {
                    videoView.stopStream()
                }
            }
        } catch (e: NumberFormatException) {
            TLog.info(TAG, "onRTCUserVideoAvailableInWindow ${e.message} ")
        }
    }


    fun singleCall() {
        TLog.info(TAG, "singleCall")
        mPageIndex.postValue(PAGE_INDEX_CALL)
        meetingData.singleCall()
    }

    fun singleWaiting() {
        TLog.info(TAG, "singleWaiting")
        mPageIndex.postValue(PAGE_INDEX_WAITING)
        meetingData.singleWaiting()
    }

    fun createMeeting() {
        TLog.info(TAG, "createMeeting")
        mPageIndex.postValue(PAGE_INDEX_CREATE)
        meetingData.createMeeting()
    }

    fun chatMeeting() {
        TLog.info(TAG, "chatMeeting")
        mPageIndex.postValue(PAGE_INDEX_MEETING)
        meetingData.chatMeeting()
    }

    fun chatMeetingWithoutUI() {
        TLog.info(TAG, "chatMeetingWithoutUI")
        meetingData.chatMeeting()
    }

    fun joinMeeting() {
        TLog.info(TAG, "joinMeeting")
        mPageIndex.postValue(PAGE_INDEX_JOIN)
        meetingData.joinMeeting()
    }

    fun showMeetingView() {
        if (mPageIndex.value != PAGE_INDEX_MEETING) {
            TLog.info(TAG, "showMeetingView")
            mPageIndex.postValue(PAGE_INDEX_MEETING)
            meetingData.currentStatus = meetingData.chatMeetingStatus
        }
    }

    /**
     * 暂时无法解决小米手机小窗切大窗时surfaceView造成的透明背景问题，采用延时加载兼容
     */
    fun showMeetingViewIfNeedDelay() {
        TLog.info(TAG, "showMeetingViewDelay")
        mHandler.postDelayed(
            {
                if (mPageIndex.value != PAGE_INDEX_MEETING) {
                    mPageIndex.postValue(PAGE_INDEX_MEETING)
                    meetingData.currentStatus = meetingData.chatMeetingStatus
                }
            }, if (QMUIDeviceHelper.isXiaomi()) {
                500
            } else {
                0
            }
        )
    }

    fun refreshLocal() {
        val ownUserId = HiKernel.getHikernel().account.userId
        getMeetingRtcSyncInfo().refreshMemberVideoAudio(
            ownUserId,
            null,
            true
        )
    }

    fun onRecordingToggleClick(pointKey: String) {
        //全局弹窗不用成员变量保存，防止内存泄漏
        TLog.info(TAG, "onRecordingToggleClicked")
        if (isHost()) {
            hostClickRecordingToggle(pointKey)
        } else {
            if (getMeetingRtcSyncInfo().isMeetingRecording.value != true) {
                mEngine.popupApplyRecordConfirmationDialog()
            }
        }
    }

    private fun hostClickRecordingToggle(pointKey: String) {
        if (getMeetingRtcSyncInfo().isMeetingRecording.value == true) {
            //结束录制
            mEngine.popupStopRecordConfirmationDialog(pointKey)
        } else {
            //开始录制
            mEngine.popupStartRecordConfirmationDialog()
        }
    }

    fun destroyExpiredDialogInRecording() {
        mEngine.destroyExpiredDialogInRecording()
    }

    fun destroyExpiredDialogAfterRecording() {
        mEngine.destroyExpiredDialogAfterRecording()
    }

    fun dealListMembersStreamType(members: List<MeetingParticipantBean>) {
        val pageSize = 9
        val pageLimit = 3
        val pages: MutableList<List<MeetingParticipantBean>> = arrayListOf()
        var page: MutableList<MeetingParticipantBean> = arrayListOf()
        members.forEachIndexed { index, meetingParticipantBean ->
            if (index % pageSize == 0 && page.size == pageSize) {
                pages.add(page)
                page = arrayListOf()
            }
            page.add(meetingParticipantBean)
        }
        if (page.isNotEmpty()) {
            pages.add(page)
        }
        pages.forEach { list ->
            val limit = list.size < pageLimit
            list.forEach {
                it.isUseBigStream = limit
                getMeetingRtcSyncInfo().getVideoView(it.userId.toString())?.run {
                    if (this is HiVideoMeetingView) {
                        this.setUseBigStream(limit)
                    }
                }
            }
        }

    }

    fun refreshInviteList() {
        mEngine.syncMeetingInvitationList()
    }

    fun dynamicAdapterShareView() {
        mEngine.onDynamicAdapterShareView(
            getMeetingRtcSyncInfo().shareScreenContentRatio.value ?: 1f
        )
    }

    fun invite(calledIds: String) {
        mEngine.invite(calledIds)
    }

    fun findDisableIds(): List<String> {
        val res = arrayListOf<String>()
        getMeetingRtcSyncInfo().memberList.value?.forEach {
            res.add(it.userId)
        }
        getMeetingRtcSyncInfo().inviteList.value?.filter { it.isCalling() }?.forEach {
            it.userId?.run { res.add(this) }
        }
        return res
    }

    fun checkGroupStatus(callback: GroupStatusCheckCallback) {
        GroupInfoHelper.optWithGroupStatusCheck(getChatId(), callback = callback)
    }

    fun registerAudioDeviceCallback(){
        mHeadsetManager.registerAudioDeviceCallback(object : HeadsetPlugListener {
            public override fun onHeadsetPlug(headsetType: Int) {
                getMeetingRtcSyncInfo().setHeadsetType(headsetType)
            }
        })
    }

    fun unregisterAudioDeviceCallback() {
        mHeadsetManager.unregisterAudioDeviceCallback()
    }

    /**
     * 释放视图
     */
    fun releaseVideoView() {
        mEngine.release()
    }

    //region ai分析流程
    private fun hostClickAiSummaryToggle(pointKey:String, activity: Activity, onClose: Runnable?) {
        if (getMeetingRtcSyncInfo().isMeetingAiSummary.value == true) {
            //结束ai分析
            mEngine.popupStopAiSummaryConfirmationDialog(pointKey, onClose)
        } else {
            //开始ai分析
            showSelectAISubjectDialog(activity, 1)
        }
    }

    fun onAiSummaryToggleClick(pointKey: String, activity: Activity, onClose: Runnable?) {
        //全局弹窗不用成员变量保存，防止内存泄漏
        TLog.info(TAG, "onAiSummaryToggleClick")
        if (isHost()) {
            hostClickAiSummaryToggle(pointKey, activity, onClose)
        } else {
            if (getMeetingRtcSyncInfo().isMeetingAiSummary.value != true) {
                mEngine.popupApplyAiSummaryConfirmationDialog()
            } else {
                ToastUtils.failure(R.string.meeting_only_host_can_stop_ai_summary.getResourceString())
            }
        }
    }

    fun updateMeetingSetting(notSummaryGuide: Int) {
        mEngine.updateAIGuide(notSummaryGuide)
    }

    fun showSelectAISubjectDialog(activity: Activity, source: Int) {
        mEngine.showSelectAISubjectDialog(activity, source) {}
    }

    fun getSelectSubject(subjectListBean: List<SubjectListBean>): SubjectListBean? {
        if (LList.isEmpty(subjectListBean)) {
            return null
        }

        for (subject in subjectListBean) {
            if (subject.defaultSelected) {
                return subject
            }
        }

        return null
    }
    //endregion
}