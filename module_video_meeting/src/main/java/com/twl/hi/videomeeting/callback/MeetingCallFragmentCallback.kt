package com.twl.hi.videomeeting.callback

/**
 * <AUTHOR>
 * @date 2022/08/02 15:49
 */
interface MeetingCallFragmentCallback {
    /**
     * 本地音频出入开关点击
     */
    fun onLocalAudioMuteClicked()

    /**
     * 挂断按钮点击
     */
    fun onEndCallClicked()

    /**
     * 切换到音频按钮点击
     */
    fun onChangeToAudioClicked()

    /**
     * 免提按钮点击
     */
    fun onChangeLoudSpeakerStatusClicked()

    /**
     * 切换到小窗
     */
    fun onStartVideoWindow()

    /**
     * 切换media
     */
    fun onToggleMedia()

}