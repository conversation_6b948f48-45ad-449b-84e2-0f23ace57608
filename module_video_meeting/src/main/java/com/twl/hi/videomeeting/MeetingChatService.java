package com.twl.hi.videomeeting;

import static lib.twl.common.ext.ViewExtKt.getStatusBarsHeightCache;

import android.animation.ValueAnimator;
import android.app.Service;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothHeadset;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.PixelFormat;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleService;
import androidx.lifecycle.Observer;

import com.sdk.nebulartc.view.NebulaRtcView;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.bindadapter.BasicBindingAdapters;
import com.twl.hi.basic.util.HeadsetPlugListener;
import com.twl.hi.basic.util.ThemeUtils;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.media.MediaConstants;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.videomeeting.bean.MeetingParticipantBean;
import com.twl.hi.videomeeting.viewmodel.MeetingViewModel;
import com.twl.http.error.ErrorReason;
import com.twl.utils.network.NetworkHelper;

import org.apache.commons.lang3.StringUtils;

import java.lang.ref.WeakReference;
import java.util.List;

import hi.kernel.Constants;
import hi.kernel.HiKernel;
import lib.twl.common.ext.KotlinExtKt;
import lib.twl.common.util.LList;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.util.ReceiverUtils;
import lib.twl.common.util.ToastUtils;

public class MeetingChatService extends LifecycleService {
    private static final String TAG = "Meeting--->MeetingChatService";
    public static final int STATUS_START = 0;
    public static final int STATUS_STOP = 1;


    private static final int S_VIDEO_CHAT_WINDOW_CLOSE = 2;

    private WindowManager mWindowManager;
    private WindowManager.LayoutParams wmParams;
    private LayoutInflater inflater;
    //浮动布局view
    private View mFloatingLayout;
    //容器父布局
    private MyHandler myHandler;
    private int mAudioWidth;
    private int mScreenWidth;
    private int mStatusBarHeight;
    private int mWidth;
    private int mRight;
    private int mMargin;
    private ValueAnimator valueAnimator;
    private int mValueStartX;
    private int mValueEndX;
    private int mAudioMargin;
    private View mAvatarView;
    private FrameLayout flVideoContainer;
    private ImageView mAudioView;
    private TextView mUserNameView;
    private FrameLayout mLayoutShareContainer;
    private FrameLayout mLayoutShare;

    private View mOfflineView;
    private View tvLeaveMeeting;
    private View tvReEnterMeeting;
    private RelativeLayout rlMain;
    private TextView tvHangUp;
    private View llUserInfo;
    private TextView tvCallingIn;
    private MeetingParticipantBean currentShowInfo;

    /**
     * 主持人
     */
    private ImageView mHostIc;
    private ImageView mShare;

    private MeetingViewModel mMeetingViewModel;

    @Override
    public void onCreate() {
        super.onCreate();
        mScreenWidth = QMUIDisplayHelper.getScreenWidth(this);
        mStatusBarHeight = getStatusBarsHeightCache();
        mAudioWidth = getResources().getDimensionPixelSize(R.dimen.video_meeting_window_audio_width);
        mAudioMargin = QMUIDisplayHelper.dp2px(this, 20);
    }

    private void initObserver() {
        if (mMeetingViewModel == null) {
            return;
        }

        mMeetingViewModel.getMeetingRtcSyncInfo().getSelfVideoOpen().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {

            }
        });

        mMeetingViewModel.getMeetingRtcSyncInfo().getMeetingQuit().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean finish) {
                if (finish != null && finish) {
                    if (mMeetingViewModel != null) {
                        mMeetingViewModel.getMeetingRtcSyncInfo().postMeetingQuit(false);
                    }
                    TLog.info(TAG, "getMeetingQuit " + this);
                    stopTips();
                    if (mMeetingViewModel != null) {
                        mMeetingViewModel.unregisterAudioDeviceCallback();
                    }
                }
            }
        });

        mMeetingViewModel.getPageIndex().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (integer == MeetingViewModel.PAGE_INDEX_MEETING) {
                    connectedFloatingChange();
                }
            }
        });

        mMeetingViewModel.getMeetingRtcSyncInfo().getMeetingKickOut().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    ToastUtils.ssd(R.string.video_meeting_kickout);
                    stopTips();
                }
            }
        });

        mMeetingViewModel.getMeetingRtcSyncInfo().getConfMutedAllAudio().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean audio) {
                if (audio != null) {
                    if (!audio) {
                        mAudioView.setImageResource(R.drawable.meeting_initiate_silence_open);
                    }
                }
            }
        });

        mMeetingViewModel.getMeetingRtcSyncInfo().getShareStatus().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                TLog.info(TAG, "getShareStatus status = " + integer);
                if (integer == null || mMeetingViewModel == null) {
                    return;
                }
                switch (integer) {
                    case MeetingRtcSyncInfo.SHARE_START:
                        String userId = mMeetingViewModel.getMeetingRtcSyncInfo().getTempConShareId();
                        if (userId != null && mMeetingViewModel.getMeetingRtcSyncInfo().isWindowShow()) {
                            mLayoutShareContainer.setVisibility(View.VISIBLE);
                            mMeetingViewModel.showShareOnWindow(userId, mLayoutShare);
                            setShareScreenMask("", false);
                            setPeerView(userId);
                        }
                        break;
                    case MeetingRtcSyncInfo.SHARE_PAUSE:
                        setShareScreenMask(getString(R.string.video_share_pause), true);
                        break;
                    case MeetingRtcSyncInfo.SHARE_RESUME:
                        setShareScreenMask("", false);
                        break;
                    case MeetingRtcSyncInfo.SHARE_STOP:
                        mAvatarView.setVisibility(View.VISIBLE);
                        setShareScreenMask("", false);
                        mLayoutShareContainer.setVisibility(View.GONE);
                        mLayoutShare.removeAllViews();
                        initPeerView();
                        break;
                    default:
                        break;
                }
            }
        });

        mMeetingViewModel.getMeetingRtcSyncInfo().setFirstVoiceVolumeId(null);
        mMeetingViewModel.getMeetingRtcSyncInfo().getFirstVoiceVolumeId().observe(this, new Observer<String>() {
            @Override
            public void onChanged(String userId) {
                if (userId == null || mMeetingViewModel == null || mMeetingViewModel.isSingleChatRoom()) {
                    return;
                }
                List<MeetingParticipantBean> member = mMeetingViewModel.getMeetingRtcSyncInfo().getMemberList().getValue();
                boolean isInList = false;
                for (MeetingParticipantBean bean : member) {
                    if (TextUtils.equals(bean.getUserId(), userId)) {
                        isInList = true;
                        break;
                    }
                }
                if (isInList) {
                    setPeerView(userId);
                }
            }
        });

        mMeetingViewModel.getMeetingRtcSyncInfo().getOfflineTimeOut().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    //清除小窗当前显示成员缓存，等重连时设置新的显示成员
                    currentShowInfo = null;
                    mOfflineView.setVisibility(View.VISIBLE);
                } else {
                    mOfflineView.setVisibility(View.GONE);
                }
            }
        });

        mMeetingViewModel.getMeetingRtcSyncInfo().setVideoChangedId(null);
        mMeetingViewModel.getMeetingRtcSyncInfo().getVideoChangedId().observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                if (currentShowInfo != null && TextUtils.equals(s, currentShowInfo.getUserId() + "")) {
                    showVideoOfTheSpoken(currentShowInfo);
                }
            }
        });

        mMeetingViewModel.getMeetingRtcSyncInfo().setAudioChangedId(null);
        mMeetingViewModel.getMeetingRtcSyncInfo().getAudioChangedId().observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                TLog.info(TAG, "getAudioChangedId userId = " + s + "  currentShowInfo " + currentShowInfo);
                if (currentShowInfo != null && TextUtils.equals(s, currentShowInfo.getUserId() + "")) {
                    mAudioView.setImageResource(currentShowInfo.isAudioOpen().get() ? R.drawable.meeting_initiate_silence_open : R.drawable.meeting_initiate_silence_close);
                }
            }
        });


        mMeetingViewModel.getMeetingRtcSyncInfo().setHostChangedId(null);
        mMeetingViewModel.getMeetingRtcSyncInfo().getHostChangedId().observe(this, new Observer<String>() {
            @Override
            public void onChanged(String hostId) {
                TLog.info(TAG, "getHostChangedId userId = " + hostId);
                if (currentShowInfo != null && hostId != null) {
                    mHostIc.setVisibility(TextUtils.equals(currentShowInfo.getUserId(), hostId) ? View.VISIBLE : View.GONE);
                } else {
                    if (currentShowInfo == null) {
                        initPeerView();
                    }
                }
            }
        });

        mMeetingViewModel.getMeetingRtcSyncInfo().setUserLeaveMeetingId(null);
        mMeetingViewModel.getMeetingRtcSyncInfo().getUserLeaveMeetingId().observe(this, new Observer<String>() {
            @Override
            public void onChanged(String hostId) {
                TLog.info(TAG, "getUserLeaveMeetingId userId = " + hostId);
                if (currentShowInfo != null && hostId != null && hostId.equals(currentShowInfo.getUserId())) {
                    if (currentShowInfo.getIsHost().get()) {
                        currentShowInfo = null;
                    } else {
                        initPeerView();
                    }
                }
            }
        });

        mMeetingViewModel.getMeetingRtcSyncInfo().getMeetingEntered().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean != null && aBoolean) {
                    switchSingleCallOrNormalView();
                    initPeerView();
                }
            }
        });

    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            int intExtra = intent.getIntExtra(Constants.STATUS, STATUS_START);
            if (intExtra == STATUS_START) {
                if (mMeetingViewModel == null) {
                    mMeetingViewModel = new MeetingViewModel();
                    mMeetingViewModel.getMeetingRtcSyncInfo().setWindowShow(true);
                    mWidth = mAudioWidth;
                    mMargin = mAudioMargin;
                    mRight = mScreenWidth - mWidth - mMargin;
                    initValueAnimator();
                    if (myHandler == null) {
                        myHandler = new MyHandler(this);
                    }
                    initWindow();//设置悬浮窗基本参数（位置、宽高等）
                    initFloating();//悬浮框点击事件的处理
                    initObserver();
                    setShareWindow();
//                    IntentFilter intentFilter = new IntentFilter();
//                    intentFilter.addAction(Intent.ACTION_HEADSET_PLUG);
//                    intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
//                    intentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
//                    intentFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
//                    intentFilter.addAction(BluetoothHeadset.ACTION_AUDIO_STATE_CHANGED);
//                    intentFilter.addAction(BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED);
//                    ReceiverUtils.registerSystem(this,
//                            intentFilter, mHeadsetPlugReceiver);
                }
            } else {
                if (mFloatingLayout != null) {
                    stopTips();
                }
            }
        }
        return super.onStartCommand(intent, flags, startId);
    }

    private void initValueAnimator() {
        if (valueAnimator == null) {
            valueAnimator = new ValueAnimator();
            valueAnimator.setDuration(200);
            valueAnimator.setFloatValues(0, 1);
            valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    if (wmParams != null) {
                        float value = (float) animation.getAnimatedValue();
                        wmParams.x = (int) (mValueStartX + (mValueEndX - mValueStartX) * value);
                        mWindowManager.updateViewLayout(mFloatingLayout, wmParams);
                    }
                }
            });
        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        super.onBind(intent);
        return new MyBinder();
    }

    public class MyBinder extends Binder {
        public MeetingChatService getService() {
            return MeetingChatService.this;
        }
    }

    /**
     * 设置悬浮框基本参数（位置、宽高等）
     */
    private void initWindow() {
        if (mWindowManager == null) {
            mWindowManager = (WindowManager) getApplicationContext().getSystemService(Context.WINDOW_SERVICE);
            //设置好悬浮窗的参数
            wmParams = getWindowParams();
            // 悬浮窗默认显示以左上角为起始坐标
            wmParams.gravity = Gravity.RIGHT | Gravity.TOP;
            //悬浮窗的开始位置，因为设置的是从右上角开始，所以屏幕左上角是x=screenWidth;y=0
            wmParams.x = mMargin;
            wmParams.y = QMUIDisplayHelper.dp2px(this, 143);
            wmParams.format = PixelFormat.RGBA_8888;
            inflater = LayoutInflater.from(getApplicationContext());
        }
    }


    private WindowManager.LayoutParams getWindowParams() {
        wmParams = new WindowManager.LayoutParams();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            wmParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            wmParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        wmParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH | WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON;

        //设置悬浮窗口长宽数据
        wmParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        wmParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        return wmParams;
    }


    private void connectedFloatingChange() {
        if (mMeetingViewModel != null && mMeetingViewModel.isSingleChatRoom()) {
            mAudioView.setVisibility(View.VISIBLE);
            Contact contact = mMeetingViewModel.getMeetingRtcSyncInfo().getContact();
            if (contact != null) {
                mUserNameView.setText(contact.getShowName(Contact.SHOW_NAME_SCENE_NAME));
            } else {
                mUserNameView.setText(R.string.video_chat_answer);
            }
        }
    }

    private void initFloating() {
        if (mFloatingLayout != null) {
            mFloatingLayout.setVisibility(View.GONE);
            mWindowManager.removeView(mFloatingLayout);
        }
        // 获取浮动窗口视图所在布局
        mFloatingLayout = inflater.inflate(R.layout.meeting_alert_float_audio_layout, null);
        mAvatarView = mFloatingLayout.findViewById(R.id.layout_avatar);
        flVideoContainer = mFloatingLayout.findViewById(R.id.fl_video_container);
        mUserNameView = mFloatingLayout.findViewById(R.id.tv_user_name);
        mAudioView = mFloatingLayout.findViewById(R.id.iv_audio);
        mLayoutShareContainer = mFloatingLayout.findViewById(R.id.fl_con_share);
        mLayoutShare = mFloatingLayout.findViewById(R.id.fl_con_video_share);
        mHostIc = mFloatingLayout.findViewById(R.id.iv_is_host);
        mShare = mFloatingLayout.findViewById(R.id.iv_share);
        mOfflineView = mFloatingLayout.findViewById(R.id.ll_offline);
        tvLeaveMeeting = mFloatingLayout.findViewById(R.id.tv_leave_meeting);
        tvReEnterMeeting = mFloatingLayout.findViewById(R.id.tv_reeneter_meeting);
        rlMain = mFloatingLayout.findViewById(R.id.rl_main);
        tvHangUp = mFloatingLayout.findViewById(R.id.tv_hang_up);
        llUserInfo = mFloatingLayout.findViewById(R.id.ll_user_info);
        tvCallingIn = mFloatingLayout.findViewById(R.id.tv_calling_in);
        // 添加悬浮窗的视图
        wmParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        wmParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        mWindowManager.addView(mFloatingLayout, wmParams);
        switchSingleCallOrNormalView();
        initPeerView();
        mFloatingLayout.setVisibility(View.VISIBLE);
        //悬浮框触摸事件，设置悬浮框可拖动
        mFloatingLayout.setOnTouchListener(new FloatingListener());
        //悬浮框点击事件
        mFloatingLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //在这里实现点击重新回到Activity
                stopTips();
                Intent intent = new Intent(MeetingChatService.this, MeetingActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
            }
        });

        tvLeaveMeeting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mMeetingViewModel != null) {
                    //防抖，只能点击一次，出错了会直接退出会议
                    tvLeaveMeeting.setClickable(false);
                    tvReEnterMeeting.setClickable(false);
                    mMeetingViewModel.leaveMeeting();
                }
            }
        });

        tvReEnterMeeting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NetworkHelper.INSTANCE.isAvailable()
                        && mMeetingViewModel != null) {
                    //防抖，只能点击一次，出错了会直接退出会议
                    tvLeaveMeeting.setClickable(false);
                    tvReEnterMeeting.setClickable(false);
                    mMeetingViewModel.checkMeeting(
                            true,
                            true
                    );
                } else {
                    ToastUtils.ssd(ErrorReason.ERROR_NETWORK_FAILED);
                }
            }
        });

        tvHangUp.setOnClickListener(v -> {
            if (mMeetingViewModel != null) {
                mMeetingViewModel.singleChatEndClick();
            }
        });
    }

    /**
     * 根据状态调整是单聊 接听/拨打的视图 还是普通的视图
     */
    private void switchSingleCallOrNormalView() {
        boolean showSingleView = mMeetingViewModel != null && mMeetingViewModel.isSingleChatRoom() && !mMeetingViewModel.getMeetingRtcSyncInfo().isConnected();
        ViewGroup.MarginLayoutParams rlMainParams = (ViewGroup.MarginLayoutParams) rlMain.getLayoutParams();
        ViewGroup.LayoutParams mFloatingLayoutLayoutParams = mFloatingLayout.getLayoutParams();
        int dp_4 = (int) KotlinExtKt.getDp(4);
        if (showSingleView) {
            mAudioView.setVisibility(View.GONE);
            rlMainParams.setMargins(dp_4, dp_4, dp_4, 0);
            rlMainParams.height = (int) KotlinExtKt.getDp(80);
            rlMain.setBackground(KotlinExtKt.getResourceDrawable(R.drawable.bg_corner_4_color_f3f4f5));
            mFloatingLayout.setBackground(KotlinExtKt.getResourceDrawable(R.drawable.bg_corner_6_color_white));
            tvHangUp.setVisibility(View.VISIBLE);
            mFloatingLayoutLayoutParams.height = (int) KotlinExtKt.getDp(112);
            llUserInfo.setVisibility(View.GONE);
            tvCallingIn.setVisibility(View.VISIBLE);
        } else {
            mAudioView.setVisibility(View.VISIBLE);
            rlMainParams.setMargins(0, 0, 0, 0);
            rlMainParams.height = (int) KotlinExtKt.getDp(88);
            rlMain.setBackground(KotlinExtKt.getResourceDrawable(R.drawable.bg_translucent));
            mFloatingLayout.setBackground(KotlinExtKt.getResourceDrawable(R.drawable.bg_corner_12_f3f4f5));
            tvHangUp.setVisibility(View.GONE);
            mFloatingLayoutLayoutParams.height = (int) KotlinExtKt.getDp(88);
            llUserInfo.setVisibility(View.VISIBLE);
            tvCallingIn.setVisibility(View.GONE);
        }
        mWindowManager.updateViewLayout(mFloatingLayout, mFloatingLayoutLayoutParams);
    }

    /**
     * 初始化peer
     */
    private void initPeerView() {
        TLog.info(TAG, "initPeerView");
        if (mMeetingViewModel == null) {
            return;
        }
        if (mMeetingViewModel.isSingleChatRoom()) {
            setPeerView(mMeetingViewModel.getPeerId());
        } else {
            setPeerView(mMeetingViewModel.getMeetingNetSyncInfo().hostId);
        }
    }

    //开始触控的坐标，移动时的坐标（相对于屏幕左上角的坐标）
    private int mTouchStartX, mTouchStartY, mTouchCurrentX, mTouchCurrentY;
    //开始时的坐标和结束时的坐标（相对于自身控件的坐标）
    private int mStartX, mStartY, mStopX, mStopY;
    //判断悬浮窗口是否移动，这里做个标记，防止移动后松手触发了点击事件
    private boolean isMove;

    private class FloatingListener implements View.OnTouchListener {

        @Override
        public boolean onTouch(View v, MotionEvent event) {
            //异常情况下wmParams可能为空，此时不处理onTouch
            if (wmParams == null) {
                return false;
            }
            int action = event.getAction();
            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    isMove = false;
                    mTouchStartX = (int) event.getRawX();
                    mTouchStartY = (int) event.getRawY();
                    mStartX = (int) event.getX();
                    mStartY = (int) event.getY();
                    break;
                case MotionEvent.ACTION_MOVE:
                    mTouchCurrentX = (int) event.getRawX();
                    mTouchCurrentY = (int) event.getRawY();
                    wmParams.x -= mTouchCurrentX - mTouchStartX;
                    wmParams.y += mTouchCurrentY - mTouchStartY;
                    if (wmParams.x < mMargin) {
                        wmParams.x = mMargin;
                    } else if (wmParams.x > mRight) {
                        wmParams.x = mRight;
                    }
//                    if (wmParams.y <= mStatusBarHeight) {
//                        wmParams.y = mStatusBarHeight;
//                    }
                    mWindowManager.updateViewLayout(mFloatingLayout, wmParams);
                    mTouchStartX = mTouchCurrentX;
                    mTouchStartY = mTouchCurrentY;
                    break;
                case MotionEvent.ACTION_UP:
                    mStopX = (int) event.getX();
                    mStopY = (int) event.getY();
                    if (Math.abs(mStartX - mStopX) >= 1 || Math.abs(mStartY - mStopY) >= 1) {
                        isMove = true;
                    }
                    if (isMove) {
                        if (wmParams.x != mMargin || mRight != wmParams.x) {
                            mValueStartX = wmParams.x;
                            mValueEndX = wmParams.x + mWidth / 2 <= mScreenWidth / 2 ? mMargin : mRight;
                            if (valueAnimator != null) {
                                valueAnimator.start();
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            //如果是移动事件不触发OnClick事件，防止移动的时候一放手形成点击事件
            return isMove;
        }
    }


    private class MyHandler extends Handler {
        WeakReference<Service> reference;

        public MyHandler(Service service) {
            reference = new WeakReference<>(service);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            if (null != reference) {
                MeetingChatService service = (MeetingChatService) reference.get();
                if (null != service) {
                    switch (msg.what) {
                        case S_VIDEO_CHAT_WINDOW_CLOSE:
                            stop();
                            break;
                    }

                }
            }
        }
    }

    public void stopTips() {
        if (valueAnimator != null) {
            valueAnimator.removeAllUpdateListeners();
            valueAnimator = null;
        }
        if (mMeetingViewModel != null) {
            mMeetingViewModel.getMeetingRtcSyncInfo().setWindowShow(false);
            mMeetingViewModel = null;
        }
        if (myHandler != null) {
            myHandler.sendEmptyMessage(S_VIDEO_CHAT_WINDOW_CLOSE);
        }
    }

    public void stop() {
//        ReceiverUtils.unregisterSystem(this, mHeadsetPlugReceiver);
        if (mFloatingLayout != null) {
            // 移除悬浮窗口
            mWindowManager.removeView(mFloatingLayout);
            mFloatingLayout = null;
            mWindowManager = null;
            wmParams = null;
        }
        mStartX = 0;
        mStartY = 0;
        mValueStartX = 0;
        mValueEndX = 0;
        if (myHandler != null) {
            myHandler.removeCallbacksAndMessages(null);
            myHandler = null;
        }
        stopSelf();
    }

//    private final HeadsetPlugManager.HeadsetPlugReceiver mHeadsetPlugReceiver = new HeadsetPlugManager.HeadsetPlugReceiver(new HeadsetPlugManager.HeadsetPlugReceiver.HeadsetPlugListener() {
//        @Override
//        public void onHeadsetPlug(int headsetType) {
//            if (mMeetingViewModel != null) {
//                mMeetingViewModel.getMeetingRtcSyncInfo().setHeadsetType(headsetType);
//            }
//        }
//    });


    /**
     * 展示正在说话的人的视频状态
     */
    private void showVideoOfTheSpoken(MeetingParticipantBean bean) {
        if (mMeetingViewModel == null) {
            return;
        }
        if (!TextUtils.isEmpty(mMeetingViewModel.getMeetingRtcSyncInfo().getConShareId().getValue())) {
            return;
        }
        try {
            String id = String.valueOf(bean.getUserId());
            boolean available = TextUtils.equals(bean.getUserId(), HiKernel.getHikernel().getAccount().getUserId()) ?
                    mMeetingViewModel.getMeetingRtcSyncInfo().getSelfVideoOpenStatus() : bean.isVideoOpen().get();
            mMeetingViewModel.onRTCUserVideoAvailableInWindow(id, available, flVideoContainer);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置展示人员信息
     */
    private void setPeerView(String userIdStr) {
        if (mMeetingViewModel == null) {
            return;
        }
        String conShareId = mMeetingViewModel.getMeetingRtcSyncInfo().getConShareId().getValue();
        if (StringUtils.isEmpty(userIdStr)
                || (currentShowInfo != null && TextUtils.equals(userIdStr, currentShowInfo.getUserId()))
                || (!TextUtils.isEmpty(conShareId) && !TextUtils.equals(conShareId, userIdStr))) {
            if (currentShowInfo != null) {
                mShare.setVisibility(currentShowInfo.getIsShare().get() ? View.VISIBLE : View.GONE);
            }
            return;
        }
        TLog.info(TAG, "setPeerView userIdStr = " + userIdStr);
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(userIdStr);
        if (contact != null) {
            mAudioView.setVisibility(View.VISIBLE);
            if (mAvatarView != null) {
                BasicBindingAdapters.setAvatar(mAvatarView, userIdStr);
                mUserNameView.setText(contact.getShowName(Contact.SHOW_NAME_SCENE_NAME));
                if (mMeetingViewModel.getMeetingRtcSyncInfo().isConnected()) {
                    List<MeetingParticipantBean> memberList = mMeetingViewModel.getMeetingRtcSyncInfo().getMemberList().getValue();
                    if (!LList.isEmpty(memberList)) {
                        for (int i = 0; i < memberList.size(); i++) {
                            MeetingParticipantBean bean = memberList.get(i);
                            if (TextUtils.equals(bean.getUserId(), userIdStr)) {
                                TLog.info(TAG, "currentShowInfo = " + bean);
                                currentShowInfo = bean;
                                mHostIc.setImageResource(ThemeUtils.useNewTheme ? R.drawable.meeting_ic_member_host_new : R.drawable.meeting_ic_member_host);
                                mHostIc.setVisibility(bean.getIsHost().get() ? View.VISIBLE : View.GONE);
                                mShare.setVisibility(bean.getIsShare().get() ? View.VISIBLE : View.GONE);
                                mAudioView.setImageResource(bean.isAudioOpen().get() ? R.drawable.meeting_initiate_silence_close : R.drawable.meeting_initiate_silence_open);
                                showVideoOfTheSpoken(bean);
                            } else {
                                //关闭除了此时展示的其他远端流，节省带宽。
                                if (!TextUtils.equals(bean.getUserId(), HiKernel.getHikernel().getAccount().getUserId())
                                        && bean.isVideoOpen().get()) {
                                    mMeetingViewModel.toStopRemoteVideoView(bean.getUserId());
                                }
                            }
                        }
                    }
                } else if (mMeetingViewModel.getMeetingNetSyncInfo().roomType == MediaConstants.MEETING_ROOM_TYPE_SINGLE) {
                    mAudioView.setImageResource(R.drawable.meeting_initiate_silence_close);
                }
            }
        }
    }

    //从全屏切到小窗之后对屏幕共享状态的处理
    private void setShareWindow() {
        if (mMeetingViewModel == null) {
            return;
        }
        String conShareId = mMeetingViewModel.getMeetingRtcSyncInfo().getConShareId().getValue();
        if (!TextUtils.isEmpty(conShareId)) {
            Log.i(TAG, "setShareWindow sharId :" + mMeetingViewModel.getMeetingRtcSyncInfo().getConShareId().getValue());
            NebulaRtcView view = mMeetingViewModel.getShareVideoView();
            if (view.getParent() != null) {
                ((ViewGroup) view.getParent()).removeAllViews();
            }
            mLayoutShare.addView(view);
            mMeetingViewModel.dynamicAdapterShareView();
            mLayoutShareContainer.setVisibility(View.VISIBLE);

            setShareScreenMask(getString(R.string.video_share_pause), mMeetingViewModel.getMeetingRtcSyncInfo().isConfSharePause());
            setPeerView(conShareId);
        }
    }

    /**
     * 共享屏幕显示蒙层
     */
    private void setShareScreenMask(String tips, boolean visible) {
        View mask = mFloatingLayout.findViewById(R.id.view_mask);
        ((TextView) mask.findViewById(R.id.tv_tips)).setText(tips);
        mask.setVisibility(visible ? View.VISIBLE : View.GONE);
    }
}
