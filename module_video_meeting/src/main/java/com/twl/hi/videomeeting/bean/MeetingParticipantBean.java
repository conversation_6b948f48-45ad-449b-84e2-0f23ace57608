package com.twl.hi.videomeeting.bean;

import android.text.TextUtils;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.media.MediaConstants;
import com.twl.hi.foundation.model.Contact;

import hi.kernel.HiKernel;

/**
 * <AUTHOR>
 * @date 2021/11/30.
 * 添加属性时需要更新updateFrom方法！！！！！
 */
public class MeetingParticipantBean {
    private String userId;//成员id
    private boolean useBigStream = true; //是否用大流
    private final ObservableField<String> userNameObservable = new ObservableField<>("");
    private ObservableBoolean audioOpen = new ObservableBoolean(false); //音频开启
    private ObservableBoolean videoOpen = new ObservableBoolean(false); //视频开启
    private ObservableBoolean isHost = new ObservableBoolean(false); //是否是主持人
    private ObservableBoolean isShare = new ObservableBoolean(false); //是否是共享人
    private ObservableBoolean isSpeaker = new ObservableBoolean(false); //是否是说话人

    private ObservableBoolean isHandsUp = new ObservableBoolean(false); //是否举手
    private ObservableInt audioVolume = new ObservableInt(0);
    private ObservableInt state = new ObservableInt(MediaConstants.MEETING_ROOM_STATE_ONLINE);
    /**
     * 0：普通用户 1: 会议室用户
     * 字段不存在代表普通用户，兼容老版本
     */
    private int confRole = ROLE_NORMAL;
    public static final int ROLE_NORMAL = 0;
    public static final int ROLE_MEETING_BOT = 1;

    /**
     * 本地搜索时展示的名字
     */
    private String searchShowName = "";

    private long version = 0L;

    public MeetingParticipantBean() {
    }

    public MeetingParticipantBean(String userId, boolean audioOpen, boolean videoOpen, int state, String hostId, int confRole) {
        this.userId = userId;
        this.audioOpen.set(audioOpen);
        this.videoOpen.set(videoOpen);
        this.state.set(state);
        this.isHost.set(TextUtils.equals(userId, hostId));
        this.confRole = confRole;
        updateDisplayName();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public void setUseBigStream(boolean useBigStream) {
        this.useBigStream = useBigStream;
    }

    public boolean isUseBigStream() {
        return useBigStream;
    }

    public ObservableBoolean isAudioOpen() {
        return audioOpen;
    }

    public void setAudioOpen(boolean audioOpen) {
        this.audioOpen.set(audioOpen);
    }

    public ObservableBoolean isVideoOpen() {
        return videoOpen;
    }

    public void setVideoOpen(boolean videoOpen) {
        this.videoOpen.set(videoOpen);
    }

    public ObservableInt getAudioVolume() {
        return audioVolume;
    }

    public void setAudioVolume(int audioVolume) {
        this.audioVolume.set(audioVolume);
    }

    public ObservableField<String> getDisplayName() {
        return userNameObservable;
    }

    public void updateDisplayName() {
        String name;

        if (isUserSelf()) {
            String myName = HiKernel.getHikernel().getAccount().getUserName();
            name = myName + " (我)";
        } else {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
            name = contact == null ? "未知用户" : contact.getShowName(Contact.SHOW_NAME_SCENE_NAME);
        }
        TLog.info("Meeting--->bean", super.toString() + " getDisplayName name = " + name);
        userNameObservable.set(name);
    }

    public void setSearchShowName(String searchShowName) {
        this.searchShowName = searchShowName;
    }

    public String getSearchShowName() {
        return searchShowName;
    }

    public ObservableInt getState() {
        return state;
    }

    public void setState(int state) {
        this.state.set(state);
    }

    public ObservableBoolean getIsHost() {
        return isHost;
    }

    public void setIsHost(boolean isHost) {
        this.isHost.set(isHost);
    }

    public ObservableBoolean getIsShare() {
        return isShare;
    }

    public void setIsShare(boolean isShare) {
        this.isShare.set(isShare);
    }

    public ObservableBoolean getIsSpeaker() {
        return isSpeaker;
    }

    public void setIsSpeaker(boolean isSpeaker) {
        this.isSpeaker.set(isSpeaker);
    }

    public ObservableBoolean getIsHandsUp() {
        return isHandsUp;
    }

    public void setIsHandsUp(boolean isHandsUp) {
        this.isHandsUp.set(isHandsUp);
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return " MeetingParticipantBean{" +
                " userId=" + userId +
                " state=" + state.get() +
                " videoOpen=" + videoOpen.get() +
                " audioOpen=" + audioOpen.get() +
                " isHost=" + isHost.get() +
                " isShare=" + isShare.get() +
                " isHandsUp=" + isHandsUp.get() +
                '}';
    }

    public boolean isUserSelf() {
        return TextUtils.equals(userId, HiKernel.getHikernel().getAccount().getUserId());
    }

    public MeetingParticipantBean updateFrom(MeetingParticipantBean origin) {
        setUserId(origin.userId);
        setAudioOpen(origin.audioOpen.get());
        setVideoOpen(origin.videoOpen.get());
        setIsHost(origin.getIsHost().get());
        setIsShare(origin.getIsShare().get());
        setIsSpeaker(origin.getIsSpeaker().get());
        setIsHandsUp(origin.getIsHandsUp().get());
        setVersion(origin.getVersion());
        setAudioVolume(origin.getAudioVolume().get());
        setState(origin.state.get());
        setConfRole(origin.confRole);
        return this;
    }
    public MeetingParticipantBean updateKeepHandsFrom(MeetingParticipantBean origin) {
        setUserId(origin.userId);
        setAudioOpen(origin.audioOpen.get());
        setVideoOpen(origin.videoOpen.get());
        setIsHost(origin.getIsHost().get());
        setIsShare(origin.getIsShare().get());
        setIsSpeaker(origin.getIsSpeaker().get());
        setAudioVolume(origin.getAudioVolume().get());
        setState(origin.state.get());
        setConfRole(origin.confRole);
        return this;
    }

    public int getConfRole() {
        return confRole;
    }

    public void setConfRole(int confRole) {
        this.confRole = confRole;
    }
}
