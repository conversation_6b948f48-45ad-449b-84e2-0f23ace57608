package com.twl.hi.videomeeting.api.request

import com.google.gson.annotations.Expose
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.http.client.BaseApiRequest
import com.twl.http.client.HttpResponse
import com.twl.http.config.RequestMethod

/**
 * <AUTHOR>
 * @date 2023/5/4
 * description: 拒绝呼叫邀请
 */
class MeetingInviteRejectRequest : BaseApiRequest<HttpResponse>() {

    @Expose
    @JvmField
    var roomId: String? = null

    override fun getUrl() = URLConfig.URL_MEETING_CALL_INVITE_REJECT

    override fun getMethod() = RequestMethod.POST
}