package com.twl.hi.videomeeting.api.request;

import static com.twl.hi.foundation.api.base.URLConfig.URL_VIDEO_MEETING_UNMUTE_MEMBER;

import com.google.gson.annotations.Expose;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

/**
 * @author: musa on 2022/4/12
 * @e-mail: <EMAIL>
 * @desc: 主持人解除群成员静音
 */
public class HostUnMuteTheMemberRequest extends BaseApiRequest<HttpResponse> {
    /**音视频会议房间号*/
    @Expose
    public String roomId;
    /**解除静音对象id*/
    @Expose
    public String unmuteUserId;

    @Override
    public String getUrl() {
        return URL_VIDEO_MEETING_UNMUTE_MEMBER;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
