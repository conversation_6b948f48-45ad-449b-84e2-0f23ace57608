package com.twl.hi.videomeeting.dialog;

import android.os.Bundle;
import android.text.TextUtils;

import com.twl.hi.basic.BottomSheetBehaviorBaseDialog;
import com.twl.hi.export.select.bean.SelectBaseParams;
import com.twl.hi.export.select.bean.SelectConversationParams;
import com.twl.hi.foundation.SendMessageContent;
import com.twl.hi.export.select.router.SelectPageRouter;
import com.twl.hi.basic.util.PointParamsUtils;
import com.twl.hi.videomeeting.BR;
import com.twl.hi.videomeeting.R;
import com.twl.hi.videomeeting.callback.MeetingTitleBottomCallback;
import com.twl.hi.videomeeting.databinding.MeetingLayoutMeetingTitleBottomBinding;
import com.twl.hi.videomeeting.utils.MeetingPointUtils;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import hi.kernel.RequestCodeConstants;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.base.BaseViewModel;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.CommonUtils;
import lib.twl.common.util.ToastUtils;

/**
 * @author: musa on 2022/3/30
 * @e-mail: <EMAIL>
 * @desc: 音视频会议底部用来显示完整title的弹窗
 */
public class MeetingTitleBottomDialog extends BottomSheetBehaviorBaseDialog<MeetingLayoutMeetingTitleBottomBinding, BaseViewModel> implements MeetingTitleBottomCallback {
    public static final String TAG = "MeetingTitleBottomDialog";
    /**
     * 使用activity的viewmodule
     */
    private String theme;
    private String code;

    public static MeetingTitleBottomDialog newInstance(String theme, String code) {
        Bundle args = new Bundle();
        args.putString(Constants.THEME, theme);
        args.putString(Constants.CONF_CODE, code);
        MeetingTitleBottomDialog fragment = new MeetingTitleBottomDialog();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public BaseViewModel getViewModel() {
        return null;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.meeting_layout_meeting_title_bottom;
    }

    @Override
    protected void initFragment() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            theme = arguments.getString(Constants.THEME);
            code = arguments.getString(Constants.CONF_CODE);
        } else {
            theme = "";
            code = "";
        }
        getDataBinding().setTheme(theme);
        getDataBinding().setCode(getShowConfCode(code));
        getDataBinding().notifyPropertyChanged(BR.theme);

    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return 0;
    }

    @Override
    public void onCopyClick() {
        CommonUtils.copyText(getContext(), theme + "\n" + "会议号：" + code);
        dismiss();
        ToastUtils.success("复制成功");
    }

    @Override
    public void onShareClick() {
        SelectConversationParams params = new SelectConversationParams().setSendMessageType(SendMessageContent.TYPE_GET_MULTI_CONVERSATION_IDS_TYPES)
                .setShowRightTitleVisible(SelectBaseParams.INVISIBLE)
                .<SelectConversationParams>setTitle(BaseApplication.getApplication().getResources().getString(R.string.select_contact))
                .setCreateGroupVisible(SelectBaseParams.INVISIBLE)
                .setShowFileHelper(SelectBaseParams.INVISIBLE)
                .setOtherVisible(SelectBaseParams.VISIBLE)
                .setMultiSelect(true);
        Bundle bundleSelect = new Bundle();
        bundleSelect.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        AppUtil.startUriForResult(getContext(), SelectPageRouter.SELECT_CONVERSATION_ACTIVITY, RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_MEETING_ENTER, bundleSelect, ActivityAnimType.UP_GLIDE);
        PointParamsUtils.push(PointParamsUtils.SOURCE_SHARE_MEETING, MeetingPointUtils.MEETING_INVITE_SOURCE_TITLE_SHARE);
        dismiss();
    }


    public String getShowConfCode(String confCode) {
        if (!TextUtils.isEmpty(confCode)) {
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < confCode.length(); i++) {
                if (i > 0 && i % 3 == 0) {
                    result.append(" ");
                }
                result.append(confCode.charAt(i));
            }
            return result.toString();
        } else {
            return "";
        }
    }
}
