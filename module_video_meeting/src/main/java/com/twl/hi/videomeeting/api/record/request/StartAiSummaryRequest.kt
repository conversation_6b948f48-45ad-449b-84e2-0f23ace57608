package com.twl.hi.videomeeting.api.record.request

import com.google.gson.annotations.Expose
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.http.callback.AbsRequestCallback
import com.twl.http.client.BaseApiRequest
import com.twl.http.client.HttpResponse
import com.twl.http.config.RequestMethod

/**
 *@desc: 主持人同意开启Ai总结
 */
class StartAiSummaryRequest(mCallback: AbsRequestCallback<HttpResponse>?) :
    BaseApiRequest<HttpResponse>(mCallback) {
    @Expose
    @JvmField
    var roomId = ""

    @Expose
    @JvmField
    var applyUserId = ""

    /**
     * 会议类型code，3.38及以后版本必填
     */
    @Expose
    @JvmField
    var subjectCode: String? = ""

    override fun getUrl() = URLConfig.URL_START_AI_SUMMARY

    override fun getMethod() = RequestMethod.POST

}