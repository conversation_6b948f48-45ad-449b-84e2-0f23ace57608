package com.twl.hi.videomeeting.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.response.MeetingCheckResponse;
import com.twl.hi.videomeeting.R;
import com.twl.hi.videomeeting.api.request.UpdateSubjectRequest;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;

import lib.twl.common.util.LText;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.util.ToastUtils;
import lib.twl.common.views.adapter.BaseQuickAdapter;
import lib.twl.common.views.adapter.BaseViewHolder;

/**
 * author: dubojun
 * date: 2025/5/19
 * description:
 **/
public class MeetingSettingsAdapter extends BaseQuickAdapter<MeetingCheckResponse.SubjectListBean, BaseViewHolder> {
    private TextView titleView;
    private ImageView selectView;

    private Context context;
    private String roomId;
    private MeetingCheckResponse.SubjectListBean selectItem;

    public MeetingSettingsAdapter(Context context, String roomId, MeetingCheckResponse.SubjectListBean selectItem) {
        super(R.layout.meeting_item_ai_model_setting);
        this.context = context;
        this.roomId = roomId;
        this.selectItem = selectItem;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, MeetingCheckResponse.SubjectListBean item) {
        if (item == null) {
            return;
        }

        titleView = helper.getView(R.id.tv_title);
        selectView = helper.getView(R.id.iv_select_view);

        titleView.setText(item.name);
        if (selectItem == null) {
            selectItem = item.defaultSelected ? item : null;
        }
        selectView.setVisibility(isSelect(item) ? View.VISIBLE : View.GONE);
        try {
            helper.getView(R.id.cl_item_root_view).setOnClickListener(view -> {
                selectItem = item;
                updateSubject(selectItem);
            });
        } catch (Exception e) {
            TLog.error(TAG, "error msg = %s", e);
        }

    }

    private boolean isSelect(MeetingCheckResponse.SubjectListBean item) {
        return selectItem != null && item != null && LText.equal(selectItem.code, item.code);
    }

    private void updateSubject(MeetingCheckResponse.SubjectListBean subjectCode) {
        if (subjectCode == null) {
            return;
        }

        UpdateSubjectRequest request = new UpdateSubjectRequest();
        request.setCallback(new BaseApiRequestCallback<HttpResponse>() {
            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                notifyDataSetChanged();
                ToastUtils.ssd(String.format("已切换成%s类型", subjectCode.name), QMUIDisplayHelper.dp2px(context, 137));
            }

            @Override
            public void onFailed(ErrorReason reason) {
                showFailed(reason);
            }
        });
        request.roomId = roomId;
        request.subjectCode = subjectCode.code;
        HttpExecutor.execute(request);
    }
}
