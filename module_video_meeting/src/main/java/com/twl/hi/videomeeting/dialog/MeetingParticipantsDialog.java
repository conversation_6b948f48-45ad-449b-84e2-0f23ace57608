package com.twl.hi.videomeeting.dialog;

import android.app.Dialog;
import android.content.DialogInterface;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.twl.hi.basic.BottomSheetBehaviorBaseDialog;
import com.twl.hi.export.select.bean.SelectBaseParams;
import com.twl.hi.export.select.bean.SelectContactsParams;
import com.twl.hi.export.select.bean.SelectConversationParams;
import com.twl.hi.foundation.SendMessageContent;
import com.twl.hi.basic.util.PointParamsUtils;
import com.twl.hi.export.select.router.SelectPageRouter;
import com.twl.hi.foundation.api.response.bean.MeetingInvitationListBean;
import com.twl.hi.videomeeting.BR;
import com.twl.hi.videomeeting.MeetingInviteListFragment;
import com.twl.hi.videomeeting.MeetingInviteLocalSearchFragment;
import com.twl.hi.videomeeting.MeetingMemberListFragment;
import com.twl.hi.videomeeting.R;
import com.twl.hi.videomeeting.callback.MeetingParticipantsCallback;
import com.twl.hi.videomeeting.databinding.MeetingFragmentMeetingParticipantsBinding;
import com.twl.hi.videomeeting.utils.MeetingPointUtils;
import com.twl.hi.videomeeting.view.MeetingSharePopupWindow;
import com.twl.hi.videomeeting.viewmodel.MeetingParticipantsViewModel;
import com.twl.hi.videomeeting.viewmodel.MeetingViewModel;
import com.twl.utils.SettingBuilder;

import java.util.ArrayList;
import java.util.List;

import hi.kernel.BundleConstants;
import hi.kernel.RequestCodeConstants;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.util.QMUIKeyboardHelper;

public class MeetingParticipantsDialog extends BottomSheetBehaviorBaseDialog<MeetingFragmentMeetingParticipantsBinding, MeetingParticipantsViewModel> implements MeetingParticipantsCallback {
    public static final String TAG = "MeetingParticipantsDialog";
    MeetingViewModel activityViewModel;
    private MeetingSharePopupWindow sharePop;
    private List<String> titles = new ArrayList<>();

    private boolean isScreenPortrait = true;

    public MeetingParticipantsDialog() {
    }

    public MeetingParticipantsDialog(MeetingViewModel activityViewModel, boolean isScreenPortrait) {
        this.activityViewModel = activityViewModel;
        this.isScreenPortrait = isScreenPortrait;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.meeting_fragment_meeting_participants;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return 0;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mDataBinding = DataBindingUtil.inflate(inflater, getContentLayoutId(), container, false);
        View view = mDataBinding.getRoot();
        //横屏下靠右
        if (!isScreenPortrait) {
            FrameLayout.LayoutParams horizontalLayoutParam = new FrameLayout.LayoutParams(QMUIDisplayHelper.dp2px(getContext(), 300),
                    ViewGroup.LayoutParams.MATCH_PARENT);
            horizontalLayoutParam.gravity = Gravity.END;
            view.setLayoutParams(horizontalLayoutParam);
        }
        initViewHolder();
        return view;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        // 设置监听器
        dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                // 按下back键时，关闭DialogFragment，并返回true
                if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_UP) {
                    if (QMUIKeyboardHelper.isKeyboardVisible(activity)) {
                        QMUIKeyboardHelper.hideKeyboard(getDataBinding().etInput);
                    } else if (getChildFragmentManager().findFragmentByTag(MeetingInviteLocalSearchFragment.class.getSimpleName()) != null) {
                        getChildFragmentManager().popBackStack();
                    } else {
                        dismiss();
                    }
                    return true;
                }

                // 返回false表示没有处理此事件，将事件传递给后续处理
                return false;
            }
        });
        dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        return dialog;
    }

    @Override
    protected void initFragment() {
        if (activityViewModel == null) {
            return;
        }
        initData();
        mDataBinding.setViewModel(activityViewModel);
        mDataBinding.viewPager.setAdapter(new FragmentStateAdapter(this) {
            @NonNull
            @Override
            public Fragment createFragment(int position) {
                if (position == 0) {
                    return new MeetingMemberListFragment();
                } else {
                    return new MeetingInviteListFragment(activityViewModel);
                }
            }

            @Override
            public int getItemCount() {
                return titles.size();
            }
        });
        new TabLayoutMediator(mDataBinding.tabLayout, mDataBinding.viewPager, new TabLayoutMediator.TabConfigurationStrategy() {
            @Override
            public void onConfigureTab(@NonNull TabLayout.Tab tab, int position) {
                int count;
                if (position == 0) {
                    Integer value = activityViewModel.getMeetingRtcSyncInfo().getMemberCount().getValue();
                    count = value == null ? 0 : value;
                } else {
                    List<MeetingInvitationListBean> value = activityViewModel.getMeetingRtcSyncInfo().getInviteList().getValue();
                    count = value == null ? 0 : value.size();
                }
                String countStr = count > 99 ? "99+" : count + "";
                String title = String.format(titles.get(position), countStr);
                tab.setText(title);
            }
        }).attach();

        mDataBinding.etInput.setHintTextColor(Color.parseColor("#818188"));

        activityViewModel.getMeetingRtcSyncInfo().getMemberCount().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer count) {
                String countStr = count > 99 ? "99+" : count + "";
                TabLayout.Tab tab = getDataBinding().tabLayout.getTabAt(0);
                if (tab != null) {
                    tab.setText(String.format(titles.get(0), countStr));
                }
            }
        });

        activityViewModel.getMeetingRtcSyncInfo().getInviteList().observe(this, new Observer<List<MeetingInvitationListBean>>() {
            @Override
            public void onChanged(List<MeetingInvitationListBean> list) {
                if (list != null) {
                    String countStr = list.size() > 99 ? "99+" : list.size() + "";
                    TabLayout.Tab tab = getDataBinding().tabLayout.getTabAt(1);
                    if (tab != null) {
                        tab.setText(String.format(titles.get(1), countStr));
                    }
                }
            }
        });
    }

    private void initData() {
        titles.clear();
        titles.add("会议中(%s)");
        titles.add("未进会(%s)");
    }

    @Override
    public int getBottomSheetLayoutParamsHeight() {
        return ViewGroup.LayoutParams.MATCH_PARENT;
    }

    @Override
    public void close() {
        dismiss();
    }

    @Override
    public void addParticipants() {
        if (sharePop == null) {
            sharePop = new MeetingSharePopupWindow(activity, new MeetingSharePopupWindow.OnItemClickListener() {
                @Override
                public void onItemClick(int position) {
                    if (position == 0) {
                        SelectContactsParams urgent = new SelectContactsParams()
                                .<SelectContactsParams>setTitle(getString(R.string.meeting_call_invite))
                                .<SelectContactsParams>setMultiSelect(true)
                                .<SelectContactsParams>setFromMeeting(true)
                                .setOnlyConversationContacts(true)
                                .<SelectContactsParams>setMaxSelectCount(SettingBuilder.getInstance().getShiningUserMax())
                                .setMaxSelectCount(50)
                                .setShowMultiConfirm(SelectBaseParams.VISIBLE)
                                .setExistIds(activityViewModel.findDisableIds());
                        Bundle bundle = new Bundle();
                        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, urgent);
                        AppUtil.startUriForResult(activity,
                                SelectPageRouter.SELECT_CONTACT_ACTIVITY,
                                RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_MEETING_INVITE,
                                bundle,
                                ActivityAnimType.UP_GLIDE);
                    } else {
                        SelectConversationParams params = new SelectConversationParams().setSendMessageType(SendMessageContent.TYPE_GET_MULTI_CONVERSATION_IDS_TYPES)
                                .setShowRightTitleVisible(SelectBaseParams.INVISIBLE)
                                .setOtherVisible(SelectBaseParams.VISIBLE)
                                .<SelectConversationParams>setTitle(getString(R.string.select_contact))
                                .setCreateGroupVisible(SelectBaseParams.INVISIBLE)
                                .setShowFileHelper(SelectBaseParams.INVISIBLE)
                                .setMaxSelectCount(50)
                                .setMultiSelect(true)
                                .setFromMeeting(true);
                        Bundle bundleSelect = new Bundle();
                        bundleSelect.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
                        AppUtil.startUriForResult(activity,
                                SelectPageRouter.SELECT_CONVERSATION_ACTIVITY,
                                RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_MEETING_ENTER,
                                bundleSelect,
                                ActivityAnimType.UP_GLIDE);
                    }

                    PointParamsUtils.push(PointParamsUtils.SOURCE_SHARE_MEETING, MeetingPointUtils.MEETING_INVITE_SOURCE_TOP_SHARE);
                }
            });
        }
        sharePop.showAsDropDown(getDataBinding().ivAddParticipants, -QMUIDisplayHelper.dpToPx(72), -QMUIDisplayHelper.dpToPx(10));
    }

    @Override
    public void doSearch() {
        getChildFragmentManager()
                .beginTransaction()
                .add(R.id.cl_content, new MeetingInviteLocalSearchFragment(), MeetingInviteLocalSearchFragment.class.getSimpleName())
                .addToBackStack(null)
                .commitAllowingStateLoss();
    }

}