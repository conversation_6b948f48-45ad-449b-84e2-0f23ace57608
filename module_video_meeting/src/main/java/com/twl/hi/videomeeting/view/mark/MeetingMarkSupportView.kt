package com.twl.hi.videomeeting.view.mark

import com.twl.hi.videomeeting.api.response.MeetingBoardVersionResponse
import com.twl.hi.videomeeting.bean.MeetingMarkPathData

/**
 * <AUTHOR>
 * @date 2023/11/3
 * description:
 */
interface MeetingMarkSupportView {

    fun setUserInfo(userId: String, userName: String)

    fun addPathData(data: List<MeetingMarkPathData>)

    fun rollback(actionIdCallback: (Long, String) -> Unit)

    fun clear()

    fun onExit()

    fun setDrawMode(paintType: Int, color: Int)

    fun getShareWidth(): Int

    fun getShareHeight(): Int

    fun updateIds(actionId: List<Long>?, cid: Long, userId: String)

    fun setBoardInfo(info: MeetingBoardVersionResponse)

    fun suitOnResize(width: Int, height: Int)

    fun isReady(): Boolean

}