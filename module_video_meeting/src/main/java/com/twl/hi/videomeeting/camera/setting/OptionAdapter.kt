package com.twl.hi.videomeeting.camera.setting

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import com.twl.hi.basic.adapter.DataBoundListAdapter
import com.twl.hi.basic.adapter.DataBoundViewHolder
import com.twl.hi.videomeeting.databinding.MeetingItemCameraBackgroundSettingBinding
import kotlin.math.log

/**
 *@author: musa on 2024/4/25
 *@e-mail: <EMAIL>
 *@desc: 相机设置选项列表适配器
 */
class OptionAdapter(private var clickListener: (OptionBean) -> Unit) : DataBoundListAdapter<OptionBean, MeetingItemCameraBackgroundSettingBinding>(
    object : DiffUtil.ItemCallback<OptionBean>() {
        override fun areItemsTheSame(oldItem: OptionBean, newItem: OptionBean): Boolean {
            return oldItem.tabId == newItem.tabId && oldItem.optionId == newItem.optionId
        }

        override fun areContentsTheSame(oldItem: OptionBean, newItem: OptionBean): Boolean {
            return (oldItem == newItem)
        }

    }
) {

    override fun createBinding(parent: ViewGroup?, viewType: Int): MeetingItemCameraBackgroundSettingBinding =
        MeetingItemCameraBackgroundSettingBinding.inflate(LayoutInflater.from(parent!!.context), parent, false)

    override fun initViewHolder(holder: DataBoundViewHolder<MeetingItemCameraBackgroundSettingBinding>) {
        holder.databinding.rootView.setOnClickListener{
            clickListener(getItem(holder.bindingAdapterPosition))
        }
    }

    override fun bind(vdb: MeetingItemCameraBackgroundSettingBinding?, item: OptionBean?, position: Int) {
        if (vdb == null || item == null) return
        vdb.ivSetting.setImageResource(if (item.isChosen) item.iconChosen else item.iconNormal)
        vdb.tvName.text = item.name

        if (item is BeautyFilterSubOption) {
            vdb.ivLabel.visibility = if (item.canShowLabel) View.VISIBLE else View.GONE
        } else {
            vdb.ivLabel.visibility = View.GONE
        }
    }

}