package com.twl.hi.videomeeting.adapter;

import android.graphics.Outline;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.library.baseAdapters.BR;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.videomeeting.bean.MeetingParticipantBean;
import com.twl.hi.videomeeting.bindadapter.MeetingBindingAdapters;
import com.twl.hi.videomeeting.callback.MeetingEngine;
import com.twl.hi.videomeeting.databinding.MeetingItemEnterMeetingParticipantsBinding;
import com.twl.hi.videomeeting.view.HiVideoMeetingView;
import com.twl.utils.StringUtils;

import java.util.List;

import hi.kernel.HiKernel;
import lib.twl.common.adapter.BaseDataBindingViewHolder;
import lib.twl.common.util.Utils;


/**
 *
 */
public class MeetingMainAdapter extends ListAdapter<MeetingParticipantBean, BaseDataBindingViewHolder<MeetingItemEnterMeetingParticipantsBinding>> {
    public static final String TAG = "Meeting--->MeetingMainAdapter";
    private final MeetingEngine mEngine;
    private final Object mCallback;

    public MeetingMainAdapter(MeetingEngine meetingEngine, Object callback) {
        super(new DiffUtil.ItemCallback<MeetingParticipantBean>() {
            @Override
            public boolean areItemsTheSame(@NonNull MeetingParticipantBean oldItem, @NonNull MeetingParticipantBean newItem) {
                return StringUtils.isEquals(oldItem.getUserId(), newItem.getUserId());
            }

            @Override
            public boolean areContentsTheSame(@NonNull MeetingParticipantBean oldItem, @NonNull MeetingParticipantBean newItem) {
                return true;
            }
        });
        mEngine = meetingEngine;
        mCallback = callback;
        registerAdapterDataObserver(new RecyclerView.AdapterDataObserver() {
            @Override
            public void onChanged() {
                super.onChanged();
                Log.e(TAG, "onChanged");
            }

            @Override
            public void onItemRangeChanged(int positionStart, int itemCount) {
                super.onItemRangeChanged(positionStart, itemCount);
                Log.e(TAG, "onItemRangeChanged");
            }

            @Override
            public void onItemRangeChanged(int positionStart, int itemCount, @Nullable Object payload) {
                super.onItemRangeChanged(positionStart, itemCount, payload);
                Log.e(TAG, "onItemRangeChanged");
            }

            @Override
            public void onItemRangeInserted(int positionStart, int itemCount) {
                super.onItemRangeInserted(positionStart, itemCount);
                Log.e(TAG, "onItemRangeInserted positionStart : " + positionStart + " itemCount : " + itemCount);
            }

            @Override
            public void onItemRangeRemoved(int positionStart, int itemCount) {
                super.onItemRangeRemoved(positionStart, itemCount);
                Log.e(TAG, "onItemRangeRemoved");
            }

            @Override
            public void onItemRangeMoved(int fromPosition, int toPosition, int itemCount) {
                super.onItemRangeMoved(fromPosition, toPosition, itemCount);
                Log.e(TAG, "onItemRangeMoved");
            }
        });
    }

    @NonNull
    @Override
    public BaseDataBindingViewHolder<MeetingItemEnterMeetingParticipantsBinding> onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        TLog.info(TAG, "onCreateViewHolder");
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        MeetingItemEnterMeetingParticipantsBinding binding = MeetingItemEnterMeetingParticipantsBinding.inflate(inflater, parent, false);
        binding.layoutAvatar.setVisibility(View.GONE);
        return new BaseDataBindingViewHolder<>(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseDataBindingViewHolder<MeetingItemEnterMeetingParticipantsBinding> holder, int position) {
        MeetingParticipantBean bean = getItem(position);
        TLog.info(TAG, "onBindViewHolder position = " + position + " bean = " + bean.toString());
        holder.getDataBinding().setBean(bean);
        holder.getDataBinding().setEngine(mEngine);
        holder.getDataBinding().setVariable(BR.callback, mCallback);
        FrameLayout container = holder.getDataBinding().flVideoContainer;
        container.setTag(bean.getUserId());
        container.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                int left = 0;
                int top = 0;
                int right = container.getMeasuredWidth();
                int bottom = container.getMeasuredHeight();
                if (right != 0 && bottom != 0) {
                    Rect selfRect = new Rect(left, top, right, bottom);
                    outline.setRoundRect(selfRect, Utils.dp2px(8));
                }
            }
        });
        container.setClipToOutline(true);
    }

    @Override
    public void submitList(@Nullable List<MeetingParticipantBean> list) {
        super.submitList(list);
        TLog.info(TAG, list.toString());
    }

    @Override
    public void onViewAttachedToWindow(@NonNull BaseDataBindingViewHolder<MeetingItemEnterMeetingParticipantsBinding> holder) {
        super.onViewAttachedToWindow(holder);
        int position = holder.getBindingAdapterPosition();
        TLog.info(TAG, "onViewAttachedToWindow position : " + position);
        ViewGroup viewGroup = holder.getDataBinding().flVideoContainer;
        if (position >= 0 && position < getItemCount()) {
            MeetingParticipantBean item = getItem(position);
            MeetingBindingAdapters.onChangeVideoStatus(viewGroup, item.isVideoOpen().get(), item.getUserId(), item.isUseBigStream(), mEngine);
        }
    }

    @Override
    public void onViewDetachedFromWindow(@NonNull BaseDataBindingViewHolder<MeetingItemEnterMeetingParticipantsBinding> holder) {
        super.onViewDetachedFromWindow(holder);
        TLog.info(TAG, "onViewDetachedFromWindow position : " + holder.getBindingAdapterPosition());
        ViewGroup viewGroup = holder.getDataBinding().flVideoContainer;
        if (viewGroup.getChildCount() == 1) {
            View child = viewGroup.getChildAt(0);
            if (child instanceof HiVideoMeetingView) {
                HiVideoMeetingView hiVideoMeetingView = (HiVideoMeetingView) child;
                TLog.info(TAG, "onViewDetachedFromWindow userId : " + hiVideoMeetingView.getUserId());
                if (!TextUtils.equals(hiVideoMeetingView.getUserId(), HiKernel.getHikernel().getAccount().getUserId() + "")) {
                    hiVideoMeetingView.stopStream();
                    viewGroup.removeView(child);
                }
            }
        }
    }
}
