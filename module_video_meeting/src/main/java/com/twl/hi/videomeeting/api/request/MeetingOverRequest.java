package com.twl.hi.videomeeting.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

public class MeetingOverRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String roomId;

    public MeetingOverRequest(BaseApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_MEETING_CLOSE;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
