package com.twl.hi.videomeeting.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

/**
 * <AUTHOR>
 * @date 2021/12/3.
 */
public class MeetingForwardRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String roomId;//房间 id
    @Expose
    public String chatIds;//聊天 id ，多个英文逗号分隔
    @Expose
    public String chatTypes;//聊天 id 对应聊天类型 ，顺序保持一致，多个英文逗号分隔 1 单聊  2 群聊

    public MeetingForwardRequest(BaseApiRequestCallback<HttpResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_MEETING_FORWARD;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
