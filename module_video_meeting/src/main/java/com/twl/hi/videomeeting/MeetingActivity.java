package com.twl.hi.videomeeting;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothHeadset;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Process;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;

import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.dialog.DialogUtils;
import com.twl.hi.basic.dialog.PermissionDialogUtil;
import com.twl.hi.basic.helpers.AppPageRouterHelper;
import com.twl.hi.basic.util.HeadsetPlugListener;
import com.twl.hi.export.video.meeting.router.VideoAndAudioPageRouter;
import com.twl.hi.export.video.meeting.service.VideoMeetingEntranceHelper;
import com.twl.hi.foundation.logic.SecurityService;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.media.IMeetingDialogService;
import com.twl.hi.foundation.media.MediaConstants;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.model.message.MessageForAction;
import com.twl.hi.foundation.model.security.SilenceForbidModuleEnum;
import com.twl.hi.foundation.utils.GroupStatusCheckCallback;
import com.twl.hi.videomeeting.databinding.MeetingMeetingActivityMeetingLayoutBinding;
import com.twl.hi.videomeeting.helpers.VideoMeetingEntranceHelperImpl;
import com.twl.hi.videomeeting.service.MeetingForegroundService;
import com.twl.hi.videomeeting.utils.MeetingPointUtils;
import com.twl.hi.videomeeting.viewmodel.MeetingViewModel;
import com.twl.utils.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import hi.kernel.HiKernel;
import hi.kernel.RequestCodeConstants;
import lib.twl.common.activity.BaseVMActivity;
import lib.twl.common.base.BaseViewModel;
import lib.twl.common.permission.PermissionAvoidManager;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.CommonUtils;
import lib.twl.common.util.LList;
import lib.twl.common.util.ReceiverUtils;
import lib.twl.common.util.TimeDifferenceUtil;
import lib.twl.common.util.TimeTag;
import lib.twl.common.util.ToastUtils;

/**
 * 音视频会议页面，包含从发起到结束的逻辑
 */
@RouterUri(path = VideoAndAudioPageRouter.MEETING_ACTIVITY)
public class MeetingActivity extends BaseVMActivity<MeetingMeetingActivityMeetingLayoutBinding, BaseViewModel> {
    private static final String TAG = "Meeting--->MeetingActivity";
    String[] permissionsAudio = new String[]{Manifest.permission.MODIFY_AUDIO_SETTINGS, Manifest.permission.RECORD_AUDIO};

    String[] permissionsCamera = new String[]{Manifest.permission.CAMERA};
    PermissionAvoidManager manager;

    private MeetingViewModel mViewModel;

    private DialogFragment meetingInvitationDialog;

    private VideoMeetingEntranceHelperImpl.Strategy strategy;

    private String[] mBlueToothPermission;
    private boolean needRequestPermissionOnResume = false;

    @Override
    public int getContentLayoutId() {
        return R.layout.meeting_meeting_activity_meeting_layout;
    }

    @Override
    public int getCallbackVariable() {
        return 0;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return 0;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        TLog.info(TAG, "MeetingActivity onCreate");
        mViewModel = new MeetingViewModel();
        registerSecurityBusinessModule();
//        registerHeadsetPlug();
        mViewModel.registerAudioDeviceCallback();
        initObservers();
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        setStatusBarDarkMode();
        stopVideoService();
        setPermissionAvoidManager();
    }

    /**
     * 在安全平台模块注册关联业务
     */
    private void registerSecurityBusinessModule() {
        SecurityService securityService = ServiceManager.getInstance().getSecurityService();
        if (securityService != null) {
            securityService.registerBusinessModule(this, SilenceForbidModuleEnum.VIDEO);
        }
    }

    private void initView() {
        MeetingRtcSyncInfo meetingRtcSyncInfo = mViewModel.getMeetingRtcSyncInfo();
        boolean hasAudio = ActivityCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED;
        boolean hasCamera = ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED;
        if (!hasAudio) {
            meetingRtcSyncInfo.setSelfAudioOpen(false);
        }
        if (!hasCamera) {
            meetingRtcSyncInfo.setSelfVideoOpen(false);
        }
        doAfterPermissionCheck();
    }

    private void initObservers() {
        // 监听ProgressBar状态
        mViewModel.getShowProgressBar().observe(this, s -> {
            if (s == null) {
                dismissProgressDialog();
            } else {
                showProgressDialog(s, true);
                setProgressDialogCanceledOnTouchOutside(false);
                setOnProgressDialogCancelListener((dialog) -> {
                    mViewModel.leaveMeeting();
                });
            }
        });

        mViewModel.getPageIndex().observe(this, pageIndex -> {
            if (pageIndex != null) {
                switchFragment(pageIndex);
            }
        });

        mViewModel.getMeetingRtcSyncInfo().getMeetingQuit().observe(this, finish -> {
            TLog.info(TAG, "getMeetingQuit finish ： " + finish);
            if (finish != null && finish) {
                mViewModel.getMeetingRtcSyncInfo().postMeetingQuit(false);
                mViewModel.stopCalcTimes();
                mViewModel.setCurrentConfCode(null);
                mViewModel.checkGroupStatus(new GroupStatusCheckCallback() {
                    @Override
                    public void onStatusAbnormal() {
                        AppPageRouterHelper.backToMainTabActivity(MeetingActivity.this);
                    }

                    @Override
                    public void onStatusNormal() {
                        AppUtil.finishActivity(MeetingActivity.this, ActivityAnimType.UP_GLIDE);
                    }
                });
                mViewModel.unregisterAudioDeviceCallback();
            }

        });

        mViewModel.getMeetingRtcSyncInfo().getAudioChange().observe(this, o -> {
            //headsetType 或者 audioOutputIsSpeaker 发生变化,二者决定了扬声器如何显示，这里只是触发MediatorLiveData
        });

        mViewModel.getMeetingRtcSyncInfo().setMeetingEntered(false);
        mViewModel.getMeetingRtcSyncInfo().getMeetingEntered().observe(this, aBoolean -> {
            if (aBoolean != null && aBoolean) {
                mViewModel.hideShowProgressBar();
                mViewModel.showMeetingView();
                startForegroundService();
                mViewModel.setCurrentConfCode(mViewModel.getMeetingNetSyncInfo().confCode);
                ServiceManager.getInstance().getMeetingService().tryToNext();
            }
        });


        ServiceManager.getInstance().getMeetingService().getCurrentInvitation().observe(this, meetingInvitationBean -> {
            if (meetingInvitationBean == null) {
                if (meetingInvitationDialog != null) {
                    meetingInvitationDialog.dismiss();
                }
            } else {
                TLog.info(TAG, meetingInvitationBean.toString());
                if (TextUtils.equals(meetingInvitationBean.getRoomId(), mViewModel.getRoomId())) {
                    ServiceManager.getInstance().getMeetingService().clearShow();
                    ServiceManager.getInstance().getMeetingService().tryToNext();
                } else {
                    meetingInvitationDialog = Router.getService(IMeetingDialogService.class, MediaConstants.MEETING_DIALOG_KEY).createMeetingInvitationDialog(meetingInvitationBean);
                    meetingInvitationDialog.show(getSupportFragmentManager(), MediaConstants.MEETING_DIALOG_KEY);
                }
            }
        });

        ServiceManager.getInstance().getMessageService().getVideoInvitation().observe(this, new Observer<MessageForAction>() {
            @Override
            public void onChanged(@Nullable MessageForAction messageForAction) {
                try {
                    if (messageForAction != null) {
                        ServiceManager.getInstance().getMessageService().setVideoInvitation(null);
                        if (!messageForAction.isSendByMe()) {
                            MessageForAction.VideoChatActionInfo videoChatActionInfo = (MessageForAction.VideoChatActionInfo) messageForAction.getBody();
                            String roomId = videoChatActionInfo.getRoomId();
                            Router.getService(VideoMeetingEntranceHelper.class, VideoAndAudioPageRouter.SERVICE_VIDEO_MEETING_ENTRANCE).joinVideoMeeting(MeetingActivity.this, roomId, messageForAction.getSender().getSenderId(), null);
                        }
                    }
                } catch (Exception e) {

                }
            }
        });
        //安全禁令监听
        SecurityService securityService = ServiceManager.getInstance().getSecurityService();
        if(securityService != null){
            securityService.addProhibitionIsAdapterEvent().observe(this, aBoolean -> {
                if (!securityService.isNotClosedBusiness(SilenceForbidModuleEnum.VIDEO)) {
                    mViewModel.leaveMeeting();
                }
            });
        }

    }

    private void initStartData() {
        final Intent intent = getIntent();
        strategy = (VideoMeetingEntranceHelperImpl.Strategy) intent.getSerializableExtra(BundleConstants.BUNDLE_MEETING_STRATEGY);
        if (!mViewModel.getMeetingRtcSyncInfo().isEngineReady()) {
            mViewModel.setInitialMeetingInfo(strategy);
        }

        String initiateId = "";
        if (mViewModel.isSingleChatRoom()) {
            initiateId = Optional.ofNullable(strategy).map(VideoMeetingEntranceHelperImpl.Strategy::getPeerId).orElse("");
            if (StringUtils.isNotEmpty(initiateId)) {
                mViewModel.setPeerId(initiateId);
                mViewModel.setContactId(initiateId);
            }
        } else {
            initiateId = Optional.ofNullable(strategy).map(VideoMeetingEntranceHelperImpl.Strategy::getChatId).orElse("");
            if (StringUtils.isNotEmpty(initiateId)) {
                mViewModel.setSelectIds("" + initiateId);
                mViewModel.setSelectTypes("" + MessageConstants.MSG_GROUP_CHAT);
                mViewModel.setCreateMeetingBtnText(getResources().getString(R.string.video_start_meeting_with_select_num, 1));
            }
            mViewModel.setPeerId(HiKernel.getHikernel().getAccount().getUserId());
            mViewModel.setContactId(HiKernel.getHikernel().getAccount().getUserId());
            mViewModel.setChatId(initiateId);
        }
        if (strategy != null) {
            mViewModel.getMeetingRtcSyncInfo().setSelfVideoOpen(strategy.getVideoStats());
            mViewModel.getMeetingRtcSyncInfo().setSelfAudioOpen(strategy.getAudioStatus());
        }

        ArrayList<String> initiateSelectAddIds = new ArrayList<>();
        initiateSelectAddIds.add(initiateId);
        mViewModel.getMeetingRtcSyncInfo().setAddedIds(initiateSelectAddIds);
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    /**
     * 开启悬浮Video服务
     */
    private void startVideoService() {
        TLog.info(TAG, "startVideoService");
        //开启服务显示悬浮框
        Intent serviceVideoIntent = new Intent(this, MeetingChatService.class);
        serviceVideoIntent.putExtra(Constants.STATUS, MeetingChatService.STATUS_START);
        startService(serviceVideoIntent);
        AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
    }

    private void stopVideoService() {
        TLog.info(TAG, "stopVideoService");
        //开启服务显示悬浮框
        mViewModel.unStashVideoView();
        Intent serviceVideoIntent = new Intent(this, MeetingChatService.class);
        serviceVideoIntent.putExtra(Constants.STATUS, MeetingChatService.STATUS_STOP);
        startService(serviceVideoIntent);
    }


//    private final HeadsetPlugManager.HeadsetPlugReceiver mHeadsetPlugReceiver = new HeadsetPlugManager.HeadsetPlugReceiver(new HeadsetPlugManager.HeadsetPlugReceiver.HeadsetPlugListener() {
//        @Override
//        public void onHeadsetPlug(int headsetType) {
//            mViewModel.getMeetingRtcSyncInfo().setHeadsetType(headsetType);
//        }
//    });

//    private void registerHeadsetPlug() {
//        IntentFilter intentFilter = new IntentFilter();
//        intentFilter.addAction(Intent.ACTION_HEADSET_PLUG);
//        intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
//        intentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
//        intentFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
//        intentFilter.addAction(BluetoothHeadset.ACTION_AUDIO_STATE_CHANGED);
//        intentFilter.addAction(BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED);
//        ReceiverUtils.registerSystem(this, intentFilter, mHeadsetPlugReceiver);
//    }

    @Override
    protected void onResume() {
        super.onResume();
        //防止用户手动关闭通知
        startForegroundService();
        if (needRequestPermissionOnResume) {
            needRequestPermissionOnResume = false;
            setPermissionAvoidManager();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        //防止用户手动关闭通知
        startForegroundService();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        MeetingPointUtils.pointMeetingEndFinish();
        MeetingPointUtils.pointMeetingUserLeaveFinish();
        mViewModel.hideShowProgressBar();
//        ReceiverUtils.unregisterSystem(this, mHeadsetPlugReceiver);
        mViewModel.unStashVideoView();

        // 清空之前添加的参会人信息
        mViewModel.setSelectIds("");
        mViewModel.getMeetingRtcSyncInfo().setAddedIds(new ArrayList<>());
        mViewModel.setSelectTypes("");
        mViewModel.setCreateMeetingBtnText(getResources().getString(R.string.video_start_meeting_with_select_num, 0));

        if (CommonUtils.canDrawOverlays(this) && !TextUtils.isEmpty(mViewModel.getCurrentConfCode())) {
            mViewModel.getMeetingRtcSyncInfo().preShareMarkStatus =
                    Boolean.TRUE.equals(mViewModel.getMeetingRtcSyncInfo().getShowShareMarkMode().getValue());
            startVideoService();
        }
    }

    private void startForegroundService() {
        if (mViewModel.isConnected()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {//8.0后才支持
                startForegroundService(new Intent(this, MeetingForegroundService.class));
                TLog.info(TAG, "startForegroundService");
            } else {
                startService(new Intent(this, MeetingForegroundService.class));
                TLog.info(TAG, "startService");
            }
        }
    }

    /**
     * 请求视频会议所需权限
     */
    public void setPermissionAvoidManager() {
        if (manager == null) {
            manager = new PermissionAvoidManager(this);
        }
        requestMicrophonePermission(()-> {
            requestCameraPermission(()-> requestBluetoothPermission(()-> {
                if (mViewModel.isConnected()) {
                    mViewModel.setCurrentConfCode(mViewModel.getMeetingNetSyncInfo().confCode);
                    mViewModel.showMeetingViewIfNeedDelay();
                } else {
                    initStartData();
                    initView();
                }
            }));
        });
    }

    @SuppressLint("NewApi")
    private void requestMicrophonePermission(Runnable next) {
        manager.requestPermission(permissionsAudio, (hasPermission, shouldShowAllRequestPermissionRationale) -> {
            mViewModel.getMeetingRtcSyncInfo().setHasAudioPermission(hasPermission);
            if (hasPermission) {
                Optional.ofNullable(next).ifPresent(Runnable::run);
            } else {
                showMicrophonePermissionFailDialog(next);
            }
        });
    }

    private void showMicrophonePermissionFailDialog(Runnable next) {
        PermissionDialogUtil.showMicrophoneFailedDialog(this, () -> {
            next.run();
            return null;
        }, () -> {
            AppPageRouterHelper.jumpToAppSettingsPage(this);
            needRequestPermissionOnResume = true;
            return null;
        });
    }

    @SuppressLint("NewApi")
    public void requestCameraPermission(Runnable next) {
        manager.requestPermission(permissionsCamera, (hasPermission, shouldShowAllRequestPermissionRationale) -> {
            if (hasPermission) {
                Optional.ofNullable(next).ifPresent(Runnable::run);
            } else {
                showCameraPermissionFailDialog(next);
            }
            TLog.debug(TAG, "shouldShowAllRequestPermissionRationale " + shouldShowAllRequestPermissionRationale + "相机权限" + hasPermission + ":" + Arrays.toString(permissionsCamera));
        });
    }

    private void showCameraPermissionFailDialog(Runnable next) {
        PermissionDialogUtil.showCameraFailedDialog(this, () -> {
            next.run();
            return null;
        }, () -> {
            AppPageRouterHelper.jumpToAppSettingsPage(this);
            needRequestPermissionOnResume = true;
            return null;
        });
    }

    private void doAfterPermissionCheck() {
        if (mViewModel.getMeetingRtcSyncInfo().isEngineReady()) {
            mViewModel.getMeetingRtcSyncInfo().setWindowShow(false);
        }
        /*单聊拨打流程
        * 1.是单聊房间
        * 2.是我的创建的房间
        * 3.并且confCode是为空 (如果confCode不为空说明是重新入会，不走拨打流程，走加入会议流程) */
        if (mViewModel.isSingleChatRoom() && mViewModel.isMyRoom() && TextUtils.isEmpty(mViewModel.getMeetingNetSyncInfo().confCode)) {
            mViewModel.singleCall();
        } else {
            if (!TextUtils.isEmpty(mViewModel.getMeetingNetSyncInfo().confCode)) {
                if (!mViewModel.isConnected()) {
                    mViewModel.setShowProgressBar();
                }
                //如果是系统启动meetingActivity，需要开启计时
                if (!TimeDifferenceUtil.getInstance().hasStarted(TimeTag.VIDEO_MEETING_ENTER)) {
                    TimeDifferenceUtil.getInstance().start(TimeTag.VIDEO_MEETING_ENTER);
                }
                mViewModel.chatMeeting();
            } else {
                String meetingNumberCode = Optional.ofNullable(strategy).map(VideoMeetingEntranceHelperImpl.Strategy::getMeetingNumberCode).orElse(null);
                if (meetingNumberCode == null) {
                    mViewModel.createMeeting();
                } else {
                    mViewModel.joinMeeting();
                }
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {

            if (mViewModel.getPageIndex().getValue() != null &&
                    mViewModel.getPageIndex().getValue() == MeetingViewModel.PAGE_INDEX_CALL) {
                handleExitMeeting();
                return false;
            }

            int backStackEntryCount = getSupportFragmentManager().getBackStackEntryCount();
            if (backStackEntryCount > 0) {
                getSupportFragmentManager().popBackStack();
                return true;
            }

            if (!mViewModel.getMeetingRtcSyncInfo().isConnected()) {
                mViewModel.releaseVideoView();//未入会退出的时候需要释放单例中存储的videoView
                mViewModel.clearData();
                AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
            } else {
                handleExitMeeting();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 处理页面退出逻辑，退出页面后需要展示小窗，先申请悬浮窗权限
     */
    private void handleExitMeeting() {
        if (!CommonUtils.canDrawOverlays(this)) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + getPackageName()));
            startActivityForResult(intent, RequestCodeConstants.REQUEST_CODE_OVERLAY_PERMISSION);
        } else {
            AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != Activity.RESULT_OK) {
            return;
        }
        switch (requestCode) {
            case RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_MEETING_INITIATE:
                mViewModel.setSelectIds(data.getStringExtra(BundleConstants.BUNDLE_SELECT_IDS));
                mViewModel.setSelectTypes(data.getStringExtra(BundleConstants.BUNDLE_SELECT_TYPES));
                mViewModel.setCreateMeetingBtnText(getResources().getString(R.string.video_start_meeting_with_select_num, data.getIntExtra(BundleConstants.BUNDLE_SELECT_NUM, 0)));
                ArrayList<String> addIds = (ArrayList<String>) data.getSerializableExtra(BundleConstants.BUNDLE_SELECT_ID_LIST);
                if (addIds != null) {
                    mViewModel.getMeetingRtcSyncInfo().setAddedIds(addIds);
                }
                break;
            case RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_MEETING_ENTER:
                mViewModel.setSelectIds(data.getStringExtra(BundleConstants.BUNDLE_SELECT_IDS));
                mViewModel.setSelectTypes(data.getStringExtra(BundleConstants.BUNDLE_SELECT_TYPES));
                mViewModel.inviteMeetingPerson();
                break;
            case RequestCodeConstants.REQUEST_CODE_SELECT_CONVERSATION_MEETING_INVITE:
                ArrayList<String> extra = (ArrayList<String>) data.getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE);
                if (!LList.isEmpty(extra)) {
                    StringBuilder sb = new StringBuilder();
                    List<String> existIds = mViewModel.findDisableIds();
                    for (String s : extra) {
                        if (!existIds.contains(s)) {
                            if (sb.length() > 0) {
                                sb.append(Constants.INSIDE_INTERVAL);
                            }
                            sb.append(s);
                        }
                    }
                    String ids = sb.toString();
                    if (!TextUtils.isEmpty(ids)) {
                        if (ids.split(Constants.INSIDE_INTERVAL).length > Constants.MAX_INVITE_NUMBER) {
                            ToastUtils.failure("每次最多呼叫" + Constants.MAX_INVITE_NUMBER + "人");
                        } else {
                            mViewModel.invite(ids);
                        }
                    }
                }
                break;
            default:
                break;
        }
    }

    public MeetingViewModel getNewViewModel() {
        return mViewModel;
    }

    private void switchFragment(int pageIndex) {
        Fragment fragment;
        switch (pageIndex) {
            case MeetingViewModel.PAGE_INDEX_CALL:
                fragment = MeetingCallFragment.newInstance();
                break;
            case MeetingViewModel.PAGE_INDEX_WAITING:
                fragment = MeetingReceiverFragment.newInstance();
                break;
            case MeetingViewModel.PAGE_INDEX_CREATE:
                fragment = MeetingCreateFragment.newInstance();
                break;
            case MeetingViewModel.PAGE_INDEX_MEETING:
                fragment = MeetingMainFragment.newInstance();
                break;
            case MeetingViewModel.PAGE_INDEX_JOIN:
                fragment = MeetingJoinFragment.newInstance();
                break;
            default:
                return;
        }
        getSupportFragmentManager().beginTransaction().replace(R.id.activityMain, fragment).commitAllowingStateLoss();
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION);
        } else {
            getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        }
        mViewModel.dynamicAdapterShareView();
    }

    public String[] getBlueToothPermission() {
        if (mBlueToothPermission == null) {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                mBlueToothPermission = new String[]{Manifest.permission.BLUETOOTH_CONNECT};
            } else {
                mBlueToothPermission = new String[]{};
            }
        }
        return mBlueToothPermission;
    }

    public void requestBluetoothPermission(Runnable next) {
        new PermissionAvoidManager(this).requestPermission(getBlueToothPermission(), (hasPermission, shouldShowAllRequestPermissionRationale) -> {
            if (hasPermission) {
                Optional.ofNullable(next).ifPresent(Runnable::run);
            } else {
                showBluetoothFailDialog(next);
            }
            TLog.debug(TAG, "shouldShowAllRequestPermissionRationale " + shouldShowAllRequestPermissionRationale + "蓝牙权限" + hasPermission + ":" + Arrays.toString(mBlueToothPermission));
        });
    }

    private void showBluetoothFailDialog(Runnable next) {
        PermissionDialogUtil.showBluetoothFailedDialog(this, () -> {
            next.run();
            return null;
        }, () -> {
            next.run();
            return null;
        }, () -> {
            AppPageRouterHelper.jumpToAppSettingsPage(this);
            needRequestPermissionOnResume = true;
            return null;
        });
    }
}
