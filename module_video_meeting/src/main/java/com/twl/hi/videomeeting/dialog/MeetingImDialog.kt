package com.twl.hi.videomeeting.dialog

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.FrameLayout
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener
import com.twl.hi.basic.BaseBottomDialogFragment
import com.twl.hi.basic.views.LengthNoticeFilter
import com.twl.hi.basic.views.group.LinearLayoutManagerWrapper
import com.twl.hi.videomeeting.BR
import com.twl.hi.videomeeting.R
import com.twl.hi.videomeeting.bean.MeetingShowMessage
import com.twl.hi.videomeeting.callback.MeetingImDialogCallback
import com.twl.hi.videomeeting.databinding.MeetingDialogMeetingImBinding
import com.twl.hi.videomeeting.helpers.MeetingImHelper
import com.twl.hi.videomeeting.viewmodel.MeetingImDialogViewModel
import com.twl.mms.simple.SimpleMqttClient
import com.twl.utils.StringUtils.isEmpty
import hi.kernel.HiKernel
import lib.twl.common.adapter.CommonAdapter
import lib.twl.common.util.LList
import lib.twl.common.util.QMUIDisplayHelper
import lib.twl.common.util.QMUIKeyboardHelper
import lib.twl.common.util.ToastUtils
import org.eclipse.paho.client.mqttv3.internal.wire.MqttPubAck
import kotlin.math.min

/**
 * <AUTHOR>
 * @date 2023/11/30
 * description:
 */
class MeetingImDialog :
    BaseBottomDialogFragment<MeetingDialogMeetingImBinding, MeetingImDialogViewModel>(),
    MeetingImDialogCallback {

    companion object {
        const val TAG = "meeting--->MeetingImDialog"
        private const val ARGUMENT_IS_SCREEN_PORTRAIT = "argumentIsScreenPortrait"

        fun newInstance(isScreenPortrait: Boolean): MeetingImDialog {
            return MeetingImDialog().apply {
                arguments = Bundle().apply {
                    putBoolean(ARGUMENT_IS_SCREEN_PORTRAIT, isScreenPortrait)
                }
            }
        }
    }

    var onDismissListener: (() -> Unit)? = null

    override fun getContentLayoutId() = R.layout.meeting_dialog_meeting_im

    override fun getCallbackVariable() = BR.callback

    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel

    var isFirstIn = true

    private val imAdapter = object :
        CommonAdapter<MeetingShowMessage>(object : DiffUtil.ItemCallback<MeetingShowMessage>() {
            override fun areItemsTheSame(
                oldItem: MeetingShowMessage,
                newItem: MeetingShowMessage
            ) = oldItem.id == newItem.id

            override fun areContentsTheSame(
                oldItem: MeetingShowMessage,
                newItem: MeetingShowMessage
            ): Boolean {
                return TextUtils.equals(oldItem.content, newItem.content)
                        && TextUtils.equals(oldItem.sender, newItem.sender)
                        && oldItem.time == newItem.time
            }
        }) {
        val TYPE_RECEIVE = 0
        val TYPE_SEND = 1

        override fun getItemViewType(position: Int): Int {
            val item = getItem(position)
            item?.run {
                return if (TextUtils.equals(HiKernel.getHikernel().account.userId, this.sender)) {
                    TYPE_SEND
                } else {
                    TYPE_RECEIVE
                }
            }
            return TYPE_RECEIVE
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CustomViewHolder {
            val binding = DataBindingUtil.inflate<ViewDataBinding>(
                LayoutInflater.from(parent.context), if (viewType == TYPE_RECEIVE) {
                    R.layout.meeting_item_im_receive
                } else {
                    R.layout.meeting_item_im_send
                }, parent, false
            )
            return CustomViewHolder(binding)
        }
    }.apply { setVariableId(BR.data) }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        view?.run {
            if (arguments?.getBoolean(ARGUMENT_IS_SCREEN_PORTRAIT) == false) {
                val layoutParams = FrameLayout.LayoutParams(
                    QMUIDisplayHelper.dp2px(
                        context, 300
                    ),
                    min(
                        QMUIDisplayHelper.getScreenHeight(activity),
                        QMUIDisplayHelper.getScreenWidth(activity)
                    ) - 50
                )
                layoutParams.gravity = Gravity.END
                this.layoutParams = layoutParams
                dataBinding.tvTitle.gravity = Gravity.LEFT
            } else {
                val layoutParams = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    QMUIDisplayHelper.dp2px(
                        context, 450
                    )
                )
                this.layoutParams = layoutParams
                dataBinding.tvTitle.gravity = Gravity.CENTER
            }
        }
        return view
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initFragment() {
        dataBinding.ivClose.setOnClickListener { dismiss() }
        dataBinding.revEdit.apply {
            setHintTextColor(Color.parseColor("#858A99"))
            filters = arrayOf(LengthNoticeFilter(500))
            inputType = EditorInfo.TYPE_CLASS_TEXT
            imeOptions = EditorInfo.IME_ACTION_SEND
            setOnEditorActionListener { v, actionId, _ ->
                if (actionId == EditorInfo.IME_ACTION_SEND && !isEmpty(v.text.toString())) {
                    MeetingImHelper.sendTextMessage(dataBinding.revEdit.text.toString().trim(),
                        object : SimpleMqttClient.OnMessageSendListener {
                            override fun onSendSuccess(ack: MqttPubAck?) {
                                dataBinding.revEdit.setText("")
                            }

                            override fun onSendFailed() {
                                ToastUtils.failure("发送失败")
                            }
                        })
                    true
                } else {
                    false
                }
            }
        }
        dataBinding.rvImContent.apply {
            layoutManager =
                LinearLayoutManagerWrapper(activity)
            adapter = imAdapter
        }

        MeetingImHelper.meetingShowMessage.observe(viewLifecycleOwner) {
            dataBinding.srlImContent.finishRefresh()
            if (LList.isEmpty(it)) {
                dataBinding.srlImContent.visibility = View.INVISIBLE
                dataBinding.tvImEmpty.visibility = View.VISIBLE
            } else {
                dataBinding.srlImContent.visibility = View.VISIBLE
                dataBinding.tvImEmpty.visibility = View.INVISIBLE
                val completelyVisibleItemPosition =
                    (dataBinding.rvImContent.layoutManager as LinearLayoutManager).findLastCompletelyVisibleItemPosition()
                //已经处于最底部时来信消息需要自动滑动到最底部
                val needToBottom = completelyVisibleItemPosition == imAdapter.itemCount - 1
                imAdapter.submitList(it)
                dataBinding.srlImContent.setEnableRefresh(it[0].seq > SimpleMqttClient.MIN_MESSAGE_SEQ)

                dataBinding.rvImContent.postDelayed({
                    if (isFirstIn) {
                        isFirstIn = false
                        dataBinding.rvImContent.scrollToPosition(it.size - 1)
                    }
                    if (needToBottom) {
                        dataBinding.rvImContent.smoothScrollToPosition(it.size - 1)
                    }
                }, 100)
            }
        }

        dataBinding.srlImContent.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                MeetingImHelper.loadBefore()
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {

            }
        })

        dataBinding.revEdit.setOnTouchListener { v, event ->
            if(event.action == MotionEvent.ACTION_DOWN){
                dataBinding.rvImContent.apply {
                    scrollToPosition(imAdapter.itemCount - 1)
                }
            }
            false
        }
        dataBinding.rvImContent.setOnTouchListener { v, event ->
            if(event.action == MotionEvent.ACTION_DOWN){
                QMUIKeyboardHelper.hideKeyboard(dataBinding.revEdit)
            }
            false
        }

    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDismissListener?.invoke()
    }

}