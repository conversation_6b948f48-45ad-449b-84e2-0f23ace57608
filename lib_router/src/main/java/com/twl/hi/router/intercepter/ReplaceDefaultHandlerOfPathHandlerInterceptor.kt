package com.twl.hi.router.intercepter

import com.sankuai.waimai.router.Router
import com.sankuai.waimai.router.common.DefaultRootUriHandler
import com.sankuai.waimai.router.core.UriCallback
import com.sankuai.waimai.router.core.UriHandler
import com.sankuai.waimai.router.core.UriInterceptor
import com.sankuai.waimai.router.core.UriRequest
import com.twl.hi.router.handler.HiRootUriHandler

/**
 *@author: musa on 2023/7/13
 *@e-mail: <EMAIL>
 *@desc: 这里主要做的东西是替换 WMRouter原来的默认处理器[com.sankuai.waimai.router.common.NotFoundHandler]
 *  为什么要在拦截器里做这个事，是因为WMRouter的路由表初始化是懒加载的，第一次调用的时候才会初始化而且我们必须在初始化之后，才能找到对应节点[com.sankuai.waimai.router.common.PathHandler]
 *  去设置/替换默认处理器。
 *  使用的时候需要手动将这个拦截器添加到[com.sankuai.waimai.router.common.UriAnnotationHandler]的拦截器列表中。
 *  替换的维度是scheme+host，对应了一个PathHandler。
 */
class ReplaceDefaultHandlerOfPathHandlerInterceptor(private val scheme: String, private val host: String, private val defaultHandler: UriHandler) : UriInterceptor {
    /**
     * 执行过替换的标记
     */
    var done = false

    override fun intercept(request: UriRequest, callback: UriCallback) {
        if (done) {
            callback.onNext()
            return
        }
        //找到对应的PathHandler
        val uriAnnotationHandler = (Router.getRootHandler() as HiRootUriHandler).getUriAnnotationHandler()
        val targetPathHandler = uriAnnotationHandler.getPathHandler(scheme, host)
        //执行替换
        targetPathHandler.setDefaultChildHandler(defaultHandler)
        done = true
        callback.onNext()
    }
}