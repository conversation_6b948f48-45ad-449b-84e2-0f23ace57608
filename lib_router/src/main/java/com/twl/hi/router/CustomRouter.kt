package com.twl.hi.router

import android.net.Uri
import android.text.TextUtils
import com.twl.utils.URLUtils

object CustomRouter {
    const val INTERNAL_SCHEME = "private"
    const val INTERNAL_HOST = "bosshi.app"
    const val INTERNAL_SCHEME_HOST_PREFIX = "$INTERNAL_SCHEME://$INTERNAL_HOST"

    const val PATH_TO_CONTACT = "/local/contact"
    const val PATH_TO_IMAGE_PREVIEW = "/local/image/preview"

    @JvmStatic
    fun routeToContact(name: String, id: String, type: Int) = Uri.Builder()
        .scheme(INTERNAL_SCHEME)
        .authority(INTERNAL_HOST)
        .path(PATH_TO_CONTACT)
        .appendQueryParameter("name", name)
        .appendQueryParameter("id", id)
        .appendQueryParameter("type", type.toString())
        .build()
        .toString()

    @JvmStatic
    fun String.paramsOfContactUrl(): Triple<String, String, Int> {
        val param = URLUtils.parseUrlParams(this)
        val name: String = param["name"] ?: ""
        val id: String = param["id"] ?: ""
        val type: String = param["type"] ?: "0"
        if (TextUtils.isDigitsOnly(type)) {
            return Triple(name, id, type.toInt())
        }
        return Triple("", "", 0)
    }

    fun routeToImagePreview(url: String) = Uri.Builder()
        .scheme(INTERNAL_SCHEME)
        .authority(INTERNAL_HOST)
        .path(PATH_TO_IMAGE_PREVIEW)
        .appendQueryParameter("url", url)
        .build()
        .toString()

    @JvmStatic
    fun String.destinationOfImagePreview(): String {
        val param = URLUtils.parseUrlParams(this)
        return param["url"] ?: return this
    }
}