package com.twl.debug.sqlite.server.handler

import com.twl.debug.sqlite.db.DBManage
import com.twl.debug.sqlite.server.base.IRouteHandler
import com.twl.debug.sqlite.util.LogUtil
import java.io.ByteArrayOutputStream
import java.io.FileInputStream

/**
 * <AUTHOR>
 * 处理 downloadDB() 请求
 *
 * 下载数据库文件
 */
class DownloadDBHandler : IRouteHandler {

	override fun handle(path: String): ByteArray? {
		var response: ByteArray? = null

		var input: FileInputStream? = null
		var baos: ByteArrayOutputStream? = null
		try {
			DBManage.get().getCurrentDBFile()
				?.let { file ->
					input = FileInputStream(file)
					baos = ByteArrayOutputStream()
					val buf = ByteArray(1024 * 8)
					var len = 0
					while (input?.read(buf)?.also { len = it } != -1) {
						baos?.write(buf, 0, len)
					}
					baos?.flush()

					response = baos?.toByteArray()
				}
		} catch (e: Exception) {
			LogUtil.error("DownloadDBHandler handle() error! ${e.message}")
		} finally {
			try {
				input?.close()
			} catch (_: Exception) {
			} finally {
				input = null
			}
			try {
				baos?.close()
			} catch (_: Exception) {
			} finally {
				baos = null
			}
		}

		return response
	}
}