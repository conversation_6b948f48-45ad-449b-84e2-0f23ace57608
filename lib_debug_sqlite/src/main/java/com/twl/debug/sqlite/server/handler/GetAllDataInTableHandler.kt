package com.twl.debug.sqlite.server.handler

import android.database.Cursor
import android.net.Uri
import com.twl.debug.sqlite.db.DBManage
import com.twl.debug.sqlite.db.base.ISQLiteDB
import com.twl.debug.sqlite.model.DataListResp
import com.twl.debug.sqlite.model.Table
import com.twl.debug.sqlite.model.annotation.DataType
import com.twl.debug.sqlite.server.base.IRouteHandler
import com.twl.debug.sqlite.util.DataBaseUtil
import com.twl.debug.sqlite.util.GsonUtil
import com.twl.debug.sqlite.util.LogUtil

/**
 * <AUTHOR>
 * 处理 getAllDataInTable() 请求
 *
 * 获取指定表下的所有数据
 */
class GetAllDataInTableHandler : IRouteHandler {

	override fun handle(path: String): ByteArray {
		val response = DataListResp()

		try {
			val uri = Uri.parse(path)
			val tableName = uri.getQueryParameter("tableName")

			if (!tableName.isNullOrEmpty()) {
				DBManage.get().sqliteDB
					?.let { db ->
						queryAllColumnInfo(db, tableName, response)
						queryAllData(db, tableName, response)
					}
				response.isEditable = true
				response.isSuccessful = true
			}
		} catch (e: Exception) {
			LogUtil.error("GetAllDataInTableHandler handle() error! ${e.message}")
		}

		return GsonUtil.toJson(response).toByteArray(Charsets.UTF_8)
	}

	/**
	 * 查询表所有列的信息
	 * @param tableName 表名
	 */
	private fun queryAllColumnInfo(
		database: ISQLiteDB,
		tableName: String,
		response: DataListResp
	) {
		database.rawQuery("PRAGMA table_info($tableName);", null)
			?.let { cursor ->
				if (cursor.count > 0) {
					cursor.moveToFirst()
					do {
						val indexOfColumnName = cursor.getColumnIndex("name")
						val columnName = cursor.getString(indexOfColumnName)
						val indexOfPK = cursor.getColumnIndex("pk")
						val isPrimaryKey = cursor.getInt(indexOfPK) == 1
						response.tableColumnList.add(Table.Column(columnName, isPrimaryKey))
					} while (cursor.moveToNext())
				}
				cursor.close()
			}
	}

	/**
	 * 查询表所有数据
	 */
	private fun queryAllData(database: ISQLiteDB, tableName: String, response: DataListResp) {
		database.rawQuery("SELECT * FROM $tableName", null)
			?.let { cursor ->
				if (cursor.count > 0) {
					cursor.moveToFirst()
					do {
						val row = arrayListOf<Table.Data>()
						for (index in 0 until cursor.columnCount) {
							val data = when (cursor.getType(index)) {
								Cursor.FIELD_TYPE_FLOAT -> {
									Table.Data(DataType.REAL, cursor.getDouble(index))
								}
								Cursor.FIELD_TYPE_BLOB -> {
									Table.Data(
										DataType.TEXT,
										DataBaseUtil.blobToString(cursor.getBlob(index))
									)
								}
								Cursor.FIELD_TYPE_INTEGER -> {
									Table.Data(DataType.INTEGER, cursor.getLong(index))
								}
								else -> {
									Table.Data(DataType.TEXT, cursor.getString(index))
								}
							}
							row.add(data)
						}
						response.rows.add(row)
					} while (cursor.moveToNext())
				}
				cursor.close()
			}
	}
}