package com.twl.hi.export.email.router;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.fragment.app.FragmentActivity;
import com.sankuai.waimai.router.Router;
import com.twl.hi.export.email.bean.MailInfoBean;
import com.twl.hi.export.email.service.IEmailRouterService;

import hi.kernel.BundleConstants;

/**
 * 邮箱模块路由
 */
public class EmailPageRouter {

    public static final String EMAIL_SERVICE = "email_service";

    private static final String EMAIL_MODULE_NAME = "/email";

    public static final String EMAIL_DETAILS_PAGE = EMAIL_MODULE_NAME + "/main_tab_activity";
    public static final String EMAIL_SETTING_PAGE = EMAIL_MODULE_NAME + "/setting_activity";

    /**
     * 跳转创建邮件界面
     * @param activity
     */
    public static void jumpToCreateEmailDialog(FragmentActivity activity) {
        IEmailRouterService service = Router.getService(IEmailRouterService.class, EMAIL_SERVICE);
        if (service != null) {
            service.jumpToCreateEmailDialog(activity);
        }
    }

    /**
     * 创建邮件
     * @param activity
     * @param mailInfoBean
     */
    public static void jumpToCreateEmailDialog(FragmentActivity activity, MailInfoBean mailInfoBean) {
        IEmailRouterService service = Router.getService(IEmailRouterService.class, EMAIL_SERVICE);
        if (service != null) {
            Bundle bundle = new Bundle();
            bundle.putParcelable(BundleConstants.BUNDLE_EMAIL_INFO, mailInfoBean);
            service.jumpToCreateEmailDialog(activity, bundle);
        }
    }

    public static void jumpToEditEmailDialog(FragmentActivity activity, String mailId) {
        IEmailRouterService service = Router.getService(IEmailRouterService.class, EMAIL_SERVICE);
        if (service != null) {
            service.jumpToEditEmailDialog(activity, mailId);
        }
    }

    /**
     * 查看邮件详情页面
     * @param activity
     * @param mailId
     */
    public static void jumpToViewEmailDetails(FragmentActivity activity, String mailId, boolean fromPush) {
        IEmailRouterService service = Router.getService(IEmailRouterService.class, EMAIL_SERVICE);
        if (service != null) {
            service.jumpToViewEmailDetails(activity, mailId, fromPush);
        }
    }

    /**
     * 查看eml邮件文件详情
     * @param activity
     * @param filePath
     */
    public static void jumpToViewEmailFileDetails(Activity activity, String filePath) {
        IEmailRouterService service = Router.getService(IEmailRouterService.class, EMAIL_SERVICE);
        if (service != null && !TextUtils.isEmpty(filePath)) {
            service.jumpToViewEmailFileDetails(activity, filePath);
        }
    }

    /**
     * 跳转邮箱设置
     */
    public static void jumpToEmailSetting(Context context) {
        IEmailRouterService service = Router.getService(IEmailRouterService.class, EMAIL_SERVICE);
        if (service != null) {
            service.jumpToEmailSetting(context);
        }
    }
}
