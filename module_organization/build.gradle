apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

apply from: rootProject.file('bzl-push.gradle')
apply from: rootProject.file('ksp_config.gradle')

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion build_versions.build_tools

    defaultConfig {
        minSdkVersion build_versions.min_sdk
        targetSdkVersion build_versions.target_sdk
        resourcePrefix 'organization_'
    }

    dataBinding {
        enabled = true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependenciesForImplementation(project, ":lib_common", deps.commonLibs.lib_common)

dependenciesForImplementation(project, ":module_foundation_business", deps.commonLibs.module_foundation_business)
dependenciesForImplementation(project, ":module_login", deps.commonLibs.module_login)
dependenciesForImplementation(project, ":module_viewer", deps.commonLibs.module_viewer)

dependenciesForImplementation(project, ":export_module_organization", deps.commonLibs.export_module_organization)
dependenciesForImplementation(project, ":export_module_chat", deps.commonLibs.export_module_chat)
dependenciesForImplementation(project, ":export_module_me", deps.commonLibs.export_module_me)
dependenciesForImplementation(project, ":export_module_schedule", deps.commonLibs.export_module_schedule)
dependenciesForImplementation(project, ":export_module_select", deps.commonLibs.export_module_select)
dependenciesForImplementation(project, ":export_module_video_meeting", deps.commonLibs.export_module_video_meeting)
dependenciesForImplementation(project, ":export_module_webview", deps.commonLibs.export_module_webview)
dependenciesForImplementation(project, ":export_module_main", deps.commonLibs.export_module_main)
dependenciesForImplementation(project, ":export_module_audio_shorthand", deps.commonLibs.export_module_audio_shorthand)

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
}