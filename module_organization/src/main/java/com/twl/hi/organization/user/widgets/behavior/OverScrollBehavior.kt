package com.twl.hi.organization.user.widgets.behavior

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.google.android.material.appbar.AppBarLayout
import kotlin.math.roundToInt

/**
 *@author: musa on 2022/6/7
 *@e-mail: <EMAIL>
 *@desc: 用于 CoordinatorLayout 体系的 下拉变大的behavior
 */
private const val OVER_SCROLL_TAG = "overScroll"
private const val TARGET_HEIGHT = 1500f

class OverScrollBehavior(context: Context?, attrs: AttributeSet?) :
    AppBarLayout.Behavior(context, attrs) {
    /**目标View*/
    private var mTargetView: View? = null
    /**绑定的 AppBarLayout 的高度*/
    private var mParentHeight = 0
    /**目标View的高度*/
    private var mTargetViewHeight = 0

    private var mTotalDy = 0f
    private var mLastScale = 0f
    private var mLastBottom = 0
    private var isAnimate = false
    private var isRecovering = false //是否正在自动回弹中

    override fun onLayoutChild(
        parent: CoordinatorLayout,
        abl: AppBarLayout,
        layoutDirection: Int
    ): Boolean {
        val handled = super.onLayoutChild(parent, abl, layoutDirection)
        // 需要在调用过super.onLayoutChild()方法之后获取
        mTargetView ?: run {
            mTargetView = parent.findViewWithTag<View>(OVER_SCROLL_TAG)?.run {
                abl.clipChildren = false
                mParentHeight = abl.height
                mTargetViewHeight = height
                this
            }
        }
        return handled
    }

    override fun onNestedPreScroll(
        coordinatorLayout: CoordinatorLayout,
        child: AppBarLayout,
        target: View,
        dx: Int,
        dy: Int,
        consumed: IntArray,
        type: Int
    ) {
        if (!isRecovering
            && child.bottom >= mParentHeight
        ) { //AppBarLayout展示完全之后继续往下滑，则放大
            scale(child, target, dy)
        }
        if (child.bottom <= mParentHeight){
            //默认的逻辑
            super.onNestedPreScroll(coordinatorLayout, child, target, dx, dy, consumed, type)
        }
    }

    override fun onNestedPreFling(
        coordinatorLayout: CoordinatorLayout,
        child: AppBarLayout,
        target: View,
        velocityX: Float,
        velocityY: Float
    ): Boolean {
        if (velocityY > 1000) //快速滑动时，启用快速恢复静止状态
            isAnimate = false
        return super.onNestedPreFling(coordinatorLayout, child, target, velocityX, velocityY)
    }

    override fun onStopNestedScroll(
        coordinatorLayout: CoordinatorLayout,
        abl: AppBarLayout,
        target: View,
        type: Int
    ) {
        if (abl.bottom >= mParentHeight) {
            recovery(abl)
        }
        super.onStopNestedScroll(coordinatorLayout, abl, target, type)
    }

    /**
     * 放大动画
     */
    private fun scale(abl: AppBarLayout, target: View, dy: Int) {
        mTotalDy += -dy
        //控制target视图下拉放大上限
        mTotalDy = mTotalDy.coerceAtMost(TARGET_HEIGHT)
        //最后比例
        mLastScale = 1f.coerceAtLeast(1f + mTotalDy / TARGET_HEIGHT)
        Log.i("scale", "scale: $mLastScale")
        //改变比例
        mTargetView?.scaleX = mLastScale
        mTargetView?.scaleY = mLastScale
        //AppBarLayout 随着图片放大，自己的底部往下平移
        mLastBottom = mParentHeight + (mTargetViewHeight / 2 * (mLastScale - 1)).toInt()
        abl.bottom = mLastBottom
        target.scrollY = 0

    }


    /**
     * 缩小动画
     */
    private fun recovery(abl: AppBarLayout) {
        if (isRecovering) return

        if (mTotalDy > 0) {
            isRecovering = true
            if (isAnimate) { //动画的形式恢复
                val anim = ValueAnimator.ofFloat(mLastScale, 1f).setDuration(200)
                anim.addUpdateListener { animation ->
                    val value = animation.animatedValue as Float
                    mTargetView?.scaleY = value
                    mTargetView?.scaleX = value
                    abl.bottom =
                        ((mLastBottom - (mLastBottom - mParentHeight) * animation.animatedFraction).roundToInt())
                }
                anim.addListener(object : Animator.AnimatorListener {
                    override fun onAnimationStart(animation: Animator) {}
                    override fun onAnimationEnd(animation: Animator) {
                        isRecovering = false
                        mTotalDy = 0f
                        isAnimate = true
                    }

                    override fun onAnimationCancel(animation: Animator) {}
                    override fun onAnimationRepeat(animation: Animator) {}
                })
                anim.start()
            } else { //立马恢复
                mTargetView?.scaleY = 1f
                mTargetView?.scaleX = 1f
                abl.bottom = mParentHeight
                mTotalDy = 0f
                isRecovering = false
                isAnimate = true
            }
        }
    }
}
