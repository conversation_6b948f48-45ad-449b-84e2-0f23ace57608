package com.twl.hi.organization.viewmodel;

import android.app.Application;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.twl.hi.basic.api.response.bean.ApiResponse;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.utils.GroupInfoHelper;
import com.twl.hi.foundation.utils.GroupStatusCheckCallback;
import com.twl.hi.organization.api.request.GroupGetManagerRequest;
import com.twl.hi.organization.api.request.GroupUpdateManagerRequest;
import com.twl.hi.organization.api.response.GroupGetManagerResponse;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.List;

public class GroupAdminsViewModel extends FoundationViewModel {
    private ArrayList<String> mSelectedIds = new ArrayList<>();
    private List<Contact> contacts = new ArrayList<>();
    private MutableLiveData<List<Contact>> contactsLiveData = new MutableLiveData<>();

    public GroupAdminsViewModel(Application application) {
        super(application);
    }

    public void addSelectedIds(ArrayList<String> ids) {
        this.mSelectedIds.addAll(ids);
        initInBackground();
    }

    public ArrayList<String> getSelectedIds() {
        return mSelectedIds;
    }

    @Override
    protected void initData() {
        contacts = ServiceManager.getInstance().getContactService().getContactsByIds(mSelectedIds);
        contactsLiveData.postValue(contacts);
    }

    public MutableLiveData<List<Contact>> getContactsLiveData() {
        return contactsLiveData;
    }

    public void removeSelected(String userId) {
        mSelectedIds.remove(userId);
    }

    public void deleteContact(Contact contact) {
        if (contacts == null || contacts.isEmpty()) {
            return;
        }
        contacts.remove(contact);
        contactsLiveData.postValue(contacts);
    }

    public void getManagers(String groupId) {
        GroupGetManagerRequest request = new GroupGetManagerRequest(new BaseApiRequestCallback<GroupGetManagerResponse>() {

            @Override
            public void handleInChildThread(ApiData<GroupGetManagerResponse> data) {
                mSelectedIds = (ArrayList<String>) data.resp.result;
                contacts = ServiceManager.getInstance().getContactService().getContactsByIds(mSelectedIds);

            }

            @Override
            public void onSuccess(ApiData<GroupGetManagerResponse> data) {

            }

            @Override
            public void onComplete() {
                contactsLiveData.postValue(contacts);
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.groupId = groupId;
        HttpExecutor.execute(request);
    }

    public void checkGroupStatus(String groupId, GroupStatusCheckCallback callback) {
        GroupInfoHelper.optWithGroupStatusCheck(groupId, MessageConstants.MSG_GROUP_CHAT, callback);
    }

    public LiveData<ApiResponse<HttpResponse>> requestUpdateManagers(String groupId) {
        MutableLiveData<ApiResponse<HttpResponse>> liveData = new MutableLiveData<>();
        GroupUpdateManagerRequest req = new GroupUpdateManagerRequest(new BaseApiRequestCallback<HttpResponse>() {

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                liveData.setValue(ApiResponse.create(true, data.resp, null));
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                liveData.setValue(ApiResponse.create(false, null, reason));
            }
        });
        req.groupId = groupId;
        req.managerIds = convertString(mSelectedIds);
        HttpExecutor.execute(req);
        return liveData;
    }

    private String convertString(List<String> mSelects) {
        StringBuilder sb = new StringBuilder();
        for (String existId : mSelects) {
            sb.append(existId);
            sb.append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }
}