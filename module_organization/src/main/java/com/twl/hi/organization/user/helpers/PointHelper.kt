package com.twl.hi.organization.user.helpers

import com.twl.hi.basic.*
import com.twl.hi.foundation.utils.PointUtils

/**
 *@author: musa on 2022/6/17
 *@e-mail: yang<PERSON><PERSON><PERSON>@kanzhun.com
 *@desc: 个人名片页埋点辅助类
 */
class PointHelper {

    private val sourceMap by lazy {
        mapOf(
            Pair(ME_PAGE, 1),
            Pair(SINGLE_CONVERSATION_PAGE_TITLE, 2),
            Pair(CONVERSATION_PAGE_AVATAR, 3),
            Pair(SINGLE_CONVERSATION_SETTING_PAGE, 4),
            Pair(GROUP_CONVERSATION_SETTING_PAGE_MEMBER_LIST, 5),
            Pair(GROUP_CONVERSATION_SETTING_PAGE_FOLLOW_MEMBER, 6),
            Pair(MARK_MESSAGE_LIST, 7),
            Pair(ADDRESS_LIST_PAGE_NEW_CONTRACTS, 8),
            Pair(ADDRESS_LIST_PAGE_MY_CONTRACTS, 9),
            Pair(ADDRESS_LIST_PAGE_MEMBER_OF_ORGANIZATION, 10),
            Pair(SCHEDULE_CARD_PARTICIPANTS, 11),
            Pair(WORK_FLOW_TASK_PARTICIPANTS, 12),
            Pair(USER_INFO_PAGE_REPORTER, 13),
            Pair(EMAIL_DETAILS, 14)
            )
    }

    fun getSourceCode(pageFrom: String) = sourceMap[pageFrom]

}