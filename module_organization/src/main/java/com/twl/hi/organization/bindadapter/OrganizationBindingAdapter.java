package com.twl.hi.organization.bindadapter;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.databinding.BindingAdapter;

import com.twl.hi.basic.bindadapter.BasicBindingAdapters;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.organization.R;
import com.twl.hi.organization.api.response.ContactAppliesResponse;
import com.twl.hi.organization.model.UserCardInfoBean;

import hi.kernel.Constants;
import hi.kernel.HiKernel;
import lib.twl.common.util.ProcessHelper;
import lib.twl.common.util.Utils;

/**
 * <AUTHOR>
 * @date 2021/7/2.
 */
public class OrganizationBindingAdapter {

    @BindingAdapter({"friendApply", "userId"})
    public static void setFriendApplyTvStatus(TextView textView, int status, String userId) {
        boolean fromSelf = TextUtils.equals(userId, HiKernel.getHikernel().getAccount().getUserId());
        if (status == Constants.FRIEND_APPLY_STATUS_APPLYING) {
            textView.setCompoundDrawables(null, null, null, null);
            textView.setGravity(Gravity.CENTER);
            if (fromSelf) {
                // 等待验证
                textView.setTextColor(textView.getResources().getColor(com.twl.hi.basic.R.color.color_f5a623));
                textView.setText(R.string.organization_wait_agree);
                textView.setBackground(null);
            } else {
                textView.setTextColor(textView.getResources().getColor(com.twl.hi.basic.R.color.white));
                textView.setBackgroundResource(R.drawable.bg_corner_5_stroke_color_blue_5d68e8);
                textView.setText(textView.getContext().getResources().getString(R.string.organization_has_applying));
            }
        } else {
            textView.setGravity(Gravity.CENTER | Gravity.END);
            textView.setTextColor(textView.getResources().getColor(R.color.color_B1B1B8));
            textView.setBackground(null);
            // 自己是发起者时设置图标
            if (fromSelf) {
                Drawable drawable = ProcessHelper.getContext().getDrawable(R.mipmap.organize_icon_right_arrow);
                drawable.setBounds(new Rect(0, 0, Utils.dp2px(12), Utils.dp2px(10)));
                textView.setCompoundDrawablePadding(Utils.dp2px(6));
                textView.setCompoundDrawables(drawable, null, null, null);
            } else {
                textView.setCompoundDrawables(null, null, null, null);
            }

            switch (status) {
                case Constants.FRIEND_APPLY_STATUS_AGREE:
                    textView.setText(textView.getResources().getString(R.string.organization_has_agree));
                    break;
                case Constants.FRIEND_APPLY_STATUS_IGNORE:
                    if (fromSelf) {
                        // 自己发起的被忽略，也显示等待验证
                        textView.setGravity(Gravity.CENTER);
                        textView.setCompoundDrawables(null, null, null, null);
                        textView.setTextColor(textView.getResources().getColor(com.twl.hi.basic.R.color.color_f5a623));
                        textView.setText(R.string.organization_wait_agree);
                        textView.setBackground(null);
                    } else {
                        textView.setText(textView.getResources().getString(R.string.organization_has_ignore));
                    }
                    break;
                case Constants.FRIEND_APPLY_STATUS_OVERDUE:
                    textView.setText(textView.getResources().getString(R.string.organization_has_overdue));
                    break;
            }
        }
    }

    @BindingAdapter("friendName")
    public static void setFriendName(TextView textView, ContactAppliesResponse.ContactApplyInfo applyInfo) {
        boolean fromSelf = TextUtils.equals(HiKernel.getHikernel().getAccount().getUserId(), applyInfo.userId);
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(fromSelf ? applyInfo.friendId : applyInfo.userId);
        if (contact != null) {
            textView.setText(contact.getShowName());
        }
    }

    @BindingAdapter(value = {"friendAvatar", "pageFrom"}, requireAll = false)
    public static void setFriendAvatar(View view, ContactAppliesResponse.ContactApplyInfo applyInfo, String pageFrom) {
        boolean fromSelf = TextUtils.equals(HiKernel.getHikernel().getAccount().getUserId(), applyInfo.userId);
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(fromSelf ? applyInfo.friendId : applyInfo.userId);
        BasicBindingAdapters.setContactAvatar(view, contact, null, null, null, pageFrom, false);
    }

    @BindingAdapter("title")
    public static void setContactTitle(TextView textView, Contact contact) {
        if (!TextUtils.isEmpty(contact.getTitle())) {
            textView.setText(" | " + contact.getTitle());
            textView.setTextColor(textView.getResources().getColor(com.twl.hi.basic.R.color.color_9B9B9B));
        }
    }

    @BindingAdapter("cardInfo")
    public static void setUserCardActionBehaviour(ViewGroup viewGroup, UserCardInfoBean cardInfo) {
        if (cardInfo == null) {
            return;
        }
        Context context = viewGroup.getContext();
        View tvAudioAndVideo = viewGroup.findViewById(R.id.tv_audio_and_video);
        View space = viewGroup.findViewById(R.id.space);
        TextView tvSendOrApplyForFriend = viewGroup.findViewById(R.id.tv_send);
        boolean switchOn = ProcessHelper.getUserCompanyPreferences().getBoolean(Constants.KEY_CHAT_VISIBLE_SWITCH, false);
        if (switchOn) {
            if (cardInfo.chatVisible == 1) {
                tvAudioAndVideo.setVisibility(View.VISIBLE);
                space.setVisibility(View.VISIBLE);
                tvSendOrApplyForFriend.setText(context.getString(R.string.send_message));
            } else {
                tvAudioAndVideo.setVisibility(View.GONE);
                space.setVisibility(View.GONE);
                tvSendOrApplyForFriend.setText(context.getString(R.string.organization_add_contact));
            }
        } else {
            if (cardInfo.isSelf && cardInfo.canSend && cardInfo.mediaEnable) {
                tvAudioAndVideo.setVisibility(View.VISIBLE);
                space.setVisibility(View.VISIBLE);
            } else {
                tvAudioAndVideo.setVisibility(View.GONE);
                space.setVisibility(View.GONE);
            }
            if (cardInfo.canSend) {
                tvSendOrApplyForFriend.setText(context.getString(R.string.send_message));
            } else {
                tvSendOrApplyForFriend.setText(context.getString(R.string.organization_add_contact));
            }
        }
        if (cardInfo.showBottomButton) {
            viewGroup.setVisibility(View.VISIBLE);
        } else {
            viewGroup.setVisibility(View.GONE);
        }
    }
}
