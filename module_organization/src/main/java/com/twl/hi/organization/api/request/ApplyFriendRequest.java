package com.twl.hi.organization.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;

public class ApplyFriendRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public String friendId;
    @Expose
    public String reason;
    @Expose
    public Integer shiningType;

    public ApplyFriendRequest(BaseApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_CONTACT_FRIEND_APPLY;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}

