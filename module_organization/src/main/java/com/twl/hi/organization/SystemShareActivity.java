package com.twl.hi.organization;

import android.app.Activity;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.recyclerview.widget.GridLayoutManager;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.basic.model.SendFileBean;
import com.twl.hi.basic.model.WebViewBean;
import com.twl.hi.basic.util.ImageDownLoadUtils;
import com.twl.hi.basic.views.GridSpacingItemDecoration;
import com.twl.hi.export.chat.router.ChatPageRouter;
import com.twl.hi.export.select.bean.SelectBaseParams;
import com.twl.hi.export.select.bean.SelectConversationParams;
import com.twl.hi.export.select.router.SelectPageRouter;
import com.twl.hi.export.webview.WebViewPageRouter;
import com.twl.hi.foundation.SendMessageContent;
import com.twl.hi.foundation.model.message.MessageConstants;
import com.twl.hi.foundation.model.message.MessageForLink;
import com.twl.hi.organization.adapter.SystemShareAdapter;
import com.twl.hi.organization.callback.SystemShareCallback;
import com.twl.hi.organization.databinding.OrganizationActivitySystemShareBinding;
import com.twl.hi.organization.viewmodel.SystemShareViewModel;
import com.twl.hi.viewer.DraggableImageViewerHelper;
import com.twl.utils.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import hi.kernel.HiKernel;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.CommonUtils;
import lib.twl.common.util.LList;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.util.ToastUtils;
import lib.twl.common.views.adapter.BaseQuickAdapter;
import lib.twl.common.views.imagesview.Image;

/**
 * Created by ChaiJiangpeng on 2020-03-05
 * Describe:系统分享调用app
 */
public class SystemShareActivity extends FoundationVMActivity<OrganizationActivitySystemShareBinding, SystemShareViewModel>
        implements SystemShareCallback {

    private static final String TAG = "SystemShareActivity";

    public static final int SYSTEM_SHARE_REQUEST_CODE = 101;
    private Cursor mCursor;
    private String type;
    private ArrayList<File> mFiles = new ArrayList<>();
    private MessageForLink.LinkInfo mLinkInfo;

    @Override
    public int getContentLayoutId() {
        return R.layout.organization_activity_system_share;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return 0;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window window = getWindow();
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams layoutParams = window.getAttributes();
        layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        window.setAttributes(layoutParams);
        initView();
    }

    private void initView() {
        if (!HiKernel.getHikernel().isLogin()) {
            getDataBinding().clDesc.setVisibility(View.VISIBLE);
            return;
        }
        Intent intent = getIntent();
        String action = intent.getAction();//action
        type = intent.getType();//类型
        ArrayList<Image> images = new ArrayList<>();
        if (type != null) {
            if (Intent.ACTION_SEND.equals(action)) {
                try {
                    getDataBinding().flContent.setVisibility(View.VISIBLE);
                    if (type.equals("text/plain")) {
                        String stringExtra = intent.getStringExtra(Intent.EXTRA_TEXT);
                        if (stringExtra != null) {
                            String title = stringExtra.substring(0, stringExtra.indexOf("http"));
                            String url = stringExtra.substring(stringExtra.indexOf("http"));
                            if (TextUtils.isEmpty(title)) {
                                title = url;
                            }
                            mLinkInfo = new MessageForLink.LinkInfo(url, title);
                            getDataBinding().clFile.setVisibility(View.VISIBLE);
                            getDataBinding().setFileName(title);
                            getDataBinding().executePendingBindings();
                            return;
                        }
                    }
                    ExecutorFactory.execWorkTask(() -> {
                        Uri contentUri = intent.getParcelableExtra(Intent.EXTRA_STREAM);
                        File file = ImageDownLoadUtils.getCanReadFile(this, contentUri);
                        mFiles = new ArrayList<>(1);
                        mFiles.add(file);
                        ExecutorFactory.execMainTask(() -> {
                            if (type.equals("image/*") || type.equals("video/*")) {
                                getDataBinding().clSingle.setVisibility(View.VISIBLE);
                                ImageRequest request = ImageRequestBuilder.newBuilderWithSource(contentUri)
                                        .build();
                                getDataBinding().ivPic.setController(
                                        Fresco.newDraweeControllerBuilder()
                                                .setOldController(getDataBinding().ivPic.getController())
                                                .setImageRequest(request)
                                                .build());

                                if (type.equals("video/*")) {
                                    getDataBinding().ivIconVideo.setVisibility(View.VISIBLE);
                                    getDataBinding().tvSize.setVisibility(View.VISIBLE);
                                    getDataBinding().tvTime.setVisibility(View.VISIBLE);
                                    try {
                                        mCursor = getContentResolver().query(contentUri, null, null, null, null);
                                        if (mCursor != null && mCursor.moveToFirst()) {
                                            long size = mCursor.getLong(mCursor.getColumnIndex(MediaStore.MediaColumns.SIZE));
                                            long duration = mCursor.getLong(mCursor.getColumnIndex("duration"));
                                            getDataBinding().tvSize.setText(CommonUtils.getPrintSize(size));
                                            getDataBinding().tvTime.setText(DateUtils.formatElapsedTime(duration));
                                        }
                                    } finally {
                                        if (mCursor != null)
                                            mCursor.close();
                                    }
                                }
                                getDataBinding().ivPic.setOnClickListener(v -> {
                                    if (type.equals("video/*")) {
                                        WebViewBean bean = new WebViewBean();
                                        bean.setPath(file.getAbsolutePath());
                                        Bundle bundle = new Bundle();
                                        bundle.putSerializable(Constants.DATA_WEB_BEAN, bean);
                                        AppUtil.startUri(this, WebViewPageRouter.WEB_VIEW_ACTIVITY, bundle);
                                    } else {
                                        Image image = new Image("file://" + file.getAbsoluteFile(), null, null, 0);
                                        images.clear();
                                        images.add(image);
                                        openPic(images, 0, getDataBinding().ivPic);
                                    }
                                });
                            } else {
                                //文件
                                getDataBinding().clFile.setVisibility(View.VISIBLE);
                                getDataBinding().fileSize.setText(CommonUtils.getPrintSize(file.length()));
                                getDataBinding().setFileName(file.getName());
                                getDataBinding().executePendingBindings();
                            }
                        });
                    });

                } catch (Exception e) {
                    ToastUtils.failure("分享失败，不支持此格式");
                    onBackPressed();
                }

            } else if (Intent.ACTION_SEND_MULTIPLE.equals(action)) {
                if (!"image/*".equals(type)) {
                    ToastUtils.failure("分享失败，多文件分享仅支持照片格式");
                    onBackPressed();
                } else {
                    List<Uri> imageUris = intent.getParcelableArrayListExtra(Intent.EXTRA_STREAM);
                    if (imageUris.size() > 9) {
                        ToastUtils.ss("最多可以分享9张照片");
                        onBackPressed();
                    } else {
                        getDataBinding().flContent.setVisibility(View.VISIBLE);
                        ExecutorFactory.execWorkTask(() -> {
                            setUriToFile(imageUris);
                            for (File file : mFiles) {
                                images.add(new Image("file://" + file.getAbsolutePath(), null, null, 0));
                            }
                            ExecutorFactory.execMainTask(() -> {
                                getDataBinding().recyclerView.setLayoutManager(new GridLayoutManager(this, 3));
                                getDataBinding().recyclerView.addItemDecoration(new GridSpacingItemDecoration(
                                        3, QMUIDisplayHelper.dpToPx(12), QMUIDisplayHelper.dpToPx(9)
                                        , false));
                                SystemShareAdapter adapter = new SystemShareAdapter(imageUris);
                                getDataBinding().recyclerView.setAdapter(adapter);
                                adapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                                    @Override
                                    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                                        openPic(images, position, view);
                                    }
                                });
                            });
                        });
                    }
                }
            }
        }
    }

    private void openPic(ArrayList<Image> images, int index, View view) {
        DraggableImageViewerHelper.showImages(SystemShareActivity.this, view, images,
                index);
    }

    private void setUriToFile(List<Uri> imageUris) {
        mFiles = new ArrayList<>(imageUris.size());
        for (Uri uri : imageUris) {
            try {
                File canReadFile = ImageDownLoadUtils.getCanReadFile(this, uri);
                mFiles.add(canReadFile);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void close() {
        onBackPressed();
    }

    @Override
    public void toJumpSelector() {
        SelectConversationParams params = new SelectConversationParams()
                .<SelectConversationParams>setTitle(getResources().getString(R.string.select_contact))
                .<SelectConversationParams>setOrgVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setSearchVisible(SelectBaseParams.VISIBLE)
                .<SelectConversationParams>setOtherVisible(SelectBaseParams.VISIBLE);
        if (type.equals("text/plain") && mLinkInfo != null) {
            params.setSendMessageType(MessageConstants.MSG_LINK);
            params.sendMessageContent.content = mLinkInfo;
            params.sendMessageContent.msgShowContent = SendMessageContent.getLinkTitle(mLinkInfo);
        } else {
            if (type.equals("image/*")) {
                params.setSendMessageType(MessageConstants.MSG_PIC);
                params.sendMessageContent.msgShowContent = "[图片]";
                params.sendMessageContent.content = mFiles;
            } else {
                if (LList.isEmpty(mFiles)) {
                    return;
                }
                params.setSendMessageType(MessageConstants.MSG_FILE);
                SendFileBean sendFileBean = new SendFileBean();
                sendFileBean.file = mFiles.get(0);
                if (type.equals("video/*")) {
                    params.sendMessageContent.msgShowContent = "[视频]";
                } else {
                    params.sendMessageContent.msgShowContent = "[文件]";
                }
                params.sendMessageContent.content = sendFileBean;
            }

        }
        // 标识跳转来源
        params.jumpSource = SelectConversationParams.SOURCE_JUMP_SHARED;
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params);
        AppUtil.startUriForResult(this, SelectPageRouter.SELECT_CONVERSATION_ACTIVITY, SYSTEM_SHARE_REQUEST_CODE, bundle, ActivityAnimType.UP_GLIDE);
    }

    @Override
    public void toSendSelf() {
        if (type.equals("text/plain") && mLinkInfo != null) {
            getViewModel().sendLinkMessage(mLinkInfo, Constants.CHAT_ID_FILE_HELPER);
        } else {
            if (type.equals("image/*")) {
                getViewModel().handleMediaFilesSend(mFiles, Constants.CHAT_ID_FILE_HELPER,
                        MessageConstants.MSG_SINGLE_CHAT, null);
            } else {
                if (LList.isEmpty(mFiles)) {
                    return;
                }
                getViewModel().sendFile(mFiles.get(0), Constants.CHAT_ID_FILE_HELPER,
                        MessageConstants.MSG_SINGLE_CHAT, null);
            }

        }
        // VIVO手机当前页面展示吐司后，再跳转延迟activity后吐司会被销毁
        // 通过延迟消息，再跳转页面完成后展示
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                ToastUtils.success(R.string.select_has_shared);
            }
        }, 500);
        finish();
        Bundle bundle = new Bundle();
        bundle.putBoolean(Constants.CHAT_NEED_BACK_TO_MAIN, true);
        bundle.putString(BundleConstants.BUNDLE_USER_ID, Constants.CHAT_ID_FILE_HELPER);
        AppUtil.startUri(this, ChatPageRouter.SINGLE_CHAT_ACTIVITY, bundle);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == SYSTEM_SHARE_REQUEST_CODE) {
                finish();
                ToastUtils.success(R.string.select_has_shared);
                Bundle bundle = new Bundle();
                bundle.putBoolean(Constants.CHAT_NEED_BACK_TO_MAIN, true);
                int chatType = data.getIntExtra(Constants.CHAT_TYPE, MessageConstants.MSG_SINGLE_CHAT);
                String chatId = data.getStringExtra(Constants.CHAT_ID);
                if (chatType == MessageConstants.MSG_GROUP_CHAT) {
                    bundle.putString(BundleConstants.BUNDLE_CHAT_ID, chatId);
                    AppUtil.startUri(this, ChatPageRouter.GROUP_CHAT_ACTIVITY, bundle);
                } else {
                    bundle.putString(BundleConstants.BUNDLE_USER_ID, chatId);
                    AppUtil.startUri(this, ChatPageRouter.SINGLE_CHAT_ACTIVITY, bundle);
                }
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (getDataBinding().clDesc.getVisibility() == View.VISIBLE) {
            finish();
        }
    }
}
