package com.twl.hi.organization;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.foundation.model.Company;
import com.twl.hi.login.InviteTeamMembersInnerActivity;
import com.twl.hi.login.TeamManagerActivity;
import com.twl.hi.organization.callback.TeamContactsManagerCallback;
import com.twl.hi.organization.databinding.OrganizationActivityTeamContactsManagerBinding;
import com.twl.hi.organization.viewmodel.TeamContactsManagerViewModel;

import hi.kernel.BundleConstants;
import lib.twl.common.util.AppUtil;

public class TeamContactsManagerActivity extends FoundationVMActivity<OrganizationActivityTeamContactsManagerBinding, TeamContactsManagerViewModel> implements TeamContactsManagerCallback {

    private Company company;

    public static Intent createIntent(Context context, Company companiesBean) {
        Intent intent = new Intent(context, TeamContactsManagerActivity.class);
        intent.putExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE, companiesBean);
        return intent;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.organization_activity_team_contacts_manager;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getDataBinding().titleBar.tvBack.setVisibility(View.INVISIBLE);
        company = (Company) getIntent().getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE);
    }

    @Override
    public void addMember() {
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, company);
        bundle.putString(BundleConstants.BUNDLE_DATA_LONG, "");
        bundle.putString(BundleConstants.BUNDLE_DATA_STRING, company.comName);
        bundle.putBoolean(BundleConstants.BUNDLE_DATA_BOOLEAN, true);
        Intent intent = new Intent(this, InviteTeamMembersInnerActivity.class);
        intent.putExtras(bundle);
        AppUtil.startActivity(this, intent);
    }

    @Override
    public void loginManager() {
        AppUtil.startActivity(this, new Intent(this, TeamManagerActivity.class));
    }

    @Override
    public void userInviteSwitchClick() {
        getViewModel().setContactManagerSettingSwitch(1, getViewModel().userInviteSwitch.get() ? 0 : 1);
    }

    @Override
    public void managerAuditSwitchClick() {
        getViewModel().setContactManagerSettingSwitch(2, getViewModel().managerAuditSwitch.get() ? 0 : 1);
    }
}
