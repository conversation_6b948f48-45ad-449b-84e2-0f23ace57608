<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.organization.user.viewmodel.UserInfoViewModelNew" />

        <variable
            name="callback"
            type="com.twl.hi.organization.user.callback.UserInfoCallbackNew" />

        <import type="com.twl.hi.foundation.model.Contact" />

        <import type="android.text.TextUtils" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="40dp">

        <FrameLayout
            android:id="@+id/fl_back"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:onClick="@{() -> callback.onBack()}"
            android:paddingHorizontal="5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_gravity="center"
                android:layout_marginStart="15dp"
                android:src="@drawable/ic_icon_black_back" />
        </FrameLayout>

        <TextView
            android:id="@+id/tv_avatar"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:background="@drawable/bg_text_avatar"
            android:gravity="center"
            android:text="@{viewModel.userCardInfo.contact.strAvatar}"
            android:textColor="@color/app_white"
            android:textSize="10sp"
            app:layout_constraintEnd_toEndOf="@+id/iv_avatar"
            app:layout_constraintStart_toStartOf="@id/iv_avatar"
            app:layout_constraintTop_toTopOf="@+id/iv_avatar"
            tools:text="Sa" />

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/iv_avatar"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="8dp"
            app:imageUrl="@{viewModel.userCardInfo.avatar}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_name"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/fl_back"
            app:layout_constraintTop_toTopOf="parent"
            app:roundAsCircle="true" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{!TextUtils.isEmpty(viewModel.getUserRemark()) ? viewModel.getUserRemark() : viewModel.getUserCardInfo().contact.getShowName(Contact.SHOW_NAME_SCENE_REMARK_OR_NAME_WITH_NICK)}"
            android:textColor="@color/app_black"
            android:textSize="17sp"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_edit"
            app:layout_constraintStart_toEndOf="@id/iv_avatar"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="达芬奇达芬奇达芬达芬奇达芬奇达芬达芬奇达芬奇达芬" />

        <ImageView
            android:id="@+id/iv_edit"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="24dp"
            android:onClick="@{() -> callback.onEdit()}"
            android:src="@mipmap/organization_ic_edit_grey"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_share"
            app:layout_constraintTop_toTopOf="parent"
            app:visibleGone="@{viewModel.userCardInfo.isSelf}" />

        <ImageView
            android:id="@+id/iv_share"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="20dp"
            android:onClick="@{() -> callback.onShare()}"
            android:src="@drawable/organization_ic_share_gray"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>