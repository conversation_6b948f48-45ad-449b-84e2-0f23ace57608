<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <import type="android.text.TextUtils" />
        <import type="com.twl.hi.organization.user.span.AppendClickMovementMethod" />

        <variable
            name="bean"
            type="android.text.SpannableStringBuilder" />
        <variable
            name="callback"
            type="com.twl.hi.organization.user.callback.UserInfoCallbackNew" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingEnd="10dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:showIfPresent="@{bean}"
            android:textColor="@color/app_black"
            android:textSize="16sp"
            app:movementMethod="@{AppendClickMovementMethod.INSTANCE}" />

    </LinearLayout>

</layout>