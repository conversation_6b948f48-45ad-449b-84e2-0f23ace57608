package com.twl.hi.shorthand.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.hi.shorthand.api.response.GetShortHandTypeResponse;
import com.twl.hi.shorthand.api.response.ShortHandInfoResponse;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

public class QueryShortHandByIdRequest extends BaseApiRequest<ShortHandInfoResponse> {
    public QueryShortHandByIdRequest(BaseApiRequestCallback<ShortHandInfoResponse> callback) {
        super(callback);
    }

    @Expose
    public String id;

    @Override
    public String getUrl() {
        return URLConfig.QUERY_SHORTHAND_BY_ID;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
