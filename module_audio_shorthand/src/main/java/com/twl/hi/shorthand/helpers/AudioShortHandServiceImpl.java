package com.twl.hi.shorthand.helpers;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.annotation.RouterService;
import com.twl.hi.audioshorthand.R;
import com.twl.hi.basic.dialog.DialogUtils;
import com.twl.hi.export.audio.shorthand.IAudioShortHandService;
import com.twl.hi.export.audio.shorthand.router.AudioShortHandPageRouter;
import com.twl.hi.foundation.media.IMeetingService;
import com.twl.hi.foundation.media.MediaConstants;
import com.twl.hi.shorthand.api.response.ShortHandInfoResponse;
import com.twl.hi.shorthand.callback.HttpEndShortHandCallback;
import com.twl.hi.shorthand.callback.HttpQueryShortHandListCallback;
import com.twl.utils.network.NetworkHelper;

import java.util.List;

import lib.twl.common.base.BaseApplication;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.LList;
import lib.twl.common.util.ToastUtils;

@RouterService(interfaces = IAudioShortHandService.class, key = AudioShortHandPageRouter.AUDIO_SHORTHAND_SERVICE)
public class AudioShortHandServiceImpl implements IAudioShortHandService {
    protected DialogUtils mShortHandTipDialog;

    @Override
    public void startAudioShortHand(@NonNull Context context) {
        if (!NetworkHelper.INSTANCE.isAvailable()) {
            ToastUtils.failure(R.string.shorthand_check_net_status);
            return;
        }

        IMeetingService service = Router.getService(IMeetingService.class, MediaConstants.MEETING_ENGINE_LISTENER_KEY);
        if (service.isConnected()) {
            new DialogUtils.Builder(context)
                    .setTitle("你正在Boss Hi会议中，无法使用AI速记")
                    .setAutoCloseAfterClick(true)
                    .setPositive("我知道了")
                    .build()
                    .show();
        } else {
            AppUtil.startUri(context, AudioShortHandPageRouter.AUDIO_SHORTHAND, ActivityAnimType.UP_GLIDE);
        }
    }

    @Override
    public MutableLiveData<Boolean> stopHistoryAudioShortHand() {
        MutableLiveData<Boolean> shortHandEnd = new MutableLiveData<>();

        if (!NetworkHelper.INSTANCE.isAvailable()) {
            ToastUtils.failure(R.string.shorthand_check_net_status);
            shortHandEnd.postValue(false);
            return shortHandEnd;
        }
        ShortHandHelper.getInstance().queryShortHandListByUserId(new HttpQueryShortHandListCallback() {
            @Override
            public void queryShortHandListByUserId(List<ShortHandInfoResponse.ShortHandInfo> shortHandInfos) {
                if (LList.isNotEmpty(shortHandInfos) && shortHandInfos.size() == 1) {
                    for (ShortHandInfoResponse.ShortHandInfo shortHandinfo : shortHandInfos) {
                        ShortHandHelper.getInstance().endShortHand(shortHandinfo.id, new HttpEndShortHandCallback() {
                            @Override
                            public void endShortHandHttpCallback(boolean state) {
                                shortHandEnd.postValue(state);
                                killShortHandActivity();
                            }
                        });
                    }
                } else {
                    shortHandEnd.postValue(false);
                }

            }
        });
        return shortHandEnd;
    }

    @Override
    public boolean getShortHanding() {
        return ShortHandHelper.getInstance().isShortHanding();
    }

    @Override
    public void killShortHandActivity() {
        ShortHandHelper.getInstance().killShortHandActivity();
    }

    @Override
    public MutableLiveData<Boolean> showShortHandTipDialog(LifecycleOwner lifecycleOwner) {

        MutableLiveData<Boolean> isShortHandEnd = new MutableLiveData<>();
        if (mShortHandTipDialog == null) {
            mShortHandTipDialog = new DialogUtils.Builder(BaseApplication.getApplication().getTopContext())
                    .setCancelable(true)
                    .setAutoCloseAfterClick(true)
                    .setCanceledOnTouchOutside(true)
                    .setTitle("你正在通过AI速记进行录音\n若发起/加入会议则将自动结束录音")
                    .setNegative("取消")
                    .setNegativeListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (mShortHandTipDialog != null) {
                                mShortHandTipDialog.dismiss();
                                isShortHandEnd.postValue(false);
                            }
                        }
                    })
                    .setPositive("确定")
                    .setPositiveListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (!NetworkHelper.INSTANCE.isAvailable()) {
                                ToastUtils.failure(R.string.shorthand_check_net_status);
                                return;
                            }
                            stopHistoryAudioShortHand().observe(lifecycleOwner, new Observer<Boolean>() {
                                @Override
                                public void onChanged(Boolean aBoolean) {
                                    if (aBoolean) {
                                        killShortHandActivity();
                                    }
                                    isShortHandEnd.postValue(aBoolean);
                                }
                            });

                        }
                    }).build();
        }
        mShortHandTipDialog.show();
        return isShortHandEnd;
    }

}
