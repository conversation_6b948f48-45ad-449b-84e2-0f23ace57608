package com.twl.hi.shorthand.adapter;

import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.twl.hi.audioshorthand.R;
import com.twl.hi.shorthand.api.response.bean.ShortHandTypeBean;
import com.twl.hi.shorthand.callback.OnSelectAIThemeCallback;

import lib.twl.common.views.adapter.BaseQuickAdapter;
import lib.twl.common.views.adapter.BaseViewHolder;

/**
 **/
public class ShortHandAIThemeAdapter extends BaseQuickAdapter<ShortHandTypeBean, BaseViewHolder> {
    private TextView titleView;

    private TextView descView;

    private ConstraintLayout container;

    private ImageView selectIconView;

    private ShortHandTypeBean selectItem;
    private boolean hasOperated;
    private OnSelectAIThemeCallback callback;

    public ShortHandAIThemeAdapter(OnSelectAIThemeCallback callback) {
        super(R.layout.item_ai_shorthand_theme);
        this.callback = callback;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, ShortHandTypeBean item) {
        if (item == null) {
            return;
        }
        selectIconView = helper.getView(R.id.iv_select_view);
        container = helper.getView(R.id.container);
        titleView = helper.getView(R.id.tv_title);
        descView = helper.getView(R.id.tv_desc);
        if (item.defaultSelected && !hasOperated) {
            selectItem = item;
        }

        titleView.setText(item.name);
        descView.setText(item.desc);
        selectIconView.setImageResource(isSelected(item) ? R.drawable.ic_box_select : R.drawable.ic_box_unselect);
        container.setBackgroundResource(isSelected(item) ? R.drawable.shorthand_bg_item_ai_theme_select : R.drawable.shorthand_bg_item_ai_theme_unselect);
        container.setOnClickListener(v -> {
            hasOperated = true;
            if (selectItem == item) {
                selectItem = null;
            } else {
                selectItem = item;
            }

            if (callback != null) {
                callback.onSelectAITheme(selectItem);
            }
            notifyDataSetChanged();
        });
    }

    private boolean isSelected(ShortHandTypeBean item) {
        return item != null && selectItem == item;
    }
}
