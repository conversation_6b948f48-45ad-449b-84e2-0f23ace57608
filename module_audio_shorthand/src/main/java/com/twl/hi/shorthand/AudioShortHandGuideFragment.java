package com.twl.hi.shorthand;

import static lib.twl.common.ext.ViewExtKt.getStatusBarsHeight;


import android.Manifest;
import android.content.Context;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.LeadingMarginSpan;


import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.audioshorthand.BR;
import com.twl.hi.audioshorthand.R;
import com.twl.hi.audioshorthand.databinding.FragmentAudioShortHandGuideBinding;

import com.twl.hi.basic.dialog.PermissionDialogUtil;
import com.twl.hi.basic.helpers.AppPageRouterHelper;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.shorthand.api.response.bean.ShortHandTypeBean;
import com.twl.hi.shorthand.callback.AudioShortHandGuideFragmentCallback;
import com.twl.hi.shorthand.dialog.SelectShortHandThemeBottomDialog;
import com.twl.hi.shorthand.helpers.ShortHandTimerHelper;
import com.twl.hi.shorthand.viewmodel.AudioShortHandGuideViewModel;
import com.twl.hi.shorthand.helpers.ShortHandHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import lib.twl.common.permission.PermissionAvoidManager;
import lib.twl.common.util.ActivityAnimType;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.LList;


public class AudioShortHandGuideFragment extends ShortHandBaseFragment<FragmentAudioShortHandGuideBinding, AudioShortHandGuideViewModel>
        implements AudioShortHandGuideFragmentCallback {

    private static final String TAG = "AudioShortHandGuideFragment";
    /**
     * 权限管理器，注意及时释放
     */
    PermissionAvoidManager manager;
    /**
     * 录音权限数组
     */
    String[] permissionsAudio = new String[]{Manifest.permission.MODIFY_AUDIO_SETTINGS, Manifest.permission.RECORD_AUDIO};
    /**
     * 蓝牙权限数组
     */
    private String[] mBlueToothPermission;
    /**
     * 标记是否需要在onResume时重新请求权限
     */
    private boolean needRequestPermissionOnResume = false;
    /**
     * 速记核心ViewModel/Helper，负责业务数据和录音状态
     */
    ShortHandHelper shortHandHelper;

    public static AudioShortHandGuideFragment newInstance() {
        AudioShortHandGuideFragment fragment = new AudioShortHandGuideFragment();
        Bundle bundle = new Bundle();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.fragment_audio_short_hand_guide;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public AudioShortHandGuideFragmentCallback getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void initView() {
        super.initView();
        new PointUtils.BuilderV4()
                .name("bosshi_video-shorthand-access-to-the-main-page-pageview")
                .point();
        getDataBinding().llContainer.setPadding(0, getStatusBarsHeight(activity), 0, 0);
//        TextView textView = findViewById(R.id.textView);
        String precautions_info = this.getResources().getString(R.string.precautions_info);
        int indent = (int) getDataBinding().tvNotice.getPaint().measureText("1. "); // 对齐字符宽度
        SpannableString spannableString = new SpannableString(precautions_info);
        spannableString.setSpan(new LeadingMarginSpan.Standard(0, indent), 0, precautions_info.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        getDataBinding().tvNotice.setText(spannableString);
        shortHandHelper = ShortHandHelper.getInstance();
        if (shortHandHelper != null) {
            shortHandHelper.setDefaultSelectBean(null);
            shortHandHelper.getSubjectList().postValue(new ArrayList<>());
            shortHandHelper.getShortHandType();
        }

    }

    @Override
    protected void initData() {
        super.initData();
    }

    @Override
    protected void initObserver() {
        super.initObserver();

    }

    @Override
    public void onResume() {
        super.onResume();
        if (needRequestPermissionOnResume) {
            needRequestPermissionOnResume = false;
            setPermissionAvoidManager();
        }
    }

    @Override
    public void closeClicked() {
        AppUtil.finishActivity(getActivity(), ActivityAnimType.UP_GLIDE);
    }

    @Override
    public void startRecord() {
        setPermissionAvoidManager();
    }

    /**
     * 请求速记所需权限
     */
    public void setPermissionAvoidManager() {
        if (manager == null) {
            manager = new PermissionAvoidManager(this);
        }
        requestMicrophonePermission(() -> {
            requestBluetoothPermission(() -> {
                if (shortHandHelper != null) {
                    List<ShortHandTypeBean> subjectList = shortHandHelper.getSubjectList() != null ? shortHandHelper.getSubjectList().getValue() : null;
                    if (LList.isNotEmpty(subjectList)) {
                        showSelectAIShortHandThemeBottomDialog(subjectList);
                    } else {
                        if (shortHandHelper.getShortHandType() != null && activity != null) {
                            shortHandHelper.getShortHandType().observe((FragmentActivity) activity, new Observer<List<ShortHandTypeBean>>() {
                                @Override
                                public void onChanged(List<ShortHandTypeBean> subjectListBeanList) {
                                    if (LList.isNotEmpty(subjectListBeanList)) {
                                        showSelectAIShortHandThemeBottomDialog(subjectListBeanList);
                                    }
                                }
                            });
                        }
                    }
                }
            });
        });
    }


    public String[] getBlueToothPermission() {
        if (mBlueToothPermission == null) {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                mBlueToothPermission = new String[]{Manifest.permission.BLUETOOTH_CONNECT};
            } else {
                mBlueToothPermission = new String[]{};
            }
        }
        return mBlueToothPermission;
    }

    private void requestMicrophonePermission(Runnable next) {
        if (manager != null) {
            manager.requestPermission(permissionsAudio, (hasPermission, shouldShowAllRequestPermissionRationale) -> {
                if (hasPermission) {
                    Optional.ofNullable(next).ifPresent(Runnable::run);
                } else {
                    showMicrophonePermissionFailDialog(next);
                }
            });
        }
    }

    private void showMicrophonePermissionFailDialog(Runnable next) {
        if (getActivity() == null) return;
        PermissionDialogUtil.showMicrophoneFailedDialog(getActivity(), () -> {
            if (next != null) next.run();
            AppUtil.finishActivity(getActivity(), ActivityAnimType.UP_GLIDE);
            return null;
        }, () -> {
            AppPageRouterHelper.jumpToAppSettingsPage(getActivity());
            needRequestPermissionOnResume = true;
            return null;
        });
    }

    public void requestBluetoothPermission(Runnable next) {
        if (getActivity() == null) return;
        new PermissionAvoidManager(this).requestPermission(getBlueToothPermission(), (hasPermission, shouldShowAllRequestPermissionRationale) -> {
            if (hasPermission) {
                Optional.ofNullable(next).ifPresent(Runnable::run);
            } else {
                showBluetoothFailDialog(next);
            }
            TLog.debug(TAG, "shouldShowAllRequestPermissionRationale " + shouldShowAllRequestPermissionRationale + "蓝牙权限" + hasPermission + ":" + Arrays.toString(mBlueToothPermission));
        });
    }

    private void showBluetoothFailDialog(Runnable next) {
        if (activity == null) return;
        PermissionDialogUtil.showBluetoothFailedDialog(activity, () -> {
            if (next != null) next.run();
            AppUtil.finishActivity(activity, ActivityAnimType.UP_GLIDE);
            return null;
        }, () -> {
            if (next != null) next.run();
            AppUtil.finishActivity(activity, ActivityAnimType.UP_GLIDE);
            return null;
        }, () -> {
            AppPageRouterHelper.jumpToAppSettingsPage(activity);
            needRequestPermissionOnResume = true;
            return null;
        });
    }

    protected void showSelectAIShortHandThemeBottomDialog(List<ShortHandTypeBean> subjectListBeanList) {
        SelectShortHandThemeBottomDialog dialog = new SelectShortHandThemeBottomDialog(subjectListBeanList, new SelectShortHandThemeBottomDialog.OnAIThemeSelectCallback() {

            @Override
            public void onSelect(@Nullable ShortHandTypeBean selectSubject) {
                if (selectSubject != null) {
                    if (shortHandHelper != null) {

                        List<ShortHandTypeBean> subjectListBeanList = shortHandHelper.getSubjectList().getValue();
                        for (int i = 0; i < subjectListBeanList.size(); i++) {
                            ShortHandTypeBean bean = subjectListBeanList.get(i);
                            if (TextUtils.equals(selectSubject.code, bean.code)) {
                                bean.defaultSelected = true;
                            } else {
                                bean.defaultSelected = false;
                            }
                        }
                        shortHandHelper.setDefaultSelectBean(selectSubject);
                        ShortHandTimerHelper.getInstance().setHasCalledTenMinutesCallback(false);
                        shortHandHelper.shortHandBegin(shortHandHelper.getShortHandTitle(), shortHandHelper.getDefaultSelectBean().code).observe(getActivity(), new Observer<String>() {
                            @Override
                            public void onChanged(String s) {
                                new PointUtils.BuilderV4()
                                        .name("bosshi_video-shorthand-initiate-shorthand-notes-other")
                                        .params("meeting_type", selectSubject.code)
                                        .point();
                                shortHandHelper.setShorthandReqId(s);
                                shortHandHelper.initVoiceRecognizeManager(s);
                                shortHandHelper.executeStartRecord();
                                shortHandHelper.setPageIndex(shortHandHelper.PAGE_INDEX_SHORTHAND_MAIN);
                            }
                        });
                    }
                }
            }

            @Override
            public void onDismiss() {
            }
        });
        dialog.show(((FragmentActivity) activity).getSupportFragmentManager(), "ShortHandSubjectSelectDialog");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 释放权限管理器资源
        manager = null;
        shortHandHelper = null;
        mBlueToothPermission = null;
    }
}
