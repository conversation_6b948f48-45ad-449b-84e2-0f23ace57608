package com.twl.hi.voice.recognizer.sr.ui;



import static com.twl.hi.voice.recognizer.sr.Constant.MAX_AMPLITUDE_TO_DRAW;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import com.twl.hi.audioshorthand.R;
import com.twl.hi.voice.recognizer.sr.coder.AudioSummary;


public class Waveform extends View implements AudioSummary.DecodeCallback {
    private static final String TAG = "WaveformView";


    private short[] mAudioData;
    private Paint mPaint;
    private AudioSummary mAudioSummary;

    public Waveform(Context context) {
        this(context, null, 0);
    }

    public Waveform(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public Waveform(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mPaint = new Paint();
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setStrokeWidth(0);
        mPaint.setAntiAlias(true);

        mPaint.setColor(Color.parseColor("#429F99"));
        setBackgroundResource(R.drawable.bg_my_bullet_shadow);
        getBackground().mutate().setAlpha(20);
    }

    public void setSource(AudioSummary summary) {
        mAudioSummary = summary;
        mAudioSummary.setDecodeCallback(this);
        mAudioSummary.getWaveform();
    }



    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (mAudioSummary != null) {
            mAudioSummary.setDecodeCallback(this);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mAudioSummary != null) {
            mAudioSummary.setDecodeCallback(null);
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawWaveform(canvas);
    }



    private void drawWaveform(Canvas canvas) {
        // Clear the screen each time because SurfaceView won't do this for us.
//        canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);


        float width = getMeasuredWidth();
        float height = getMeasuredHeight();
        float centerY = height / 2;


        if (mAudioData != null && mAudioData.length > 0) {
            float xDelta = width / mAudioData.length;

            float x = 0;
            for (short amplitude : mAudioData) {
                float gap = (Math.abs(amplitude) / MAX_AMPLITUDE_TO_DRAW) * centerY;
                float temp = x + xDelta;
                canvas.drawRect(new RectF(x, centerY + gap, temp, centerY - gap), mPaint);
                x = temp;
            }
        }


        if (mAudioSummary == null) return;
        int mProgress = mAudioSummary.getProgress();
        if (mProgress > 0 && mProgress <= 100) {
            canvas.drawRect(new RectF(0, height, width * mProgress / 100, 0f), getMaskPaint());
        }
    }


    private Paint mMaskPaint;

    private Paint getMaskPaint() {
        if (mMaskPaint == null) {
            mMaskPaint = new Paint();
            mMaskPaint.setStyle(Paint.Style.FILL);
            mMaskPaint.setColor(0x22000000);
            mMaskPaint.setStrokeWidth(0);
        }
        return mMaskPaint;
    }

    @Override
    public synchronized void onResult(short[] waveform) {
        Log.d(TAG, "onResult() called with: waveform = [" + mAudioSummary + "]");
        // Update the display.
        mAudioData = waveform;
        postInvalidate();
    }

    @Override
    public void onProgressListener() {
        postInvalidate();
    }
}
