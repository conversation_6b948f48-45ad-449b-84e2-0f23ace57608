package com.twl.hi.voice.recognizer.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;


/*
 * 语音波形自定义View
 * 用于根据分贝数据动态绘制音量波形，支持历史数据恢复与实时动画。
 * 内部注意动画资源的释放，防止内存泄漏。
 */
public class VoiceWaveView extends View {
    // 最大波形条数（动态根据宽度调整）
    private int maxWaveCount = 100;
    // 每条波形的宽度（px）
    private int waveWidth = 6;
    // 波形之间的间隔（px）
    private int waveGap = 1;
    // 当前所有波形的高度（单位：px，实际为分贝映射值）
    private final ArrayList<Integer> waveHeights = new ArrayList<>();
    // 画笔对象
    private final Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
    // 动画集合，防止内存泄漏，onDetachedFromWindow 时需全部 cancel
    private final ArrayList<android.animation.ValueAnimator> animators = new ArrayList<>();
    // 防止 setWaveData 在未 layout 时多次 post，导致消息队列堆积
    private boolean waitingForLayout = false;

    /**
     * 构造方法
     *
     * @param context 上下文
     * @param attrs   属性
     */
    public VoiceWaveView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        // 设置线性渐变色，竖直方向
        // 注意：此处 shader 需在 onSizeChanged 里动态设置高度，否则初始高度为0
//        paint.setStrokeWidth(waveWidth); // 设置初始线宽

    }

    /**
     * 尺寸变化时，动态调整最大波形数和每条波形宽度/间隔
     * 并重新设置渐变色
     */
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        // 小宽度下自动减小宽度和间隔，保证悬浮窗下波形密集
        if (w <= dp2px(getContext(), 30)) {
            waveWidth = dp2px(getContext(), 2); // 3dp
            waveGap = dp2px(getContext(), 2);   // 1dp
        } else {
            waveWidth = dp2px(getContext(), 3); // 5dp
            waveGap = dp2px(getContext(), 3);   // 2dp
        }

        paint.setStrokeWidth(waveWidth); // 设置初始线宽
        paint.setStrokeCap(Paint.Cap.ROUND); // 圆角线帽
        paint.setStrokeJoin(Paint.Join.ROUND); // 圆角线帽

        int total = w / (waveWidth + waveGap);
        maxWaveCount = Math.max(5, total); // 保证最少5个
        // 渐变色
        android.graphics.LinearGradient shader = new android.graphics.LinearGradient(
                0, 0, 0, h,
                new int[]{0xFF567BF7, 0xFF698FF9, 0xFF88AFFD},
                new float[]{0.0f, 0.5f, 1f},
                android.graphics.Shader.TileMode.CLAMP
        );
        paint.setShader(shader);
        invalidate();
    }

    // dp转px工具方法
    private int dp2px(Context context, float dp) {
        return (int) (context.getResources().getDisplayMetrics().density * dp + 0.5f);
    }

    /**
     * 批量设置波形数据（用于初始化/恢复历史数据，不做动画）
     * 如果 View 尚未 layout（getHeight()==0），post 延迟刷新，防止高度为0导致波形不可见。
     *
     * @param dbList 分贝数据列表
     */
    public void setWaveData(List<Integer> dbList) {
        if (getHeight() == 0) {
            // View 还没 layout，延迟到 layout 完成后再设置，防止高度为0导致波形高度为0
            if (!waitingForLayout) {
                waitingForLayout = true;
                post(() -> {
                    waitingForLayout = false;
                    setWaveData(dbList);
                });
            }
            return;
        }
        waveHeights.clear();
        if (dbList != null) {
            int start = Math.max(0, dbList.size() - maxWaveCount);
            for (int i = start; i < dbList.size(); i++) {
                int db = dbList.get(i);
                int height = mapDbToHeight(db);
                waveHeights.add(height);
            }
        }
        invalidate();
    }

    /**
     * 新分贝动画（每次新分贝到来时调用，带动画效果）
     * 只做实时动画，不缓存历史。
     *
     * @param db 当前分贝值
     */
    public void setVolume(int db) {
        int height = mapDbToHeight(db);
        if (waveHeights.size() >= maxWaveCount) {
            waveHeights.remove(0);
        }
        waveHeights.add(height);
        invalidate();
    }

    /**
     * 分贝值映射为波形高度
     *
     * @param db 分贝值（建议10~100）
     * @return 映射后的像素高度
     */
    private int mapDbToHeight(int db) {
        int viewHeight = getHeight();
        db = Math.max(10, Math.min(db, 100)); // 限制分贝范围
        int height = (int) (viewHeight * (db - 10) / 90f);
        int minHeight = dp2px(getContext(), 5); // 最小高度3dp
        return Math.max(height, minHeight);
    }

    /**
     * 绘制所有波形条
     *
     * @param canvas 画布
     */
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int size = waveHeights.size();
        int totalWidth = maxWaveCount * waveWidth + (maxWaveCount - 1) * waveGap;
        int right = getWidth();
        int left = right - totalWidth;
        int startIndex = maxWaveCount - size; // 空白数量
        int i = 0;
        // 跳过左侧空白
        for (; i < startIndex; i++) {
        }
        // 右侧依次绘制波形
        for (Integer h : waveHeights) {
            int x = left + i * (waveWidth + waveGap);
            int yStart = getHeight() / 2 - h / 2;
            int yEnd = getHeight() / 2 + h / 2;
            float radius = waveWidth; // 推荐
            // 使用 drawRoundRect 替代 drawLine，保证高波形时圆角依然细腻
            canvas.drawRoundRect(
                    x, yStart,
                    x + waveWidth, yEnd,
                    radius, radius, paint
            );
            i++;
        }
    }

    /**
     * 释放动画资源，防止内存泄漏。
     * onDetachedFromWindow 时彻底 cancel 并清空 animators。
     */
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        // 防止动画引用 View 导致内存泄漏
        for (android.animation.ValueAnimator animator : animators) {
            animator.cancel();
        }
        animators.clear();
    }
}