package com.twl.hi.voice.recognizer.sr.sounds;

import android.annotation.SuppressLint;

import com.twl.hi.foundation.api.request.BaseFileDownloadCallback;
import com.twl.hi.foundation.api.request.FileDownloadRequest;
import com.twl.hi.voice.recognizer.tool.FileUtils;
import com.twl.hi.voice.recognizer.tool.PathUtils;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;



import java.io.File;

import lib.twl.common.util.MD5;


/**
 * Created by monch on 15/4/15.
 */
public class SoundFile {

    public static final int MAX_RETRY_COUNT = 1;

    private SoundFile() {
    }

    private static SoundFile instance;

    public static SoundFile getInstance() {
        if (instance == null) {
            instance = new SoundFile();
        }
        return instance;
    }


    public String getFileName(String url) {
        return MD5.convert(url) + ".amr";
    }


    public File createLocalSoundFilePath(final String url) {
        String soundPath = PathUtils.getCacheDirChildPathExternalFirst("sound");
        return FileUtils.getFileByPath(soundPath, getFileName(url));
    }


    @SuppressLint("twl_utils_file")
    public boolean checkSoundFileExist(final String url) {
        try {
            return createLocalSoundFilePath(url).exists();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @SuppressLint("twl_utils_file")
    public void addNewTaskDownloadFile(final String url) {
        File file = createLocalSoundFilePath(url);
        addNewTaskDownloadFile(url, file, MAX_RETRY_COUNT);
    }

    @SuppressLint("twl_utils_file")
    public void addNewTaskDownloadFile(final String url, final File file, int retryCount) {
        if (!file.exists()) {
            HttpExecutor.download(new FileDownloadRequest(url, file.getParent(), file.getName(), new BaseFileDownloadCallback() {
                @Override
                public void onFail(String url, ErrorReason reason) {
                    if ((ErrorReason.ERROR_SERVER_FAILED + " 403").equals(reason.getErrReason()) && retryCount > 0) {
//                        SimpleApiRequest.GET(URLConfig.URL_SIGN_REFRESH_UPLOAD)
//                                .addParam("url", URLEncoder.encode(url))
//                                .setRequestCallback(new SimpleCommonApiRequestCallback<SignRefreshHttpResponse>() {
//                                    @Override
//                                    public void handleInChildThread(ApiData<SignRefreshHttpResponse> data) {
//                                        addNewTaskDownloadFile(data.resp.url, file, retryCount - 1);
//                                    }
//                                })
//                                .execute();
                    }
                }

                @Override
                public void onSuccess(String url, File result) {

                }
            }));
        }
    }

    @SuppressLint("twl_utils_file")
    public void addNewTaskDownloadFile(final String url, BaseFileDownloadCallback downloadCallback) {
        String soundPath = PathUtils.getCacheDirChildPathExternalFirst("sound");
        File file = FileUtils.getFileByPath(soundPath, getFileName(url));

        if (!file.exists()) {
            HttpExecutor.download(new FileDownloadRequest(url, soundPath, getFileName(url), downloadCallback));
        }
    }

    public boolean copyFile(File oldFile, String url) {
        String soundPath = PathUtils.getCacheDirChildPathExternalFirst("sound");
        FileUtils.copy(oldFile, FileUtils.getFileByPath(soundPath, getFileName(url)));
        return true;
    }

}
