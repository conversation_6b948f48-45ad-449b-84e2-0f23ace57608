<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="callback"
            type="com.twl.hi.basic.dialog.bottom.BottomRadioSelectCallback" />
    </data>

    <com.twl.hi.basic.views.HiShadowFrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_white"
        app:lyt_topLeftRadius="20dp"
        app:lyt_topRightRadius="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginLeft="20dp"
                android:layout_marginBottom="12dp"
                android:textColor="@color/color_0D0D1A"
                android:textSize="20sp"
                tools:text="选择提醒时间" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <com.twl.hi.basic.views.CommonTextView
                android:id="@+id/tv_cancel"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_margin="20dp"
                android:background="@color/color_F2F2F5"
                android:gravity="center"
                android:text="@string/cancel"
                android:textColor="@color/color_0D0D1A"
                android:textSize="17sp"
                app:btn_roundRadius="7dp"
                android:onClick="@{v->callback.onClickDismiss()}" />


        </LinearLayout>
    </com.twl.hi.basic.views.HiShadowFrameLayout>
</layout>