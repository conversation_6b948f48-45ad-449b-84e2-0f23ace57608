<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.text.TextUtils" />

        <import type="android.view.View" />

        <import type="com.twl.hi.foundation.utils.ContactUtils" />

        <variable
            name="user"
            type="com.twl.hi.basic.model.SearchContact" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@drawable/sel_item_user"
        android:clickable="true"
        android:focusable="true"
        android:orientation="horizontal"
        android:paddingLeft="20dp">

        <include
            android:id="@+id/vg_avatar"
            layout="@layout/item_avatar2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            app:avatarContactOnly="@{user.contact}" />

        <com.twl.hi.basic.views.multitext.ChatRecordSearchTextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="16dp"
            android:singleLine="true"
            android:textColor="@color/color_0D0D1A"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="8dp"
            android:background="@drawable/bg_rect_radius_4_solid_294c596a"
            android:paddingLeft="4dp"
            android:paddingTop="2dp"
            android:paddingRight="4dp"
            android:paddingBottom="2dp"
            android:text="@string/have_resign"
            android:textColor="@color/color_4C596A"
            android:textSize="10sp"
            app:visibleGone="@{user.contact.status==2}" />

        <TextView
            android:id="@+id/tv_sign"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="  -  "
            android:textColor="#9B9B9B"
            android:textSize="17sp"
            app:visibleGone="@{!TextUtils.isEmpty(user.getContact().showDeptNames)&amp;&amp;user.status!=2}" />

        <TextView
            android:id="@+id/tv_department"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left|center_vertical"
            android:layout_marginRight="20dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{user.getContact().showDeptNames}"
            android:textColor="#9B9B9B"
            android:textSize="17sp"
            app:visibleGone="@{!TextUtils.isEmpty(user.getContact().showDeptNames)&amp;&amp;user.status!=2}" />


    </LinearLayout>

</layout>
