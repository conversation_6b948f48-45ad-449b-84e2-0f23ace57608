<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="showIndicator"
            type="androidx.databinding.ObservableBoolean" />

        <variable
            name="callback"
            type="com.twl.hi.basic.dialog.bottom.BottomRadioSelectCallback" />
    </data>

    <com.twl.hi.basic.views.HiShadowFrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_white"
        app:lyt_topLeftRadius="20dp"
        app:lyt_topRightRadius="20dp"
        android:paddingBottom="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:id="@+id/indicator"
                android:layout_width="36dp"
                android:layout_height="4dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/bg_corner_3_color_cfcfcf"
                app:visibleInVisible="@{showIndicator}"/>

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginBottom="12dp"
                android:textColor="@color/color_0D0D1A"
                android:textSize="20sp"
                android:textStyle="bold"
                tools:text="选择提醒时间" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_bottom_bt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="20dp">

                <com.twl.hi.basic.views.CommonTextView
                    android:id="@+id/tv_cancel"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:background="@color/color_F2F2F5"
                    android:gravity="center"
                    android:text="@string/cancel"
                    android:textColor="@color/color_0D0D1A"
                    android:textSize="17sp"
                    app:btn_roundRadius="7dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/tv_sure"
                    app:layout_constraintTop_toTopOf="parent"
                    android:onClick="@{v->callback.onClickDismiss()}" />

                <com.twl.hi.basic.views.CommonTextView
                    android:id="@+id/tv_sure"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginLeft="10dp"
                    android:gravity="center"
                    android:onClick="@{v->callback.onClickSure()}"
                    android:text="@string/sure"
                    android:textColor="@color/app_white"
                    android:textSize="17sp"
                    app:btn_roundRadius="7dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:background="@color/color_primary" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </com.twl.hi.basic.views.HiShadowFrameLayout>
</layout>