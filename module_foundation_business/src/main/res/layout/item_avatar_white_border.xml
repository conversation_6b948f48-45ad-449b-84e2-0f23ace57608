<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="avatar"
            type="String" />

        <variable
            name="userName"
            type="String" />

        <variable
            name="avatarDecoration"
            type="String" />

        <import type="android.text.TextUtils"/>
        <import type="android.view.View"/>

    </data>

    <FrameLayout
        android:id="@+id/cd_avatar"
        android:layout_width="match_parent"
        android:background="@drawable/bg_avatar_shadows"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tv_avatar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="3dp"
            android:background="@drawable/bg_text_avatar"
            android:gravity="center"
            android:text="@{userName}"
            android:textColor="@color/app_white"
            android:textSize="24sp"
            tools:text="Sa" />

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/iv_avatar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="3dp"
            android:scaleType="centerCrop"
            app:imageUrl="@{avatar}"
            app:progressBarImage="@drawable/bg_text_avatar"
            app:roundAsCircle="true" />

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/iv_pendant"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:pendantUrl="@{avatarDecoration}"
            android:visibility="@{TextUtils.isEmpty(avatarDecoration)?View.GONE:View.VISIBLE}"
            app:progressBarImage="@drawable/bg_text_avatar" />

    </FrameLayout>
</layout>