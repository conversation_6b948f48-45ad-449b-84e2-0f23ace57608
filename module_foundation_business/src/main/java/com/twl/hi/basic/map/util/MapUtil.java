package com.twl.hi.basic.map.util;

import static java.lang.Math.PI;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;

import com.amap.api.services.core.LatLonPoint;
import com.twl.hi.basic.R;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

import lib.twl.common.util.AppUtil;
import lib.twl.common.util.ToastUtils;

public class MapUtil {

    public static void jumpToMapApp(Context context, double lng, double lat) {
        String uriString = String.format("geo:%s,%s", lat, lng);
        Uri uri = Uri.parse(uriString);  //打开地图定位
        Intent intent = new Intent(Intent.ACTION_VIEW, uri);
        ComponentName componentName = intent.resolveActivity(context.getPackageManager());
        if (componentName == null) {
            ToastUtils.ss(R.string.string_no_map_app_installed);
        } else {
            AppUtil.startActivity(context, intent);
        }
    }

    public static boolean checkApkExist(Context context, String packageName) {
        if (TextUtils.isEmpty(packageName))
            return false;
        try {
            ApplicationInfo info = context.getPackageManager()
                    .getApplicationInfo(packageName,
                            PackageManager.GET_UNINSTALLED_PACKAGES);
            return info != null;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    public static List<MapAppInfoEntity> resolveMapApps(Context context, LatLonPoint latLonPoint, String address) {
        List<MapAppInfoEntity> appList = new ArrayList<>();
        boolean haveGPS = latLonPoint.getLatitude() != 0 || latLonPoint.getLongitude() != 0;
        if (checkApkExist(context, "com.autonavi.minimap")) {
            MapAppInfoEntity entity = getMapAppInfo(context, "com.autonavi.minimap");
            String aMapUriStr;
            if (haveGPS) {
                aMapUriStr = "androidamap://viewMap?sourceApplication=BossHi&poiname=" + address
                        + "&lat=" + latLonPoint.getLatitude() + "&lon=" + latLonPoint.getLongitude()
                        + "&dev=0";
            } else {
                //搜索地点
                aMapUriStr = "androidamap://poi?sourceApplication=BossHi&keywords=" + address + "&dev=0";
            }
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(aMapUriStr));
            intent.setPackage("com.autonavi.minimap");
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            entity.mapIntent = intent;
            appList.add(entity);
        }

        if (checkApkExist(context, "com.baidu.BaiduMap")) {
            LatLonPoint baiDuPoint = gaode2baidu(latLonPoint);
            MapAppInfoEntity entity = getMapAppInfo(context, "com.baidu.BaiduMap");
            String baiDuUriStr;
            if (haveGPS) {
                baiDuUriStr = "intent://map/marker?location=" + baiDuPoint.getLatitude() + ","
                        + baiDuPoint.getLongitude() + "&title=" + address + "&content=" + address
                        + "&src=BossHi|BossHi#Intent;scheme=bdapp;package=com.baidu.BaiduMap;end";
            } else {
                baiDuUriStr = "intent://map/place/search?query=" + address
                        + "&src=BossHi|BossHi#Intent;scheme=bdapp;package=com.baidu.BaiduMap;end";
            }
            try {
                Intent intent = Intent.parseUri(baiDuUriStr, 0);
                intent.setPackage("com.baidu.BaiduMap");
                intent.addCategory(Intent.CATEGORY_DEFAULT);
                entity.mapIntent = intent;
                appList.add(entity);
            } catch (URISyntaxException e) {
                e.printStackTrace();
            }
        }

        if (checkApkExist(context, "com.tencent.map")) {
            MapAppInfoEntity entity = getMapAppInfo(context, "com.tencent.map");
            String tMapUriStr;
            if (haveGPS) {
                tMapUriStr = "qqmap://map/routeplan?type=drive&from=&fromcoord=&to=" + address + "&tocoord="
                        + latLonPoint.getLatitude() + "," + latLonPoint.getLongitude() + "&policy=0&referer=BossHi";
            } else {
                tMapUriStr = "qqmap://map/search?keyword=" + address + "&referer=BossHi";
            }
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(tMapUriStr));
            intent.setPackage("com.tencent.map");
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            entity.mapIntent = intent;
            appList.add(entity);

        }

        if (checkApkExist(context, "com.google.android.apps.maps")) {
            MapAppInfoEntity entity = getMapAppInfo(context, "com.google.android.apps.maps");

            String googleUriStr = "geo:" + latLonPoint.getLatitude() + ","
                    + latLonPoint.getLongitude();
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(googleUriStr));
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            intent.setPackage("com.google.android.apps.maps");
            entity.mapIntent = intent;
            appList.add(entity);
        }
        return appList;
    }

    /**
     * 获取地图应用信息
     */
    private static MapAppInfoEntity getMapAppInfo(Context context, String packageName) {
        MapAppInfoEntity entity = null;
        try {
            if (!TextUtils.isEmpty(packageName) && context != null) {
                entity = new MapAppInfoEntity();
                entity.packageName = packageName;

                PackageManager packageManager = context.getPackageManager();

                PackageInfo packageInfo = packageManager.getPackageInfo(packageName, 0);
                entity.appName = packageInfo.applicationInfo.loadLabel(packageManager).toString();
                entity.appIcon = packageManager.getApplicationIcon(packageName);
                entity.versionCode = packageInfo.versionCode;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return entity;
    }

    /**
     * AMap transform to BaiDuMap location
     *
     * @param gaode AMap location
     * @return BaiDuMap location
     */
    private static LatLonPoint gaode2baidu(LatLonPoint gaode) {
        double x = gaode.getLongitude(), y = gaode.getLatitude();
        double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * PI);
        double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * PI);
        double bd_lon = z * Math.cos(theta) + 0.0065;
        double bd_lat = z * Math.sin(theta) + 0.006;
        return new LatLonPoint(bd_lat, bd_lon);
    }

}
