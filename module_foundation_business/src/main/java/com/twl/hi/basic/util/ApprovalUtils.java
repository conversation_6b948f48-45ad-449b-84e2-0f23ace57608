package com.twl.hi.basic.util;

import com.twl.hi.basic.R;
import com.twl.hi.foundation.api.base.HostConfig;
import com.twl.hi.foundation.api.base.URLConfig;

import hi.kernel.Constants;
import lib.twl.common.base.BaseApplication;

public class ApprovalUtils {
    public static StringBuilder getApprovalUrl(String applyType) {
        StringBuilder stringBuilder = new StringBuilder();
        switch (applyType) {
            case Constants
                    .PATCH_TYPE:
                stringBuilder.append(HostConfig.transformUrlHost(URLConfig.URL_H5_APPLY_DETAIL_ATTENDANCE));
                break;
            case Constants.LEAVE_TYPE:
                stringBuilder.append(HostConfig.transformUrlHost(URLConfig.URL_H5_APPLY_DETAIL_LEAVE));
                break;
            case Constants.AWAY_TYPE:
                stringBuilder.append(HostConfig.transformUrlHost(URLConfig.URL_H5_APPLY_DETAIL_TRAVEL));
                break;
        }
        return stringBuilder;
    }

    public static String getApprovalTitle(String applyType) {
        switch (applyType) {
            case Constants.PATCH_TYPE:
                return BaseApplication.getApplication().getResources().getString(R.string.patch);
            case Constants
                    .LEAVE_TYPE:
                return BaseApplication.getApplication().getResources().getString(R.string.leave);
            case Constants.AWAY_TYPE:
                return BaseApplication.getApplication().getResources().getString(R.string.away);
        }
        return "";

    }
}
