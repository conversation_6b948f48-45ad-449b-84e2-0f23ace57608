package com.twl.hi.basic.util

import android.app.Application
import androidx.lifecycle.viewModelScope
import com.twl.hi.basic.R
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.request.SyncContactRequest
import com.twl.hi.foundation.api.response.SyncContactResponse
import com.twl.hi.foundation.api.response.toContact
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.foundation.model.message.MessageConstants
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.error.ErrorReason
import hi.kernel.Constants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import lib.twl.common.base.BaseViewModel
import lib.twl.common.util.ProcessHelper
import lib.twl.common.util.ToastUtils

/**
 * Author : Xuweixiang .
 * Date   : On 2024/2/28
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

/**
 * 服务端处理了开关等逻辑，只需要判断是否可开聊
 */
fun BaseViewModel.handleContact(
    userId: String,
    chatVisible: Boolean,
    goChat: () -> Unit,
    goUserInfo: () -> Unit
) {
    viewModelScope.launch {
        val contact = withContext(Dispatchers.Default) {
            ServiceManager.getInstance().contactService.queryContactFromDb(userId)
        }
        if (contact == null) {
            ToastUtils.ss(getApplication<Application>().getString(R.string.contact_sync_in_progress))
            syncContact(userId)
            return@launch
        } else {
            handleContactJump(userId, chatVisible, goChat, goUserInfo)
        }
    }
}

/**
 * 需要根据 KEY_CHAT_VISIBLE_SWITCH 开关，判断是否用新的开聊逻辑。
 *
 */
fun BaseViewModel.handleLocalContact(
    userId: String,
    canChat: Boolean,
    chatVisible: Boolean,
    goChat: () -> Unit,
    goUserInfo: () -> Unit
) {
    val chatVisibleSwitchOn = ProcessHelper.getUserCompanyPreferences()
        .getBoolean(Constants.KEY_CHAT_VISIBLE_SWITCH, false)
    viewModelScope.launch {
        val contact = withContext(Dispatchers.Default) {
            ServiceManager.getInstance().contactService.queryContactFromDb(userId)
        }
        if (contact == null) {
            ToastUtils.ss(getApplication<Application>().getString(R.string.contact_sync_in_progress))
            syncContact(userId)
            return@launch
        }
        if (chatVisibleSwitchOn) {
            handleContactJump(userId, chatVisible, goChat, goUserInfo)
        } else {
            if (canChat) {
                goChat()
            } else {
                goUserInfo()
            }
        }
    }
}

/**
 * 根据chatVisible和本地是否有会话来处理联系人点击跳转
 */
private suspend fun handleContactJump(
    userId: String,
    chatVisible: Boolean,
    goChat: () -> Unit,
    goUserInfo: () -> Unit
) {
    if (chatVisible) {
        goChat()
    } else {
        // chatVisible为false但本地有会话 允许跳转聊天页面
        val hasConversation = withContext(Dispatchers.IO) {
            ServiceManager.getInstance().conversationService.hasConversation(
                userId,
                MessageConstants.MSG_SINGLE_CHAT
            )
        }
        if (hasConversation) {
            goChat()
        } else {
            goUserInfo()
        }
    }
}

private fun syncContact(userId: String) =
    SyncContactRequest(userId, object : BaseApiRequestCallback<SyncContactResponse>() {
        override fun onSuccess(data: ApiData<SyncContactResponse>?) {

        }

        override fun handleInChildThread(data: ApiData<SyncContactResponse>?) {
            super.handleInChildThread(data)
            data?.resp?.contacts?.firstOrNull()?.toContact()?.let {
                ServiceManager.getInstance().contactService.insertContact(it)
            }
        }

        override fun onFailed(reason: ErrorReason?) {

        }

    }).let {
        HttpExecutor.execute(it)
    }

