package com.twl.hi.basic.api.request

import com.google.gson.annotations.Expose
import com.twl.hi.foundation.api.base.BaseSyncApiRequestCallback
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.hi.basic.api.response.ExChangeBH2OpenResponse
import com.twl.http.client.BaseApiRequest
import com.twl.http.config.RequestMethod

/**
 * Author : Xuweixiang .
 * Date   : On 2023/5/10
 * Email  : Contact <EMAIL>
 * Desc   : 用 bosshi 字段置换开放平台字段
 *
 */

class ExChangeBH2OpenRequest(callback: BaseSyncApiRequestCallback<ExChangeBH2OpenResponse>) :
    BaseApiRequest<ExChangeBH2OpenResponse>(callback) {

    @Expose
    @JvmField
    var userIds: String? = ""

    @Expose
    @JvmField
    var chatIds: String? = ""

    @Expose
    @JvmField
    var appId: String? = ""

    override fun getUrl(): String {
        return URLConfig.URL_EXCHANGE_BH_TO_OPEN
    }

    override fun getMethod(): RequestMethod {
        return RequestMethod.GET
    }
}