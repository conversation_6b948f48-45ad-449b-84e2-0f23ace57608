package com.twl.hi.basic.helpers;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.logic.IDocumentUploader;
import com.twl.hi.basic.download.OkRenewalUpload;
import com.twl.hi.basic.download.RenewalUploadTask;
import com.twl.hi.foundation.api.base.BaseUpdateRequestCallback;
import com.twl.hi.foundation.api.response.FileUploadResponse;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import java.io.File;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于 RenewalUploadTask 的文档上传器实现
 * 使用分片上传和统一取消机制
 */
public class RenewalDocumentUploader implements IDocumentUploader {
    private static final String TAG = "RenewalDocumentUploader";
    
    // 保存活跃的上传任务引用（用于取消）
    private final Map<String, RenewalUploadTask> activeDocumentTasks = new ConcurrentHashMap<>();
    
    @Override
    public void uploadDocumentWithProgress(
            File file,
            DocumentUploadCallback callback,
            ProgressListener progressListener,
            String attachmentId
    ) {
        TLog.info(TAG, "uploadDocumentWithProgress: " + file.getAbsolutePath() + ", attachmentId: " + attachmentId);
        
        try {
            // 创建文件上传任务
            RenewalUploadTask renewalTask = OkRenewalUpload.createFileUploadTask(attachmentId, file);
            
            // 设置分片进度回调
            if (progressListener != null) {
                renewalTask.setChunkProgressCallback(new RenewalUploadTask.ChunkProgressCallback() {
                    @Override
                    public void onChunkCompleted(long completedChunks, long totalChunks, float progress) {
                        // 计算字节级进度（估算）
                        long totalBytes = file.length();
                        long uploadBytes = (long) (totalBytes * progress);
                        
                        progressListener.onProgress(progress, uploadBytes, totalBytes);
                    }
                });
            }
            
            // 设置完成回调
            renewalTask.register(new BaseUpdateRequestCallback<FileUploadResponse>() {
                @Override
                public void handleInChildThread(ApiData<FileUploadResponse> data) {
                    // 清理任务引用
                    activeDocumentTasks.remove(attachmentId);
                    
                    FileUploadResponse response = data.resp;
                    if (response != null && response.url != null && !response.url.isEmpty()) {
                        TLog.info(TAG, "Document upload success: " + attachmentId + ", url: " + response.url);
                        callback.onUploadSuccess(response.url);
                    } else {
                        TLog.error(TAG, "Document upload success but URL is empty: " + attachmentId);
                        callback.onUploadFailed(new ErrorReason(-1, "文档上传失败：服务器返回空URL"));
                    }
                }
                
                @Override
                public void handleErrorInChildThread(ErrorReason reason) {
                    // 清理任务引用
                    activeDocumentTasks.remove(attachmentId);
                    
                    TLog.error(TAG, "Document upload failed: " + attachmentId + ", reason: " + reason.getErrReason());
                    callback.onUploadFailed(reason);
                }
            });
            
            // 保存任务引用
            activeDocumentTasks.put(attachmentId, renewalTask);
            
            // 启动上传
            renewalTask.start();
            
        } catch (Exception e) {
            TLog.error(TAG, "Error in uploadDocumentWithProgress", e);
            callback.onUploadFailed(new ErrorReason(-1, "文档上传异常：" + e.getMessage()));
        }
    }
    
    /**
     * 取消指定的文档上传任务
     * 这个方法可以被外部调用来取消上传
     */
    public boolean cancelDocumentUpload(String attachmentId) {
        TLog.info(TAG, "cancelDocumentUpload: " + attachmentId);
        
        RenewalUploadTask task = activeDocumentTasks.remove(attachmentId);
        if (task != null) {
            // 使用静态方法取消任务
            RenewalUploadTask.cancel(attachmentId);
            TLog.info(TAG, "Successfully cancelled document upload task: " + attachmentId);
            return true;
        } else {
            TLog.info(TAG, "No active document upload task found for: " + attachmentId);
            return false;
        }
    }
    
    /**
     * 获取活跃的文档上传任务数量
     */
    public int getActiveTaskCount() {
        return activeDocumentTasks.size();
    }
    
    /**
     * 检查是否有指定的活跃上传任务
     */
    public boolean hasActiveTask(String attachmentId) {
        return activeDocumentTasks.containsKey(attachmentId);
    }
    
    /**
     * 清理所有活跃的上传任务
     */
    public void clearAllTasks() {
        TLog.info(TAG, "clearAllTasks: " + activeDocumentTasks.size() + " tasks");
        
        for (Map.Entry<String, RenewalUploadTask> entry : activeDocumentTasks.entrySet()) {
            RenewalUploadTask.cancel(entry.getKey());
        }
        activeDocumentTasks.clear();
    }
}
