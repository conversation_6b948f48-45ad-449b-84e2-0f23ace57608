package com.twl.hi.basic.api.request

import com.google.gson.annotations.Expose
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.http.client.BaseApiRequest
import com.twl.http.client.HttpResponse
import com.twl.http.config.RequestMethod

/**
 * 检测文件/视频在服务端是否已存在
 *
 * Created by tanshicheng on 2023/6/8
 */
class MediaCheckRequest(
    callback: BaseApiRequestCallback<MediaCheckResponse?>,
    var category: MediaCheckCategory = MediaCheckCategory.FILE
) : BaseApiRequest<MediaCheckResponse>(callback) {

    /**
     * 文件md5值
     */
    @Expose
    @JvmField
    var checkSum: String? = ""

    /**
     * 文件名称
     */
    @Expose
    @JvmField
    var name: String? = ""

    override fun getUrl() =
        if (category == MediaCheckCategory.VIDEO) URLConfig.URL_MEDIA_CHECK_VIDEO else URLConfig.URL_MEDIA_CHECK_FILE

    override fun getMethod() = RequestMethod.POST
}

class MediaCheckResponse : HttpResponse() {
    var category: String? = null
    var url: String? = null
    var coverUrl: String? = null // 视频缩略图
    var checkSum: String? = null
    var postfix: String? = null
    var duration: Long? = 0 // 视频时长
    var videoWidth: Int? = 0
    var videoHeight: Int? = 0
}

enum class MediaCheckCategory {
    FILE,
    VIDEO
}