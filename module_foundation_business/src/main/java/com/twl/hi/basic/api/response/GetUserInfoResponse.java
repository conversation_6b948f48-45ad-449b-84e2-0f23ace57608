package com.twl.hi.basic.api.response;

import com.twl.http.client.HttpResponse;

import java.io.Serializable;

/**
 * Created by Xuweixiang on 2022/4/19 21:44
 * email : <EMAIL>
 * describe :
 */
public class GetUserInfoResponse extends HttpResponse {

 public UserInfo userInfo;

 public String rawData;

 public String signature;

 public String encryptedData;

 public String iv;

 public static class UserInfo implements Serializable {
  public String avatarUrl;

  public String gender;

  public String city;

  public String nickName;

 }

}
