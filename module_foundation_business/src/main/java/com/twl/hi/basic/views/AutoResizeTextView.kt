package com.twl.hi.basic.views

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import androidx.appcompat.widget.AppCompatTextView
import lib.twl.common.util.QMUIDisplayHelper

/**
 * <AUTHOR>
 * @date 2023/2/9
 * description:会议间名称显示需要，字体大小随头像大小改变
 */
class AutoResizeTextView
@JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    AppCompatTextView(context, attrs, defStyleAttr) {

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)

        /*
        大小设置为原BasicBindingAdapters的逻辑
         */
        var size = QMUIDisplayHelper.px2dp(context, measuredWidth)
        if (size == 0) {
            size = 50
        }
        setTextSize(TypedValue.COMPLEX_UNIT_DIP, size * 18 / 48.0f)
    }
}