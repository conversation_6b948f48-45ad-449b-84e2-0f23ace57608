package com.twl.hi.basic.views.menu;


import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.drawable.ColorDrawable;
import android.text.TextPaint;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.PopupWindow;

import androidx.recyclerview.widget.RecyclerView;

import com.twl.hi.basic.BR;
import com.twl.hi.basic.R;

import java.util.ArrayList;
import java.util.List;

import lib.twl.common.adapter.CommonAdapter;
import lib.twl.common.util.QMUIDisplayHelper;

/**
 * Created by xiaoqi on 2017/12/11.
 */

public class FloatMenu extends PopupWindow {


    protected static final int ANCHORED_GRAVITY = Gravity.TOP | Gravity.START;

    /**
     * 默认最小宽度
     */
    protected int DEFAULT_MENU_WIDTH;

    /**
     * 垂直偏移量
     */
    protected final int VERTICAL_OFFSET;

    protected Context context;
    protected List<MenuItem> menuItemList;
    protected View view;
    protected Point screenPoint;
    protected int clickX;
    protected int clickY;
    protected int menuWidth;
    protected int menuHeight;
    protected FloatMenuCallback callback;
    
    // 添加全屏黑色透明背景相关变量
    protected boolean showWithIcon = false;
    protected int iconResId = 0;
    protected String iconUrl = null;
    protected RecyclerView recyclerView;

    /**
     * 获取上下文
     * @return
     */
    public Context getContext() {
        return context;
    }

    public interface OnItemClickListener {
        void onClick(View v, int position);
    }

    public FloatMenu(Activity activity) {
        this(activity, (View) activity.findViewById(android.R.id.content));
    }

    public void setCallback(FloatMenuCallback callback) {
        this.callback = callback;
    }

    public FloatMenu(Context context, View view) {
        super(context);
        setOutsideTouchable(true);
        setFocusable(true);
        setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        view.setOnTouchListener(new MenuTouchListener());
        this.context = context;
        this.view = view;
        VERTICAL_OFFSET = QMUIDisplayHelper.dp2px(context, 10);
        DEFAULT_MENU_WIDTH = context.getResources().getDimensionPixelSize(R.dimen.float_menu_with);
        screenPoint = new Point(QMUIDisplayHelper.getScreenWidth(context), QMUIDisplayHelper.getScreenHeight(context));
        menuItemList = new ArrayList<>();

    }



    public void items(List<MenuItem> itemList) {
        String text = "";
        for (int i = 0; i < itemList.size(); i++) {
            if (itemList.get(i).getItem().length() > text.length()) {
                text = itemList.get(i).getItem().toString();
            }
        }
        float width = getTextWidth(context, text, 15) + getExtraSpaceWidth();
        if (width < DEFAULT_MENU_WIDTH) {
            width = DEFAULT_MENU_WIDTH;
        }
        items((int) width, itemList);
    }

    /**
     * 获取额外的空间宽度
     * @return
     */
    protected int getExtraSpaceWidth() {
        return 2 * context.getResources().getDimensionPixelSize(R.dimen.float_item_padding_left_or_right);
    }

    public void items(int itemWidth, List<MenuItem> itemList) {
        menuItemList.clear();
        menuItemList.addAll(itemList);
        generateLayout(itemWidth);
    }

    private void generateLayout(int itemWidth) {
        // 直接使用View.inflate而不是数据绑定
        View contentView = LayoutInflater.from(context).inflate(R.layout.pop_float_menu, null);
        recyclerView = contentView.findViewById(R.id.rv_menu);
        
        ViewGroup.LayoutParams params = recyclerView.getLayoutParams();
        menuWidth = itemWidth;
        // 计算实际需要的高度
        menuHeight = menuItemList.size() * context.getResources().getDimensionPixelSize(R.dimen.float_menu_item_height);
        
        // 确保不超出屏幕
        if (menuHeight > screenPoint.y - QMUIDisplayHelper.dp2px(context, 100)) {
            menuHeight = screenPoint.y - QMUIDisplayHelper.dp2px(context, 100);
        }
        
        params.height = menuHeight;
        if (menuWidth > 0) {
            params.width = menuWidth;
        }
        recyclerView.setLayoutParams(params);
        setContentView(contentView);
        setAdapter();
    }

    protected void setAdapter() {
        if (recyclerView == null) return;
        
        CommonAdapter<MenuItem> commonAdapter = new CommonAdapter<MenuItem>(getMenuItemLayout(), BR.bean);
        if (callback != null) {
            commonAdapter.addCallback(BR.callback, callback);
        }
        recyclerView.setAdapter(commonAdapter);
        commonAdapter.submitList(menuItemList);
    }

    protected int getMenuItemLayout() {
        return R.layout.item_float_menu;
    }


    public void show(Point point) {
        clickX = point.x;
        clickY = point.y;
        show();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public void show() {
        if (isShowing()) {
            return;
        }
        
        // 设置PopupWindow为包裹内容大小
        setWidth(WindowManager.LayoutParams.WRAP_CONTENT);
        setHeight(WindowManager.LayoutParams.WRAP_CONTENT);
        
        // 计算图标和菜单位置
        int iconSize = QMUIDisplayHelper.dp2px(context, 40);
        int menuX, menuY;


        if (clickX <= screenPoint.x / 2) {
            // 左半屏 - 弹框左边缘与图标右边缘对齐
            if (clickY + menuHeight < screenPoint.y) {
                setAnimationStyle(R.style.menu_animation_top_left);
                menuX = clickX + iconSize/2; // 图标右边缘位置
                menuY = clickY + VERTICAL_OFFSET;
            } else {
                setAnimationStyle(R.style.menu_animation_bottom_left);
                menuX = clickX + iconSize/2;
                menuY = clickY - menuHeight - VERTICAL_OFFSET;
            }
        } else {
            // 右半屏 - 弹框右边缘与图标左边缘对齐
            if (clickY + menuHeight < screenPoint.y) {
                setAnimationStyle(R.style.menu_animation_top_right);
                menuX = clickX - menuWidth - iconSize/2; // 图标左边缘位置
                menuY = clickY + VERTICAL_OFFSET;
            } else {
                setAnimationStyle(R.style.menu_animation_bottom_right);
                menuX = clickX - menuWidth - iconSize/2;
                menuY = clickY - menuHeight - VERTICAL_OFFSET;
            }
        }
        
        showAtLocation(view, ANCHORED_GRAVITY, menuX, menuY);
    }

    @Override
    public void setOnDismissListener(OnDismissListener onDismissListener) {
        super.setOnDismissListener(onDismissListener);
    }

    class MenuTouchListener implements View.OnTouchListener {

        @Override
        public boolean onTouch(View v, MotionEvent event) {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                clickX = (int) event.getRawX();
                clickY = (int) event.getRawY();
            }
            return false;
        }
    }

    public float getTextWidth(Context context, String text, int textSize) {
        TextPaint paint = new TextPaint();
        float scaledDensity = context.getResources().getDisplayMetrics().scaledDensity;
        paint.setTextSize(scaledDensity * textSize);
        return paint.measureText(text);
    }
}
