package com.twl.hi.basic.helpers;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.api.response.VideoUploadResult;
import com.twl.hi.foundation.logic.IVideoUploader;
import com.twl.hi.foundation.model.ChatAttachment;
import com.twl.hi.foundation.utils.ChatAttachmentBuilder;
import com.twl.hi.basic.download.OkRenewalUpload;
import com.twl.hi.basic.download.VideoRenewalUploadTask;
import com.twl.hi.foundation.api.base.BaseUpdateRequestCallback;
import com.twl.hi.foundation.api.response.FileUploadResponse;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import java.io.File;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于 VideoRenewalUploadTask 的视频上传器实现
 * 集成 CosUploadHelper 和统一取消机制
 */
public class RenewalVideoUploader implements IVideoUploader {
    private static final String TAG = "RenewalVideoUploader";
    
    // 保存活跃的上传任务引用（用于取消）
    private final Map<String, VideoRenewalUploadTask> activeVideoTasks = new ConcurrentHashMap<>();
    
    @Override
    public void uploadVideoWithProgress(
            File file,
            VideoUploadCallback callback,
            ProgressListener progressListener,
            String attachmentId
    ) {
        TLog.info(TAG, "uploadVideoWithProgress: " + file.getAbsolutePath() + ", attachmentId: " + attachmentId);
        
        try {
            // 创建视频上传任务
            VideoRenewalUploadTask renewalTask = createVideoUploadTask(attachmentId, file);
            
            // 设置进度回调
            if (progressListener != null) {
                renewalTask.setProgressCallback(new VideoRenewalUploadTask.VideoProgressCallback() {
                    @Override
                    public void onProgressUpdate(long uploadBytes, long totalBytes) {
                        float progress = totalBytes > 0 ? (float) uploadBytes / totalBytes : 0f;
                        progressListener.onProgress(progress, uploadBytes, totalBytes);
                    }
                });
            }
            
            // 设置完成回调
            renewalTask.register(new BaseUpdateRequestCallback<FileUploadResponse>() {
                @Override
                public void handleInChildThread(ApiData<FileUploadResponse> data) {
                    // 清理任务引用
                    activeVideoTasks.remove(attachmentId);
                    
                    // 从VideoRenewalUploadTask获取VideoUploadResult
                    VideoUploadResult videoResult = renewalTask.progress.getVideoResult();
                    if (videoResult != null) {
                        TLog.info(TAG, "Video upload success: " + attachmentId);
                        callback.onUploadSuccess(videoResult);
                    } else {
                        TLog.error(TAG, "Video upload success but result is null: " + attachmentId);
                        callback.onUploadFailed(new ErrorReason(-1, "视频上传成功但结果为空"));
                    }
                }
                
                @Override
                public void handleErrorInChildThread(ErrorReason reason) {
                    // 清理任务引用
                    activeVideoTasks.remove(attachmentId);
                    
                    TLog.error(TAG, "Video upload failed: " + attachmentId + ", reason: " + reason.getErrReason());
                    callback.onUploadFailed(reason);
                }
            });
            
            // 保存任务引用
            activeVideoTasks.put(attachmentId, renewalTask);
            
            // 启动上传
            renewalTask.start();
            
        } catch (Exception e) {
            TLog.error(TAG, "Error in uploadVideoWithProgress", e);
            callback.onUploadFailed(new ErrorReason(-1, "视频上传异常：" + e.getMessage()));
        }
    }
    
    /**
     * 创建视频上传任务
     */
    private VideoRenewalUploadTask createVideoUploadTask(String attachmentId, File file) {
        // 使用Builder创建ChatAttachment对象
        ChatAttachment tempAttachment = new ChatAttachmentBuilder()
            .setId(attachmentId)
            .setFileType(ChatAttachment.FILE_TYPE_VIDEO)
            .setFileName(file.getName())
            .setChatId("temp") // 临时值
            .build();

        return OkRenewalUpload.createVideoUploadTask(attachmentId, file, tempAttachment);
    }
    
    /**
     * 取消指定的视频上传任务
     * 这个方法可以被外部调用来取消上传
     */
    public boolean cancelVideoUpload(String attachmentId) {
        TLog.info(TAG, "cancelVideoUpload: " + attachmentId);
        
        VideoRenewalUploadTask task = activeVideoTasks.remove(attachmentId);
        if (task != null) {
            task.cancel();
            TLog.info(TAG, "Successfully cancelled video upload task: " + attachmentId);
            return true;
        } else {
            TLog.info(TAG, "No active video upload task found for: " + attachmentId);
            return false;
        }
    }
    
    /**
     * 获取活跃的视频上传任务数量
     */
    public int getActiveTaskCount() {
        return activeVideoTasks.size();
    }
    
    /**
     * 检查是否有指定的活跃上传任务
     */
    public boolean hasActiveTask(String attachmentId) {
        return activeVideoTasks.containsKey(attachmentId);
    }
    
    /**
     * 清理所有活跃的上传任务
     */
    public void clearAllTasks() {
        TLog.info(TAG, "clearAllTasks: " + activeVideoTasks.size() + " tasks");
        
        for (VideoRenewalUploadTask task : activeVideoTasks.values()) {
            task.cancel();
        }
        activeVideoTasks.clear();
    }
}
