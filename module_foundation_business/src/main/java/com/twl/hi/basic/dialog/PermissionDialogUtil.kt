package com.twl.hi.basic.dialog

import android.app.Activity
import android.content.Context
import android.view.View
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.R
import com.twl.hi.basic.helpers.AppPageRouterHelper

object PermissionDialogUtil {
    /**
     * 定位弹窗
     */
    @JvmOverloads
    @JvmStatic
    fun showLocationFailedDialog(
        context: Context?,
        onCancelClick:(()->Unit)? = null,
        onSettingClick:(()->Unit)? = null,
    ) {
        if (context == null) {
            return
        }
        if (context is Activity && (context.isFinishing || context.isDestroyed)) {
            return
        }
        DialogUtils.Builder(context)
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .setTitle(R.string.location_permission_failed_title)
            .setContent(R.string.location_permission_failed_tips)
            .setAutoCloseAfterClick(true)
            .setNegative(R.string.cancel)
            .setNegativeListener {
                TLog.info(context::class.java.simpleName, "showLocationFiledDialog onCancel")
                onCancelClick?.invoke()
            }
            .setPositive(R.string.imei_failed_go_setting)
            .setPositiveListener { _: View? ->
                onSettingClick?.invoke()?:apply {
                    if (context is Activity) {
                        AppPageRouterHelper.jumpToAppSettingsPage(context)
                    }
                }
            }.build().show()
    }

    /**
     * 通知弹窗
     */
    @JvmStatic
    fun showNotificationFailedDialog(
        context: Context?,
        onCancelClick:(()->Unit)? = null,
        onSettingClick:(()->Unit)? = null,
    ) {
        if (context == null) {
            return
        }
        if (context is Activity && (context.isFinishing || context.isDestroyed)) {
            return
        }
        DialogUtils.Builder(context)
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .setTitle(R.string.notification_permission_failed_title)
            .setContent(R.string.notification_permission_failed_tips)
            .setAutoCloseAfterClick(true)
            .setNegative(R.string.cancel)
            .setNegativeListener {
                TLog.info(
                    context::class.java.simpleName,
                    "showNotificationFailedDialog onComplete"
                )
                onCancelClick?.invoke()
            }
            .setPositive(R.string.imei_failed_go_setting)
            .setPositiveListener { _: View? ->
                onSettingClick?.invoke()?:apply {
                    if (context is Activity) {
                        AppPageRouterHelper.jumpToAppSettingsPage(context)
                    }
                }
            }.build().show()
    }

    /**
     * 存储权限弹窗
     */
    @JvmStatic
    fun showStorageFailedDialog(
        context: Context?,
        onCancelClick:(()->Unit)? = null,
        onSettingClick:(()->Unit)? = null,
    ) {
        if (context == null) {
            return
        }
        if (context is Activity && (context.isFinishing || context.isDestroyed)) {
            return
        }
        DialogUtils.Builder(context)
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .setTitle(R.string.storage_permission_failed_title)
            .setContent(R.string.storage_permission_failed_tips)
            .setAutoCloseAfterClick(true)
            .setNegative(R.string.cancel)
            .setNegativeListener { onCancelClick?.invoke() }
            .setPositive(R.string.imei_failed_go_setting)
            .setPositiveListener { v: View? ->
                onSettingClick?.invoke()?:apply {
                    if (context is Activity) {
                        AppPageRouterHelper.jumpToAppSettingsPage(context)
                    }
                }
            }.build().show()
    }

    @JvmStatic
    fun showCalendarFailedDialog(
        context: Context?,
        onCancelClick:(()->Unit)? = null,
        onSettingClick:(()->Unit)? = null,
    ) {
        if (context == null) {
            return
        }
        if (context is Activity && (context.isFinishing || context.isDestroyed)) {
            return
        }
        DialogUtils.Builder(context)
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .setTitle(R.string.calendar_permission_failed_title)
            .setContent(R.string.calendar_permission_failed_tips)
            .setAutoCloseAfterClick(true)
            .setNegative(R.string.cancel)
            .setNegativeListener { onCancelClick?.invoke() }
            .setPositive(R.string.imei_failed_go_setting)
            .setPositiveListener { v: View? ->
                onSettingClick?.invoke()?:apply {
                    if (context is Activity) {
                        AppPageRouterHelper.jumpToAppSettingsPage(context)
                    }
                }
            }.build().show()
    }

    @JvmStatic
    fun showMicrophoneFailedDialog(
        context: Context?,
        onCancelClick:(()->Unit)? = null,
        onSettingClick:(()->Unit)? = null,
    ) {
        if (context == null) {
            return
        }
        if (context is Activity && (context.isFinishing || context.isDestroyed)) {
            return
        }
        DialogUtils.Builder(context)
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .setTitle(R.string.microphone_permission_failed_title)
            .setContent(R.string.microphone_permission_failed_tips)
            .setAutoCloseAfterClick(true)
            .setNegative(R.string.cancel)
            .setNegativeListener { onCancelClick?.invoke() }
            .setPositive(R.string.imei_failed_go_setting)
            .setPositiveListener { v: View? ->
                onSettingClick?.invoke()?:apply {
                    if (context is Activity) {
                        AppPageRouterHelper.jumpToAppSettingsPage(context)
                    }
                }
            }.build().show()
    }

    @JvmStatic
    fun showCameraFailedDialog(
        context: Context?,
        onCancelClick:(()->Any)? = null,
        onSettingClick:(()->Unit)? = null,
    ) {
        if (context == null) {
            return
        }
        if (context is Activity && (context.isFinishing || context.isDestroyed)) {
            return
        }
        DialogUtils.Builder(context)
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .setTitle(R.string.camera_permission_failed_title)
            .setContent(R.string.camera_permission_failed_tips)
            .setAutoCloseAfterClick(true)
            .setNegative(R.string.cancel)
            .setNegativeListener { onCancelClick?.invoke() }
            .setPositive(R.string.imei_failed_go_setting)
            .setPositiveListener { v: View? ->
                onSettingClick?.invoke()?:apply {
                    if (context is Activity) {
                        AppPageRouterHelper.jumpToAppSettingsPage(context)
                    }
                }
            }.build().show()
    }

    @JvmStatic
    fun showBluetoothFailedDialog(
        context: Context?,
        onCancel:(()->Unit)? = {},
        onCancelClick:(()->Unit)? = null,
        onSettingClick:(()->Unit)? = null,
    ) {
        if (context == null) {
            return
        }
        if (context is Activity && (context.isFinishing || context.isDestroyed)) {
            return
        }
        DialogUtils.Builder(context)
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .setTitle(R.string.bluetooth_permission_failed_title)
            .setContent(R.string.bluetooth_permission_failed_tips)
            .setAutoCloseAfterClick(true)
            .setCancelListener {
                onCancel?.invoke()
            }
            .setNegative(R.string.cancel)
            .setNegativeListener { onCancelClick?.invoke() }
            .setPositive(R.string.imei_failed_go_setting)
            .setPositiveListener { v: View? ->
                onSettingClick?.invoke()?:apply {
                    if (context is Activity) {
                        AppPageRouterHelper.jumpToAppSettingsPage(context)
                    }
                }
            }.build().show()
    }

    @JvmStatic
    fun showFloatWindowDialog(
        context: Context?,
        onCancel:(()->Unit)? = {},
        onCancelClick:(()->Unit)? = null,
        onSettingClick:(()->Unit)? = null,
    ) {
        if (context == null) {
            return
        }
        if (context is Activity && (context.isFinishing || context.isDestroyed)) {
            return
        }
        DialogUtils.Builder(context)
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .setTitle(R.string.float_permission_failed_title)
            .setContent(R.string.float_permission_failed_tips)
            .setAutoCloseAfterClick(true)
            .setCancelListener {
                onCancel?.invoke()
            }
            .setNegative(R.string.cancel)
            .setNegativeListener { onCancelClick?.invoke() }
            .setPositive(R.string.imei_failed_go_open)
            .setPositiveListener { v: View? ->
                onSettingClick?.invoke()?:apply {
                    if (context is Activity) {
                        AppPageRouterHelper.jumpToAppSettingsPage(context)
                    }
                }
            }.build().show()
    }
}