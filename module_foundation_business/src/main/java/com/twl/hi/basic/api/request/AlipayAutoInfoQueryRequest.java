package com.twl.hi.basic.api.request;

import com.twl.hi.basic.api.response.AlipayAuthInfoQueryResponse;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

/**
 * 查询支付宝认证授权请求参数
 * <p>
 * Created by tanshi<PERSON> on 2023/11/10
 */
public class AlipayAutoInfoQueryRequest extends BaseApiRequest<AlipayAuthInfoQueryResponse> {

    public AlipayAutoInfoQueryRequest(BaseApiRequestCallback<AlipayAuthInfoQueryResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_ALIPAY_AUTH_INFO;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
