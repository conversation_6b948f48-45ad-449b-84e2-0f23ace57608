package com.twl.hi.basic.dialog.bottom;


import androidx.annotation.DrawableRes;
import androidx.databinding.ObservableInt;

import com.twl.hi.basic.R;
import com.twl.hi.basic.databinding.ItemBottomSelectItemBinding;
import com.twl.hi.basic.util.ThemeUtils;

import java.util.List;

import lib.twl.common.adapter.BaseDataBindingAdapter;
import lib.twl.common.adapter.BaseDataBindingViewHolder;

/**
 * Created by ChaiJiangpeng on 2020/7/30
 * Describe:
 */
public class HiBottomRadioSelectAdapter extends BaseDataBindingAdapter<HiBottomSelectItemBean, ItemBottomSelectItemBinding> {
    private final ObservableInt resCheckIc = new ObservableInt(ThemeUtils.useNewTheme ? R.drawable.hd_icon_check : R.drawable.ic_icon_drawer_personal_checked);
    private final ObservableInt highlightColor = new ObservableInt(R.color.app_black);

    public HiBottomRadioSelectAdapter(List<HiBottomSelectItemBean> data) {
        super(R.layout.item_bottom_select_item, data);
    }

    @Override
    protected void bind(BaseDataBindingViewHolder<ItemBottomSelectItemBinding> helper, ItemBottomSelectItemBinding binding, HiBottomSelectItemBean item) {
        binding.setItem(item);
        binding.setResCheckIc(resCheckIc);
        binding.setHighlightColor(highlightColor);
    }

    public void setResCheckIc(@DrawableRes int resCheckIc) {
        this.resCheckIc.set(resCheckIc);
    }

    public void setHighlightColor(int color) {
        highlightColor.set(color);
    }
}
