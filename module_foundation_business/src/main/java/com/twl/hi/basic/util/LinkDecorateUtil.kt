package com.twl.hi.basic.util

import com.twl.hi.router.base.RouterBaseConstant
import hi.kernel.Constants
import lib.twl.common.util.ProcessHelper

/**
 *@author: musa on 2023/9/13
 *@e-mail: <EMAIL>
 *@desc: 修改url链接的工具类
 */
object LinkDecorateUtil {
    /**
     * 默认的邮箱地址正则表达式
     */
    private const val EMAIL_ADDRESS = "([\\w!#$%&'*+=?^_`{|}~-]+(?:\\.[\\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+(com|cn|net|edu|org|site|io))"

    /**
     * 优先使用服务端下发的正则表达式
     */
    private val emailPattern = ProcessHelper.getUserPreferences().getString(Constants.KEY_EMAIL_PATTERNS, EMAIL_ADDRESS)


    /**
     * 如果是邮箱地址则加上mailto:前缀
     */
    fun decorateMailToUrl(url: String): String {
        return if (url.matches(Regex(emailPattern))) {
            addMailToUrl(url)
        } else {
            url
        }
    }

    /**
     * 将邮箱地址加上mailto:前缀
     */
    private fun addMailToUrl(url: String): String {
        return if (url.startsWith(RouterBaseConstant.SCHEME_MAIL)) {
            url
        } else {
            "${RouterBaseConstant.SCHEME_MAIL}$url"
        }
    }
}