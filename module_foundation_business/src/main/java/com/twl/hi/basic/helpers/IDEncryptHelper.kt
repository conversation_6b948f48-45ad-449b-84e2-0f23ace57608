package com.twl.hi.basic.helpers

import com.twl.hi.basic.api.request.IDDecryptRequest
import com.twl.hi.basic.api.request.IDDecryptResponse
import com.twl.hi.basic.api.request.IDEncryptRequest
import com.twl.hi.basic.api.request.IDEncryptResponse
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.error.ErrorReason
import java.util.Collections
import java.util.stream.Collectors

/**
 *
 * id加密工具类
 *
 * Created by tanshicheng on 2023/4/13
 */
object IDEncryptHelper {
    /**
     * Id类型
     */
    @JvmField
    val USER_ID = "user_id"
    @JvmField
    val CHAT_ID = "chat_id"

    /**
     * 多个id请求加密
     * @param paramsMapStr {"chatId": "28993"}
     * @param callback 加密回调
     */
    @JvmStatic
    fun requestEncrypt(paramsMapStr: String?, callback: IDEncryptCallback?) {
        val request = IDEncryptRequest(object : BaseApiRequestCallback<IDEncryptResponse?>() {
            override fun onSuccess(data: ApiData<IDEncryptResponse?>?) {
                callback?.onEncryptFinish(data?.resp?.result)
            }

            override fun onFailed(reason: ErrorReason?) {
                callback?.onEncryptFail(reason?.errReason)
            }

            override fun showFailed(reason: ErrorReason?) {
//                super.showFailed(reason)
            }
        })
        request.paramMap = paramsMapStr
        HttpExecutor.execute(request)
    }

    @JvmStatic
    fun requestDecrypt(idType:String, ids: List<String>, callback:IDDecryptCallback) {
        IDDecryptRequest(object : BaseApiRequestCallback<IDDecryptResponse?>(){
            override fun onSuccess(data: ApiData<IDDecryptResponse?>?) {
                data?.resp?.result?.let { it ->
                    if (!it.userIds.isNullOrEmpty()) {
                        it.userIds
                    } else if (!it.chatIds.isNullOrEmpty()) {
                        it.chatIds
                    } else {
                        emptyList()
                    }
                }?.let {
                    callback.onDecryptFinish(it)
                }?:let {
                    callback.onDecryptFail()
                }
            }

            override fun onFailed(reason: ErrorReason?) {
                callback.onDecryptFail()
            }

        }).let {
            if (idType == USER_ID) {
                it.userIds = ids.stream().collect(Collectors.joining(","))
            } else if (idType == CHAT_ID) {
                it.chatIds = ids.stream().collect(Collectors.joining(","))
            }
            HttpExecutor.execute(it)
        }
    }
}

interface IDEncryptCallback {
    fun onEncryptFinish(resultMap: HashMap<String, String>?)
    fun onEncryptFail(reason: String?)
}

interface IDDecryptCallback {
    fun onDecryptFinish(ids: List<String>)
    fun onDecryptFail()
}