package com.twl.hi.basic.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

public class SendMsgEmojiRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public long msgId;
    @Expose
    public int type;
    @Expose
    public int operate;//操作，1-增加，2-减少

    public SendMsgEmojiRequest(BaseApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_MSG_EMOJI;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
