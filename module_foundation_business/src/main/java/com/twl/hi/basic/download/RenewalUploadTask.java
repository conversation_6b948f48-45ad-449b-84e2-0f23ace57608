package com.twl.hi.basic.download;

import android.text.TextUtils;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.api.base.BaseUpdateRequestCallback;
import com.twl.hi.foundation.api.request.FileUploadRequest;
import com.twl.hi.foundation.api.response.FileUploadResponse;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.RenewalProgress;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.HttpUtils;
import com.twl.http.error.ErrorReason;
import com.twl.utils.file.FileUtils;
import hi.kernel.utils.MD5Utils;
import lib.twl.common.util.TlogHelper;

import java.io.File;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;

/**
 * 文件分片上传任务
 */
public class RenewalUploadTask implements Runnable {
    private static final String TAG = "RenewalUploadTask";

    private static final long SUBSECTION_SIZE = 1024 * 1024 * 10;

    /**
     * 上传中的RenewalProgress
     * key: RenewalProgress.tag
     * value: RenewalProgress
     */
    private static final ConcurrentHashMap<String, RenewalProgress> uploadingRenewalProgress = new ConcurrentHashMap<>();

    public RenewalProgress progress;
    private BaseUpdateRequestCallback<FileUploadResponse> listener;
    private ExecutorService executor;
    private File file;

    /**
     * 分片进度回调接口
     */
    public interface ChunkProgressCallback {
        void onChunkCompleted(long completedChunks, long totalChunks, float progress);
    }

    private ChunkProgressCallback chunkProgressCallback;

    /**
     * 整个文件的md5值
     */
    private String uniqMd5;
    private String folder;
    private CountDownLatch countDownLatch;
    private boolean isContinueUpload = true;

    public RenewalUploadTask(String tag, File file) {
        HttpUtils.checkNotNull(tag, "tag == null");
        progress = new RenewalProgress();
        progress.setTag(tag);
        progress.setStatus(RenewalProgress.NONE);
        this.file = file;
        executor = OkRenewalUpload.getInstance().getExecutor();
        folder = ServiceManager.getInstance().getFileService().getFileRootPath();
    }

    public RenewalUploadTask register(BaseUpdateRequestCallback<FileUploadResponse> listener) {
        if (listener != null) {
            this.listener = listener;
        }
        return this;
    }

    /**
     * 设置分片进度回调
     */
    public RenewalUploadTask setChunkProgressCallback(ChunkProgressCallback callback) {
        this.chunkProgressCallback = callback;
        return this;
    }

    public void start() {
        if (progress.getStatus() != RenewalProgress.LOADING) {
            progress.setStatus(RenewalProgress.NONE);
            uploadingRenewalProgress.put(progress.getTag(), progress);
            executor.execute(this);
        } else {
            TLog.error(TAG, "the task with tag " + progress.getTag() + " is already in the upload queue, current task status is " + progress.getStatus());
        }
    }


    private void upLoadFile(File file, long tempRenewalIndex, long renewalNum) {
        FileUploadRequest request = new FileUploadRequest(new BaseUpdateRequestCallback<FileUploadResponse>() {

            @Override
            public void handleInChildThread(ApiData<FileUploadResponse> data) {
                super.handleInChildThread(data);
                TLog.info(TAG, "RenewalUploadTask -> handleInChildThread success " + data.resp + " " + tempRenewalIndex + " " + renewalNum + " " + file.getAbsolutePath());

                // 分片上传进度回调
                if (chunkProgressCallback != null && renewalNum > 1) {
                    float chunkProgress = (float) tempRenewalIndex / (float) renewalNum;
                    TLog.info(TAG, "分片进度回调: " + tempRenewalIndex + "/" + renewalNum + " = " + (chunkProgress * 100) + "%");
                    chunkProgressCallback.onChunkCompleted(tempRenewalIndex, renewalNum, chunkProgress);
                }

                if (data.resp != null && !TextUtils.isEmpty(data.resp.url)) {
                    progress.setStatus(RenewalProgress.FINISH);
                    File tempFile = new File(folder, file.getName().split("\\.")[0] + ".tmp");
                    FileUtils.delete(tempFile);
                    if (listener != null) {
                        listener.handleInChildThread(data);
                    }
                    isContinueUpload = false;
                }
                if (countDownLatch != null) {
                    countDownLatch.countDown();
                }
            }


            @Override
            public void handleErrorInChildThread(ErrorReason reason) {
                super.handleErrorInChildThread(reason);
                progress.setStatus(RenewalProgress.ERROR);
                TLog.info(TAG, "RenewalUploadTask -> handleErrorInChildThread reason " + reason + ", " + tempRenewalIndex + ", " + renewalNum + ", " + file.getAbsolutePath());
                if (listener != null) {
                    listener.handleErrorInChildThread(reason);
                }
                isContinueUpload = false;
                if (countDownLatch != null) {
                    countDownLatch.countDown();
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });

        if (renewalNum > 1) {
            request.fileName = file.getName();
            request.index = String.valueOf(tempRenewalIndex);
            request.totalIndex = String.valueOf(renewalNum);
            request.totalSize = String.valueOf(file.length());
            request.uniqMd5 = uniqMd5;
            File splitFile = FileUtils.splitFile(file, tempRenewalIndex - 1, SUBSECTION_SIZE, folder);
            request.setFile(splitFile);
            request.partMd5 = MD5Utils.getMD5(splitFile);
            TLog.info(TAG, file.getName()+" totalSize:" + file.length() +" totalIndex:" + renewalNum + " index:" + tempRenewalIndex + " uniqueMd5:" + uniqMd5);
        } else {
            request.setFile(file);
        }
        HttpExecutor.execute(request);
        TLog.info(TAG, "RenewalUploadTask -> execute upload request " + request.index + " " + request.totalIndex + " " + request.totalSize + " " + uniqMd5 + " " + file.getAbsolutePath());
    }


    @Override
    public void run() {
        progress.setStatus(RenewalProgress.LOADING);
        // 当天的日志文件上传时拷贝,防止边写边传导致的异常
        final File[] files = new File[1];
        if (file.length() > SUBSECTION_SIZE && TlogHelper.fileMaybeInWrite(file.getAbsolutePath())) {
            files[0] = FileUtils.createTempFile(file);
            FileUtils.copyFile(file, files[0]);
        }
        File finalFile = files[0] != null ? files[0] : file;

        long length = finalFile.length();
        progress.setTotalSize(length);
        long renewalNum = length / SUBSECTION_SIZE + 1;
        long renewalIndex = 0L;
        if (renewalNum > 1) {
//            String uniqFile = finalFile.getName() + "_" + length + "_" + renewalNum + "_" + finalFile.lastModified();
//            uniqMd5 = MD5Utils.text2Md5(uniqFile);
            uniqMd5 = MD5Utils.getMD5(finalFile);
        }
        TLog.debug(TAG, "renewalNum " + renewalNum + " length " + finalFile.length());
        if (renewalNum == 1) {
            upLoadFile(finalFile, 1, renewalNum);
            return;
        }
        isContinueUpload = true;
        while (isContinueUpload && progress.getStatus() == RenewalProgress.LOADING) {
            countDownLatch = new CountDownLatch(1);
            renewalIndex++;
            upLoadFile(finalFile, renewalIndex, renewalNum);
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        Optional.ofNullable(files[0]).ifPresent(File::delete);
        uploadingRenewalProgress.remove(progress.getTag());
    }

    public static void cancel(String tag) {
        if (TextUtils.isEmpty(tag)) {
            return;
        }
        RenewalProgress progress = uploadingRenewalProgress.get(tag);
        if (progress != null) {
            progress.setStatus(RenewalProgress.NONE);
        }
    }

}
