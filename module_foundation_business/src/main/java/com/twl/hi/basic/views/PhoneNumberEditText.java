package com.twl.hi.basic.views;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.Editable;
import android.text.TextWatcher;
import android.text.method.DigitsKeyListener;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.appcompat.widget.AppCompatEditText;

import com.twl.hi.basic.R;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by huang<PERSON><PERSON> on 6/24/16.
 */
public class PhoneNumberEditText extends AppCompatEditText implements View.OnFocusChangeListener {
    private Drawable mClearDrawable;//删除按钮的引用
    private boolean hasFocus;//控件是否有焦点

    public PhoneNumberEditText(Context context) {
        super(context);
        init();
    }

    public PhoneNumberEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public PhoneNumberEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        //获取EditText的DrawableRight,假如没有设置我们就使用默认的图片
        mClearDrawable = getCompoundDrawables()[2];
        if (mClearDrawable == null) {
            mClearDrawable = getResources().getDrawable(R.drawable.ic_icon_del);
        }
        mClearDrawable.setBounds(0, 0, mClearDrawable.getIntrinsicWidth(), mClearDrawable.getIntrinsicHeight());
        //默认设置隐藏图标
        setClearIconVisible(false);
        String digits = "0123456789";
        setKeyListener(DigitsKeyListener.getInstance(digits));
        removeTextChangedListener(textWatchListener);
        addTextChangedListener(textWatchListener);
        setOnFocusChangeListener(this);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {

        if (mClearDrawable != null && event.getAction() == MotionEvent.ACTION_UP) {
            int x = (int) event.getX();
            //判断触摸点是否在水平范围内
            boolean isInnerWidth = (x > (getWidth() - getTotalPaddingRight())) &&
                    (x < (getWidth() - getPaddingRight()));
            //获取删除图标的边界，返回一个Rect对象
            Rect rect = mClearDrawable.getBounds();
            //获取删除图标的高度
            int height = rect.height();
            int y = (int) event.getY();
            //计算图标底部到控件底部的距离
            int distance = (getHeight() - height) / 2;
            //判断触摸点是否在竖直范围内(可能会有点误差)
            //触摸点的纵坐标在distance到（distance+图标自身的高度）之内，则视为点中删除图标
            boolean isInnerHeight = (y > distance) && (y < (distance + height));
            if (isInnerHeight && isInnerWidth) {
                this.setText("");
            }
        }
        return super.onTouchEvent(event);
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        this.hasFocus = hasFocus;
        if (hasFocus) {
            setClearIconVisible(getText().length() > 0);
        } else {
            setClearIconVisible(false);
        }
    }

    /**
     * 设置清除图标的显示与隐藏，调用setCompoundDrawables为EditText绘制上去
     *
     * @param visible
     */
    private void setClearIconVisible(boolean visible) {
        Drawable right = visible ? mClearDrawable : null;
        setCompoundDrawables(getCompoundDrawables()[0], getCompoundDrawables()[1],
                right, getCompoundDrawables()[3]);
    }

    public final TextWatcher textWatchListener = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence text, int start, int lengthBefore, int lengthAfter) {
            String str = text.toString().replace(" ", "");
            if (hasFocus) {
                setClearIconVisible(str.length() > 0);
            }
            if (str.length() > 0 && str.length() <= 11) {

                boolean deleteEmpty = false;

                String pattern1 = "^(\\d{7}) (/\\d{4})$";//1835723 7923
                String pattern2 = "^(\\d{3}) (\\d{8})$";//183 57127923

                Pattern r1 = Pattern.compile(pattern1);
                Pattern r2 = Pattern.compile(pattern2);

                Matcher m1 = r1.matcher(text.toString());
                Matcher m2 = r2.matcher(text.toString());
                if (m1.find()) {
                    deleteEmpty = true;
                } else if (m2.find()) {
                    deleteEmpty = true;
                }

                if (deleteEmpty && lengthBefore == 1) {
                    start = start - 1;
                    text = text.toString().substring(0, start) + text.toString().substring(start + 1);
                    str = text.toString().replace(" ", "");
                }

                String endStr = "";
                int len = str.length();
                for (int i = 0; i < len; i++) {
                    endStr += str.charAt(i);
                    if ((i + 2) % 4 == 0 && (i + 1) != len) {
                        endStr += " ";
                    }
                }

                if (endStr.endsWith(" ")) {
                    endStr = endStr.substring(0, endStr.lastIndexOf(" "));
                }

                removeTextChangedListener(this);
                setText(endStr);
                addTextChangedListener(this);

                //计算光标位置
                if (lengthAfter == 0) {//删除
                    int selValue = start - (deleteEmpty ? 1 : 0) - (text.length() - endStr.length());
                    if (selValue < 0) {
                        selValue = 0;
                    }
                    setSelection(selValue);
                } else if (lengthAfter == 1) {//输入一个数字
                    if ((start - 3) % 5 == 0) {
                        start++;
                    }
                    int selValue = start + lengthAfter;
                    if (selValue < 0) {
                        selValue = 0;
                    }
                    setSelection(selValue);
                } else if (lengthAfter > 1) {//输入多个数字
                    setSelection(getText().toString().length());
                }
            } else {
                removeTextChangedListener(this);
                setText(str);
                addTextChangedListener(this);
                setSelection(getText().toString().length());
            }

        }

        @Override
        public void afterTextChanged(Editable s) {

        }
    };
}
