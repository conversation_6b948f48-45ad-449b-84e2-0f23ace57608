package com.twl.hi.basic.download;

import android.text.TextUtils;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.api.base.BaseUpdateRequestCallback;
import com.twl.hi.foundation.api.response.FileUploadResponse;
import com.twl.hi.foundation.api.response.VideoUploadResult;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.RenewalProgress;
import com.twl.hi.basic.helpers.cos.CosUploadHelper;
import com.twl.hi.basic.helpers.cos.FileUploadCallback;
import com.twl.hi.basic.helpers.cos.UploadFileInfo;
import com.twl.hi.basic.helpers.cos.UploadFileResponse;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import lib.twl.common.util.ExecutorFactory;

import java.io.File;

import hi.kernel.utils.MD5Utils;

/**
 * 视频上传任务 - 集成CosUploadHelper
 * 继承自RenewalUploadTask，使用COS上传替代分片上传
 */
public class VideoRenewalUploadTask extends RenewalUploadTask {
    private static final String TAG = "VideoRenewalUploadTask";
    
    // COS上传相关
    private CosUploadHelper cosUploadHelper;
    private String coverUrl;
    private int[] videoInfo; // [width, height]
    private VideoProgressCallback progressCallback;

    // 回调监听器
    private BaseUpdateRequestCallback<FileUploadResponse> videoListener;
    
    /**
     * 视频进度回调接口
     */
    public interface VideoProgressCallback {
        void onProgressUpdate(long uploadBytes, long totalBytes);
    }
    
    public VideoRenewalUploadTask(String tag, File file) {
        super(tag, file);
        // 标记为视频上传类型
        progress.setAsVideoUpload();
        // 设置文件路径
        progress.setFilePath(file.getAbsolutePath());
        TLog.info(TAG, "创建视频上传任务: " + tag);
    }
    
    /**
     * 设置视频进度回调
     */
    public VideoRenewalUploadTask setProgressCallback(VideoProgressCallback callback) {
        this.progressCallback = callback;
        return this;
    }
    
    /**
     * 设置视频信息
     */
    public VideoRenewalUploadTask setVideoInfo(int[] videoInfo) {
        this.videoInfo = videoInfo;
        return this;
    }
    
    /**
     * 设置封面URL
     */
    public VideoRenewalUploadTask setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    /**
     * 注册回调监听器
     */
    @Override
    public VideoRenewalUploadTask register(BaseUpdateRequestCallback<FileUploadResponse> listener) {
        this.videoListener = listener;
        // 也调用父类的register方法
        super.register(listener);
        return this;
    }
    
    @Override
    public void run() {
        try {
            TLog.info(TAG, "开始视频上传: " + progress.getTag());
            progress.setStatus(RenewalProgress.LOADING);
            
            // 更新进度到数据库
            updateProgressToDatabase();
            
            // 创建上传文件信息
            UploadFileInfo uploadFileInfo = createUploadFileInfo();
            
            // 创建COS上传助手
            cosUploadHelper = new CosUploadHelper(uploadFileInfo);
            
            // 设置回调
            FileUploadCallback callback = new VideoUploadCallbackAdapter();
            
            // 开始上传
            cosUploadHelper.uploadFile(callback);
            
        } catch (Exception e) {
            TLog.error(TAG, "视频上传异常: " + progress.getTag(), e);
            handleError(e);
        }
    }
    
    /**
     * 取消视频上传任务
     */
    public void cancel() {
        TLog.info(TAG, "取消视频上传: " + progress.getTag());

        // 取消COS上传
        if (cosUploadHelper != null) {
            cosUploadHelper.cancel();
        }

        // 设置进度状态为取消
        progress.setStatus(RenewalProgress.NONE);

        // 清理任务
        cleanupTask();
    }
    
    /**
     * 创建上传文件信息
     */
    private UploadFileInfo createUploadFileInfo() throws Exception {
        // 获取文件信息
        String filePath = getFilePath();
        String fileName = getFileName();
        String fileMD5 = MD5Utils.getMD5(filePath);

        // 创建完整的UploadFileInfo
        return new UploadFileInfo(
            filePath,           // filePath
            fileName,           // fileName
            fileMD5,            // fileMd5
            coverUrl,           // coverUrl
            "",                 // fileId (空字符串)
            "",                 // bucketName (空字符串)
            ""                  // region (空字符串)
        );
    }

    /**
     * 获取文件路径
     */
    private String getFilePath() {
        return progress.getFilePath();
    }

    /**
     * 获取文件名
     */
    private String getFileName() {
        return progress.getFileName();
    }
    
    /**
     * COS上传回调适配器
     */
    private class VideoUploadCallbackAdapter implements FileUploadCallback {
        
        @Override
        public void onPublishProgress(long uploadBytes, long totalBytes) {
            // 更新RenewalProgress
            progress.setTotalSize(totalBytes);
            progress.setCurrentSize(uploadBytes);
            
            TLog.debug(TAG, "视频上传进度: " + uploadBytes + "/" + totalBytes + 
                      " (" + progress.getFraction() + "%) - " + progress.getTag());
            
            // 通知外部进度回调
            if (progressCallback != null) {
                progressCallback.onProgressUpdate(uploadBytes, totalBytes);
            }
            
            // 定期保存到数据库
            if (shouldSaveProgress(uploadBytes, totalBytes)) {
                updateProgressToDatabase();
            }
        }
        
        @Override
        public void onPublishComplete(UploadFileResponse response) {
            if (response.isSuccess()) {
                handleUploadSuccess(response);
            } else {
                handleUploadError(new Exception(response.descMsg != null ? response.descMsg : "上传失败"));
            }
        }
    }
    
    /**
     * 处理上传成功
     */
    private void handleUploadSuccess(UploadFileResponse response) {
        TLog.info(TAG, "视频上传成功: " + progress.getTag());
        
        // 构造VideoUploadResult
        VideoUploadResult result = new VideoUploadResult(
            response.bucketFilePath,
            coverUrl,
            videoInfo != null ? videoInfo[0] : 0,
            videoInfo != null ? videoInfo[1] : 0
        );
        result.setFileId(response.fileId);
        result.setUrlSource(1); // COS上传标识
        
        // 更新进度状态
        progress.setStatus(RenewalProgress.FINISH);
        progress.setVideoResult(result);
        updateProgressToDatabase();
        
        // 通知上传完成
        notifyUploadComplete(result);
        
        // 清理任务
        cleanupTask();
    }
    
    /**
     * 处理上传错误
     */
    private void handleError(Exception error) {
        TLog.error(TAG, "视频上传失败: " + progress.getTag(), error);

        progress.setStatus(RenewalProgress.ERROR);
        progress.recordError();
        updateProgressToDatabase();

        // 通知错误
        if (videoListener != null) {
            ExecutorFactory.execMainTask(() -> {
                videoListener.handleErrorInChildThread(new ErrorReason(-1, error.getMessage()));
            });
        }

        // 清理任务
        cleanupTask();
    }

    /**
     * 处理上传错误（UploadFileResponse版本）
     */
    private void handleUploadError(Exception error) {
        handleError(error);
    }
    
    /**
     * 通知上传完成
     */
    private void notifyUploadComplete(VideoUploadResult result) {
        if (videoListener != null) {
            ExecutorFactory.execMainTask(() -> {
                // 构造兼容的ApiData
                ApiData<FileUploadResponse> apiData = createCompatibleApiData(result);
                videoListener.handleInChildThread(apiData);
            });
        }
    }
    
    /**
     * 创建兼容的ApiData（用于现有回调接口）
     */
    private ApiData<FileUploadResponse> createCompatibleApiData(VideoUploadResult result) {
        FileUploadResponse response = new FileUploadResponse();
        response.url = result.getUrl();
        // 设置其他必要字段...

        ApiData<FileUploadResponse> apiData = new ApiData<>();
        apiData.resp = response;
        return apiData;
    }
    
    /**
     * 判断是否应该保存进度
     */
    private boolean shouldSaveProgress(long uploadBytes, long totalBytes) {
        // 每5MB或完成时保存一次
        return uploadBytes % (1024 * 1024 * 5) == 0 || uploadBytes == totalBytes;
    }
    
    /**
     * 更新进度到数据库
     */
    private void updateProgressToDatabase() {
        ExecutorFactory.execWorkTask(() -> {
            try {
                ServiceManager.getInstance().getFileService().insertRenewalProgress(progress);
            } catch (Exception e) {
                TLog.error(TAG, "保存进度到数据库失败", e);
            }
        });
    }
    
    /**
     * 清理任务
     */
    private void cleanupTask() {
        // 从任务管理Map中移除
        OkRenewalUpload.getInstance().getTaskMap().remove(progress.getTag());
        cosUploadHelper = null;
    }
}
