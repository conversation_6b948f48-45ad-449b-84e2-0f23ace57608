package com.twl.hi.basic.dialog.state;
/**
 * @author: musa on 2022/1/28
 * @e-mail: yang<PERSON>g<PERSON>@kanzhun.com
 * @desc: 用于表示加载框的状态
 */
public class LoadingState {
    /**否是展示*/
    private boolean isShowed;
    /**是否可取消*/
    private boolean isCancelable;

    public LoadingState(boolean isShowed, boolean isCancelable) {
        this.isShowed = isShowed;
        this.isCancelable = isCancelable;
    }

    public boolean isShowed() {
        return isShowed;
    }

    public boolean isCancelable() {
        return isCancelable;
    }

    /**
     * 获取关闭状态
     */
    public static LoadingState getCloseState() {
        return new LoadingState(false, false);
    }
}
