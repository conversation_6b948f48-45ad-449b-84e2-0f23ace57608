package com.twl.hi.basic.adapter;

import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.AsyncDifferConfig;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;

import com.techwolf.lib.tlog.TLog;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.Executor;

import lib.twl.common.util.SearchAdapterInterface;

/**
 * Author: <PERSON>
 * Date: 2019/03/13.
 */
public abstract class DataBoundListAdapter<T, V extends ViewDataBinding>
        extends ListAdapter<T, DataBoundViewHolder<V>> implements SearchAdapterInterface<T> {
    private final static String TAG = "DataBoundListAdapter";
    // 是否给itemView开启OnAttachStateChangeListener，用于item曝光,默认false
    private boolean mEnableOnAttachStateChangeListener = false;
    // 记录曝光item的集合，key是位置，value是曝光数量
    private HashMap<Integer, Integer> mExposedItems = null;

    public DataBoundListAdapter(@NonNull DiffUtil.ItemCallback<T> diffCallback) {
        super(diffCallback);
    }

    public DataBoundListAdapter(Executor bgExecutor, @NonNull DiffUtil.ItemCallback<T> diffCallback) {
        super(new AsyncDifferConfig.Builder<>(diffCallback).setBackgroundThreadExecutor(bgExecutor).build());
    }

    @NonNull
    @Override
    public DataBoundViewHolder<V> onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        V binding = createBinding(parent, viewType);
        DataBoundViewHolder<V> holder = new DataBoundViewHolder<>(binding);
        if (mEnableOnAttachStateChangeListener) {
            holder.itemView.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
                @Override
                public void onViewAttachedToWindow(View v) {
                    if (v.getTag() instanceof  Integer) {
                        Integer index = (Integer) v.getTag();
                        if (mExposedItems.containsKey(index)) {
                            int count = mExposedItems.get(index);
                            mExposedItems.put(index, ++count);
                        } else {
                            mExposedItems.put(index, 1);
                        }
                    } else {
                        TLog.error(TAG, "tag is error: " + v.getTag());
                    }
                }

                @Override
                public void onViewDetachedFromWindow(View v) {
                }
            });
        }
        initViewHolder(holder);
        return holder;
    }

    /**
     * 如果子Adapter需要利用ViewHolder的方法做一些初始化，
     * 这个方法提供了holder的访问
     */
    protected void initViewHolder(DataBoundViewHolder<V> holder) {

    }

    protected abstract V createBinding(ViewGroup parent, int viewType);

    @Override
    public void onBindViewHolder(@NonNull DataBoundViewHolder<V> holder, int position) {
        V vdb = holder.getDatabinding();
        bind(vdb, getItem(position), position);
        vdb.executePendingBindings();
        if (mEnableOnAttachStateChangeListener) {
            holder.itemView.setTag(position);
        }
    }

    protected abstract void bind(V vdb, T item, int position);

    @Override
    public void setEnableOnAttachStateChangeListener(boolean enableOnAttachStateChangeListener) {
        mEnableOnAttachStateChangeListener = enableOnAttachStateChangeListener;
        if (enableOnAttachStateChangeListener && mExposedItems == null) {
            mExposedItems = new HashMap<>();
        }
    }

    /**
     * 获取曝光过的items
     * @return
     */
    @Override
    public HashMap<Integer, Integer> getExposedItems() {
        return mExposedItems;
    }

    @Override
    public List<T> getData() {
        return getCurrentList();
    }
}
