package com.twl.hi.basic.views;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.InputFilter;
import android.text.Spanned;
import android.util.AttributeSet;

import com.twl.hi.basic.R;


public class InputFilterEditText extends androidx.appcompat.widget.AppCompatEditText {
    private final Context context;
    private int maxLength;//自定义属性判断是输入字符的最大长度
    private boolean allowBlank;//自定义属性判断是否允许输入空格，默认false

    public InputFilterEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
        init(attrs);
    }

    public InputFilterEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.context = context;
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        final TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.InputFilterEditText);//加载自定义styleable文件
        maxLength = a.getInteger(R.styleable.InputFilterEditText_maxLength, 0);//读取自定义属性输入字符的最大长度
        allowBlank = a.getBoolean(R.styleable.InputFilterEditText_allowBlank, false);//读取自定义属性是否允许输入空格
        if (maxLength > 0) {
            setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxLength), new InputFilter() {
                @Override
                public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                    if (!allowBlank) {
                        if (source.equals(" ")) {
                            return "";
                        }
                    }
                    if (filterInputChar(source)) {
                        return "";
                    }
                    return null;
                }
            }});

        } else {
            setFilters(new InputFilter[]{new InputFilter() {
                @Override
                public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                    if (!allowBlank) {
                        if (source.equals(" ")) {
                            return "";
                        }
                    }
                    if (filterInputChar(source)) {
                        return "";
                    }
                    return null;
                }
            }});
        }
    }

    private boolean filterInputChar(CharSequence source) {
        return false;
    }


}
