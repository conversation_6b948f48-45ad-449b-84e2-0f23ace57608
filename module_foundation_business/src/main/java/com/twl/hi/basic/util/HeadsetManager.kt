package com.twl.hi.basic.util

import android.content.Context
import android.media.AudioDeviceCallback
import android.media.AudioDeviceInfo
import android.media.AudioManager
import android.os.Build
import androidx.annotation.RequiresApi
import com.techwolf.lib.tlog.TLog
import lib.twl.common.base.BaseApplication

/**
 *@author: musa on 2024/8/27
 *@e-mail: <EMAIL>
 *@desc: 耳机状态管理类
 */
const val NO_HEADSET = 1 //没有耳机
const val WIRED_HEADSET = 2 //有线耳机
const val BLUETOOTH_HEADSET = 3 //蓝牙耳机
private const val TAG = "HeadsetManager"

class HeadsetManager(val context: Context) {
    private var headsetPlugListener: HeadsetPlugListener? = null
    private val audioManager by lazy { context.getSystemService(AudioManager::class.java) }

    /**
     * 传统蓝牙传输
     */
    private val classicBluetoothTypeSet= setOf(AudioDeviceInfo.TYPE_BLUETOOTH_A2DP, AudioDeviceInfo.TYPE_BLUETOOTH_SCO)
    /**
     * 低功耗蓝牙传输
     */
    @RequiresApi(Build.VERSION_CODES.S)
    private val BLEBluetoothTypeSet = setOf(AudioDeviceInfo.TYPE_BLE_HEADSET)

    private val bluetoothTypeSet = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
         classicBluetoothTypeSet + BLEBluetoothTypeSet
    } else {
        classicBluetoothTypeSet
    }
    private val wireTypeSet = setOf(AudioDeviceInfo.TYPE_WIRED_HEADSET)

    private val audioDeviceCallabck by lazy { object : AudioDeviceCallback(){
        override fun onAudioDevicesAdded(addedDevices: Array<out AudioDeviceInfo>?) {
            headsetPlugListener?.onHeadsetPlug(getCurType())
        }

        override fun onAudioDevicesRemoved(removedDevices: Array<out AudioDeviceInfo>?) {
            headsetPlugListener?.onHeadsetPlug(getCurType())
        }
    }}


    fun getCurType(): Int {
        var isBluetoothDevice = false
        var isWireDevice = false

        audioManager?.compactAvailableCommunicationDevices
            ?.forEachIndexed { index, audioDeviceInfo ->
                TLog.info(TAG, "getCurType: ${audioDeviceInfo.id} ${audioDeviceInfo.type.toTypeDesc()} ${audioDeviceInfo.productName} ")
                if (audioDeviceInfo.type.isBluetoothDevice()) {
                    isBluetoothDevice = true
                } else if (audioDeviceInfo.type.isWireDevice()) {
                    isWireDevice = true
                }
            }

        return if (isBluetoothDevice) {
            return BLUETOOTH_HEADSET
        } else if (isWireDevice) {
            return WIRED_HEADSET
        } else {
            NO_HEADSET
        }.apply {
            TLog.info(TAG, "getCurType : $this")
        }
    }

    fun registerAudioDeviceCallback(headsetPlugListener: HeadsetPlugListener) {
        this.headsetPlugListener = headsetPlugListener
        audioManager?.registerAudioDeviceCallback(audioDeviceCallabck, null)
        TLog.info(TAG, "registerAudioDeviceCallback")
    }

    fun unregisterAudioDeviceCallback() {
        headsetPlugListener = null
        audioManager?.unregisterAudioDeviceCallback(audioDeviceCallabck)
        stopBluetoothSoc()
        TLog.info(TAG, "unregisterAudioDeviceCallback")
    }

    /**
     * 是否是属于蓝牙耳机类型
     */
    private fun Int.isBluetoothDevice() = bluetoothTypeSet.contains(this)

    /**
     * 是否是属于有线耳机类型
     */
    private fun Int.isWireDevice() = wireTypeSet.contains(this)


//    /**
//     * 切换到蓝牙soc
//     */
//    fun startBluetoothSoc() {
//        var audioManager = BaseApplication.getApplication().getSystemService(Context.AUDIO_SERVICE) as AudioManager
//        audioManager.mode = AudioManager.MODE_IN_COMMUNICATION
//        audioManager.startBluetoothSco()
//        audioManager.isBluetoothScoOn = true
//        audioManager.isSpeakerphoneOn = false
//    }
//
    /**
     * 关闭蓝牙soc
     */
    fun stopBluetoothSoc() {
        var audioManager = BaseApplication.getApplication().getSystemService(Context.AUDIO_SERVICE) as AudioManager
        audioManager.isBluetoothScoOn = false
        audioManager.stopBluetoothSco()
        audioManager.mode = AudioManager.MODE_NORMAL
    }

}

interface HeadsetPlugListener {
    fun onHeadsetPlug(headsetType: Int)
}

private val AudioManager.compactAvailableCommunicationDevices : List<AudioDeviceInfo>
    get() = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        availableCommunicationDevices
    } else {
        getDevices(AudioManager.GET_DEVICES_INPUTS or AudioManager.GET_DEVICES_OUTPUTS).toList()
    }


private fun Int.toTypeDesc() = when (this) {
    1 -> "TYPE_BUILTIN_EARPIECE"
    2 -> "TYPE_BUILTIN_SPEAKER"
    3 -> "TYPE_WIRED_HEADSET"
    4 -> "TYPE_WIRED_HEADPHONES"
    7 -> "TYPE_BLUETOOTH_SCO"
    8 -> "TYPE_BLUETOOTH_A2DP"
    22 -> "TYPE_USB_HEADSET"
    26 -> "TYPE_BLE_HEADSET"
    27 -> "TYPE_BLE_SPEAKER"
    else -> this.toString()
}