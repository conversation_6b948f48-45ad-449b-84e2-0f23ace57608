package com.twl.hi.basic.model.select;

import androidx.databinding.ObservableBoolean;

/**
 * <AUTHOR>
 * @date 2021/3/23.
 */
public abstract class SelectBaseBean extends SelectInterface {
    private SelectProxy mSelectProxy;
    public ObservableBoolean mSelected = new ObservableBoolean(false) {
        private static final long serialVersionUID = 7878477301520106971L;

        @Override
        public void set(boolean value) {
            SelectProxy selectProxy = mSelectProxy;
            if (selectProxy != null) {
                selectProxy.setSelect(getId(), value);
            }
            super.set(value);
            //set方法新旧值相同时不会通知
            //ObservableBoolean这里默认为false，Proxy里为true，展现给用户的是ture
            //这个时候，用户希望从false设置为ture，但是 ObservableBoolean 当前值也为false，会跳过notifyChange，为避免这种问题，这里只能强制通知
            mSelected.notifyChange();
        }

        @Override
        public boolean get() {
            SelectProxy selectProxy = mSelectProxy;
            if (selectProxy != null) {
                return selectProxy.isSelect(getId());
            }
            return super.get();
        }
    };

    public SelectBaseBean(SelectProxy mSelectProxy) {
        this.mSelectProxy = mSelectProxy;
    }

    @Override
    public boolean isSelected() {
        return mSelected.get();
    }

    @Override
    public void setSelected(boolean selected) {
        mSelected.set(selected);
    }
}
