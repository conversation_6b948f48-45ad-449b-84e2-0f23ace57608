package com.twl.hi.basic.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

public class SystemSettingSetRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public int platform = 2;
    @Expose
    public String setting;

    public SystemSettingSetRequest() {
    }

    public SystemSettingSetRequest(AbsRequestCallback<HttpResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_SYSTEM_SETTING_SET;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}

