package com.twl.hi.basic.viewmodel;

import android.app.Application;
import android.net.Uri;
import android.text.TextUtils;
import androidx.annotation.Nullable;
import androidx.documentfile.provider.DocumentFile;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.R;
import com.twl.hi.basic.api.request.SendMsgEmojiRequest;
import com.twl.hi.basic.helpers.MediaUploadCallback;
import com.twl.hi.basic.helpers.MediaUploadHelper;
import com.twl.hi.basic.model.VideoFilesRetryModel;
import com.twl.hi.basic.util.ChatHelper;
import com.twl.hi.foundation.MessageFactory;
import com.twl.hi.foundation.api.base.BaseUpdateRequestCallback;
import com.twl.hi.foundation.api.request.FileUploadRequest;
import com.twl.hi.foundation.api.request.ImageUploadRequestV2;
import com.twl.hi.foundation.api.response.FileUploadResponse;
import com.twl.hi.foundation.api.response.ImageUploadResponse;
import com.twl.hi.foundation.api.response.VideoUploadResult;
import com.twl.hi.foundation.helper.GrayConfigGetHelper;
import com.twl.hi.foundation.logic.MessageService;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.logic.SystemGrayConfigService;
import com.twl.hi.foundation.model.ReplyBean;
import com.twl.hi.foundation.model.emotion.EmotionItem;
import com.twl.hi.foundation.model.message.*;
import com.twl.hi.foundation.utils.GroupInfoHelper;
import com.twl.hi.foundation.utils.GroupStatusCheckCallback;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.GsonUtils;
import com.twl.utils.SettingBuilder;
import com.twl.utils.StringUtils;
import com.twl.utils.file.FileUtils;
import com.zhihu.matisse.MimeType;
import lib.twl.common.model.ImageInfo;
import lib.twl.common.photoselect.PhotoSelectManager;
import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.LDate;
import lib.twl.common.util.LList;
import lib.twl.common.util.ToastUtils;
import lib.twl.common.util.image.ImageUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 包含发送不同消息类型的逻辑的ViewModel
 */
public class SendMessageViewModel extends TemporaryTokenViewModel {

    private static final String TAG = "SendMessageViewModel";

    public Set<String> mVideoSuffix = new TreeSet<>();
    public Set<String> mPicSuffix = new TreeSet<>();

    private final AtomicBoolean mUpdateBoolean = new AtomicBoolean(false);

    public SendMessageViewModel(Application application) {
        super(application);
        for (MimeType mimeType : MimeType.ofAll()) {
            if (mimeType.getMimeTypeName().startsWith(PhotoSelectManager.IMAGE)) {
                mPicSuffix.addAll(mimeType.getExtensions());
            } else {
                mVideoSuffix.addAll(mimeType.getExtensions());
            }
        }
    }

    public void sendMessage(ChatMessage chatMessage) {
        MessageService service = ServiceManager.getInstance().getMessageService();
        service.sendMessage(chatMessage);
    }

    public void sendTextFileMessage(String content, String chatId, int type, ReplyBean replyBean) {
        setShowProgressBar();
        ExecutorFactory.execLocalTask(() -> {
            StringBuilder name = new StringBuilder("text_");
            String date = LDate.yyyyMMdd_HH_mm_ssFormat.format(new Date());
            name.append(date);
            name.append(".txt");
            String path = ServiceManager.getInstance().getFileService().getFileExternalDownloadPath();
            File file = new File(path + name);
            try {
                FileUtils.write(file, content);
                sendFile(file, file.getName(), file.length(), chatId, type, replyBean);
            } catch (IOException e) {
                TLog.error(TAG, e.getMessage());
                ToastUtils.failure("转文件发送失败");
                hideShowProgressBar();
            }
        });
    }

    private void requestSendVideo(File file, MessageForVideo messageForVideo, boolean isResend) {
        ExecutorFactory.execLocalTask(() -> {
            MessageService messageService = ServiceManager.getInstance().getMessageService();
            MessageForVideo videoMessage = messageForVideo;
            if (isResend) {
                videoMessage = (MessageForVideo) messageService.prepareMessageForResend(messageForVideo);
            } else {
                messageService.prepareMessage(videoMessage);
            }
            MessageForVideo finalVideoMessage = videoMessage;
            MediaUploadHelper.uploadVideoWithCheckAndProgressV2(file, new MediaUploadCallback() {
                @Override
                public void onUploadFinish(@Nullable VideoUploadResult result) {
                    ExecutorFactory.execLocalTask(new Runnable() {
                        @Override
                        public void run() {
                            if (result != null && finalVideoMessage.getVideoInfo() != null) {
                                finalVideoMessage.getVideoInfo().setUrl(result.getUrl());
                                ImageInfo imageInfo = finalVideoMessage.getVideoInfo().getThumbnail();
                                if(imageInfo == null){
                                    finalVideoMessage.getVideoInfo().setThumbnail(
                                            new ImageInfo(result.getWidth(), result.getHeight(), result.getCoverUrlOrEmpty())
                                    );
                                }else{
                                    imageInfo.setUrl(result.getCoverUrlOrEmpty());
                                }
                                finalVideoMessage.getVideoInfo().setVurl(result.getFileId());
                                finalVideoMessage.getVideoInfo().setUrlSource(MessageForVideo.VideoInfo.URL_SOURCE_VGROUP);
                                messageService.sendMediaMessage(finalVideoMessage, true);
                            } else {
                                messageService.sendMediaMessage(finalVideoMessage, false);
                            }
                        }
                    });

                }

                @Override
                public void onUploadFail(@Nullable ErrorReason reason) {
                    TLog.info(TAG, "sendFileForMain upload fail");
                    ExecutorFactory.execLocalTask(new Runnable() {
                        @Override
                        public void run() {
                            messageService.sendMediaMessage(finalVideoMessage, false);
                        }
                    });
                }
            }, null, finalVideoMessage.getMid());
        });
    }

    public void sendEmotionItemMessage(EmotionItem emotionItem, long packageId, String chatId, int type, ReplyBean replyBean) {
        MessageService service = ServiceManager.getInstance().getMessageService();
        MessageForSticker messageForSticker = MessageFactory.createSticker(emotionItem, packageId, chatId, type, replyBean);
        service.sendMessage(messageForSticker);
    }

    /**
     * 主线程调用
     */
    public void sendFileForMain(File file, String name, long size, String chatId, int type, ReplyBean replyBean) {
        ExecutorFactory.execLocalTask(() -> sendFile(file, name, size, chatId, type, replyBean));
    }

    private void requestSendFile(File file, MessageForFile messageForFile, boolean isResend) {
        TLog.info(TAG, "SendMessageViewModel -> requestSendFile " + file.getAbsolutePath());
        MessageService service = ServiceManager.getInstance().getMessageService();
        MessageForFile fileMessage = messageForFile;
        if (isResend) {
            fileMessage = (MessageForFile) service.prepareMessageForResend(messageForFile);
        } else {
            service.prepareMessage(fileMessage);
        }

        MessageForFile finalFileMessage = fileMessage;

        MediaUploadHelper.uploadFileWithCheck(file, new MediaUploadCallback() {

            @Override
            public void onUploadFinish(@Nullable String mediaUrl) {
                if (StringUtils.isNotEmpty(mediaUrl)) {
                    finalFileMessage.withNewUrl(mediaUrl);
                    service.sendMediaMessage(finalFileMessage, true);
                } else {
                    service.sendMediaMessage(finalFileMessage, false);
                }
                hideShowProgressBar();
                TLog.info(SendMessageViewModel.TAG, "SendMessageViewModel -> send success file " + file.getAbsolutePath());
            }

            @Override
            public void onUploadFail(@Nullable ErrorReason reason) {
                service.sendMediaMessage(finalFileMessage, false);
                hideShowProgressBar();
                TLog.info(TAG, "SendMessageViewModel -> handleErrorInChildThread " + reason + ", file " + file.getAbsolutePath());
            }
        }, finalFileMessage.getMid());
    }

    public void sendAudio(String fileString, long duration, String chatId, int type, ReplyBean replyBean, String voiceText, boolean isVoiceText) {
        File file = new File(fileString);
        MessageForAudio messageForAudio = MessageFactory.createAudioMessage(file, duration, file.length(), chatId, type, replyBean, voiceText, isVoiceText);
        requestSendAudio(file, messageForAudio, false);
    }

    private void requestSendAudio(File file, MessageForAudio messageForAudio, boolean isResend) {
        ExecutorFactory.execLocalTask(() -> {
            MessageService service = ServiceManager.getInstance().getMessageService();
            MessageForAudio audioMessage = messageForAudio;
            if (isResend) {
                audioMessage = (MessageForAudio) service.prepareMessageForResend(messageForAudio);
            } else {
                service.prepareMessage(audioMessage);
            }
            MessageForAudio finalAudioMessage = audioMessage;
            FileUploadRequest request = new FileUploadRequest(new BaseUpdateRequestCallback<FileUploadResponse>() {
                @Override
                public void handleInChildThread(ApiData<FileUploadResponse> data) {
                    super.handleInChildThread(data);
                    FileUploadResponse response = data.resp;
                    if (response != null) {
                        finalAudioMessage.withNewUrl(response.url);
                        service.sendPrepareMessage(finalAudioMessage, true);
                    } else {
                        service.sendPrepareMessage(finalAudioMessage, false);
                    }
                }

                @Override
                public void handleErrorInChildThread(ErrorReason reason) {
                    super.handleErrorInChildThread(reason);
                    service.sendPrepareMessage(finalAudioMessage, false);

                }
            });
            request.setFile(file);
            HttpExecutor.execute(request);
        });
    }

    /**
     * 处理图片/视频等多媒体文件发送逻辑
     */
    public void handleMediaFilesSend(final List<File> files, String chatId, int type, ReplyBean replyBean) {
        ExecutorFactory.execLocalTask(() -> {
            List<File> videoFiles = new ArrayList<>();
            if (LList.isEmpty(files)) {
                return;
            }
            boolean hasBigFile = false;
            Iterator<File> fileIterator = files.iterator();
            while (fileIterator.hasNext()) {
                File file = fileIterator.next();
                TLog.info(TAG, "file:" + file.getAbsolutePath() + " file size:" + file.length());
                Integer maxSize = GrayConfigGetHelper.getSingleValue(
                        SystemGrayConfigService.GraySettings.CHAT_FILE_MAX_SIZE,
                        GrayConfigGetHelper.MAX_FILE_SIZE,
                        Integer.class
                );
                String mimeType = checkVideoOrPic(FileUtils.getFileSuffix(file));
                if (file.length() > (long) Optional.ofNullable(maxSize).orElse(2) * 1024 * 1024 * 1024) {
                    hasBigFile = true;
                    fileIterator.remove();
                } else if (isVideoOverflow(mimeType, file) || isNormalMediaOverflow(mimeType, file)) {
                    //文件格式
                    sendFile(file, file.getName(), file.length(), chatId, type, replyBean);
                    fileIterator.remove();
                } else {
                    if (TextUtils.equals(mimeType, PhotoSelectManager.VIDEO)) {
                        videoFiles.add(file);
                        fileIterator.remove();
                    }
                }
            }
            // 图片文件发送
            requestSendPicFiles(files, chatId, type, replyBean);
            if (!videoFiles.isEmpty()) {
                // 视频文件发送
                requestSendVideoFiles(videoFiles, chatId, type, replyBean);
            }
            if (hasBigFile) {
                ToastUtils.failure(getResources().getString(R.string.over_send_file_max_size));
            }
        });
    }

    private static boolean isVideoOverflow(String mimeType, File file) {
        return TextUtils.equals(mimeType, PhotoSelectManager.VIDEO) && file.length() > SettingBuilder.getInstance().getVideoMaxSize() * 1024 * 1024;
    }

    private static boolean isNormalMediaOverflow(String mimeType, File file) {
        return !TextUtils.equals(mimeType, PhotoSelectManager.VIDEO) && file.length() > SettingBuilder.getInstance().getMediaMaxSize() * 1024 * 1024;
    }

    /**
     * 图片消息发送
     */
    private void requestSendPicFiles(List<File> files, String chatId, int type, ReplyBean replyBean) {

        if (LList.isEmpty(files)) {
            return;
        }

        File file = files.remove(0);

        if (file == null) {
            return;
        }

        if (ImageUtils.isHeicOrHeifImage(file)) {
            file = ImageUtils.convertToJPEG(file);
        }

        MessageService service = ServiceManager.getInstance().getMessageService();
        final MessageForPic messageForPic = MessageFactory.createPicMessage(file, chatId, type, replyBean);
        service.prepareMessage(messageForPic);
        ImageUploadRequestV2 request = new ImageUploadRequestV2(new BaseUpdateRequestCallback<ImageUploadResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                TLog.info(TAG, "start upload image");
            }

            @Override
            public void handleInChildThread(ApiData<ImageUploadResponse> data) {
                ImageUploadResponse response = data.resp;
                if (response != null) {
                    TLog.info(TAG, "upload image result is : " + GsonUtils.getGson().toJson(response));
                }
                if (response != null && response.originImage != null && response.tinyImage != null) {
                    if (ImageUtils.isGif(response.originImage.getUrl())) {
                        response.tinyImage.setUrl(response.originImage.getUrl());
                    }
                    messageForPic.setImage(response.originImage, response.tinyImage);
                    service.sendMediaMessage(messageForPic, true);
                } else {
                    service.sendMediaMessage(messageForPic, false);
                }
                requestSendPicFiles(files, chatId, type, replyBean);
            }

            @Override
            public void handleErrorInChildThread(ErrorReason reason) {
                service.sendMediaMessage(messageForPic, false);
                requestSendPicFiles(files, chatId, type, replyBean);
                TLog.error(TAG, "upload image error: " + reason.toString());
            }

        });
        request.file = file;
        HttpExecutor.execute(request);
    }

    private void resendPicMessage(File file, MessageForPic picMessage) {
        TLog.info(TAG, "resendPicMessage -> file : " + file.getAbsolutePath());
        if (!file.exists()) {
            ToastUtils.failure("原图片文件已不存在");
            return;
        }
        MessageService service = ServiceManager.getInstance().getMessageService();
        final MessageForPic messageForPic = (MessageForPic) service.prepareMessageForResend(picMessage);
        ImageUploadRequestV2 request = new ImageUploadRequestV2(new BaseUpdateRequestCallback<ImageUploadResponse>() {
            @Override
            public void handleInChildThread(ApiData<ImageUploadResponse> data) {
                ImageUploadResponse response = data.resp;
                if (response != null) {
                    TLog.info(TAG, "upload image result is : " + GsonUtils.getGson().toJson(response));
                }
                if (response != null && response.originImage != null && response.tinyImage != null) {
                    if (ImageUtils.isGif(response.originImage.getUrl())) {
                        response.tinyImage.setUrl(response.originImage.getUrl());
                    }
                    messageForPic.setImage(response.originImage, response.tinyImage);
                    service.sendMediaMessage(messageForPic, true);
                } else {
                    service.sendMediaMessage(messageForPic, false);
                }

            }

            @Override
            public void handleErrorInChildThread(ErrorReason reason) {
                service.sendMediaMessage(messageForPic, false);
            }
        });
        request.file = file;
        HttpExecutor.execute(request);
    }

    public void sendFileWithCheck(String chatId, int type, GroupStatusCheckCallback callback) {
        GroupInfoHelper.optWithGroupStatusCheck(chatId, type, callback);
    }

    public void sendFile(Uri uri, String chatId, int type, ReplyBean replyBean) {
        try {
            DocumentFile documentFile = DocumentFile.fromSingleUri(getApplication(), uri);
            File file = new File(ServiceManager.getInstance().getFileService().getCachePath(), documentFile.getName());
            if (!file.exists()) {
                if (!file.isFile()) {
                    file.createNewFile();
                }
                FileUtils.copyFile(getApplication().getContentResolver().openInputStream(uri), file);
            }

            if (file.exists()) {
                if (file.length() <= 0) {
                    ToastUtils.failure(R.string.selected_file_damage);
                    return;
                }
                sendFileMessage(file, uri, chatId, type, replyBean);
            } else {
                ToastUtils.failure(R.string.selected_file_damage);
            }
        } catch (Exception e) {
            ToastUtils.failure(R.string.selected_file_damage);
            e.printStackTrace();
        }
    }

    public void sendFile(File file, String chatId, int type, ReplyBean replyBean) {
        try {
            if (file.exists()) {
                if (file.length() <= 0) {
                    ToastUtils.failure(R.string.selected_file_damage);
                    return;
                }
                sendFileMessage(file, null, chatId, type, replyBean);
            } else {
                ToastUtils.failure(R.string.selected_file_damage);
            }
        } catch (Exception e) {
            ToastUtils.failure(R.string.selected_file_damage);
        }
    }

    public void sendFile(File file, String name, long size, String chatId, int type, ReplyBean replyBean) {
        MessageForFile messageForFile = MessageFactory.createFileMessage(file, "", name, size, chatId, type, replyBean);
        requestSendFile(file, messageForFile, false);
    }

    /**
     * 发送文件类型的消息
     *
     * @param file      要发送的文件
     * @param uri       uri地址
     * @param chatId    消息Id
     * @param replyBean 回复所需Id信息
     */
    public void sendFileMessage(File file, Uri uri, String chatId, int type, ReplyBean replyBean) {
        TLog.info(TAG, "file:" + file.getAbsolutePath() + " file size:" + file.length());
        Integer maxSize = GrayConfigGetHelper.getSingleValue(
                SystemGrayConfigService.GraySettings.CHAT_FILE_MAX_SIZE,
                GrayConfigGetHelper.MAX_FILE_SIZE,
                Integer.class
        );
        if (file.length() > (long) Optional.ofNullable(maxSize).orElse(2) * 1024 * 1024 * 1024) {
            ToastUtils.failure(getResources().getString(R.string.over_send_file_max_size));
            return;
        }
        String upLoadFileName = file.getName();
        // 依扩展名的类型决定MimeType
        String mimeType = checkVideoOrPic(FileUtils.getFileSuffix(file));

        switch (mimeType) {
            case PhotoSelectManager.IMAGE:
                if (file.length() <= SettingBuilder.getInstance().getMediaMaxSize() * 1024 * 1024) {
                    ExecutorFactory.execLocalTask(new Runnable() {
                        @Override
                        public void run() {
                            List<File> files = new ArrayList<>();
                            files.add(file);
                            requestSendPicFiles(files, chatId, type, replyBean);
                        }
                    });
                    return;
                } else {
                    sendFileForMain(file, upLoadFileName, file.length(), chatId, type, replyBean);
                }

                break;
            case PhotoSelectManager.VIDEO:
                if (file.length() <= SettingBuilder.getInstance().getVideoMaxSize() * 1024 * 1024) {
                    MessageForVideo videoMessage = createMessageForVideoFromFile(file, uri, chatId, type, replyBean);
                    requestSendVideo(file, videoMessage, false);
                } else {
                    sendFileForMain(file, upLoadFileName, file.length(), chatId, type, replyBean);
                }
                break;
            default:
                sendFileForMain(file, upLoadFileName, file.length(), chatId, type, replyBean);
                break;
        }
    }

    public void requestSendVideoFiles(List<File> files, String chatId, int type, ReplyBean replyBean) {
        List<VideoFilesRetryModel> models = new ArrayList<>();
        for (File file : files) {
            MessageForVideo videoMessage = createMessageForVideoFromFile(file, null, chatId, type, replyBean);
            ExecutorFactory.getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    ExecutorFactory.execLocalTask(new Runnable() {
                        @Override
                        public void run() {
                            ServiceManager.getInstance().getMessageService().prepareMessage(videoMessage);
                        }
                    });
                }
            }, 100);
            VideoFilesRetryModel retryModel = new VideoFilesRetryModel();
            retryModel.file = file;
            retryModel.messageForVideo = videoMessage;
            retryModel.retryCount = MessageConstants.VIDEO_SEND_RETRY_COUNT;
            models.add(retryModel);
        }
        sendVideoFiles(models);
    }

    public MessageForVideo createMessageForVideoFromFile(File file, Uri uri, String chatId, int type, ReplyBean replyBean) {
        int[] info = new int[2];
        String thumbnail = FileUtils.getVideoThumbnail(getApplication(), file.getAbsolutePath(), info);
        int duration;
        if (uri == null) {
            duration = FileUtils.getVideoDuration("file://" + file.getAbsolutePath());
        } else {
            duration = FileUtils.getVideoDuration(getApplication(), uri);
            if (duration <= 0) {
                duration = FileUtils.getVideoDuration("file://" + file.getAbsolutePath());
            }
        }
        MessageForVideo messageForVideo = MessageFactory.createVideoMessage(file, file.length(), duration, chatId, type, replyBean,null,0);
        messageForVideo.getVideoInfo().setThumbnail(new ImageInfo(info[0], info[1], "file://" + thumbnail));
        return messageForVideo;
    }

    /**
     * 遍历视频文件列表并依次发送
     */
    public void sendVideoFiles(List<VideoFilesRetryModel> models) {
        if (LList.isEmpty(models)) {
            return;
        }
        VideoFilesRetryModel videoFilesRetryModel = models.remove(0);
        if (videoFilesRetryModel == null) {
            return;
        }
        MediaUploadHelper.uploadVideoWithCheckAndProgressV2(videoFilesRetryModel.file, new MediaUploadCallback() {
            @Override
            public void onUploadFinish(@Nullable VideoUploadResult result) {
                ExecutorFactory.execLocalTask(() -> {
                    MessageService service = ServiceManager.getInstance().getMessageService();
                    if (result != null && StringUtils.isNotEmpty(result.getUrl())) {
                        videoFilesRetryModel.messageForVideo.getVideoInfo().setUrl(result.getUrl());
                        videoFilesRetryModel.messageForVideo.getVideoInfo().setUrlSource(result.getUrlSource());
                        ImageInfo info = videoFilesRetryModel.messageForVideo.getVideoInfo().getThumbnail();
                        if (info != null) {
                            info.setUrl(result.getCoverUrl());
                        }
                        videoFilesRetryModel.messageForVideo.getVideoInfo().setVurl(result.getFileId());
                        service.sendMediaMessage(videoFilesRetryModel.messageForVideo, true);
                        TLog.info("UploadVideo", "onUploadFinish success" );

                    } else {
                        handleUploadFailed(videoFilesRetryModel, models, service);
                    }
                    sendVideoFiles(models);
                });

            }

            @Override
            public void onUploadFail(@Nullable ErrorReason reason) {
                ExecutorFactory.execLocalTask(() -> {
                    MessageService service = ServiceManager.getInstance().getMessageService();

                    TLog.info("UploadVideo", "onUploadFail" + reason.getErrReason());
                    handleUploadFailed(videoFilesRetryModel, models, service);
                    sendVideoFiles(models);
                });
            }

            private void handleUploadFailed(VideoFilesRetryModel videoFilesRetryModel, List<VideoFilesRetryModel> models, MessageService service) {
                if (videoFilesRetryModel.retryCount > 0) {
                    videoFilesRetryModel.retryCount -= 1;
                    models.add(videoFilesRetryModel);
                } else {
                    service.sendMediaMessage(videoFilesRetryModel.messageForVideo, false);
                }
            }
        }, null, videoFilesRetryModel.messageForVideo.getMid());

//        ExecutorFactory.execLocalTask(() -> {
//            MessageService service = ServiceManager.getInstance().getMessageService();

//            MessageService service = ServiceManager.getInstance().getMessageService();
//            ImageUploadRequestV2 thumbRequest = new ImageUploadRequestV2(new BaseUpdateRequestCallback<ImageUploadResponse>() {
//                @Override
//                public void handleInChildThread(ApiData<ImageUploadResponse> data) {
//                    super.handleInChildThread(data);
//                    videoFilesRetryModel.messageForVideo.getVideoInfo().setThumbnail(data.resp.originImage);
//
//                }
//
//                @Override
//                public void handleErrorInChildThread(ErrorReason reason) {
//                    handleUploadFailed(videoFilesRetryModel, models, service);
//                    sendVideoFiles(models);
//                }
//
//                private void handleUploadFailed(VideoFilesRetryModel videoFilesRetryModel, List<VideoFilesRetryModel> models, MessageService service) {
//                    if (videoFilesRetryModel.retryCount > 0) {
//                        videoFilesRetryModel.retryCount -= 1;
//                        models.add(videoFilesRetryModel);
//                    } else {
//                        service.sendMediaMessage(videoFilesRetryModel.messageForVideo, false);
//                    }
//                }
//            });
//            thumbRequest.file = new File(videoFilesRetryModel.messageForVideo.getVideoInfo().getThumbnail().getUrl().replace("file://", ""));
//            HttpExecutor.execute(thumbRequest);

        }

        public void resendMessage (ChatMessage chatMessage){
            TLog.debug(TAG, "resendMessage,type " + chatMessage.getMediaType());
            switch (chatMessage.getMediaType()) {
                case MessageConstants.MSG_FILE:
                    MessageForFile messageForFile = (MessageForFile) chatMessage;
                    if (TextUtils.isEmpty(messageForFile.getUrl())) {
                        ExecutorFactory.execLocalTask(() -> {
                            requestSendFile(new File(messageForFile.getLocalPath()), messageForFile, true);
                        });
                    } else {
                        serviceManagerResendMessage(messageForFile);
                    }
                    break;
                case MessageConstants.MSG_PIC:
                    MessageForPic messageForPic = (MessageForPic) chatMessage;
                    TLog.info(TAG, "resend pic message , content : " + messageForPic.getContent());
                    if (messageForPic.isOriginOrTinyEmpty()) {
                        ExecutorFactory.execLocalTask(() -> {
                            resendPicMessage(new File(messageForPic.getLocalInfo().getUrl().replace("file:", "")), messageForPic);
                        });
                    } else {
                        serviceManagerResendMessage(messageForPic);
                    }
                    break;
                case MessageConstants.MSG_VIDEO:
                    MessageForVideo messageForVideo = (MessageForVideo) chatMessage;
                    if (TextUtils.isEmpty(messageForVideo.getUrl())) {
                        requestSendVideo(new File(messageForVideo.getVideoInfo().getLocalPath()), messageForVideo, true);
                    } else {
                        serviceManagerResendMessage(messageForVideo);
                    }
                    break;
                case MessageConstants.MSG_AUDIO:
                    MessageForAudio messageForAudio = (MessageForAudio) chatMessage;
                    if (TextUtils.isEmpty(messageForAudio.getUrl())) {
                        String picPath = messageForAudio.getAudioInfo().getLocalPath();
                        requestSendAudio(new File(picPath), messageForAudio, true);
                    } else {
                        serviceManagerResendMessage(messageForAudio);
                    }
                    break;
                default:
                    serviceManagerResendMessage(chatMessage);
                    break;
            }
        }

        public void serviceManagerResendMessage (ChatMessage chatMessage){
            ExecutorFactory.execLocalTask(() -> {
                ServiceManager.getInstance().getMessageService().resend(chatMessage);
            });
        }

        /**
         * 对指定消息进行表情回复
         *
         * @param replyMsgId   消息id
         * @param replyEmojiType 表情id
         */
        public void sendThumbsUpEmoji ( long replyMsgId, int replyEmojiType){
            ExecutorFactory.execLocalTask(() -> {
                if (mUpdateBoolean.compareAndSet(false, true)) {
                    boolean added = ServiceManager.getInstance().getMessageService().updateEmojiReply(replyMsgId, replyEmojiType);
                    if (added) {
                        ChatHelper.addReplyMsgId(replyMsgId);
                    }
                    SendMsgEmojiRequest request = new SendMsgEmojiRequest(null);
                    request.msgId = replyMsgId;
                    request.operate = added ? 1 : 2;
                    request.type = replyEmojiType;
                    HttpExecutor.execute(request);
                    mUpdateBoolean.set(false);
                }
            });
        }

        public String checkVideoOrPic (String endName){
            if (mPicSuffix.contains(endName)) {
                return PhotoSelectManager.IMAGE;
            } else if (mVideoSuffix.contains(endName)) {
                return PhotoSelectManager.VIDEO;
            }
            return "";
        }

        /**
         * 发送 markdown 消息
         *
         * @param title     标题
         * @param content   内容
         * @param chatId    会话 id
         * @param atIds     at 的用户 id
         * @param chatType  聊天类型
         * @param replyBean 回复
         */
        public void sendMarkdownMessage (String title, String content, String chatId, List < String > atIds,
        int chatType, ReplyBean replyBean, Integer sourceType,String pbExtension){
            TLog.debug(TAG, "markdown: " + content);
            MessageForMarkdownText markdownMessage = MessageFactory.createMarkdownTextMessage(title, content, chatId, chatType, replyBean, atIds, sourceType);
            markdownMessage.setPbExtension(pbExtension);
            sendMessage(markdownMessage);
            afterSendAtMessage(chatId, atIds);
        }

        protected void afterSendAtMessage (String chatId, List < String > atIds){
        }
    }
