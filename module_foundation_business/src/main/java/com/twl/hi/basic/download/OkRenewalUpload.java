package com.twl.hi.basic.download;

import com.twl.hi.foundation.model.ChatAttachment;

import java.io.File;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 文件分片上传 - 重构版
 * 支持基于 ChatAttachment.fileType 的上传任务创建
 */
public class OkRenewalUpload {

    private ExecutorService executorService;

    // 任务管理Map
    private final Map<String, RenewalUploadTask> taskMap = new ConcurrentHashMap<>();


    public static OkRenewalUpload getInstance() {
        return OkUploadHolder.instance;
    }

    private static class OkUploadHolder {
        private static final OkRenewalUpload instance = new OkRenewalUpload();
    }

    private OkRenewalUpload() {
    }


    /**
     * 根据附件类型创建上传任务
     */
    public static RenewalUploadTask createUploadTask(ChatAttachment attachment, File file) {
        String tag = attachment.getId();

        switch (attachment.getFileType()) {
            case ChatAttachment.FILE_TYPE_VIDEO:
                return createVideoUploadTask(tag, file, attachment);

            case ChatAttachment.FILE_TYPE_IMAGE:
            case ChatAttachment.FILE_TYPE_DOCUMENT:
            default:
                return createFileUploadTask(tag, file);
        }
    }

    /**
     * 创建视频上传任务
     */
    public static VideoRenewalUploadTask createVideoUploadTask(String tag, File file, ChatAttachment attachment) {
        Map<String, RenewalUploadTask> taskMap = getInstance().getTaskMap();

        RenewalUploadTask existingTask = taskMap.get(tag);
        if (existingTask instanceof VideoRenewalUploadTask) {
            return (VideoRenewalUploadTask) existingTask;
        }

        VideoRenewalUploadTask task = new VideoRenewalUploadTask(tag, file);

        // 设置视频信息
        if (attachment.getVideoWidth() > 0 && attachment.getVideoHeight() > 0) {
            task.setVideoInfo(new int[]{attachment.getVideoWidth(), attachment.getVideoHeight()});
        }

        if (attachment.getVideoCoverUrl() != null && !attachment.getVideoCoverUrl().isEmpty()) {
            task.setCoverUrl(attachment.getVideoCoverUrl());
        }

        taskMap.put(tag, task);
        return task;
    }

    /**
     * 创建文件上传任务
     */
    public static RenewalUploadTask createFileUploadTask(String tag, File file) {
        Map<String, RenewalUploadTask> taskMap = getInstance().getTaskMap();

        RenewalUploadTask existingTask = taskMap.get(tag);
        if (existingTask != null && !(existingTask instanceof VideoRenewalUploadTask)) {
            return existingTask;
        }

        RenewalUploadTask task = new RenewalUploadTask(tag, file);
        taskMap.put(tag, task);
        return task;
    }

    /**
     * 保持原有接口兼容性
     */
    public static RenewalUploadTask request(String tag, File file) {
        return createFileUploadTask(tag, file);
    }

    /**
     * 获取任务管理Map
     */
    public Map<String, RenewalUploadTask> getTaskMap() {
        return taskMap;
    }

    public ExecutorService getExecutor() {
        if (executorService == null) {
            executorService = Executors.newCachedThreadPool();
        }
        return executorService;
    }
}
