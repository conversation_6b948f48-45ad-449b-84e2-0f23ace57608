package com.twl.hi.basic.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.basic.api.response.GroupCreateResponse;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class JoinGroupRequest extends BaseApiRequest<GroupCreateResponse> {
    @Expose
    public String groupId;

    @Expose
    public String qrCode;

    public JoinGroupRequest(BaseApiRequestCallback<GroupCreateResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_GROUP_JOIN;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
