package com.twl.hi.basic.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.basic.api.response.GetUserInfoResponse;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

/**
 * Created by Xuweixiang on 2022/4/19 21:43
 * email : <EMAIL>
 * describe :
 */
public class GetUserInfoRequest extends BaseApiRequest<GetUserInfoResponse> {

    @Expose
    public String appId;

    @Expose
    public boolean withCredentials;

    public GetUserInfoRequest(AbsRequestCallback<GetUserInfoResponse> callback) {
        mCallback = callback;
    }

    public GetUserInfoRequest( String appId, boolean withCredentials, AbsRequestCallback<GetUserInfoResponse> callback) {
        mCallback = callback;
        this.appId = appId;
        this.withCredentials = withCredentials;
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_GET_USER_INFO;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET;
    }
}
