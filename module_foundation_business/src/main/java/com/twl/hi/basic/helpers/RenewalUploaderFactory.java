package com.twl.hi.basic.helpers;

import com.twl.hi.foundation.logic.IDocumentUploader;
import com.twl.hi.foundation.logic.IUploaderFactory;
import com.twl.hi.foundation.logic.IVideoUploader;

/**
 * 基于 RenewalUploadTask 的上传器工厂实现
 * 提供统一的上传器创建和管理
 */
public class RenewalUploaderFactory implements IUploaderFactory {
    
    private static RenewalUploaderFactory instance;
    
    // 单例上传器实例
    private RenewalVideoUploader videoUploader;
    private RenewalDocumentUploader documentUploader;
    
    private RenewalUploaderFactory() {
        // 私有构造函数
    }
    
    /**
     * 获取工厂单例
     */
    public static synchronized RenewalUploaderFactory getInstance() {
        if (instance == null) {
            instance = new RenewalUploaderFactory();
        }
        return instance;
    }
    
    @Override
    public IVideoUploader createVideoUploader() {
        if (videoUploader == null) {
            videoUploader = new RenewalVideoUploader();
        }
        return videoUploader;
    }
    
    @Override
    public IDocumentUploader createDocumentUploader() {
        if (documentUploader == null) {
            documentUploader = new RenewalDocumentUploader();
        }
        return documentUploader;
    }
    
    /**
     * 获取视频上传器实例（用于取消操作）
     */
    public RenewalVideoUploader getVideoUploader() {
        return videoUploader;
    }
    
    /**
     * 获取文档上传器实例（用于取消操作）
     */
    public RenewalDocumentUploader getDocumentUploader() {
        return documentUploader;
    }
    
    @Override
    public boolean cancelUpload(String attachmentId) {
        boolean cancelled = false;

        // 尝试取消视频上传
        if (videoUploader != null) {
            cancelled |= videoUploader.cancelVideoUpload(attachmentId);
        }

        // 尝试取消文档上传
        if (documentUploader != null) {
            cancelled |= documentUploader.cancelDocumentUpload(attachmentId);
        }

        return cancelled;
    }
    
    @Override
    public void clearAllTasks() {
        if (videoUploader != null) {
            videoUploader.clearAllTasks();
        }

        if (documentUploader != null) {
            documentUploader.clearAllTasks();
        }
    }
    
    /**
     * 获取活跃任务总数
     */
    public int getTotalActiveTaskCount() {
        int count = 0;
        
        if (videoUploader != null) {
            count += videoUploader.getActiveTaskCount();
        }
        
        if (documentUploader != null) {
            count += documentUploader.getActiveTaskCount();
        }
        
        return count;
    }
    
    /**
     * 检查是否有指定的活跃上传任务
     */
    public boolean hasActiveTask(String attachmentId) {
        boolean hasTask = false;
        
        if (videoUploader != null) {
            hasTask |= videoUploader.hasActiveTask(attachmentId);
        }
        
        if (documentUploader != null) {
            hasTask |= documentUploader.hasActiveTask(attachmentId);
        }
        
        return hasTask;
    }
}
