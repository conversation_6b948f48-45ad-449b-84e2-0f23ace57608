package com.twl.hi.basic.views.multitext;

import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.annotation.NonNull;

import com.twl.hi.router.base.RouterBaseConstant;

import hi.kernel.Constants;
import lib.twl.common.util.AppUtil;

public class UrlInnerClickSpan extends ClickableSpan {

    private final String url;
    private final boolean isAuthor;
    private final int authorLinkColor;
    private final int normalLinkColor;

    public UrlInnerClickSpan(String url, boolean isAuthor, int authorLinkColor, int normalLinkColor) {
        this.url = url;
        this.isAuthor = isAuthor;
        this.authorLinkColor = authorLinkColor;
        this.normalLinkColor = normalLinkColor;
    }

    @Override
    public void updateDrawState(@NonNull TextPaint ds) {
        int color = isAuthor ? authorLinkColor : normalLinkColor;
        ds.setColor(color);
//        ds.setUnderlineText(true);
    }

    @Override
    public void onClick(@NonNull View widget) {
        AppUtil.getDefaultUriRequest(widget.getContext(), url)
                .putField(RouterBaseConstant.WEB_STYLE, Constants.WEB_STYLE_SHARE_URL)
                .start();
    }
}
