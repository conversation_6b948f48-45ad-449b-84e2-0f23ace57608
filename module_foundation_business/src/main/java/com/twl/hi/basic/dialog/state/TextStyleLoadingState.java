package com.twl.hi.basic.dialog.state;

/**
 * @author: musa on 2022/1/28
 * @e-mail: yang<PERSON><PERSON><PERSON>@kanzhun.com
 * @desc: 有文字的加载框的显示状态
 */
public class TextStyleLoadingState extends LoadingState {
    /**展示文本*/
    private String text;

    public TextStyleLoadingState(boolean isShowed,boolean isCancelable, String text) {
        super(isShowed, isCancelable);
        this.text = text;
    }

    public String getText() {
        return text;
    }
}
