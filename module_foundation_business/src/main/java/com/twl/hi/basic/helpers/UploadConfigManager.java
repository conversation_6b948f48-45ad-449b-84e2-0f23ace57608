package com.twl.hi.basic.helpers;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.logic.ChatAttachmentService;
import com.twl.hi.foundation.logic.ServiceManager;

/**
 * 上传配置管理器
 * 负责配置和初始化新的上传器架构
 */
public class UploadConfigManager {
    private static final String TAG = "UploadConfigManager";
    
    private static boolean isInitialized = false;
    
    /**
     * 初始化新的上传器架构
     * 应该在应用启动时调用
     */
    public static void initializeUploadArchitecture() {
        if (isInitialized) {
            TLog.info(TAG, "Upload architecture already initialized");
            return;
        }
        
        try {
            TLog.info(TAG, "Initializing new upload architecture...");
            
            // 获取 ChatAttachmentService 实例
            ChatAttachmentService chatAttachmentService = ServiceManager.getInstance().getChatAttachmentService();
            
            if (chatAttachmentService != null) {
                // 创建并设置新的上传器工厂
                RenewalUploaderFactory factory = RenewalUploaderFactory.getInstance();

                // 设置上传器工厂到 ChatAttachmentService
                chatAttachmentService.setUploaderFactory(factory);
                TLog.info(TAG, "Uploader factory configured in ChatAttachmentService");

                isInitialized = true;
                TLog.info(TAG, "✅ New upload architecture initialized successfully!");

                // 打印配置信息
                logUploadConfiguration(factory);

            } else {
                TLog.error(TAG, "❌ Failed to get ChatAttachmentService instance");
            }
            
        } catch (Exception e) {
            TLog.error(TAG, "❌ Error initializing upload architecture", e);
        }
    }
    
    /**
     * 检查是否已初始化
     */
    public static boolean isInitialized() {
        return isInitialized;
    }
    
    /**
     * 获取当前配置的上传器工厂
     */
    public static RenewalUploaderFactory getUploaderFactory() {
        if (!isInitialized) {
            TLog.info(TAG, "Upload architecture not initialized yet");
            return null;
        }
        return RenewalUploaderFactory.getInstance();
    }
    
    /**
     * 强制重新初始化（用于测试）
     */
    public static void forceReinitialize() {
        TLog.info(TAG, "Force reinitializing upload architecture...");
        isInitialized = false;
        initializeUploadArchitecture();
    }
    
    /**
     * 打印当前上传配置信息
     */
    private static void logUploadConfiguration(RenewalUploaderFactory factory) {
        TLog.info(TAG, "=== Upload Architecture Configuration ===");
        TLog.info(TAG, "Video Uploader: " + (factory.getVideoUploader() != null ? "RenewalVideoUploader" : "Not configured"));
        TLog.info(TAG, "Document Uploader: " + (factory.getDocumentUploader() != null ? "RenewalDocumentUploader" : "Not configured"));
        TLog.info(TAG, "Active Tasks: " + factory.getTotalActiveTaskCount());
        TLog.info(TAG, "Factory Instance: " + factory.getClass().getSimpleName());
        TLog.info(TAG, "========================================");
    }
    
    /**
     * 获取上传统计信息
     */
    public static String getUploadStats() {
        if (!isInitialized) {
            return "Upload architecture not initialized";
        }
        
        RenewalUploaderFactory factory = RenewalUploaderFactory.getInstance();
        StringBuilder stats = new StringBuilder();
        stats.append("Upload Statistics:\n");
        stats.append("- Total Active Tasks: ").append(factory.getTotalActiveTaskCount()).append("\n");
        
        if (factory.getVideoUploader() != null) {
            stats.append("- Video Tasks: ").append(factory.getVideoUploader().getActiveTaskCount()).append("\n");
        }
        
        if (factory.getDocumentUploader() != null) {
            stats.append("- Document Tasks: ").append(factory.getDocumentUploader().getActiveTaskCount()).append("\n");
        }
        
        return stats.toString();
    }
    
    /**
     * 清理所有上传任务（用于应用退出或重置）
     */
    public static void clearAllUploadTasks() {
        if (!isInitialized) {
            TLog.info(TAG, "Upload architecture not initialized, nothing to clear");
            return;
        }
        
        try {
            RenewalUploaderFactory factory = RenewalUploaderFactory.getInstance();
            int taskCount = factory.getTotalActiveTaskCount();
            
            if (taskCount > 0) {
                TLog.info(TAG, "Clearing " + taskCount + " active upload tasks...");
                factory.clearAllTasks();
                TLog.info(TAG, "✅ All upload tasks cleared");
            } else {
                TLog.info(TAG, "No active upload tasks to clear");
            }
            
        } catch (Exception e) {
            TLog.error(TAG, "❌ Error clearing upload tasks", e);
        }
    }
    
    /**
     * 取消指定的上传任务
     */
    public static boolean cancelUploadTask(String attachmentId) {
        if (!isInitialized) {
            TLog.info(TAG, "Upload architecture not initialized");
            return false;
        }
        
        try {
            RenewalUploaderFactory factory = RenewalUploaderFactory.getInstance();
            boolean cancelled = factory.cancelUpload(attachmentId);
            
            if (cancelled) {
                TLog.info(TAG, "✅ Successfully cancelled upload task: " + attachmentId);
            } else {
                TLog.info(TAG, "No active upload task found for: " + attachmentId);
            }
            
            return cancelled;
            
        } catch (Exception e) {
            TLog.error(TAG, "❌ Error cancelling upload task: " + attachmentId, e);
            return false;
        }
    }
}
