package com.twl.hi.basic.helpers

import com.twl.hi.foundation.logic.IDocumentUploader
import com.twl.hi.foundation.logic.IUploaderFactory
import com.twl.hi.foundation.logic.IVideoUploader

/**
 * 上传器工厂实现
 * 解决依赖倒置问题：在业务模块中实现工厂，为底层服务提供上传器实例
 */
class UploaderFactoryImpl : IUploaderFactory {

    // 使用 RenewalUploaderFactory 作为底层实现
    private val renewalFactory = RenewalUploaderFactory.getInstance()

    override fun createVideoUploader(): IVideoUploader? {
        return renewalFactory.createVideoUploader()
    }

    override fun createDocumentUploader(): IDocumentUploader? {
        return renewalFactory.createDocumentUploader()
    }

    override fun cancelUpload(attachmentId: String): Boolean {
        return renewalFactory.cancelUpload(attachmentId)
    }

    override fun clearAllTasks() {
        renewalFactory.clearAllTasks()
    }
}
