package com.twl.hi.basic.helpers

import com.twl.hi.foundation.logic.IDocumentUploader
import com.twl.hi.foundation.logic.IUploaderFactory
import com.twl.hi.foundation.logic.IVideoUploader

/**
 * 上传器工厂实现
 * 解决依赖倒置问题：在业务模块中实现工厂，为底层服务提供上传器实例
 */
class UploaderFactoryImpl : IUploaderFactory {
    
    override fun createVideoUploader(): IVideoUploader? {
        return VideoUploaderImplV2()
    }
    
    override fun createDocumentUploader(): IDocumentUploader? {
        return DocumentUploaderImpl()
    }
}
