package com.twl.hi.basic.viewmodel;

import android.app.Application;
import androidx.databinding.ObservableInt;
import androidx.lifecycle.MutableLiveData;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.download.OkRenewalDownload;
import com.twl.hi.basic.download.RenewalDownloadTask;
import com.twl.hi.basic.util.ImageDownLoadUtils;
import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.http.OkHttpClientFactory;

import java.io.File;

public abstract class BaseDownloadViewModel extends FoundationViewModel {

    public static final int STATUS_NONE = 0;
    public static final int STATUS_DOWNLOADING = 1;
    public static final int STATUS_DOWNLOAD = 2;
    public static final int STATUS_CONTINUE_DOWNLOAD = 3;
    private static final String TAG = "FileDownLoadViewModel";
    private boolean fileStatusChanged;
    private ObservableInt mProgress = new ObservableInt(-1);
    private ObservableInt mDownloadStatus = new ObservableInt(STATUS_NONE);
    private MutableLiveData<Boolean> mStartDownload = new MutableLiveData<>();

    public BaseDownloadViewModel(Application application) {
        super(application);
    }

    public abstract String getLocalPath();

    public abstract String getUrl();

    protected abstract void downLoadDone(String localPath);

    public boolean getFileStatusChanged() {
        return fileStatusChanged;
    }

    public void setFileStatusChanged(boolean fileStatusChanged) {
        this.fileStatusChanged = fileStatusChanged;
    }

    public abstract void downloadFile();

    public ObservableInt getDownloadStatus() {
        return mDownloadStatus;
    }

    public void setDownloadStatus(Integer downloadStatus) {
        this.mDownloadStatus.set(downloadStatus);
    }

    public ObservableInt getProgress() {
        return mProgress;
    }

    public void setProgress(int progress) {
        this.mProgress.set(progress);
        if (progress == 100) {
            setDownloadStatus(STATUS_DOWNLOAD);

            String realPath = getDownloadFilePath();
            downLoadDone(realPath);
            setFileStatusChanged(true);
        } else if (progress < 0) {
            setDownloadStatus(STATUS_NONE);
        } else {
            setDownloadStatus(STATUS_DOWNLOADING);
        }
    }

    /**
     * 下载保存视频/文件的地址可以自己设置，所以需要获取多个地址
     * 判断真正保存文件的地址
     * 获取真正下载后存放的地址
     * @return
     */
    public String getDownloadFilePath() {
        String url = getUrl();
        String downloadListenerPath = OkHttpClientFactory.get().getDownloadPath(url);
        if (new File(downloadListenerPath).exists()) {
            return downloadListenerPath;
        }
        String internalFilePath = ServiceManager.getInstance().getFileService().getLocalPath(url);
        File fileInternal = new File(internalFilePath);
        if (fileInternal.exists()) {
            return internalFilePath;
        }
        String externalFilePath = ImageDownLoadUtils.getImageDirPath() + "/" + ServiceManager.getInstance().getFileService().getLocalNameFromUrl(url);
        File fileExternal = new File(externalFilePath);
        if (fileExternal.exists()) {
            return externalFilePath;
        }
        // 下载完成的大文件保存的路径
        RenewalDownloadTask renewalDownloadTask = OkRenewalDownload.getInstance().getTask(url);
        if (renewalDownloadTask != null) {
            String downloadedFilePath = renewalDownloadTask.getDownloadedFilePath();
            if (new File(downloadedFilePath).exists()) {
                return downloadedFilePath;
            }
        }

        TLog.error(TAG, "getDownloadFilePath for %s is empty!!!", url);
        return "";
    }

    public MutableLiveData<Boolean> getStartDownload() {
        return mStartDownload;
    }

    public void setStartDownload(boolean startDownload) {
        this.mStartDownload.setValue(startDownload);
    }
}
