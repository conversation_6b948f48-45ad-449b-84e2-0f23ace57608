package com.twl.hi.basic.views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import lib.twl.common.util.QMUIDisplayHelper;

public class DownloadProgressbar extends View {

    private Paint mPaint;
    private RectF mRectF;
    private RectF mRectFClear;
    private PorterDuffXfermode xfermodeSRC = new PorterDuffXfermode(PorterDuff.Mode.SRC);
    private PorterDuffXfermode xfermodeClear = new PorterDuffXfermode(PorterDuff.Mode.CLEAR);
    private int mProgress = -1;
    private int mWidth;
    private int mHeight;
    private int mCorner;

    public DownloadProgressbar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setColor(0x4c212121);
        mRectF = new RectF();
        mRectFClear = new RectF();
        mCorner = QMUIDisplayHelper.dp2px(context, 3);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mProgress < 0) {
            return;
        }
        if (mWidth <= 0 || mHeight <= 0) {
            mWidth = getWidth();
            mHeight = getHeight();
            mRectF.set(0, 0, mWidth, mHeight);
            float coefficient = (float) (Math.sqrt(2) / 2);
            mRectFClear.set(-coefficient * mWidth, -coefficient * mHeight, (1 + coefficient) * mWidth, (1 + coefficient) * mHeight);
        }
        mPaint.setXfermode(xfermodeSRC);
        canvas.drawRoundRect(mRectF, mCorner, mCorner, mPaint);
        mPaint.setXfermode(xfermodeClear);
        canvas.drawArc(mRectFClear, -90f, (mProgress * 1.0f / 100) * 360, true, mPaint);
    }


    public void setProgress(int progress) {
        if (progress >= 0 && progress <= 100) {
            mProgress = progress;
            postInvalidate();
        }
    }

}
