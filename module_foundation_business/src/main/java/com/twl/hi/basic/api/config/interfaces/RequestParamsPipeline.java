package com.twl.hi.basic.api.config.interfaces;

import com.twl.http.config.HttpParams;

/**
 * Author: <PERSON><PERSON><PERSON>
 * Date: 2017/6/21.
 */
public interface RequestParamsPipeline {

    /**
     * 获取网络请求公共参数，根据项目参数的不同进行配置
     *
     * @param url 请求的基础url
     * @return
     */
    HttpParams getCommonParams(String url, HttpParams params);

    /**
     * 批调用的接口名字
     *
     * @return
     */
    String getBatchMethodName(String url);


    /**
     * 转换请求的url
     * @return
     */
    String getRequestUrl(String url);


}
