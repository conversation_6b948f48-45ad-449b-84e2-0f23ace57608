package com.twl.hi.basic.views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;

import com.twl.hi.basic.R;

import lib.twl.common.util.CalendarUtil;
import lib.twl.common.views.calendar.Calendar;
import lib.twl.common.views.calendar.MonthView;

public class ChatRecordDateMonthView extends MonthView {
    private int mRadius;
    private int mPadding;
    private float mPointRadius;
    private Paint mPointPaint = new Paint();

    public ChatRecordDateMonthView(Context context) {
        super(context);
        mPadding = CalendarUtil.dipToPx(getContext(), 3);
        mPointRadius = CalendarUtil.dipToPx(context, 2);
        int textSize = CalendarUtil.dipToPx(context, 14);
        mSelectTextPaint.setTextSize(textSize);
        mCurMonthTextPaint.setTextSize(textSize);
        mOtherMonthTextPaint.setTextSize(textSize);
        mSchemeTextPaint.setTextSize(textSize);

        mSelectTextPaint.setFakeBoldText(false);
        mCurMonthTextPaint.setFakeBoldText(false);
        mOtherMonthTextPaint.setFakeBoldText(false);
        mSchemeTextPaint.setFakeBoldText(false);

    }

    @Override
    protected void onPreviewHook() {
        mRadius = (int) (Math.min(mItemWidth, mItemHeight) * 0.5 + 0.5) - CalendarUtil.dipToPx(getContext(), 6);
    }

    @Override
    protected boolean onDrawSelected(Canvas canvas, Calendar calendar, int x, int y, boolean hasScheme) {
        int cx = x + mItemWidth / 2;
        int cy = y + mItemHeight / 2;
        canvas.drawCircle(cx, cy, mRadius, mSelectedPaint);
        return true;
    }

    @Override
    protected void onDrawScheme(Canvas canvas, Calendar calendar, int x, int y) {
//        mPointPaint.setColor(calendar.getSchemeColor());
//        canvas.drawCircle(x + mItemWidth / 2, y + mItemHeight - 3 * mPadding, mPointRadius, mPointPaint);
    }

    @Override
    protected void onDrawText(Canvas canvas, Calendar calendar, int x, int y, boolean hasScheme, boolean isSelected) {
        if (isOutRangeForCurr(calendar)) {
            return;
        }
        int cx = x + mItemWidth / 2;
        boolean isCurrentDay = calendar.isCurrentDay();
        if (isSelected) {
            canvas.drawText(isCurrentDay ? getContext().getResources().getString(R.string.word_today) : String.valueOf(calendar.getDay()), cx, mTextBaseLine + y,
                    mSelectTextPaint);
        } else if (hasScheme) {
            canvas.drawText(isCurrentDay ? getContext().getResources().getString(R.string.word_today) : String.valueOf(calendar.getDay()), cx, mTextBaseLine + y,
                    calendar.isCurrentMonth() ? mSchemeTextPaint : mOtherMonthTextPaint);
        } else {
            canvas.drawText(isCurrentDay ? getContext().getResources().getString(R.string.word_today) : String.valueOf(calendar.getDay()), cx, mTextBaseLine + y,
                    calendar.isCurrentMonth() ? mCurMonthTextPaint : mOtherMonthTextPaint);
        }
    }

}
