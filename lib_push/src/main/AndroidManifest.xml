<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="lib.twl.push">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.meizu.flyme.push.permission.RECEIVE" />
    <!-- vivo桌面角标权限 -->
    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />

    <permission
        android:name="${applicationId}.push.permission.MESSAGE"
        android:protectionLevel="signature" />

    <uses-permission android:name="${applicationId}.push.permission.MESSAGE" />
    <uses-permission android:name="com.meizu.c2dm.permission.RECEIVE" />

    <permission
        android:name="${applicationId}.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />

    <uses-permission android:name="${applicationId}.permission.C2D_MESSAGE" />

    <!-- Android O版本调用安装需要使用该权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <!-- 接收PUSH TOKEN的广播以及PUSH消息需要定义该权限 com.twl.hi 要替换上您应用的包名 -->
    <permission
        android:name="${applicationId}.permission.PROCESS_PUSH_MSG"
        android:protectionLevel="signatureOrSystem"
        xmlns:tools="http://schemas.android.com/tools"
        tools:replace="android:protectionLevel"/>

    <!-- 接收PUSH TOKEN的广播以及PUSH消息需要定义该权限 com.twl.hi 要替换上您应用的包名 -->
    <uses-permission android:name="${applicationId}.permission.PROCESS_PUSH_MSG" />

    <!-- 小米Push -->
    <permission
        android:name="${applicationId}.permission.MIPUSH_RECEIVE"
        android:protectionLevel="signature" />

    <uses-permission android:name="${applicationId}.permission.MIPUSH_RECEIVE" />

    <application>
        <!-- 小米Push服务 -->
        <service
            android:name="com.xiaomi.push.service.XMPushService"
            android:enabled="true"
            android:process=":pushservice" />

        <!--注：此service必须在3.0.1版本以后（包括3.0.1版本）加入-->
        <service
            android:name="com.xiaomi.push.service.XMJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":pushservice" />

        <!--注：com.xiaomi.xmsf.permission.MIPUSH_RECEIVE这里的包名不能改为app的包名-->
        <service
            android:name="com.xiaomi.mipush.sdk.PushMessageHandler"
            android:enabled="true"
            android:exported="true"
            android:permission="com.xiaomi.xmsf.permission.MIPUSH_RECEIVE" />

        <!--注：此service必须在2.2.5版本以后（包括2.2.5版本）加入-->
        <service
            android:name="com.xiaomi.mipush.sdk.MessageHandleService"
            android:enabled="true" />

        <receiver
            android:name="com.xiaomi.push.service.receivers.NetworkStatusReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.xiaomi.push.service.receivers.PingReceiver"
            android:exported="false"
            android:process=":pushservice">
            <intent-filter>
                <action android:name="com.xiaomi.push.PING_TIMER" />
            </intent-filter>
        </receiver>
        
        <receiver
            android:name="com.twl.hi.push.MiPushReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.ERROR" />
            </intent-filter>
        </receiver>

        <!--
             接入HMSSDK 需要注册的provider，authorities 一定不能与其他应用一样，所以这边 com.twl.hi 要替换上您应用的包名
            Access HMSSDK need to register provider,authorities must not be the same as other applications, so this side ${package_name} to replace the package name you applied
        -->
<!--        <provider-->
<!--            android:name="com.huawei.hms.update.provider.UpdateProvider"-->
<!--            android:authorities="${applicationId}.hms.update.provider"-->
<!--            android:exported="false"-->
<!--            android:grantUriPermissions="true" />-->

        <!--
             接入HMSSDK 需要注册的provider，authorities 一定不能与其他应用一样，所以这边 com.twl.hi 要替换上您应用的包名
            Access HMSSDK need to register provider,authorities must not be the same as other applications, so this side ${package_name} to replace the package name you applied
        -->
<!--        <provider-->
<!--            android:name="com.huawei.updatesdk.fileprovider.UpdateSdkFileProvider"-->
<!--            android:authorities="${applicationId}.updateSdk.fileProvider"-->
<!--            android:exported="false"-->
<!--            android:grantUriPermissions="true"></provider>-->

        <!-- <activity android:name=".OpendeviceActivity"/> -->


        <!-- 使用 HMSAgent 代码接入HMSSDK 需要注册的activity | Use hmsagent code to access HMSSDK activity that requires registration -->
<!--        <activity-->
<!--            android:name="com.huawei.android.hms.agent.common.HMSAgentActivity"-->
<!--            android:configChanges="orientation|locale|screenSize|layoutDirection|fontScale"-->
<!--            android:excludeFromRecents="true"-->
<!--            android:exported="false"-->
<!--            android:hardwareAccelerated="true"-->
<!--            android:theme="@android:style/Theme.Translucent">-->
<!--            <meta-data-->
<!--                android:name="hwc-theme"-->
<!--                android:value="androidhwext:style/Theme.Emui.Translucent" />-->
<!--        </activity>-->

        <!-- 接入HMSSDK 需要注册的activity | Access HMSSDK activity to be registered -->
<!--        <activity-->
<!--            android:name="com.huawei.hms.activity.BridgeActivity"-->
<!--            android:configChanges="orientation|locale|screenSize|layoutDirection|fontScale"-->
<!--            android:excludeFromRecents="true"-->
<!--            android:exported="false"-->
<!--            android:hardwareAccelerated="true"-->
<!--            android:theme="@android:style/Theme.Translucent">-->
<!--            <meta-data-->
<!--                android:name="hwc-theme"-->
<!--                android:value="androidhwext:style/Theme.Emui.Translucent" />-->
<!--        </activity>-->

        <!-- 接入HMSSDK 需要注册的activity | Access HMSSDK activity to be registered -->
<!--        <activity-->
<!--            android:name="com.huawei.updatesdk.service.otaupdate.AppUpdateActivity"-->
<!--            android:configChanges="orientation|screenSize"-->
<!--            android:exported="false"-->
<!--            android:theme="@style/upsdkDlDialog">-->
<!--            <meta-data-->
<!--                android:name="hwc-theme"-->
<!--                android:value="androidhwext:style/Theme.Emui.Translucent.NoTitleBar" />-->
<!--        </activity>-->

        <!-- 接入HMSSDK 需要注册的activity | Access HMSSDK activity to be registered -->
<!--        <activity-->
<!--            android:name="com.huawei.updatesdk.support.pm.PackageInstallerActivity"-->
<!--            android:configChanges="orientation|keyboardHidden|screenSize"-->
<!--            android:exported="false"-->
<!--            android:theme="@style/upsdkDlDialog">-->
<!--            <meta-data-->
<!--                android:name="hwc-theme"-->
<!--                android:value="androidhwext:style/Theme.Emui.Translucent" />-->
<!--        </activity>-->

        <!--
        接入HMSSDK PUSH模块需要注册，第三方相关 :接收Push消息（注册、透传消息、通知栏点击事件）广播，
                此receiver类需要开发者自己创建并继承com.huawei.hms.support.api.push.PushReceiver类，
                参考示例代码中的类：com.huawei.hmsagent.HuaweiPushRevicer
        com.twl.hi 要替换上您应用的包名
        -->
<!--        <receiver-->
<!--            android:name="com.twl.hi.push.HwPushReceiver"-->
<!--            android:permission="${applicationId}.permission.PROCESS_PUSH_MSG">-->
<!--            <intent-filter>-->

<!--                &lt;!&ndash; 必须,用于接收token | Must， for receiving token &ndash;&gt;-->
<!--                <action android:name="com.huawei.android.push.intent.REGISTRATION" />-->
<!--                &lt;!&ndash; 必须, 用于接收透传消息 &ndash;&gt;-->
<!--                <action android:name="com.huawei.android.push.intent.RECEIVE" />-->
<!--                &lt;!&ndash; 必须, 用于接收通知栏消息点击事件 此事件不需要开发者处理，只需注册就可以 &ndash;&gt;-->
<!--                <action android:name="com.huawei.intent.action.PUSH_DELAY_NOTIFY" />-->

<!--                &lt;!&ndash; 用于点击通知栏或通知栏上的按钮后触发onEvent回调 &ndash;&gt;-->
<!--                <action android:name="com.huawei.android.push.intent.CLICK" />-->
<!--                &lt;!&ndash; 查看push通道是否连接, 不查看则不需要 &ndash;&gt;-->
<!--                <action android:name="com.huawei.intent.action.PUSH_STATE" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->

        <!-- 接入HMSSDK PUSH模块需要注册该service，不需要开发者处理 -->
<!--        <service-->
<!--            android:name="com.huawei.hms.support.api.push.service.HmsMsgService"-->
<!--            android:enabled="true"-->
<!--            android:exported="true"-->
<!--            android:process=":pushservice">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.huawei.push.msg.NOTIFY_MSG" />-->
<!--                <action android:name="com.huawei.push.msg.PASSBY_MSG" />-->
<!--            </intent-filter>-->
<!--        </service>-->

<!--        &lt;!&ndash; 接入HMSSDK 需要注册的应用下载服务 | Access HMSSDK need to register app download service &ndash;&gt;-->
<!--        <service-->
<!--            android:name="com.huawei.updatesdk.service.deamon.download.DownloadService"-->
<!--            android:exported="false" />-->

        <activity
            android:name="com.twl.hi.push.NotifyDetailActivity"
            android:exported="true"
            android:theme="@style/CommonNoTitleTranslucentTheme">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data
                    android:host="bosshi.app"
                    android:path="/hw_push_detail"
                    android:scheme="bosshi" />
                <data
                    android:host="bosshi.app"
                    android:path="/vivo_push_detail"
                    android:scheme="bosshi" />
                <data
                    android:host="bosshi.app"
                    android:path="/meizu_push_detail"
                    android:scheme="bosshi" />
                <data
                    android:host="bosshi.app"
                    android:path="/honor_push_detail"
                    android:scheme="bosshi" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="${applicationId}.oppo.push" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service
            android:name="com.heytap.mcssdk.PushService"
            android:exported="true"
            android:permission="com.coloros.mcs.permission.SEND_MCS_MESSAGE">
            <intent-filter>
                <action android:name="com.coloros.mcs.action.RECEIVE_MCS_MESSAGE"/>
            </intent-filter>
        </service>

        <service
            android:name="com.heytap.mcssdk.AppPushService"
            android:exported="true"
            android:permission="com.heytap.mcs.permission.SEND_MCS_MESSAGE">
            <intent-filter>
                <action android:name="com.heytap.mcs.action.RECEIVE_MCS_MESSAGE"/>
            </intent-filter>
        </service>

        <!-- push应用定义消息receiver声明 -->
        <receiver android:name="com.twl.hi.push.VivoPushReceiver"
            android:exported="true">
            <intent-filter>
                <!-- 接收push消息 -->
                <action android:name="com.vivo.pushclient.action.RECEIVE" />
            </intent-filter>
        </receiver>

        <!--vivo-->
        <service
            android:name="com.vivo.push.sdk.service.CommandClientService"
            android:exported="true" />
        <activity
            android:name="com.vivo.push.sdk.LinkProxyClientActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
        </activity>

        <service
            android:name="com.twl.hi.push.HwPushService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.huawei.push.action.MESSAGING_EVENT"/>
            </intent-filter>
        </service>

        <meta-data
            android:name="com.hihonor.push.app_id"
            android:value="104468909" />

        <service
            android:name="com.twl.hi.push.HonorPushService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.hihonor.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service>
    </application>

    <queries>
        <intent>
            <action android:name="com.hihonor.push.action.BIND_PUSH_SERVICE" />
        </intent>
    </queries>

</manifest>
