package com.twl.hi.push;

import android.app.Activity;
import android.app.Application;
import android.content.Context;

import com.techwolf.lib.tlog.TLog;
import com.vivo.push.IPushActionListener;
import com.vivo.push.PushClient;
import com.vivo.push.util.VivoPushException;

/**
 * VIVO push SDK实现类
 */
public class VivoPushSdk implements IPushSdk {

    private static final String TAG = "VivoPushSdk";

    Context application;

    public VivoPushSdk(Application context) {
        this.application = context;
    }

    @Override
    public void start() {

    }

    @Override
    public void stop(String token) {
        if (this.application != null) {
            PushClient.getInstance(this.application).turnOffPush(new IPushActionListener() {
                @Override
                public void onStateChanged(int i) {

                }
            });
        }
    }

    @Override
    public void setForeground(boolean isForeground) {
        if (isForeground) {
            PushClient.getInstance(this.application).turnOffPush(new IPushActionListener() {
                @Override
                public void onStateChanged(int state) {
                }
            });
        } else {
            try {
                PushClient.getInstance(this.application).initialize();
                PushClient.getInstance(this.application).turnOnPush(new IPushActionListener() {
                    @Override
                    public void onStateChanged(int state) {
                        if (state == 0) {
                            PushSdkManager.getInstance().registerToken(PushClient.getInstance(application).getRegId());
                        }
                    }
                });
            } catch (VivoPushException e) {
                TLog.error(TAG, e.getMessage());
            }
        }
    }

    @Override
    public void clearPushSdkNotification() {
    }

    @Override
    public void firstActivityShow(Activity activity) {

    }

    @Override
    public void requestPermission() {

    }
}
