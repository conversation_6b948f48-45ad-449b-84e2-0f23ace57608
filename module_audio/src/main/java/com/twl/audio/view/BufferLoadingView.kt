package com.twl.audio.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import lib.twl.common.ext.dp
import lib.twl.common.ext.sp

/**
 * Author : Xuweixiang .
 * Date   : On 2024/8/23
 * Email  : Contact <EMAIL>
 * Desc   : 圆形缓冲加载View
 *
 */

class BufferLoadingView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {


    private var progress: Int = 0 // Progress percentage (0-100)
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        // 60% transparent white
        color = Color.argb(153, 255, 255, 255)
        style = Paint.Style.STROKE
        strokeWidth = 3f.dp // Background ring thickness
    }
    private val progressPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.WHITE
        style = Paint.Style.STROKE
        strokeWidth = 3f.dp // Progress ring thickness
    }
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.WHITE
        textSize = 12f.sp
        textAlign = Paint.Align.CENTER
    }
    private val rectF = RectF()

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Calculate the radius and center
        val radius = (width.coerceAtMost(height) / 2f) - backgroundPaint.strokeWidth / 2f
        val centerX = width / 2f
        val centerY = height / 2f

        // Draw the background ring
        rectF.set(
            centerX - radius,
            centerY - radius,
            centerX + radius,
            centerY + radius
        )
        canvas.drawOval(rectF, backgroundPaint)

        // Draw the progress arc based on the provided progress
        val sweepAngle = progress * 3.6f // Convert progress percentage to sweep angle
        canvas.drawArc(rectF, 0f, sweepAngle, false, progressPaint)

        // Draw the progress percentage text
        val progressText = "$progress%"
        canvas.drawText(progressText, centerX, centerY + textPaint.textSize / 4, textPaint)
    }

    /**
     * Set the progress and redraw the view.
     * @param progress the progress percentage (0-100)
     */
    fun setProgress(progress: Int) {
        this.progress = progress.coerceIn(0, 100)
        invalidate()
    }

}