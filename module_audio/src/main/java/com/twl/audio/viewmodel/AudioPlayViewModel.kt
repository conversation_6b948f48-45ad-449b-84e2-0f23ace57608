package com.twl.audio.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sankuai.waimai.router.Router
import com.techwolf.lib.tlog.TLog
import com.twl.audio.util.AudioListener
import com.twl.audio.util.AudioPlayer
import com.twl.audio.util.AudioState
import com.twl.audio.util.createAudioPlayer
import com.twl.export.audio.IAudioService
import com.twl.export.audio.router.AudioPageRouter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import lib.twl.common.base.BaseViewModel
import lib.twl.common.util.ToastUtils
import java.util.UUID

/**
 * Author : Xuweixiang .
 * Date   : On 2024/8/20
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

class AudioPlayViewModel(app: Application) : BaseViewModel(app), AudioListener {

    companion object {
        const val TAG = "AudioPlayViewModel"
    }


    private var audioPlayer: AudioPlayer? = null

    private val audioStateLiveData = MutableLiveData<AudioState>()

    private val audioProgressLiveData = MutableLiveData<Pair<Int, Int>>()

    private val audioBufferLiveData = MutableLiveData<Int>()

    private val audioNameLiveData = MutableLiveData<String>()

    fun getAudioStateLiveData(): LiveData<AudioState> {
        return audioStateLiveData
    }

    fun getAudioProgressLiveData(): LiveData<Pair<Int, Int>> {
        return audioProgressLiveData
    }

    fun getAudioBufferLiveData(): LiveData<Int> {
        return audioBufferLiveData
    }

    fun getAudioNameLiveData(): LiveData<String> {
        return audioNameLiveData
    }

    fun init(audioUrl: String, audioName: String?) {
        viewModelScope.launch {
            val length = withContext(Dispatchers.Default) {
                val audioService =
                    Router.getService(IAudioService::class.java, AudioPageRouter.AUDIO_SERVICE)
                audioService.audioFileLength(getApplication(), audioUrl)
            }
            audioPlayer = createAudioPlayer(
                getApplication(),
                UUID.randomUUID().toString(),
                length,
                audioUrl,
                false,
                0,
                false,
                0.8f,
                this@AudioPlayViewModel
            )
        }
        audioNameLiveData.postValue(audioName ?: "")
    }

    fun playAudio() {
        audioPlayer?.play()
    }

    fun pauseAudio() {
        audioPlayer?.pause()
    }

    fun seekTo(position: Int) {
        audioPlayer?.seekTo(position)
    }

    fun stop() {
        audioPlayer?.stop()
    }

    fun isPlaying(): Boolean {
        return audioPlayer?.isPlaying() ?: false
    }

    fun duration(): Int {
        return (audioPlayer?.duration() ?: 0).toInt()
    }

    override fun onStateChanged(audioPlayer: AudioPlayer, state: AudioState) {
        TLog.info(TAG, "onPlayerEvent: $state")
        audioStateLiveData.postValue(state)
        if (state is AudioState.Error) {
            ToastUtils.failure(state.errMsg)
        }
    }

    override fun onProgress(curPosition: Double, duration: Int) {
        audioProgressLiveData.postValue(curPosition.toInt() to duration)
    }

    override fun onCleared() {
        super.onCleared()
        audioPlayer?.destroy()
        audioPlayer = null
    }

    override fun onBufferingUpdate(percent: Int) {
        audioBufferLiveData.postValue(percent)
    }

}