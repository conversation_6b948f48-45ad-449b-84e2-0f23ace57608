package com.twl.utils.kv.strategy

import android.content.SharedPreferences

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/4/18 14:52
 * email : <EMAIL>
 * describe :
 */
class EmptyKV : IKVStrategy {
    override fun putString(k: String, v: String): Boolean {
        return false
    }

    override fun getString(k: String, defaultValue: String?): String {
        return ""
    }

    override fun getStringSet(k: String, defaultValue: Set<String>): Set<String> {
        return emptySet()
    }

    override fun putInt(k: String, v: Int): Boolean {
        return false
    }

    override fun getInt(k: String, defaultValue: Int?): Int {
        return 0
    }

    override fun putFloat(k: String, v: Float): Boolean {
        return false
    }

    override fun getFloat(k: String, defaultValue: Float?): Float {
        return 0f
    }

    override fun putDouble(k: String, v: Double): Boolean {
        return false
    }

    override fun getDouble(k: String, defaultValue: Double?): Double {
        return 0.0
    }

    override fun putLong(k: String, v: Long): Boolean {
        return false
    }

    override fun getLong(k: String, defaultValue: Long?): Long {
        return 0L
    }

    override fun putBoolean(k: String, v: Boolean): Boolean {
        return false
    }

    override fun getBoolean(k: String, defaultValue: Boolean?): Boolean {
        return false
    }

    override fun edit(): SharedPreferences.Editor {
        return EmptyEditor()
    }

    override fun remove(k: String): Boolean {
        return false
    }

    override fun contains(k: String): Boolean {
        return false
    }

    override fun getAllKeys() = emptyArray<String>()

    private class EmptyEditor : SharedPreferences.Editor {
        override fun putString(key: String, value: String?): SharedPreferences.Editor {
            return this
        }

        override fun putStringSet(key: String, values: Set<String>?): SharedPreferences.Editor {
            return this
        }

        override fun putInt(key: String, value: Int): SharedPreferences.Editor {
            return this
        }

        override fun putLong(key: String, value: Long): SharedPreferences.Editor {
            return this
        }

        override fun putFloat(key: String, value: Float): SharedPreferences.Editor {
            return this
        }

        override fun putBoolean(key: String, value: Boolean): SharedPreferences.Editor {
            return this
        }

        override fun remove(key: String): SharedPreferences.Editor {
            return this
        }

        override fun clear(): SharedPreferences.Editor {
            return this
        }

        override fun commit(): Boolean {
            return false
        }

        override fun apply() {}
    }

}