package com.twl.utils.status;

import android.text.TextUtils;

import com.twl.utils.R;
import com.twl.utils.SettingBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/10/21.
 */
public class WorkStatusBean implements Serializable {
    private static final long serialVersionUID = 5743989211253693966L;

    public static final int STATUS_ID_SELF = 1;//自定义状态
    public static final int STATUS_ID_NONE = 2;//无状态

    public static int statuesDefaultId = R.drawable.ic_statuus_default;
    public String statusText; // 工作状态UI显示文案
    public String statusIcon;// 工作状态图标地址
    public int status;// 使用状态 1-使用中 0-删除
    public int id;
    public int expirationType; // 截止时间类型
    public String replyText; // 默认自动回复文案

    public static String getWorkStatusIconById(int workStatusId) {
        if (workStatusId != WorkStatusBean.STATUS_ID_NONE) {
            WorkStatusBean workStatusBean = SettingBuilder.getInstance().getWorkStatus(workStatusId);
            if (workStatusBean != null) {
                return workStatusBean.statusIcon;
            }
        }
        return "";
    }

    public String getStatusText() {
        if (statusText != null) {
            if (!TextUtils.isEmpty(statusText.trim())) {
                return statusText;
            }
        }
        return "";
    }
}
