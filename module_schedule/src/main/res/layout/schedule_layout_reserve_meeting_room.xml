<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="callback"
            type="com.twl.hi.schedule.meetingroom.view.callback.ReserveMeetingRoomCallback" />

        <variable
            name="viewModel"
            type="com.twl.hi.schedule.meetingroom.viewmodel.ReserveMeetingRoomViewModel" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/app_white">

        <lib.twl.common.views.calendar.CalendarLayout
            android:id="@+id/calendarLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:calendar_content_view_id="@+id/schedule_content"
            app:calendar_divider_height="20dp"
            app:calendar_show_mode="both_month_week_view"
            app:default_status="shrink">

            <lib.twl.common.views.calendar.CalendarView
                android:id="@+id/calendarView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:calendar_height="40dp"
                app:calendar_week_rang_padding="12dp"
                app:current_day_text_color="@color/color_575CD7"
                app:current_month_text_color="@color/color_0D0D1A"
                app:month_view="com.twl.hi.schedule.widgets.ScheduleMonthView"
                app:month_view_show_mode="mode_all"
                app:other_month_text_color="@color/color_B1B1B8"
                app:selected_text_color="@color/app_white"
                app:selected_theme_color="@color/color_6873E8"
                app:week_bar_height="22dp"
                app:week_bar_view="com.twl.hi.schedule.widgets.ScheduleWeekBar"
                app:week_range_text_color="@color/color_9B9B9B"
                app:week_range_text_size="20dp"
                app:week_text_color="@color/color_0D0D1A"
                app:week_view="com.twl.hi.schedule.widgets.ScheduleWeekView" />


            <com.twl.hi.schedule.widgets.ScheduleContentWrapper
                android:id="@+id/schedule_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/app_white"
                android:orientation="vertical">

                <ImageButton
                    android:id="@+id/iv_calendar_divider"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top|center_horizontal"
                    android:background="@color/app_white"
                    android:paddingBottom="6dp"
                    android:src="@drawable/schedule_ic_icon_calendar_closed_divider" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_gravity="top"
                    android:background="@drawable/bg_calendar_divider" />

                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/app_black"
                        android:background="@drawable/schedule_bg_bt_selector"
                        android:maxLines="1"
                        android:ellipsize="end"
                        app:visibleGone="@{viewModel.showWorkPlaces}"
                        app:text="@{viewModel.selectedWorkplaceName}"
                        android:onClick="@{() -> callback.onSelectWorkplace()}"
                        android:layout_marginEnd="10dp"
                        tools:text="冠捷大厦"/>

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/app_black"
                        android:background="@drawable/schedule_bg_bt_selector"
                        android:maxLines="1"
                        android:ellipsize="end"
                        app:showIfPresent="@{viewModel.selectedFloor.floorName}"
                        android:onClick="@{() -> callback.onSelectFloor()}"
                        android:layout_marginEnd="10dp"
                        tools:text="全部楼层"/>

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/app_black"
                        android:background="@drawable/schedule_bg_bt_selector"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:text="@{viewModel.selectedCapacity}"
                        android:onClick="@{() -> callback.onSelectRoomCapacity()}"
                        app:visibleGone="@{viewModel.selectedFloor != null}"
                        tools:text="1-9人"/>

                </LinearLayout>

                <include
                    android:id="@+id/ic_empty"
                    layout="@layout/layout_empty_common" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/room_list"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:clipChildren="false"
                    android:itemAnimator="@{null}"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:listitem="@layout/schedule_item_floor_room" />


            </com.twl.hi.schedule.widgets.ScheduleContentWrapper>
        </lib.twl.common.views.calendar.CalendarLayout>

    </LinearLayout>

</layout>