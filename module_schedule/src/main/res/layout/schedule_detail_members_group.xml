<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <include
        android:id="@+id/avatar"
        layout="@layout/item_avatar2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintEnd_toStartOf="@id/group_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/group_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:drawableEnd="@drawable/ic_icon_gray_arrow_down"
        android:singleLine="true"
        android:textColor="@color/color_0D0D1A"
        android:textSize="17sp"
        app:layout_constraintBottom_toBottomOf="@id/avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/avatar"
        app:layout_constraintTop_toTopOf="@id/avatar"
        tools:text="BH测试走查小分队" />
</androidx.constraintlayout.widget.ConstraintLayout>
