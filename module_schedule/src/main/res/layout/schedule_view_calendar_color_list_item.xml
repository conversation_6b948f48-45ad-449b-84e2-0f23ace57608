<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.schedule.preference.view.rvadapter.ScheduleCalendarColorItemModel" />

        <variable
            name="callback"
            type="com.twl.hi.schedule.preference.view.rvadapter.CalendarColorListSelectCallback" />
    </data>

    <LinearLayout
        android:id="@+id/item_view_container"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_margin="20dp"
        android:gravity="center"
        android:onClick="@{v->callback.onCalendarColorItemSelect(viewModel)}"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/item_view_iv_selected"
            android:layout_width="15dp"
            android:layout_height="10dp"
            android:src="@mipmap/schedule_ic_item_selected" />
    </LinearLayout>
</layout>