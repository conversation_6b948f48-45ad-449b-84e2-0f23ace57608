<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="file"
            type="com.twl.hi.foundation.model.ScheduleFile" />

        <variable
            name="callback"
            type="com.twl.hi.schedule.editor.view.callback.ScheduleFileCallback" />

        <variable
            name="withDel"
            type="Boolean" />

        <variable
            name="canJustInvite"
            type="androidx.databinding.ObservableBoolean" />
    </data>

    <LinearLayout
        android:id="@+id/ll_file"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:onClick="@{()->callback.toFilePreview(file)}"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="45dp"
            android:layout_marginEnd="10dp"
            android:src="@drawable/hd_icon_attachments" />

        <TextView
            android:id="@+id/tv_file_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="middle"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:singleLine="true"
            android:text="@{file.name}"
            android:textColor="@color/color_B1B1B8"
            android:textSize="15sp" />

        <ImageView
            android:id="@+id/iv_file_del"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:onClick="@{()->callback.delFile(file)}"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:src="@drawable/ic_icon_new_calendar_del"
            app:visibleGone="@{withDel &amp;&amp; !canJustInvite}" />
    </LinearLayout>
</layout>