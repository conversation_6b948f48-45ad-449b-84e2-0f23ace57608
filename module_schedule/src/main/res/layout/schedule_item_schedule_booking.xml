<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="com.twl.hi.schedule.meetingroom.model.ScheduleBookingBean" />

        <variable
            name="callback"
            type="com.twl.hi.schedule.meetingroom.view.callback.ScheduleBookingCallback" />

        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:background="@color/app_white">

        <TextView
            android:id="@+id/tv_room_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:ellipsize="end"
            android:gravity="left"
            android:paddingRight="5dp"
            android:layout_marginLeft="20dp"
            android:singleLine="true"
            android:text="@{bean.getMeetingDesc()}"
            android:textColor="@color/color_212121"
            android:textSize="17sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/iv_edit"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="01-Apache(阿帕奇)" />

        <ImageView
            android:id="@+id/iv_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="22dp"
            android:paddingRight="20dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:visibility="@{bean.canCancel == 1 ? View.VISIBLE : View.GONE}"
            android:onClick="@{()->callback.onEditBookingClick(bean)}"
            android:src="@drawable/ic_edit_gray" />


        <TextView
            android:id="@+id/tv_subject"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:paddingRight="5dp"
            android:singleLine="true"
            android:layout_marginLeft="20dp"
            android:text="@{@string/schedule_booking_subject(bean.subject)}"
            android:textColor="@color/color_9B9B9B"
            android:textSize="14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/barrier"
            app:layout_constraintTop_toBottomOf="@+id/tv_room_name"
            tools:text="@string/schedule_booking_subject" />

        <TextView
            android:id="@+id/tv_begin_time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingRight="5dp"
            android:singleLine="true"
            android:layout_marginLeft="20dp"
            android:text="@{@string/schedule_booking_begin_time(bean.bookingDate,bean.beginTime)}"
            android:textColor="@color/color_9B9B9B"
            android:textSize="14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/barrier"
            app:layout_constraintTop_toBottomOf="@+id/tv_subject"
            tools:text="@string/schedule_booking_begin_time" />

        <TextView
            android:id="@+id/tv_end_time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingRight="5dp"
            android:singleLine="true"
            android:layout_marginLeft="20dp"
            android:text="@{@string/schedule_booking_end_time(bean.bookingDate,bean.endTime)}"
            android:textColor="@color/color_9B9B9B"
            android:textSize="14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/barrier"
            app:layout_constraintTop_toBottomOf="@+id/tv_begin_time"
            tools:text="@string/schedule_booking_end_time" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="left"
            app:constraint_referenced_ids="tv_cancel_booking" />

        <TextView
            android:id="@+id/tv_cancel_booking"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="8dp"
            android:background="@drawable/bg_corner_3_color_e7ecfd"
            android:onClick="@{()->callback.onCancelBookingClick(bean)}"
            android:paddingLeft="9dp"
            android:paddingTop="4dp"
            android:paddingRight="9dp"
            android:layout_marginLeft="20dp"
            android:paddingBottom="4dp"
            android:text="@string/schedule_booking_cancel"
            android:textColor="@color/color_5358DD"
            android:textSize="12sp"
            app:layout_goneMarginRight="12dp"
            android:visibility="@{bean.canCancel == 1 ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@+id/tv_end_time"
            app:layout_constraintRight_toLeftOf="@+id/tv_edit_sign_out" />

        <TextView
            android:id="@+id/tv_edit_sign_out"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp"
            android:background="@drawable/bg_corner_3_color_5358dd"
            android:onClick="@{()->callback.onSignOutClick(bean)}"
            android:paddingLeft="9dp"
            android:paddingTop="4dp"
            android:paddingRight="9dp"
            android:paddingBottom="4dp"
            android:text="@string/schedule_sing_out"
            android:textColor="@color/app_white"
            android:textSize="12sp"
            app:visibleGone="@{bean.signStatus == 1}"
            app:layout_constraintBottom_toBottomOf="@+id/tv_end_time"
            app:layout_constraintRight_toRightOf="parent" />

        <View
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:background="@color/color_F6F6F7"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintLeft_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>