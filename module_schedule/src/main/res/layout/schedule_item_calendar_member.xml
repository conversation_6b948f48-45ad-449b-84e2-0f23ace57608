<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.twl.hi.foundation.utils.ContactUtils" />

        <import type="com.twl.hi.schedule.calendar.model.CalendarPrivilege" />

        <variable
            name="member"
            type="com.twl.hi.schedule.calendar.model.bean.CalendarMemberWrapper" />

        <variable
            name="callback"
            type="com.twl.hi.schedule.calendar.fragments.CalendarOperateBaseFragment.CalendarMemberListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="74dp"
        android:background="@color/app_white"
        android:clickable="@{member.shouldShowArrow()}"
        android:onClick="@{() -> callback.onClickItem(member)}">

        <include
            android:id="@+id/vg_avatar"
            layout="@layout/item_avatar2"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:layout_centerVertical="true"
            android:layout_marginEnd="10dp"
            android:onClick="@{() -> callback.onClickAvatar(member)}"
            app:avatarCorner="@{0}"
            app:avatarName="@{ContactUtils.getDisplayAvatarStr(member.name())}"
            app:avatarUrl="@{member.avatar()}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@{member.name()}"
            android:textColor="@color/app_black"
            android:textSize="15sp"
            android:singleLine="true"
            app:layout_constraintWidth_default="wrap"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/vg_avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_group_num"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintHorizontal_bias="0"
            android:layout_marginStart="10dp"
            tools:text="史晨璇" />


        <TextView
            android:id="@+id/tv_group_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:text="@{member.groupMemberNumStr}"
            android:textColor="@color/app_black"
            android:textSize="15sp"
            app:visibleGone="@{member.isGroup()}"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_name"
            app:layout_constraintEnd_toStartOf="@id/ll_setting"
            android:layout_marginEnd="20dp"
            tools:text="(100)" />

        <LinearLayout
            android:id="@+id/ll_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_setting"
            android:layout_marginEnd="10dp"
            android:gravity="end">

            <TextView
                android:id="@+id/tv_setting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{member.roleStr()}"
                android:textColor="#80151A28"
                tools:text="可管理" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#B1B1B8"
                android:text="@{member.roleDesc()}"
                app:visibleGone="@{member.shouldShowRoleDesc()}"
                android:layout_marginTop="3dp"/>

        </LinearLayout>



        <ImageView
            android:id="@+id/iv_setting"
            android:layout_width="4dp"
            android:layout_height="8dp"
            android:layout_marginStart="10dp"
            android:src="@drawable/ic_icon_gray_arrow_right"
            app:visibleGone="@{member.shouldShowArrow()}"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>