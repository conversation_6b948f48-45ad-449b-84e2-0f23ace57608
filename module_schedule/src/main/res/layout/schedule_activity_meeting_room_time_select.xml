<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".schedule.MeetingRoomTimeSelectActivity">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.schedule.meetingroom.viewmodel.MeetingRoomTimeSelectViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.schedule.meetingroom.view.callback.MeetingRoomTimeSelectCallback" />
    </data>

    <LinearLayout
        android:id="@+id/activityMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/schedule_title_bar_meeting_room_select"
            app:callback="@{callback}" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:drawableEnd="@drawable/schedule_ic_icon_meeting_room_down"
                android:drawablePadding="4dp"
                android:onClick="@{()->callback.onDateClick()}"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="@{viewModel.selectedTime}"
                android:textColor="@color/color_212121"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="10月23日，星期五" />

            <TextView
                android:id="@+id/tv_person"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:drawableStart="@drawable/schedule_ic_icon_room_select_member"
                android:drawablePadding="4dp"
                android:text="@{@string/num_people(viewModel.personNumbers)}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="@id/tv_date"
                app:layout_constraintEnd_toStartOf="@id/tv_equipment"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/tv_date"
                app:layout_constraintTop_toTopOf="@id/tv_date"
                tools:text="12人" />

            <TextView
                android:id="@+id/tv_equipment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:drawableStart="@drawable/schedule_ic_icon_equipment"
                android:drawablePadding="4dp"
                android:text="@{viewModel.equipment}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="12sp"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@id/tv_person"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_person"
                app:layout_constraintTop_toTopOf="@id/tv_person"
                tools:text="视频会议系统、投影仪、写字板、音响系统、LED 屏、电视、台球桌" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_height"
            android:background="@color/color_EAEAEA" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.twl.hi.schedule.meetingroom.view.widgets.MeetingTimeSelect
                android:id="@+id/ts_duration"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </ScrollView>
    </LinearLayout>
</layout>
