<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="callback"
            type="com.twl.hi.basic.callback.TitleBarCallback" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_height">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:onClick="@{(view)->callback.clickLeft(view)}"
                android:paddingLeft="16dp"
                android:paddingRight="5dp"
                android:src="@drawable/ic_icon_black_back" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/color_212121"
                android:textSize="17sp"
                android:textStyle="bold"
                tools:text="标题" />
        </RelativeLayout>

        <FrameLayout
            android:id="@+id/fragmentMain"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView_schedule"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingTop="20dp"
                android:paddingBottom="20dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_schedule_empty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableTop="@drawable/schedule_ic_icon_empty"
                android:drawablePadding="13dp"
                android:gravity="center_horizontal"
                android:text="@string/schedule_no_arrangements"
                android:textColor="@color/color_B7B7B7"
                android:textSize="14sp"
                android:visibility="visible" />

        </FrameLayout>

    </LinearLayout>
</layout>