<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="ScheduleActionButton" parent="Widget.AppCompat.TextView">
        <item name="android:textColor">@color/color_0D0D1A</item>
        <item name="android:textSize">15sp</item>
        <item name="android:background">@drawable/schedule_bg_action_button_outline</item>
    </style>

    <style name="ScheduleEditorPrimaryContent" parent="Widget.AppCompat.TextView">
        <item name="android:padding">20dp</item>
        <item name="android:background">@color/app_white</item>
        <item name="android:drawablePadding">8dp</item>
        <item name="android:textColor">@color/color_text_selector</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="ScheduleDetailTabTextStyle">
        <item name="android:textColor">@color/schedule_detail_tab_text_color</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">15dp</item>
    </style>

    <style name="ScheduleSubscribeTabStyle" parent="Widget.AppCompat.TextView">
        <item name="android:button">@null</item>
        <item name="android:paddingTop">6dp</item>
        <item name="android:paddingBottom">10dp</item>
        <item name="android:textSize">14sp</item>
    </style>

</resources>