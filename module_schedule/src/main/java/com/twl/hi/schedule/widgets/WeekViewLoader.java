package com.twl.hi.schedule.widgets;

import com.twl.hi.foundation.model.ScheduleBean;

import java.util.Calendar;
import java.util.List;

public interface WeekViewLoader {
    /**
     * Convert a date into a double that will be used to reference when you're loading data.
     * <p>
     * All periods that have the same integer part, define one period. Dates that are later in time
     * should have a greater return value.
     *
     * @param instance the date
     * @return The period index in which the date falls (floating point number).
     */
    double toWeekViewPeriodIndex(Calendar instance);

    /**
     * Load the events within the period
     *
     * @param periodIndex the period to load
     * @return A list with the events of this period
     */
    List<ScheduleBean> onLoad(int periodIndex);

    /**
     * 上一月份的变化
     *
     * @param periodIndex
     * @return
     */
    List<ScheduleBean> onPreLoad(int periodIndex);

    /**
     * 下一月份的变化
     *
     * @param periodIndex
     * @return
     */
    List<ScheduleBean> onNextLoad(int periodIndex);
}
