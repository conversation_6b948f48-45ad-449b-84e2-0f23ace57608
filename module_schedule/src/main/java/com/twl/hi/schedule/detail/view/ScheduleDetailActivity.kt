package com.twl.hi.schedule.detail.view

import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.net.Uri
import android.os.Bundle
import android.view.TouchDelegate
import android.view.View
import androidx.annotation.StringRes
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.amap.api.services.core.LatLonPoint
import com.sankuai.waimai.router.Router
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.BottomListDialog
import com.twl.hi.basic.SCHEDULE_CARD_PARTICIPANTS
import com.twl.hi.basic.activity.FoundationVMActivity
import com.twl.hi.basic.dialog.DialogUtils
import com.twl.hi.basic.dialog.bottom.BottomSelectItemBean
import com.twl.hi.basic.map.util.MapAppInfoEntity
import com.twl.hi.basic.map.util.MapUtil
import com.twl.hi.basic.model.FrowardScheduleBean
import com.twl.hi.basic.model.SelectBottomBean
import com.twl.hi.basic.model.WebVideoPlayBean
import com.twl.hi.basic.util.FilePreviewUtil
import com.twl.hi.basic.util.PointParamsUtils
import com.twl.hi.basic.util.ThemeUtils
import com.twl.hi.export.chat.router.ChatPageRouter
import com.twl.hi.export.organization.router.OrganizationPageRouter
import com.twl.hi.export.select.bean.SelectBaseParams
import com.twl.hi.export.select.bean.SelectConversationParams
import com.twl.hi.export.select.router.SelectPageRouter
import com.twl.hi.export.video.meeting.router.VideoAndAudioPageRouter
import com.twl.hi.export.video.meeting.service.VideoMeetingEntranceHelper
import com.twl.hi.export.webview.WebViewPageRouter
import com.twl.hi.foundation.SendMessageContent
import com.twl.hi.foundation.api.response.MeetingCheckResponse
import com.twl.hi.foundation.base.IListRefresh
import com.twl.hi.foundation.model.Contact
import com.twl.hi.foundation.model.ScheduleFile
import com.twl.hi.foundation.model.message.MessageForFile
import com.twl.hi.foundation.model.message.MessageForVideo.VideoInfo
import com.twl.hi.foundation.utils.Analytics
import com.twl.hi.schedule.BR
import com.twl.hi.schedule.R
import com.twl.hi.schedule.common.ScheduleCreateRemindDialog
import com.twl.hi.schedule.databinding.ScheduleActivityScheduleDetailBinding
import com.twl.hi.schedule.detail.model.ScheduleCalendar
import com.twl.hi.schedule.detail.model.ScheduleDetails
import com.twl.hi.schedule.detail.model.ScheduleMember
import com.twl.hi.schedule.detail.view.bindingadapter.ScheduleViewBindingAdapter
import com.twl.hi.schedule.detail.view.callback.ScheduleDetailActivityCallback
import com.twl.hi.schedule.detail.view.rvadapter.AttachmentItemAdapter
import com.twl.hi.schedule.detail.view.rvadapter.ParticipantItemAdapter
import com.twl.hi.schedule.detail.viewmodel.ScheduleDetailViewModel
import com.twl.hi.schedule.editor.view.ScheduleCreateEditActivity
import com.twl.hi.schedule.editor.viewmodel.ScheduleConstant
import com.twl.hi.viewer.DraggableImageViewerHelper
import hi.kernel.BundleConstants
import hi.kernel.Constants
import hi.kernel.HiKernel
import kotlinx.coroutines.launch
import lib.twl.common.dialog.BaseDialog
import lib.twl.common.ext.getStatusBarsHeight
import lib.twl.common.ext.switchMap
import lib.twl.common.model.ImageInfo
import lib.twl.common.util.ActivityAnimType
import lib.twl.common.util.AppUtil
import lib.twl.common.util.CommonUtils
import lib.twl.common.util.ExecutorFactory
import lib.twl.common.util.QMUIKeyboardHelper
import lib.twl.common.util.QMUIStatusBarHelper
import lib.twl.common.util.TimeDifferenceUtil
import lib.twl.common.util.TimeTag
import lib.twl.common.util.ToastUtils
import lib.twl.common.views.imagesview.Image
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


/**
 * <AUTHOR>
 * @date 2022/08/24 15:38
 *     description: 日程详情页
 */
class ScheduleDetailActivity :
    FoundationVMActivity<ScheduleActivityScheduleDetailBinding, ScheduleDetailViewModel>(),
    ScheduleDetailActivityCallback, IListRefresh {

    private var createGroupConfirmationDialog: DialogUtils? = null

    companion object {
        const val TAG = "ScheduleDetailActivity"

        const val HOUR_IN_SECONDS = 3600

        @JvmOverloads
        @JvmStatic
        fun intentStart(
            context: Context,
            scheduleId: String,
            calendarId: String?,
            date: String?,
            from: Int,
            messageId: String,
            isNeedRequest: Boolean = false
        ) {
            val intent = Intent(context, ScheduleDetailActivity::class.java)
            intent.putExtra(Constants.SCHEDULE_ID, scheduleId)
            intent.putExtra(Constants.CALENDAR, calendarId)
            intent.putExtra(Constants.DATE, date)
            intent.putExtra(Constants.MSG_ID, messageId)
            intent.putExtra(Constants.FROM, from)
            intent.putExtra(Constants.IS_NEED_REQUEST, isNeedRequest)
            AppUtil.startActivity(context, intent)
        }
    }

    private val joinMeetingDialog by lazy {
        val details = viewModel.details.value
        val title: String
        val copyContent: String
        @StringRes
        val positiveStringRes: Int
        if (details?.videoMeetingType == ScheduleConstant.MEETING_TYPE_BOSS_HI) {
            title = getString(R.string.schedule_format_meeting_id, buildString {
                viewModel.videoMeetingId?.forEachIndexed { index, c ->
                    if (index > 0 && index % 3 == 0) {
                        append(' ')
                    }
                    append(c)
                }
            })
            copyContent = details.subject + "\n会议号：" + (viewModel.videoMeetingId ?: "")
            positiveStringRes = R.string.copy
        } else {
            title = getString(R.string.schedule_format_meeting_url, details?.videoMeetingUrl ?: "")
            copyContent = details?.videoMeetingUrl ?: ""
            positiveStringRes = R.string.share_copy
        }
        DialogUtils.Builder(this)
            .setTitle(title)
            .setAutoCloseAfterClick(true)
            .setPositive(positiveStringRes)
            .setPositiveListener { copyVideoMeetingUrl(copyContent) }
            .setNegative(R.string.cancel)
            .build()
    }

    override fun getContentLayoutId() = R.layout.schedule_activity_schedule_detail

    override fun getCallbackVariable() = BR.callback

    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel

    override fun onBackClick() {
        finish()
    }

    override fun getViewModelFactory(): ViewModelProvider.Factory {
        val scheduleId: String = intent.getStringExtra(Constants.SCHEDULE_ID) ?: ""
        val calendarId: String = intent.getStringExtra(Constants.CALENDAR) ?: ""
        val currentDate: String = intent.getStringExtra(Constants.DATE) ?: ""
        val associateMessage: String = intent.getStringExtra(Constants.MSG_ID) ?: ""
        var parsedDate: Date? = null
        try {
            parsedDate = mCurrentDateFormat.parse(currentDate)
        } catch (e: Exception) {
            TLog.error(TAG, e.message)
        }
        return object : ViewModelProvider.Factory {
            override fun <T : ViewModel> create(modelClass: Class<T>): T {
                return ScheduleDetailViewModel(
                    application,
                    scheduleId,
                    calendarId,
                    associateMessage,
                    parsedDate
                ) as T
            }
        }
    }

    override fun onScheduleShareClick() {
        val params = SelectConversationParams()
            .setSendMessageType(SendMessageContent.TYPE_FORWARD_SCHEDULE)
            .setTitle<SelectConversationParams>(resources.getString(R.string.select_contact))
            .setOrgVisible<SelectConversationParams>(
                SelectBaseParams.VISIBLE)
            .setSearchVisible<SelectConversationParams>(
                SelectBaseParams.VISIBLE)
            .setOtherVisible<SelectConversationParams>(
                SelectBaseParams.VISIBLE)
        params.sendMessageContent.content =
            FrowardScheduleBean(viewModel.scheduleId, viewModel.messageId)
        params.sendMessageContent.msgShowContent =
            resources.getString(R.string.schedule_share_schedule) + mScheduleSubject
        val bundleSelect = Bundle()
        bundleSelect.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, params)
        AppUtil.startUri(
            this,
            SelectPageRouter.SELECT_CONVERSATION_ACTIVITY,
            bundleSelect,
            ActivityAnimType.UP_GLIDE
        )
    }

    override fun onScheduleEditClick() {
        if (viewModel.isRepeatingSchedule) {
            showEditRepeatDialog()
        } else {
            gotoEdit(-1)
        }
    }

    override fun onScheduleDeleteClick() {
        if (viewModel.isRepeatingSchedule) {
            showDelRepeatDialog(viewModel.scheduleId, getFormattedCurrentDate())
        } else {
            showDelDialog(viewModel.scheduleId, getFormattedCurrentDate(), -1)
        }
    }

    override fun onScheduleRefuseClick() {
        viewModel.refuse()
    }

    override fun onScheduleAcceptClick() {
        viewModel.accept()
    }

    override fun onScheduleJoinClick() {
        viewModel.join()
    }

    override fun shouldFullScreen() = true

    private val mParticipantItemAdapter = ParticipantItemAdapter()
    private val mAttachmentItemAdapter = AttachmentItemAdapter { previewFile(it) }
    private val mCurrentDateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA)
    private var mContentDialog: BaseDialog? = null
    private var mEditConfirmDialog: ScheduleCreateRemindDialog? = null
    private var mDeleteConfirmDialog: ScheduleCreateRemindDialog? = null
    private var mMapAppsBottomView: BottomListDialog? = null
    private var mFrom = 0
    private var mDeleteDialog: DialogUtils? = null

    private val mScheduleSubject get() = dataBinding.scheduleSubject.text.toString()
    private val mScheduleTimeStr get() = dataBinding.scheduleDatetime.text.toString()
    private val mScheduleRepetitionStr get() = dataBinding.scheduleRepetition.text.toString()
    private val mScheduleMeetingRoomStr get() = dataBinding.meetingRoom.text.toString()
    private val mScheduleLocationStr get() = dataBinding.meetingLocation.text.toString()
    private val mScheduleDescription get() = dataBinding.scheduleDescription.text.toString()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initView()
        initData()
    }

    private fun initView() {
        QMUIStatusBarHelper.setStatusBarLight(this)
        dataBinding.llTop.setPadding(0, getStatusBarsHeight(), 0, 0)

        dataBinding.participants.adapter = mParticipantItemAdapter
        dataBinding.attachmentContent.adapter = mAttachmentItemAdapter
        dataBinding.videoMeetingTrigger.setOnClickListener {
            gotoVideoMeeting(
                viewModel.details.value?.videoMeetingType ?: 0,
                viewModel.details.value?.videoMeetingUrl ?: "",
                viewModel.details.value?.subject?:""
            )
        }
        dataBinding.scheduleSubject.setOnClickListener { expandSubject() }
        dataBinding.scheduleSubject.setOnLongClickListener {
            copySubject()
            true
        }
        dataBinding.scheduleDatetime.setOnLongClickListener{
            copyTimeStr()
            true
        }
        dataBinding.scheduleRepetition.setOnLongClickListener {
            copyRepetitionStr()
            true
        }
        dataBinding.meetingRoom.setOnClickListener { checkMeetingRoom() }
        dataBinding.meetingRoom.setOnLongClickListener{
            copyMeetingRoomStr()
            true
        }
        dataBinding.meetingLocation.setOnClickListener { checkMeetingLocation() }
        dataBinding.meetingLocation.setOnLongClickListener{
            copyLocationStr()
            true
        }
        dataBinding.organizerAvatar.setOnClickListener { checkOrganizer() }
        dataBinding.participantCount.setOnClickListener { checkParticipantsState() }
        dataBinding.acceptRefuseState.setOnClickListener { checkParticipantsState() }
        dataBinding.participantsOverflow.setOnClickListener { checkParticipantsState() }
        dataBinding.scheduleDescription.setOnLongClickListener {
            copyDescription()
            true
        }
        mParticipantItemAdapter.setOnClickListener { checkUserInfo(it) }

        dataBinding.actionExport.setImageResource(if (ThemeUtils.useNewTheme) R.drawable.schedule_ic_meeting_export_new else R.drawable.schedule_ic_meeting_export)
        dataBinding.actionExport.setOnClickListener {
            lifecycleScope.launch {
                if (viewModel.details.value?.videoMeetingType == ScheduleConstant.MEETING_TYPE_BOSS_HI) {
                    if (viewModel.videoMeetingId == null) {
                        showProgressDialog("查询会议号")
                        if (viewModel.queryVideoMeetingId().isNullOrEmpty()) {
                            dismissProgressDialog()
                            ToastUtils.failure("获取会议号失败")
                            return@launch
                        }
                        dismissProgressDialog()
                    }
                }
                joinMeetingDialog.show()
            }
        }
    }

    private fun initData() {
        showProgressDialog(null)
        registerHeadUrlLiveData()
        mFrom = intent.getIntExtra(Constants.FROM, 0)
        val requestRemote: Boolean = intent.getBooleanExtra(Constants.IS_NEED_REQUEST, false)
        initAndDelayFailure(requestRemote).observe(this) {
            dismissProgressDialog()
            if (it != true) {
                finish()
            }
        }
        viewModel.displayMembers.observe(this) { members: List<ScheduleMember?> ->
            dataBinding.participantsOverflow.visibility =
                if (members.size > 6) View.VISIBLE else View.GONE
            mParticipantItemAdapter.submitList(
                members.subList(0, members.size.coerceAtMost(6))
            )
        }

        viewModel.details.observe(this) {
            mAttachmentItemAdapter.submitList(it?.attachments)
            when (it?.videoMeetingType) {
                ScheduleConstant.MEETING_TYPE_BOSS_HI -> {
                    dataBinding.actionVideoMeeting.setText(R.string.schedule_detail_create_video_meeting)
                    dataBinding.timeDivider.isGone = true
                    dataBinding.timer.isGone = true
                    dataBinding.actionExport.setImageResource(if (ThemeUtils.useNewTheme) R.drawable.schedule_ic_meeting_export_new else R.drawable.schedule_ic_meeting_export)
                    val roomId = it.videoMeetingUrl ?: return@observe
                    viewModel.peekVideoMeeting(roomId)
                }
                ScheduleConstant.MEETING_TYPE_THIRD_PARTY -> {
                    dataBinding.actionVideoMeeting.setText(R.string.schedule_detail_join_third_party_video_meeting)
                    dataBinding.timeDivider.isGone = true
                    dataBinding.timer.isGone = true
                    dataBinding.actionExport.setImageResource(R.drawable.schedule_ic_third_link)
                }
                else -> {}
            }
            //扩大点击区域
            placeTouchDelegate()
            it?.let {
                initToGroupBt(it)
            }
        }
        viewModel.videoMeetingRefreshEvent.observe(this) {
            val roomId = viewModel.details.value?.videoMeetingUrl ?: return@observe
            viewModel.peekVideoMeeting(roomId)
        }

        viewModel.videoMeetingDuration.observe(this) {
            if (it == null || it < 0) {
                dataBinding.actionVideoMeeting.setText(R.string.schedule_detail_create_video_meeting)
                dataBinding.timeDivider.isGone = true
                dataBinding.timer.isGone = true
            } else {
                dataBinding.actionVideoMeeting.setText(R.string.schedule_detail_join_video_meeting)
                dataBinding.timeDivider.isVisible = true
                dataBinding.timer.isVisible = true
                dataBinding.timer.text = formatDuration(it)
            }
            //修正点击区域
            placeTouchDelegate()
        }

        viewModel.toGroup.observe(this) {
            it?.let {
                //群聊
                Bundle().apply {
                    putString(
                        BundleConstants.BUNDLE_CHAT_ID,
                        it
                    )
                }.let {bundle ->
                    AppUtil.startUri(this, ChatPageRouter.GROUP_CHAT_ACTIVITY, bundle)
                    ExecutorFactory.execMainTaskDelay({
                        finish()
                    }, 500)
                }
            }
        }
    }

    private fun placeTouchDelegate() {
        dataBinding.actionExport.post {
            val rect = Rect()
            dataBinding.actionExport.getHitRect(rect)
            rect.top -= 40
            rect.bottom += 40
            rect.left -= 40
            rect.right += 40

            val delegate = TouchDelegate(rect, dataBinding.actionExport)
            dataBinding.videoMeetingTrigger.touchDelegate = delegate
        }
    }

    /**
     * 初始化创建/加入群聊按钮
     */
    private fun initToGroupBt(details: ScheduleDetails) {
        val groupNotCreated = details.scheduleCreateGroupId.isEmpty()
        val isNotPersonal = viewModel.calendarCategory != ScheduleCalendar.Category.Personal
        val notEnoughUserCount = details.totalFriendCount < 3

        if (isNotPersonal || (groupNotCreated && notEnoughUserCount)) { // 非个人日历不展示按钮。人数不够不展示创建群聊，但可以展示进群。
            dataBinding.toGroupTvContainer.visibility = View.GONE
            //如果音视频按钮也不展示，就直接把前边的 小色块 也隐藏
            dataBinding.videoHighlight.visibility =
                if (viewModel.showVideoMeeting(details)) View.VISIBLE else View.GONE
            return
        } else {
            dataBinding.toGroupTvContainer.visibility = View.VISIBLE
        }

        dataBinding.tvToGroup.setText(if (groupNotCreated) R.string.schedule_create_group else R.string.enter_group)
        dataBinding.toGroupTvContainer.setOnClickListener {
            if (groupNotCreated) {
                createGroup(details.totalFriendCount)
            } else {
                viewModel.onToGroup()
            }
        }
    }

    private fun createGroup(totalFriendCount: Int) {
        createGroupConfirmationDialog ?: let{
            val content = if (totalFriendCount <= 100) {
                getString(R.string.schedule_create_group_confirmation_content)
            } else if (totalFriendCount < 1000) {
                getString(R.string.schedule_lots_of_memebers_create_group_confirmation_content, totalFriendCount)
            } else {
                getString(R.string.schedule_lots_of_memebers_create_group_confirmation_content_999)
            }
            createGroupConfirmationDialog = DialogUtils.Builder(this)
                .setAutoCloseAfterClick(true)
                .setTitle(content)
                .setTitleMaxLines(3)
                .setPositive(R.string.schedule_action_create)
                .setPositiveListener {
                    viewModel.createGroup()
                }
                .setNegative(R.string.cancel)
                .build()
        }
        createGroupConfirmationDialog?.show()
    }

    private fun formatDuration(seconds: Int): String {
        val hours = seconds / HOUR_IN_SECONDS
        return if (hours > 0) {
            val minutes = seconds % HOUR_IN_SECONDS / 60
            String.format("%d:%02d:%02d", hours, minutes, seconds % 60)
        } else {
            String.format("%02d:%02d", seconds / 60, seconds % 60)
        }
    }

    /**
     * 触发日程的查询操作并在查询成功后立即向下传递成功结果，查询失败则延迟 500 ms 后再向下传递失败结果，失败时需要自动关闭对话框，延迟可优化屏幕闪烁
     */
    private fun initAndDelayFailure(requestRemote: Boolean): LiveData<Boolean?> {
        return viewModel.init(requestRemote).switchMap { success ->
            pointDetailExposed()
            return@switchMap if (success) {
                MutableLiveData(true)
            } else {
                object : LiveData<Boolean?>() {
                    private val delayAction = Runnable { value = false }
                    override fun onActive() {
                        super.onActive()
                        dataBinding.rootView.postDelayed(delayAction, 500)
                    }

                    override fun onInactive() {
                        super.onInactive()
                        dataBinding.rootView.removeCallbacks(delayAction)
                    }
                }
            }
        }
    }

    private fun pointDetailExposed() {
        val source = convertFromValue()
        // 3.5 版本引入公共日历之前的埋点数据
        Analytics.point("calendar-detail-expose") {
            put("source", source)
            put("schedule_id", viewModel.scheduleId)

            if (source == 4) {
                put(
                    /**
                     * 1 - 个人日程
                     * 2 - 第三方日程
                     * 3 - 自定义日程
                     * 4 - 公共日历日程
                     */
                    "category", when (viewModel.calendarCategory) {
                        null,
                        ScheduleCalendar.Category.Personal -> 1
                        ScheduleCalendar.Category.ThirdParty -> 2
                        ScheduleCalendar.Category.Customized -> 3
                        is ScheduleCalendar.Category.Shared,
                        is ScheduleCalendar.Category.Subscribed -> 4
                    }
                )
            }
        }

        // 3.5 版本引入公共日历后增加的埋点
        if (source == 4) {
            /**
             * 1：个人日历
             * 2：其他新建日历
             * 3：共享日历
             * 4：订阅日历
             */
            val (category, type) = with(viewModel.calendarCategory) {
                when (this) {
                    ScheduleCalendar.Category.Personal -> 1 to "self"
                    is ScheduleCalendar.Category.Shared -> 3 to if (personal) "self" else "other"
                    is ScheduleCalendar.Category.Subscribed -> 4 to if (personal) "self" else "other"
                    else -> 2 to "other"
                }
            }
            Analytics.point(
                "calendar-click-from",
                "category" to category,
                "type" to type
            )
        }
    }

    private fun copySubject() {
        copy(mScheduleSubject)
    }

    private fun copyTimeStr() {
        copy(mScheduleTimeStr)
    }

    private fun copyRepetitionStr() {
        copy(mScheduleRepetitionStr)
    }

    private fun copyMeetingRoomStr() {
        copy(mScheduleMeetingRoomStr)
    }

    private fun copyLocationStr() {
        copy(mScheduleLocationStr)
    }

    private fun copyDescription() {
        copy(mScheduleDescription)
    }

    private fun copy(content: String?) {
        val items = ArrayList<SelectBottomBean>()
        items.add(SelectBottomBean(resources.getString(R.string.copy)))
        BottomListDialog.Builder(this)
            .setData(items)
            .setCanceledOnTouchOutside(true)
            .setOnBottomItemClickListener { view: View, pos: Int, bottomBean: SelectBottomBean? ->
                CommonUtils.copyText(view.context, content)
                ToastUtils.success(view.context.getString(R.string.copy_success))
            }
            .create().show()
    }

    private fun expandSubject() {
        if (dataBinding.scheduleSubject.layout != null) {
            if (dataBinding.scheduleSubject.layout
                    .getEllipsisCount(dataBinding.scheduleSubject.lineCount - 1) == 0
            ) {
                return
            }
        }
        mContentDialog?.let {
            if (!it.isShowing) {
                it.setTextView(R.id.tv_content, mScheduleSubject)
                it.show()
            }
        } ?: run {
            val builder = BaseDialog.Builder(this, R.layout.schedule_dialog_schedule_detail)
            builder.isOnTouchCanceled(true)
            mContentDialog = builder.builder()
            mContentDialog?.setTextView(R.id.tv_content, mScheduleSubject)
            mContentDialog?.show()
        }
    }

    private fun checkMeetingRoom() {
        viewModel.findMeetingRoom()?.let {
            val intent = ScheduleMeetingInfoActivity.createIntent(this, it.roomId, it.reservation)
            AppUtil.startActivity(this, intent)
        }
    }

    private fun checkMeetingLocation() {
        viewModel.findMeetingLocation()?.let {
            val latLonPoint = LatLonPoint(it.latitude, it.longitude)
            val appList = MapUtil.resolveMapApps(this, latLonPoint, it.address)
            //没有安装地图
            if (appList.isNullOrEmpty()) {
                ToastUtils.ss(R.string.string_no_map_app_installed)
                return
            }
            //如果只安装了一个地图,直接跳转
            appList.singleOrNull()?.let { app ->
                try {
                    startActivity(app.mapIntent) // 启动不了activity的crash
                } catch (e: Exception) {
                    ToastUtils.ss(R.string.string_no_map_app_installed_error)
                    e.printStackTrace()
                }
                return
            }
            //安装列多个地图,显示地图对话框让用户选择
            showMapAppsDialog(appList)
        }
    }

    private fun showMapAppsDialog(appList: List<MapAppInfoEntity>) {
        mMapAppsBottomView = BottomListDialog.Builder(this)
            .setData(appList.map { SelectBottomBean(it.appName, it.appIcon) })
            .setCanceledOnTouchOutside(true)
            .setItemLayout(R.layout.item_bottom_drawable_new)
            .setSpanCount(3)
            .setOnBottomItemClickListener { _, pos, _ ->
                val entity = appList[pos]
                try {
                    startActivity(entity.mapIntent) // 启动不了activity的crash
                } catch (e: Exception) {
                    ToastUtils.ss(R.string.string_no_map_app_installed_error)
                    e.printStackTrace()
                }
                mMapAppsBottomView!!.dismiss()
            }
            .create()
        mMapAppsBottomView?.show()
    }

    private fun copyVideoMeetingUrl(url: String) {
        CommonUtils.copyText(this, url)
        ToastUtils.ss(R.string.copy_success)
    }

    private fun gotoVideoMeeting(meetingType: Int, url: String, meetingName: String) {
        when (meetingType) {
            ScheduleConstant.MEETING_TYPE_THIRD_PARTY -> openInBrowser(url)
            ScheduleConstant.MEETING_TYPE_BOSS_HI -> startReserveMeeting(url, meetingName)
        }
    }

    private fun openInBrowser(url: String) {
        val intent = Intent()
        intent.action = Intent.ACTION_VIEW
        intent.data = Uri.parse(url)
        if (intent.resolveActivity(packageManager) != null) {
            startActivity(intent)
        } else {
            ToastUtils.failure(R.string.schedule_detail_invalid_video_meeting_url)
        }
        Analytics.point(
            "calendar-join-3rd-meeting",
            "schedule_id" to viewModel.scheduleId,
            "content" to url
        )
    }

    private fun startReserveMeeting(roomId: String, meetingName: String) {
        if ((viewModel.videoMeetingDuration.value ?: -1) < 0) {
            TimeDifferenceUtil.getInstance().start(TimeTag.VIDEO_MEETING_CREATE)
        }

        if ((viewModel.videoMeetingDuration.value ?: 0) > 0) {

            Router.getService(
                VideoMeetingEntranceHelper::class.java,
                VideoAndAudioPageRouter.SERVICE_VIDEO_MEETING_ENTRANCE
            ).joinVideoMeetingFromSchedule(this, roomId, viewModel.scheduleId, callback = object : VideoMeetingEntranceHelper.BootCallback{
                    override fun onSuccess() {
                        PointParamsUtils.push(
                            PointParamsUtils.SOURCE_ENTER_MEETING,
                            MeetingCheckResponse.SOURCE_SCHEDULE
                        )
                    }
                })
        } else {
            Router.getService(
                VideoMeetingEntranceHelper::class.java,
                VideoAndAudioPageRouter.SERVICE_VIDEO_MEETING_ENTRANCE
            ).startScheduleVideoMeeting(this, roomId, meetingName, viewModel.scheduleId)
        }
    }

    private fun checkOrganizer() {
        viewModel.findOrganizer()?.let {
            if (viewModel.isSystemUser(it)) {
                checkRobotInfo(it.contactId)
            } else {
                checkUserInfo(it.contactId)
            }
        }
    }

    private fun checkRobotInfo(robotId: String) {
        ChatPageRouter.jumpToRobotProfilePage(this, robotId)
    }

    private fun checkUserInfo(userId: String) {
        OrganizationPageRouter.jumpToUserInfoActivity(this, userId, SCHEDULE_CARD_PARTICIPANTS)
    }

    private fun checkParticipantsState() {
        val argument = viewModel.participants.value?.run {
            val contactsIds = Array(contacts.size){ it -> ""}
            for (i in contactsIds.indices) {
                val contactSample = contacts[i]
                contactsIds[i] = contactSample.contactId
            }
            val acceptance = viewModel.acceptedMembers.value
                ?.map { it.contactId }
                ?.toTypedArray()
            val refusal = viewModel.refusedMembers.value
                ?.map { it.contactId }
                ?.toTypedArray()
            ScheduleDetailMembersActivity.Argument(totalCount, groups, contactsIds, acceptance, refusal)
        } ?: ScheduleDetailMembersActivity.Argument(0, emptyList(), null, null, null)
        val intent = ScheduleDetailMembersActivity.createIntent(this, argument)
        AppUtil.startActivity(this, intent)
    }

    private fun previewFile(file: ScheduleFile) {
        when (file.category) {
            Constants.CATEGORY_PIC -> {
                val url = if (file.localPath.isNullOrEmpty()) {
                    file.tinyUrl
                } else {
                    file.localPath
                }
                val list = listOf(Image(url, file.url, file.tinyUrl, 0))
                DraggableImageViewerHelper.showImages(this, null, list, 0)
            }
            Constants.CATEGORY_VIDEO -> {
                val imageInfo = ImageInfo()
                var duration = 0
                try {
                    val extJson = JSONObject(file.ext)
                    duration = extJson.optInt(Constants.DURATION)
                    val width = extJson.optInt(Constants.COVER_WIDTH)
                    val height = extJson.optInt(Constants.COVER_HEIGHT)
                    val coverUrl = extJson.optString(Constants.COVER_URL)
                    imageInfo.setHeight(height)
                    imageInfo.setWidth(width)
                    imageInfo.setUrl(coverUrl)
                } catch (e: Exception) {
                    TLog.error(TAG, e, null)
                }
                val bean = WebVideoPlayBean().apply {
                    videoInfo = VideoInfo(file.url, file.size.toLong(), duration, file.localPath, imageInfo)
                }
                AppUtil.startUri(this, WebViewPageRouter.VIDEO_PLAY_ACTIVITY, Bundle().apply {
                    putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, bean)
                })
            }
            else -> {
                val fileInfo = MessageForFile.FileInfo(file.url, file.name, file.size.toLong())
                FilePreviewUtil.jumpToFilePreviewPage(this, fileInfo)
            }
        }
    }

    private fun convertFromValue(): Int {
        return when (mFrom) {
            Constants.CREATE_FROM_CHAT_MESSAGE -> 1
            Constants.CREATE_FROM_SEARCH -> 2
            Constants.CREATE_FROM_CONVERSATION_LIST -> 3
            else -> 4
        }
    }

    private fun gotoEdit(afterAll: Int) {
        AppUtil.startActivity(
            this,
            ScheduleCreateEditActivity.createEditIntent(
                this,
                viewModel.scheduleId,
                viewModel.calendarId,
                mFrom,
                afterAll,
                getFormattedCurrentDate()
            )
        )
        finish()
    }

    private fun getFormattedCurrentDate(): String {
        return mCurrentDateFormat.format(viewModel.currentDate)
    }

    private fun showEditRepeatDialog() {
        if (mEditConfirmDialog == null) {
            val array = resources.getStringArray(R.array.schedule_repeat_edit_range)
            val items = array.mapIndexed { index, s -> BottomSelectItemBean(index, index, s, false) }
            mEditConfirmDialog = ScheduleCreateRemindDialog(
                this,
                items,
                ScheduleCreateRemindDialog.TITLE_EDIT
            )
            mEditConfirmDialog!!.setClickSureListener(object :
                ScheduleCreateRemindDialog.OnClickSureListener {
                override fun onClickSure(indexList: BooleanArray) {}
                override fun onClickSure(index: Int) {
                    gotoEdit(index)
                }
            })
        }
        mEditConfirmDialog!!.showDialog()
    }

    private fun showDelDialog(scheduleId: String, currDate: String, afterAll: Int) {
        val participants = viewModel.participants.value
        val showSimpleConfirmDialog = if (participants != null) {
            if (participants.groups != null && participants.groups.size > 0) {
                false
            } else {
                if (participants.contacts != null) {
                    // 没有参与人 或者 参与人仅有自己
                    participants.contacts.size == 0 ||
                            participants.contacts.size == 1 && participants.contacts[0].contactId == HiKernel.getHikernel().account.userId
                } else {
                    false
                }
            }
        } else {
            true
        }

        if (showSimpleConfirmDialog) {
            showSimpleDelDialog(scheduleId, afterAll)
        } else {
            showMultiButtonDelDialog(scheduleId, currDate, afterAll)
        }
    }

    private fun showSimpleDelDialog(scheduleId: String, afterAll: Int) {
        mDeleteDialog = DialogUtils.Builder(this)
            .setTitle(R.string.schedule_confirm_to_delete_now)
            .setPositive(R.string.schedule_btn_delete)
            .setNegative(R.string.schedule_back)
            .setCancelable(true)
            .setNegativeColor(getColor(R.color.color_0D0D1A))
            .setPositiveListener {
                deleteSchedule(scheduleId, afterAll)
                mDeleteDialog?.dismiss()
            }
            .setNegativeListener {
                mDeleteDialog?.dismiss()
            }
            .build()
        mDeleteDialog?.show()
    }

    private fun deleteSchedule(scheduleId: String, afterAll: Int) {
        lifecycleScope.launch {
            val deleted = viewModel.delete(scheduleId, afterAll)
            dismissProgressDialog()
            if (deleted) {
                Analytics.point(
                    "calendar-delete-expose",
                    "source" to convertFromValue(),
                    "schedule_id" to scheduleId
                )
                finish()
            }
        }
    }

    private fun showMultiButtonDelDialog(scheduleId: String, currDate: String, afterAll: Int) {
        val deleteDialog = ScheduleDeleteDialog()
        deleteDialog.setClickSureListener(object : ScheduleDeleteDialog.OnClickSureListener {
            override fun onClickSure() {
                Analytics.point(
                    "calendar-delete-expose",
                    "source" to convertFromValue(),
                    "schedule_id" to scheduleId
                )
                finish()
            }

            override fun onDismiss() {
                QMUIKeyboardHelper.hideKeyboard(this@ScheduleDetailActivity)
            }
        })
        val bundle = Bundle()
        bundle.putString("scheduleId", scheduleId)
        bundle.putString("deleteDate", currDate)
        bundle.putInt("afterAll", afterAll)
        deleteDialog.arguments = bundle
        deleteDialog.show(supportFragmentManager, ScheduleDeleteDialog.TAG)
    }

    private fun showDelRepeatDialog(scheduleId: String, currDate: String) {
        if (mDeleteConfirmDialog == null) {
            val array = resources.getStringArray(R.array.schedule_repeat_edit_range)
            val items: MutableList<BottomSelectItemBean> = ArrayList(array.size)
            for (i in array.indices) {
                items.add(BottomSelectItemBean(i, i, array[i], false))
            }
            mDeleteConfirmDialog = ScheduleCreateRemindDialog(
                this,
                items,
                ScheduleCreateRemindDialog.TITLE_DELETE
            )
            mDeleteConfirmDialog!!.setClickSureListener(object :
                ScheduleCreateRemindDialog.OnClickSureListener {
                override fun onClickSure(indexList: BooleanArray) {}
                override fun onClickSure(index: Int) {
                    showDelDialog(scheduleId, currDate, index)
                }
            })
        }
        mDeleteConfirmDialog!!.showDialog()
    }

    override fun forceUpdateVisibleItem(): Boolean {
        return true
    }

    override fun onContactAvatarUpdate(contact: Contact) {
        val scheduleMember = viewModel.findOrganizer()
        if (scheduleMember != null && scheduleMember.contactId == contact.userId) {
            ScheduleViewBindingAdapter.bindAvatarImage(
                dataBinding.organizerAvatar, contact.avatar, contact.showName
            )
        }
    }

    override fun getRefreshPageOwner(): LifecycleOwner {
        return this
    }
}