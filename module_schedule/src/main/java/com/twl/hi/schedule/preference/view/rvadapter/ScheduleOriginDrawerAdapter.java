package com.twl.hi.schedule.preference.view.rvadapter;

import androidx.databinding.ViewDataBinding;

import com.twl.hi.foundation.api.response.bean.CalendarItemBean;
import com.twl.hi.schedule.BR;
import com.twl.hi.schedule.R;

import lib.twl.common.adapter.BaseDataBindingMultiAdapter;
import lib.twl.common.adapter.BaseDataBindingViewHolder;

/**
 * 日历来源分栏的适配器
 */
public class ScheduleOriginDrawerAdapter extends BaseDataBindingMultiAdapter<CalendarItemBean, ViewDataBinding> {
    public ScheduleOriginDrawerAdapter() {
        super(null);
    }

    @Override
    protected int getMultiItemViewType(CalendarItemBean item) {
        return 0;
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.schedule_item_calendar_group;
    }

    @Override
    protected void bind(BaseDataBindingViewHolder<ViewDataBinding> helper, ViewDataBinding binding, CalendarItemBean item) {
        binding.setVariable(<PERSON><PERSON>bean, item);
        helper.addOnClickListener(R.id.iv_edit);
    }
}
