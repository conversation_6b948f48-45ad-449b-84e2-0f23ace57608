package com.twl.hi.schedule.preference.view.rvadapter

import java.util.HashMap
import android.view.View
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.RecyclerView
import lib.twl.common.util.SearchAdapterInterface
import lib.twl.common.util.SearchResultInterface

/**
 * 分页加载的搜索结果埋点中介者，将 [PagingDataAdapter] 与 [SearchAdapterInterface] 接口联结
 */
class PagingResultExposeMediator<T : SearchResultInterface>(
    private val recyclerView: RecyclerView,
    private val adapter: PagingDataAdapter<T, *>
) : SearchAdapterInterface<T> {

    private var analyticsEnabled: Boolean = false
    private val exposedItems = hashMapOf<Int, Int>()
    private val attachStateChangeListener = object : RecyclerView.OnChildAttachStateChangeListener {
        override fun onChildViewAttachedToWindow(view: View) {
            val viewHolder = recyclerView.findContainingViewHolder(view) ?: return
            if (viewHolder.bindingAdapter == adapter) {
                val position = viewHolder.bindingAdapterPosition
                val exposedTimes = exposedItems.getOrElse(position) { 0 }
                exposedItems[position] = exposedTimes + 1
            }
        }

        override fun onChildViewDetachedFromWindow(view: View) = Unit
    }

    override fun setEnableOnAttachStateChangeListener(enable: Boolean) {
        if (analyticsEnabled == enable) {
            return
        }

        analyticsEnabled = enable
        if (enable) {
            recyclerView.addOnChildAttachStateChangeListener(attachStateChangeListener)
        } else {
            recyclerView.removeOnChildAttachStateChangeListener(attachStateChangeListener)
        }
    }

    override fun getExposedItems(): HashMap<Int, Int> {
        return exposedItems
    }

    override fun getData(): List<T> {
        return adapter.snapshot().items
    }
}