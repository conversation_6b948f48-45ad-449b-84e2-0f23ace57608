package com.twl.hi.schedule.detail.view.bindingadapter;

import static com.twl.hi.basic.bindadapter.BasicBindingAdapters.setAvatar;

import android.graphics.Paint;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.databinding.BindingAdapter;

import com.twl.hi.basic.util.ThemeUtils;
import com.twl.hi.basic.views.multitext.MLinkTextView;
import com.twl.hi.schedule.R;

public class ScheduleViewBindingAdapter {

    @BindingAdapter("richText")
    public static void bindRichText(MLinkTextView textView, String oldRichContent, String newRickContent) {
        if (!TextUtils.equals(oldRichContent, newRickContent)) {
            textView.parseUrlText(newRickContent == null ? "" : newRickContent);
        }
    }

    @BindingAdapter({"imageUrl", "fallbackText"})
    public static void bindAvatarImage(View view, String imageAvatarUrl, String textAvatar) {
        // TODO 效率较低，可考虑优化
        setAvatar(view, imageAvatarUrl, textAvatar);
    }

    @BindingAdapter({"refuse"})
    public static void bindRefuseText(TextView textView, boolean refused) {
        textView.setClickable(!refused);
        if (refused) {
            textView.setText(R.string.schedule_refuse_ed);
            textView.setTextColor(ContextCompat.getColor(textView.getContext(), R.color.color_ED6966));
            textView.setBackgroundResource(R.drawable.schedule_bg_action_button_refuse);
        } else {
            textView.setText(R.string.schedule_refuse);
            textView.setTextColor(ContextCompat.getColor(textView.getContext(), R.color.color_212121));
            textView.setBackgroundResource(R.drawable.schedule_bg_action_button_normal);
        }
    }

    @BindingAdapter({"accept"})
    public static void bindAcceptText(TextView textView, boolean accepted) {
        textView.setClickable(!accepted);
        if (accepted) {
            textView.setText(R.string.schedule_accept_ed);
            textView.setTextColor(ThemeUtils.getThemeTextColorInt());
            textView.setBackgroundResource(ThemeUtils.useNewTheme ? R.drawable.bg_corner_6_color_primary2 : R.drawable.schedule_bg_action_button_accept);
        } else {
            textView.setText(R.string.schedule_accept);
            textView.setTextColor(ContextCompat.getColor(textView.getContext(), R.color.color_212121));
            textView.setBackgroundResource(R.drawable.schedule_bg_action_button_normal);
        }
    }

    @BindingAdapter({"textStrikethrough"})
    public static void bindTextStrikethrough(TextView textView, boolean strike) {
        if (strike) {
            textView.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG | Paint.ANTI_ALIAS_FLAG);
        } else {
            textView.getPaint().setFlags(Paint.ANTI_ALIAS_FLAG);
        }
    }
}
