package com.twl.hi.schedule.preference.view.rvadapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.paging.LoadState
import androidx.paging.LoadStateAdapter
import androidx.recyclerview.widget.RecyclerView
import com.twl.hi.schedule.R

/**
 * 订阅列表的底部载入状态标识
 *
 * 具体需要处理并展示的状态有：
 *   1. 加载中
 *   2. 加载失败
 */
class SubscriptionFooterStateAdapter(
    /**
     * 数据加载失败时的重试回调
     */
    private val retry: () -> Unit
) : LoadStateAdapter<SubscriptionFooterStateAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, loadState: LoadState) = ViewHolder(parent, retry)
    override fun onBindViewHolder(holder: ViewHolder, loadState: LoadState) = holder.bind(loadState)

    class ViewHolder(parent: ViewGroup, retry: () -> Unit) : RecyclerView.ViewHolder(
        LayoutInflater.from(parent.context).inflate(R.layout.schedule_item_subscription_footer_state, parent, false)
    ) {
        private val progressBar: View = itemView.findViewById(R.id.progress)
        private val stateText: TextView = itemView.findViewById<TextView>(R.id.state)
            .also { it.setOnClickListener { retry() } }

        fun bind(loadState: LoadState) {
            val resId = when (loadState) {
                LoadState.Loading -> R.string.schedule_subscribe_loading
                is LoadState.Error -> R.string.schedule_subscribe_load_failed
                is LoadState.NotLoading -> R.string.none
            }
            stateText.setText(resId)
            stateText.isEnabled = loadState is LoadState.Error
            progressBar.isVisible = loadState == LoadState.Loading
        }
    }
}
