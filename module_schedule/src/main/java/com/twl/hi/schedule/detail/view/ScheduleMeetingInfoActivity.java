package com.twl.hi.schedule.detail.view;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.viewer.DraggableImageViewerHelper;
import com.twl.hi.schedule.BR;
import com.twl.hi.schedule.R;
import com.twl.hi.schedule.SchedulePointUtil;
import com.twl.hi.schedule.databinding.ScheduleActivityScheduleMeetingInfoBinding;
import com.twl.hi.schedule.detail.model.ScheduleMeetingInfoBean;
import com.twl.hi.schedule.detail.view.callback.ScheduleMeetingInfoCallback;
import com.twl.hi.schedule.detail.viewmodel.ScheduleMeetingInfoViewModel;

import java.util.Collections;
import java.util.Optional;

import lib.twl.common.util.AppUtil;
import lib.twl.common.views.imagesview.Image;

public class ScheduleMeetingInfoActivity extends FoundationVMActivity<ScheduleActivityScheduleMeetingInfoBinding, ScheduleMeetingInfoViewModel>
        implements ScheduleMeetingInfoCallback {

    public static Intent createIntent(Context context, String roomId, String bookingId) {
        Intent intent = new Intent(context, ScheduleMeetingInfoActivity.class);
        intent.putExtra("roomId", roomId);
        intent.putExtra("bookingId", bookingId);
        return intent;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        String roomId = getIntent().getStringExtra("roomId");
        getViewModel().requestScheduleMeetingInfo(roomId);
        getViewModel().setBookingId(getIntent().getStringExtra("bookingId"));
        getViewModel().requestMeetingSignStatus();
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.schedule_activity_schedule_meeting_info;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    public void signOut() {
        //page,1 日程  2 预定页面
        getViewModel().requestMeetingSignOut();
    }

    @Override
    public void showMeetingLocationPicture(View v, String url) {
        ScheduleMeetingInfoBean infoBean = Optional.ofNullable(getViewModel().getInfoBeanObservableField().get()).orElse(new ScheduleMeetingInfoBean());
        SchedulePointUtil.pointCalendarViewMeetingPosition(infoBean.roomName, infoBean.workplaceName, infoBean.floor);
        Image image = new Image(url);
        DraggableImageViewerHelper.showImages(
                this,
                v,
                Collections.singletonList(image),
                0
        );
    }
}
