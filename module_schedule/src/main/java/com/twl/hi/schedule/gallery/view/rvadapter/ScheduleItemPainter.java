package com.twl.hi.schedule.gallery.view.rvadapter;

import android.graphics.Paint;
import android.text.TextUtils;
import android.view.View;

import com.twl.hi.foundation.model.ScheduleBean;
import com.twl.hi.foundation.model.ScheduleMeeting;
import com.twl.hi.schedule.databinding.ScheduleGalleryItemScheduleBinding;
import com.twl.hi.schedule.widgets.ScheduleColorUtils;
import com.twl.hi.schedule.widgets.ScheduleUndecidedBackgroundDrawable;

import java.util.Objects;

import hi.kernel.Constants;
import hi.kernel.HiKernel;

public class ScheduleItemPainter {

    public static void paintSchedule(
            ScheduleGalleryItemScheduleBinding binding,
            ScheduleBean scheduleBean,
            boolean isExpired,
            int type) {
        binding.tvContent.setText(scheduleBean.getContent());
        StringBuilder contentNotice = new StringBuilder();
        if (scheduleBean.isAllDay()) {
            contentNotice.append("全天");
        } else {
            contentNotice.append(scheduleBean.getBeginTime());
            contentNotice.append("-");
            contentNotice.append(scheduleBean.getEndTime());
        }
        if (scheduleBean.getType() == Constants.TYPE_SCHEDULE_SYS) {
            contentNotice.append(" 来自：手机");
        }
        binding.tvContentNotice.setText(contentNotice.toString());
        ScheduleMeeting scheduleMeeting = scheduleBean.getMeetingInfo();
        if (scheduleMeeting != null && !TextUtils.isEmpty(scheduleMeeting.getMeetingRoomDescWithoutWorkplace())) {
            binding.tvMeeting.setVisibility(View.VISIBLE);
            binding.tvMeeting.setText(type == ScheduleListMultiAdapter.SCHEDULE_LIST_F2_TYPE ?
                    scheduleMeeting.getMeetingRoomDescWithoutWorkplace() : scheduleMeeting.getMeetingRoomDesc());
        } else {
            binding.tvMeeting.setVisibility(View.GONE);
        }
        binding.getRoot().setBackgroundColor(ScheduleColorUtils.getBackgroundColor(scheduleBean.getCalenderGroupId(),
                scheduleBean.getType(), isExpired, scheduleBean.getEchoType()));
        binding.highlight.setBackgroundColor(ScheduleColorUtils.getHighlightColor(scheduleBean.getCalenderGroupId(),
                scheduleBean.getType(), isExpired, scheduleBean.getEchoType()));
        if (Objects.equals(scheduleBean.getCreatorId(), HiKernel.getHikernel().getAccount().getUserId())) {
            binding.llSchedule.setBackground(null);
        } else {
            binding.llSchedule.setBackground(scheduleBean.getEchoType() == Constants.TYPE_SCHEDULE_ECHO_UNDETERMINED
                    ? new ScheduleUndecidedBackgroundDrawable(binding.getRoot().getContext(), 45, 10)
                    : null);
        }
        binding.tvContent.setPaintFlags(scheduleBean.getEchoType() == Constants.TYPE_SCHEDULE_ECHO_REFUSE
                        ? binding.tvContent.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG
                        : binding.tvContent.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
        binding.tvContentNotice.setPaintFlags(scheduleBean.getEchoType() == Constants.TYPE_SCHEDULE_ECHO_REFUSE
                ? binding.tvContent.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG
                : binding.tvContent.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
        int textColor = !isExpired ? ScheduleColorUtils.getTextColor(scheduleBean.getCalenderGroupId(), scheduleBean.getType(), scheduleBean.getEchoType())
                : ScheduleColorUtils.getOverdueTextColor(scheduleBean.getCalenderGroupId(), scheduleBean.getType(), scheduleBean.getEchoType());
        binding.tvContent.setTextColor(textColor);
        binding.tvContentNotice.setTextColor(textColor);
        binding.tvMeeting.setTextColor(textColor);
    }
}
