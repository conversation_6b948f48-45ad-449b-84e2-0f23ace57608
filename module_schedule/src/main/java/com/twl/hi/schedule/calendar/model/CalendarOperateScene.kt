package com.twl.hi.schedule.calendar.model

import android.graphics.Color
import android.graphics.drawable.Drawable
import com.twl.hi.foundation.api.response.bean.CalendarBean
import com.twl.hi.schedule.R
import lib.twl.common.ext.getResourceDrawable
import lib.twl.common.ext.getResourceString

/**
 *@author: musa on 2022/11/25
 *@e-mail: <EMAIL>
 *@desc: 日历操作场景 枚举
 */
enum class CalendarOperateScene {
    /**创建场景*/
    CREATE_CALENDER {
        override fun canEditTitle() = true

        override fun canEditDisclosure() = true

        override fun addSharedMember() = true

        override fun howToShowMemberList() = SHOW_ALL

        override fun showDeleteBlock() = false

        override fun deleteStr() = ""

        override fun deleterColor() = Color.BLACK

        override fun deleteIcon() = null

        override fun deleteDescribe() = ""


    },

    /**编辑我的日历*/
    EDIT_MY_CALENDAR {
        override fun canEditTitle()= false

        override fun canEditDisclosure() = true

        override fun addSharedMember() = true

        override fun howToShowMemberList() = SHOW_ALL

        override fun showDeleteBlock() = false

        override fun deleteStr() = ""

        override fun deleterColor() = Color.BLACK

        override fun deleteIcon() = null

        override fun deleteDescribe() =  ""
    },

    /**编辑共享日历 需要设置role属性，不然会展示异常*/
    EDIT_SHARED_CALENDAR {
        override fun canEditTitle() = role < CalendarPrivilege.CALENDAR_EDITOR

        override fun canEditDisclosure() = role < CalendarPrivilege.CALENDAR_EDITOR

        override fun addSharedMember() = role < CalendarPrivilege.CALENDAR_EDITOR

        override fun howToShowMemberList() = when (role) {
            CalendarPrivilege.CALENDAR_ADMIN -> SHOW_ALL
            CalendarPrivilege.CALENDAR_MANAGER -> SHOW_ALL
            CalendarPrivilege.CALENDAR_EDITOR -> SHOW_PART
            CalendarPrivilege.CALENDAR_VIEW -> SHOW_PART
            else -> SHOW_PART
        }

        override fun showDeleteBlock() = true

        override fun deleteStr() = if (role == CalendarPrivilege.CALENDAR_ADMIN) R.string.schedule_calendar_delete.getResourceString()
        else R.string.schedule_calendar_quit.getResourceString()

        override fun deleterColor() = if (role == CalendarPrivilege.CALENDAR_ADMIN) Color.RED
        else Color.BLACK

        override fun deleteIcon() = if (role == CalendarPrivilege.CALENDAR_ADMIN) R.drawable.schedule_delete.getResourceDrawable()
        else R.drawable.schedule_quit_calendar.getResourceDrawable()

        override fun deleteDescribe() = if (role == CalendarPrivilege.CALENDAR_ADMIN) R.string.schedule_delete_calendar_desc.getResourceString()
        else R.string.schedule_quit_calendar_desc.getResourceString()

    },

    /**编辑订阅日历*/
    EDIT_SUBSCRIBE_CALENDAR {
        override fun canEditTitle() = false

        override fun canEditDisclosure() = false

        override fun addSharedMember() = false

        override fun howToShowMemberList() = NOT_SHOW

        override fun showDeleteBlock() = true

        override fun deleteStr() = R.string.schedule_cancel_subscribe_someone_schedule.getResourceString()

        override fun deleterColor() = Color.BLACK

        override fun deleteIcon() = R.drawable.schedule_ic_unsubscribe.getResourceDrawable()

        override fun deleteDescribe() = R.string.schedule_unsubscribe_calendar_desc.getResourceString()
    };

    var role = CalendarPrivilege.CALENDAR_VIEW //角色只有共享日历编辑的时候需要区分 0-管理员 1-可管理 2-可编辑 3-可查看 4-仅展示闲忙

    companion object{
        /**展示成员列表常数*/
        const val NOT_SHOW = 0
        const val SHOW_PART = 1
        const val SHOW_ALL = 2

        /**
         * 共享日历需要区分角色
         */
        fun getCalendarOperateScene(calendarBean: CalendarBean, selfUserId: String)  =
            when{
                calendarBean.calendarId.isNullOrBlank()-> //空的，创建日历
                    CREATE_CALENDER
                calendarBean.type == 1-> //个人日历
                    EDIT_MY_CALENDAR
                calendarBean.type == 4 ||
                        calendarBean.type == 5 -> //共享日历(包括自定义日历和贡献给我的日历）
                    initialSharedCalendarRole(calendarBean, selfUserId)
                calendarBean.type == 6 -> //订阅日历
                    EDIT_SUBSCRIBE_CALENDAR
                else ->
                    CREATE_CALENDER
            }

        private fun initialSharedCalendarRole(calendarItem: CalendarBean, selfUserId: String): CalendarOperateScene =
            calendarItem.members.firstOrNull(){
                it.memberId == selfUserId
            }?.let {
                return  EDIT_SHARED_CALENDAR.apply {
                    role = CalendarPrivilege.parseInt(it.roleType)
                }
            }?:let {//用仅可查看闲忙来兜底为空的case
                return EDIT_SHARED_CALENDAR.apply {
                    role = CalendarPrivilege.CALENDAR_JUST_BUSINESS_TIME
                }
            }
    }

    abstract fun canEditTitle(): Boolean

    abstract fun canEditDisclosure(): Boolean

    abstract fun addSharedMember(): Boolean

    /**如何展示成员列表 0: 不展示 1：仅部分展示-我和管理员 2:全部*/
    abstract fun howToShowMemberList(): Int

    fun showDisclosure() = this != EDIT_SUBSCRIBE_CALENDAR

    abstract fun showDeleteBlock(): Boolean

    abstract fun deleteStr(): String

    abstract fun deleterColor(): Int

    abstract fun deleteIcon(): Drawable?

    abstract fun deleteDescribe(): String

}