package com.twl.hi.schedule.freetime.model.remote;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.config.RequestMethod;

public class ScheduleUserBusyRequest extends BaseApiRequest<ScheduleUserBusyResponse> {

    @Expose
    public String targetDate;
    @Expose
    public String userIds;
    @Expose
    public String excludeScheduleId;

    public ScheduleUserBusyRequest(BaseApiRequestCallback<ScheduleUserBusyResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_CALENDAR_USER_BUSY_TIME;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
