package com.twl.hi.schedule.freetime.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.foundation.utils.ScheduleUtils;
import com.twl.hi.schedule.R;
import com.twl.hi.schedule.freetime.model.ScheduleBusyTime;
import com.twl.hi.schedule.freetime.model.remote.ScheduleUserBusyResponse;
import com.twl.hi.schedule.widgets.ScheduleViewBaseView;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import hi.kernel.HiKernel;
import lib.twl.common.util.LList;

public abstract class ScheduleBusyBaseView extends ScheduleViewBaseView {

    protected String UN_DISCLOSURE_EVENT_ID = "0";
    protected Paint mEventBackgroundPaint;
    protected Paint mEventTextPaint;
    protected Map<String, List<LayoutEvent>> mEventRect = new HashMap<>();
    protected List<String> mEventIds = new ArrayList<>();

    protected List<String> mInitialEventIds = new ArrayList<>();

    protected boolean isTouch;

    /**主要文本的的颜色*/
    protected int mMainStrColor = 0xFF3B46CB;

    protected int mExpireMainStrColor = 0x7F3B46CB;

    /**背景色*/
    protected int mEventBackgroundColor = 0xFFE9E9FE;

    protected int mExpireEventBackgroundColor = 0x7FE9E9FE;

    protected int mUnDisclosureBackgroundColor = 0xD2F5F5F7;

    /**日程原本的日期*/
    private String mOriginDay = "";

    /**目标日期*/
    private String mTargetDate = "";
    public ScheduleBusyBaseView(Context context) {
        this(context, null);
    }

    public ScheduleBusyBaseView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ScheduleBusyBaseView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        busyBaseViewInit();
    }

    private void busyBaseViewInit() {
        minSelectTime = 15;

        mEventBackgroundPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mEventBackgroundPaint.setStyle(Paint.Style.FILL_AND_STROKE);

        mEventTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mEventTextPaint.setTextSize(dp2px(13));
        mEventTextPaint.setTypeface(Typeface.DEFAULT_BOLD);
        mEventTextPaint.setStyle(Paint.Style.FILL_AND_STROKE);

        mEventPadding = dp2px(5f);
        mEventVerticalPadding = dp2px(2f);

    }

    @Override
    protected void onDraw(Canvas canvas) {
        drawHeaderRowAndEvents(canvas);
        super.onDraw(canvas);
    }


    private void drawHeaderRowAndEvents(Canvas canvas) {
        mHeaderColumnWidth = mTimeTextWidth;
        updateItemWidth();
        if (mAreDimensionsInvalid) {
            mAreDimensionsInvalid = false;
            if (mScrollToHour >= 0) {
                goToHour(mScrollToHour);
            }
            mScrollToHour = -1;
            mAreDimensionsInvalid = false;
        }
        if (mIsFirstDraw) {
            mIsFirstDraw = false;
            Calendar calendar = mCalendar;
            calendar.setTimeInMillis(System.currentTimeMillis());
            goToHour(calendar.get(Calendar.HOUR_OF_DAY));
        }
        if (mCurrentOrigin.y < getHeight() - mHourHeight * 24.5f - mTimeTextHeight / 2) {
            mCurrentOrigin.y = getHeight() - mHourHeight * 24.5f - mTimeTextHeight / 2;
        }
        if (mCurrentOrigin.y > 0) {
            mCurrentOrigin.y = 0;
        }
        if (mCurrentOrigin.x < getWidth() - mHeaderColumnWidth - (mWidthPerDay + mColumnGap) * mEventIds.size() + mColumnGap) {
            mCurrentOrigin.x = getWidth() - mHeaderColumnWidth - (mWidthPerDay + mColumnGap) * mEventIds.size() + mColumnGap;
        }
        if (mCurrentOrigin.x > 0) {
            mCurrentOrigin.x = 0;
        }
        // Consider scroll offset. 全员空闲不滑动
        float startPixel = mCurrentOrigin.x + mHeaderColumnWidth;

        // Clip to paint events only.
        canvas.save();
        canvas.clipRect(mHeaderColumnWidth, 0, getWidth(), getHeight());
        // Iterate through each day.
        int dayNumber = 0;
        // Prepare to iterate for each hour to draw the hour lines.
        int lineCount = (getHeight() / mHourHeight) + 1;
        lineCount = (lineCount) * ((int) Math.ceil(mNumberOfVisibleDays) + 1);
        hourLines = new float[lineCount * 4];

        if (LList.isEmpty(mEventIds)) {
            drawDayTimeLine(hourLines, startPixel, canvas);
        }
        for (; dayNumber < mEventIds.size(); dayNumber++) {
            TLog.info("ScheduleLookBusy", "index : %d, userId = %s", dayNumber, mEventIds.get(dayNumber));
            drawUserEvent(canvas, hourLines, mEventIds.get(dayNumber), startPixel, false);
            // In the next iteration, start from the next day.
            startPixel += mWidthPerDay + mColumnGap;
        }
        // Draw the line at the current time.
        drawNowLine(canvas, mHeaderColumnWidth, getWidth());
        //draw 选择时间
        float left = mHeaderColumnWidth + dp2px(1);
        drawTimeSelect(canvas, left, getWidth() - dp2px(2));
        canvas.restore();
    }

    protected void updateItemWidth() {
        updateNumberOfVisibleDays();
        mWidthPerDay = getWidth() - mHeaderColumnWidth - mColumnGap * (mNumberOfVisibleDays - 1);
        mWidthPerDay = mWidthPerDay / mNumberOfVisibleDays;
    }

    @Override
    protected boolean isTouchSelectTimeRect(MotionEvent event) {
        float touchX = event.getX();
        float touchY = event.getY();
        if (topExtendPoint.contains(touchX, touchY)) {
            clickTopPoint = true;
            return true;
        } else if (bottomExtendPoint.contains(touchX, touchY)) {
            clickBottomPoint = true;
            return true;
        } else if (zeroRectContainEvent(selectRect, touchX, touchY)) {
            clickSelect = true;
            clickSelectTag = true;
            return true;
        } else if (containsSelectRect(selectRect, touchX, touchY)) {
            if (isTouchSelectTimeRectVerticalEdge(selectRect, touchY, topExtendPoint.height() / 2)) {
                if (topExtendPoint.top <= touchY && touchY <= topExtendPoint.bottom) {
                    clickTopPoint = true;
                    return true;
                }
                if (bottomExtendPoint.top <= touchY && touchY <= bottomExtendPoint.bottom) {
                    clickBottomPoint = true;
                    return true;
                }
            } else {
                clickSelect = true;
                clickSelectTag = true;
            }
            return true;
        }
        return false;
    }

    /**
     * 是否触摸到时间选择框到边缘
     * @param targetRect 时间选择框
     * @param touchY 触摸的 Y 值
     * @param pointRadius 触摸阈值，top ± pointRadius 为有效触摸范围
     * @return 是否触摸到时间选择框到边缘
     */
    protected boolean isTouchSelectTimeRectVerticalEdge(RectF targetRect, float touchY, float pointRadius) {
        return (targetRect.top - pointRadius <= touchY && touchY <= targetRect.top + pointRadius)
                || (targetRect.bottom - pointRadius <= touchY && touchY <= targetRect.bottom + pointRadius);
    }

    @Override
    protected void goToNearestOrigin() {
//        double leftDays = mCurrentOrigin.x / (mWidthPerDay + mColumnGap);
//        if (mCurrentFlingDirection != Direction.NONE) {
//            // snap to nearest day
//            leftDays = Math.round(leftDays);
//        } else if (mCurrentScrollDirection == Direction.LEFT) {
//            // snap to last day
//            leftDays = Math.floor(leftDays);
//        } else if (mCurrentScrollDirection == Direction.RIGHT) {
//            // snap to next day
//            leftDays = Math.ceil(leftDays);
//        } else {
//            // snap to nearest day
//            leftDays = Math.round(leftDays);
//        }
//
//        int nearestOrigin = (int) (mCurrentOrigin.x - leftDays * (mWidthPerDay + mColumnGap));
//        if (nearestOrigin != 0) {
//            // Stop current animation.
//            mScroller.forceFinished(true);
//            // Snap to date.
//            mScroller.startScroll((int) mCurrentOrigin.x, (int) mCurrentOrigin.y, -nearestOrigin, 0, (int) (Math.abs(nearestOrigin) / mWidthPerDay * mScrollDuration));
//            ViewCompat.postInvalidateOnAnimation(ScheduleBusyView.this);
//        }
//        // Reset scrolling and fling direction.
//        mCurrentScrollDirection = mCurrentFlingDirection = Direction.NONE;
    }

    public void scrollToX(int dx) {
        if (!isTouch) {
            mCurrentOrigin.x = mCurrentOrigin.x - dx;
            ViewCompat.postInvalidateOnAnimation(this);
        }
    }

    @Override
    public void gestureOnDown() {
        goToNearestOrigin();
    }

    protected void drawUserEvent(Canvas canvas, float[] hourLines, String eventId, float startPixel, boolean isAllUser) {
        // Draw the lines for hours.
        drawDayTimeLine(hourLines, startPixel, canvas);
        drawBusyEvents(eventId, startPixel, canvas, isAllUser);
    }

    /**
     * 绘制一天24小时的小时线
     * --------------------
     * @param hourLines
     * @param startPixel
     * @param canvas
     */
    private void drawDayTimeLine(float[] hourLines, float startPixel, Canvas canvas) {
        // Draw background color for each day.
        float start = Math.max(startPixel, mHeaderColumnWidth);
        // Prepare the separator lines for hours.
        int i = 0;
        for (int hourNumber = 0; hourNumber <= 24; hourNumber++) {
            float top = mCurrentOrigin.y + mHourHeight * hourNumber + mTimeTextHeight / 2;
            if (top > mTimeTextHeight / 2 - mHourSeparatorHeight && top < getHeight() && startPixel + mWidthPerDay - start > 0) {
                hourLines[i * 4] = start;
                hourLines[i * 4 + 1] = top;
                hourLines[i * 4 + 2] = startPixel + mWidthPerDay;
                hourLines[i * 4 + 3] = top;
                i++;
            }
        }
        canvas.drawLines(hourLines, mHourSeparatorPaint);
    }

    protected void drawBusyEvents(String eventId, float startFromPixel, Canvas canvas, boolean isAllUser) {
        List<LayoutEvent> eventRects = mEventRect.get(eventId);
        if (!LList.isEmpty(eventRects)) {
            for (LayoutEvent eventRect : eventRects) {
                // Calculate top.
                float top = mHourHeight * 24 * eventRect.top / 1440 + mCurrentOrigin.y + mTimeTextHeight / 2;
                // Calculate bottom.
                float bottom = eventRect.bottom;
                bottom = mHourHeight * 24 * bottom / 1440 + mCurrentOrigin.y + mTimeTextHeight / 2;
                //上下留padding
                top += 4;
                bottom -= 4;
                // Calculate left and right.
                float left = startFromPixel + eventRect.left * mWidthPerDay;
                float right = left + eventRect.width * mWidthPerDay;
                // Draw the event and the event name on top of it.
                if (left < right &&
                        left < getWidth() &&
                        top < getHeight() &&
                        right > mHeaderColumnWidth &&
                        bottom > mTimeTextHeight / 2
                ) {
                    mRectF.set(left, top, right - dp_1, bottom);
                    boolean isFutureEvent = isFutureEvent(eventRect);

                    if (!TextUtils.equals(eventRect.id, UN_DISCLOSURE_EVENT_ID)) {
                        mEventBackgroundPaint.setColor(isFutureEvent ? mEventBackgroundColor : mExpireEventBackgroundColor);
                        canvas.drawRect(mRectF, mEventBackgroundPaint);
                        drawEventText(canvas, mRectF, eventRect ,isFutureEvent);
                    } else {
                        mEventBackgroundPaint.setColor(mUnDisclosureBackgroundColor);
                        canvas.drawRect(mRectF.left, 0, mRectF.right, getHeight(), mEventBackgroundPaint);
                        drawEventUnDisclosure(canvas, mRectF, eventRect);
                    }

                }
            }
        }
    }

    /**
     * 绘制个人日历不公开
     */
    private void drawEventUnDisclosure(Canvas canvas, RectF rect, LayoutEvent eventRect) {
        mTextPaint.setColor(0xFF9999A3);
        mTextPaint.setTextSize(dp2px(14));

        StaticLayout myStaticLayout = new StaticLayout(eventRect.mainStr, mTextPaint, (int) rect.width() - mEventPadding * 2,
                Layout.Alignment.ALIGN_CENTER, 1.0f, 0.0f, false);
        float baseline = (getHeight() >> 1) - myStaticLayout.getHeight() / 2f;
        canvas.save();
        canvas.translate(rect.left + mEventPadding, baseline);
        myStaticLayout.draw(canvas);
        canvas.restore();
    }

    /**绘制日程文字*/
    private void drawEventText(Canvas canvas, RectF rect, LayoutEvent eventRect, boolean isFutureEvent) {
        if (rect.right - rect.left - mEventPadding * 2 < 0 || rect.bottom - rect.top - mEventVerticalPadding * 2 < 0) {
            return;
        }
        String mainStr = eventRect.mainStr;

        mEventTextPaint.setColor(isFutureEvent ? mMainStrColor : mExpireMainStrColor);

        /*尺寸元素*/
        /*去除padding后实际的可用范围*/
        mEventTextRect.set(rect.left + mEventPadding, rect.top + mEventVerticalPadding,
                rect.right - mEventPadding, rect.bottom - mEventVerticalPadding);
        int availableHeight = (int) mEventTextRect.height();
        int availableWidth = (int) mEventTextRect.width();

        /*渲染计算title的高度*/
        //getFontMetrics 获取的系统建议两行之间的距离，相当于每行高度
        float lineHeightOfTitle = mEventTextPaint.getFontMetrics(mFontMetrics);
        //如果可用高度连一行title都放不下，就不画了
        if (availableHeight < lineHeightOfTitle) {
            return;
        }

        canvas.save();
        canvas.clipRect(rect);
        canvas.translate(mEventTextRect.left, mEventTextRect.top);
        //总高，用来判断什么时候是最后一行
        float wholeHeight = 0;
        //双指针标记每行绘制的文字范围
        int startOffsetIndex = 0;
        int endOffsetIndex = -1;

        int titleLines = 0;
        boolean breakFlag = false;
        //绘制日程title的文字
        while (!breakFlag && !mainStr.isEmpty()) {
            //行数累加，高度累加
            titleLines ++;
            wholeHeight += lineHeightOfTitle;
            //开始位置是上一行的后一个,初始为0
            startOffsetIndex = endOffsetIndex + 1;

            if (wholeHeight + lineHeightOfTitle < availableHeight) { //可以满足折行条件
                endOffsetIndex = mEventTextPaint.breakText(mainStr, startOffsetIndex, mainStr.length(), true, availableWidth, null)
                        + endOffsetIndex;
                if (endOffsetIndex == mainStr.length() - 1) { //满足折行条件，但是文字已经展示完了
                    breakFlag = true;
                }
            } else { //已经没有折行的余地了，就在这一行全部文字画上
                endOffsetIndex = mainStr.length() - 1;
                breakFlag = true;
            }
            canvas.drawText(mainStr.substring(startOffsetIndex, endOffsetIndex + 1), 0, -mFontMetrics.top + lineHeightOfTitle * (titleLines -1), mEventTextPaint);
        }
        canvas.restore();
    }

    private boolean isFutureEvent(LayoutEvent eventRect) {
        Calendar endCalendar = ScheduleUtils.getCalendarByTargetDateWithTime(null, eventRect.endTime);
        return endCalendar.getTimeInMillis() >= System.currentTimeMillis();
    }

    @Override
    protected void drawNowLine(Canvas canvas, float startX, float stopX) {
        if (mIsToday) {
            float startY = mHeaderHeight + mHeaderRowPadding * 2 + mTimeTextHeight / 2 + mCurrentOrigin.y;
            Calendar now = Calendar.getInstance();
            float beforeNow = (now.get(Calendar.HOUR_OF_DAY) + now.get(Calendar.MINUTE) / 60.0f) * mHourHeight;
            float y = startY + beforeNow;
            canvas.drawLine(startX, y, stopX, y, mNowLinePaint);
        }
    }

    /**
     * 设置数据源，刷新
     */
    public void setData(List<String> eventIds, List<ScheduleUserBusyResponse.ScheduleUserBusy> events, String targetDate) {
        isAgainData = true;
        mTargetDate = targetDate;
        setEventIds(eventIds);
        updateNumberOfVisibleDays();
        //将服务端的日程数据转换成 渲染UI需要的 数据格式 的转换函数
        Function<ScheduleUserBusyResponse.ScheduleUserBusy, List<LayoutEvent>> transform = userBusy ->
                Optional.ofNullable(userBusy) //空安全检查
                        .map(it -> it.times)
                        .map(it -> { //真正执行转换
                            return it.stream()
                                    .map(busyTime -> {
                                        LayoutEvent layoutEvent = new LayoutEvent();
                                        if (!TextUtils.equals(busyTime.scheduleId, UN_DISCLOSURE_EVENT_ID)) {
                                            layoutEvent.id = busyTime.scheduleId;
                                            layoutEvent.mainStr = busyTime.content;
                                            layoutEvent.startTime = targetDate + " " + busyTime.beginTime;
                                            layoutEvent.endTime = targetDate + " " + busyTime.endTime;
                                        } else { //scheduleId被标记为0 说明是 不可见的日程
                                            layoutEvent.id = busyTime.scheduleId;
                                            layoutEvent.mainStr = getContext().getString(R.string.schedule_not_disclosure);
                                            layoutEvent.startTime = targetDate + " "+ "00:00";
                                            layoutEvent.endTime =  targetDate + " "+ "24:00";
                                        }
                                        return layoutEvent;
                                    })
                                    .collect(Collectors.toList());
                        })
                        .orElseGet(Collections::emptyList);

        //把日程数据 以userId为key 存入map
        mEventRect = events.stream()
                .peek(it -> {
                    if (it.visible == 1 && !TextUtils.equals(it.userId, HiKernel.getHikernel().getAccount().getUserId())) {//除自己以外的日程私密不可见
                        it.times = new ArrayList<>(1);
                        ScheduleBusyTime scheduleBusyTime = new ScheduleBusyTime();
                        scheduleBusyTime.scheduleId = UN_DISCLOSURE_EVENT_ID;
                        it.times.add(scheduleBusyTime);
                    }
                })//将私密日程用一个ScheduleId为0的日程标记出来
                .collect(Collectors.toMap(it -> it.userId, transform));//转换成一个userId作为key的map

        //计算尺寸比例
        mEventRect.values()
                .stream()
                .forEach(this::computePositionOfEvents);

        //重绘
        invalidate();
    }

    protected void updateNumberOfVisibleDays() {
        mNumberOfVisibleDays = Math.min(Math.max(1, mEventIds.size()), 4.6f);
    }

    protected int getNextIndex(int index) {
        if (currentEndTimeInd < 0) {
            return super.getNextIndex(index);
        }
        int endIdx = getSelectTimeIndex(currentEndTimeInd);
        int startIdx = getSelectTimeIndex(currentStartTimeInx);
        if (endIdx == startIdx && currentEndTimeInd != currentStartTimeInx) {
            endIdx += minFirstTouchSelectTime;
        }
        return index + endIdx - startIdx;
    }

    private void computePositionOfEvents(List<LayoutEvent> eventRects) {
        // Make "collision groups" for all events that collide with others.
        //记录了冲突分组的集合
        List<List<LayoutEvent>> collisionGroups = new ArrayList<>();
        for (LayoutEvent eventRect : eventRects) {
            //标记是此日程是否被安置到现有冲突分组中
            boolean isPlaced = false;

            //如果和之前的日程有冲突，就放入有冲突的日程分组
            outerLoop:
            for (List<LayoutEvent> collisionGroup : collisionGroups) {
                for (LayoutEvent groupEvent : collisionGroup) {
                    if (isEventsCollide(groupEvent, eventRect, mCalendar)) {
                        collisionGroup.add(eventRect);
                        isPlaced = true;
                        break outerLoop;
                    }
                }
            }

            //没有放置到现有的冲突分组，则新建一个分组
            if (!isPlaced) {
                List<LayoutEvent> newGroup = new ArrayList<>();
                newGroup.add(eventRect);
                collisionGroups.add(newGroup);
            }
        }

        //计算互相影响的日程的宽度 然后计算出布局范围
        for (List<LayoutEvent> collisionGroup : collisionGroups) {
            expandEventsToMaxWidth(collisionGroup);
        }

    }

    private void expandEventsToMaxWidth(List<LayoutEvent> collisionGroup) {
        // Expand the events to maximum possible width.
        /*
         *collisionGroup 包含数据时一天内时间有交叉的日程，如下图所示
         * 但是实际不是一定两两都相交 比如 日程a和日程e
         *=========
         *==日程a==  ========
         *========= ===日程b= ==日程c==
         *========= ======== ========= ========
         *                   ========= ========
         *=========                    ==日程d==
         *==日程e==                    ========
         *=========
         * 下边代码是将 不相交的日程 分成一组, 比如 上图a 和 e 分成一组，b，c,d各一组
         * 这样就可知上图整块日程区域的 一行最多放 4个，就能以此在后边计算每个日程的宽度了
         * */
        List<List<LayoutEvent>> columns = new ArrayList<>();
        columns.add(new ArrayList<>());
        for (LayoutEvent eventRect : collisionGroup) {
            boolean isPlaced = false;
            for (List<LayoutEvent> column : columns) {
                if (column.size() == 0) {
                    column.add(eventRect);
                    isPlaced = true;
                } else if (!isEventsCollide(eventRect, column.get(column.size() - 1), mCalendar)) {
                    column.add(eventRect);
                    isPlaced = true;
                    break;
                }
            }
            if (!isPlaced) {
                List<LayoutEvent> newColumn = new ArrayList<>();
                newColumn.add(eventRect);
                columns.add(newColumn);
            }
        }

        for (int i = 0; i < columns.size(); i++) {
            for (LayoutEvent eventRect : columns.get(i)) {
                boolean breakFlag = false;

                for (int j = i + 1; j < columns.size(); j++) {
                    for (LayoutEvent afterEvent : columns.get(j)) {
                        if (isEventsCollide(eventRect, afterEvent, mCalendar)) {
                            breakFlag = true;
                            break;
                        }
                    }
                    if (breakFlag) {
                        break;
                    }
                }
                if (breakFlag) {
                    eventRect.widthRatio = 1;
                } else {
                    eventRect.widthRatio = columns.size() - i;
                }
            }
        }


        // Calculate left and right position for all the events.
        // Get the maxRowCount by looking in all columns.
        int maxRowCount = 0;
        for (List<LayoutEvent> column : columns) {
            maxRowCount = Math.max(maxRowCount, column.size());
        }

        //columns.size 代表一行最多有几个日程
        for (int i = 0; i < maxRowCount; i++) {
            // Set the left and right values of the event.
            float j = 0;
            for (List<LayoutEvent> column : columns) {
                if (column.size() >= i + 1) {
                    LayoutEvent eventRect = column.get(i);
                    //这里的宽和左侧距离 都是用比例表示
                    eventRect.width = eventRect.widthRatio / columns.size();
                    eventRect.left = j / columns.size();

                    Calendar startTime =  ScheduleUtils.getCalendarByTargetDateWithTime(null, eventRect.startTime);
                    Calendar endTime = ScheduleUtils.getCalendarByTargetDateWithTime(null, eventRect.endTime);
                    int startM = startTime.get(Calendar.MINUTE);
                    //顶部和顶部则是用分钟为刻度来表示
                    eventRect.top = startTime.get(Calendar.HOUR_OF_DAY) * 60 + startM;
                    if (endTime.getTimeInMillis() - startTime.getTimeInMillis() < 30 * 60 * 1000) {
                        //日程长度小于30分钟，按30分钟展示
                        Calendar temp = ScheduleUtils.getCalendarByTargetDateWithTime(mCalendar, eventRect.startTime);
                        temp.add(Calendar.MINUTE, 30);
                        int endM = temp.get(Calendar.MINUTE);
                        if (temp.get(Calendar.HOUR_OF_DAY) >= startTime.get(Calendar.HOUR_OF_DAY)) { //没有超过24小时
                            eventRect.bottom = temp.get(Calendar.HOUR_OF_DAY) * 60 + endM;
                        } else { //超过24小时 会变成0，这里做处理
                            eventRect.bottom = 24 * 60 + temp.get(Calendar.MINUTE);
                        }
                        //这种情况下需要区分 bottom 和 realbottom
                        eventRect.realBottom = endTime.get(Calendar.HOUR_OF_DAY) * 60 + endTime.get(Calendar.MINUTE);
                        ;
                    } else if (endTime.get(Calendar.HOUR_OF_DAY) < startTime.get(Calendar.HOUR_OF_DAY)
                            || eventRect.endTime.contains("24:00")) {
                        //表示是24点
                        eventRect.bottom = 24 * 60;
                        eventRect.realBottom = 24 * 60;
                    } else {
                        int endM = endTime.get(Calendar.MINUTE);
                        eventRect.bottom = endTime.get(Calendar.HOUR_OF_DAY) * 60 + endM;
                        eventRect.realBottom = eventRect.bottom;
                    }
                }
                j++;
            }
        }
    }

    public void setEventIds(List<String> eventIds) {
        mInitialEventIds = eventIds;
        mEventIds.clear();
        mEventIds.addAll(mInitialEventIds);
        invalidate();
    }

    public String getOriginDay() {
        return mOriginDay;
    }

    public void setOriginDay(String originDay) {
        mOriginDay = originDay;
    }

    public String getTargetDate() {
        return mTargetDate;
    }
}
