package com.twl.hi.schedule.meetingroom.model;

import android.text.TextUtils;

import hi.kernel.HiKernel;

/**
 * create by sunyang<PERSON>
 * on 2019-10-24
 */
public class OccupyRoomTimeLineBean {
    private String mStartTime;
    private String mEndTime;
    private String ownerId;// 预订者用户id
    //"occupyType" : 2, // 1-用户预定  2-管理员锁定
    private int occupyType;
    public boolean hasSchedule; //本地是否有日程

    public OccupyRoomTimeLineBean() {

    }

    public OccupyRoomTimeLineBean(String startTime, String endTime, String ownerId, int occupyType, boolean hasSchedule) {
        this.mStartTime = startTime;
        this.mEndTime = endTime;
        this.ownerId = ownerId;
        this.occupyType = occupyType;
        this.hasSchedule = hasSchedule;
    }

    public String getStartTime() {
        return mStartTime;
    }

    public void setStartTime(String startTime) {
        this.mStartTime = startTime;
    }

    public String getEndTime() {
        return mEndTime;
    }

    public void setmEndTime(String mEndTime) {
        this.mEndTime = mEndTime;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public int getOccupyType() {
        return occupyType;
    }

    public void setOccupyType(int occupyType) {
        this.occupyType = occupyType;
    }

    public boolean isMySchedule() {
        return hasSchedule || TextUtils.equals(ownerId, HiKernel.getHikernel().getAccount().getUserId());
    }

    public boolean isLocked() {
        return occupyType == 2;
    }
}
