package com.twl.hi.schedule.select.view;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.basic.adapter.MyMultiTypeAdapter;
import com.twl.hi.basic.model.select.DisplayGroupBean;
import com.twl.hi.foundation.utils.point.SearchPointUtil;
import com.twl.hi.basic.views.group.LinearLayoutManagerWrapper;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.schedule.BR;
import com.twl.hi.schedule.R;
import com.twl.hi.schedule.databinding.ScheduleActivitySelectGroupContactsBinding;
import com.twl.hi.schedule.databinding.ScheduleSelectItemUserSelectBinding;
import com.twl.hi.schedule.select.view.callback.ScheduleSelectGroupContactsCallback;
import com.twl.hi.schedule.select.viewmodel.ScheduleSelectGroupContactsViewModel;

import java.io.Serializable;
import java.util.List;

import lib.twl.common.util.LList;
import lib.twl.common.util.QMUIKeyboardHelper;
import lib.twl.common.util.ToastUtils;

/**
 * <AUTHOR>
 * 选择群聊中的联系人
 * 联系人展示数据有特殊排序规则
 */
public class ScheduleSelectGroupContactsActivity extends FoundationVMActivity<ScheduleActivitySelectGroupContactsBinding, ScheduleSelectGroupContactsViewModel> implements ScheduleSelectGroupContactsCallback, TextWatcher {

    private MyMultiTypeAdapter mAdapter = new ScheduleSelectGroupSearchAdapter();
    private SearchPointUtil mSearchPointUtil = new SearchPointUtil();

    private String mSearchKey = "";
    private String mSearchId = "";

    private boolean hasCheckChanged = false;

    static class ScheduleSelectGroupSearchAdapter extends MyMultiTypeAdapter {
        @Override
        public String getPointLevel() {
            return "群日历搜索";
        }
    }

    public static Intent createIntent(Context context, String groupId, List<String> memberIds) {
        Intent intent = new Intent();
        Bundle bundle = new Bundle();
        bundle.putString("groupId", groupId);
        bundle.putSerializable("memberIds", (Serializable) memberIds);
        intent.putExtras(bundle);
        intent.setClass(context, ScheduleSelectGroupContactsActivity.class);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initParams();
        initView();
        getViewModel().getGroupContactSelectAble().observe(this, new Observer<List<DisplayGroupBean>>() {
            @Override
            public void onChanged(List<DisplayGroupBean> displayGroupBeans) {
                if (LList.isEmpty(displayGroupBeans)) {
                    return;
                }
                mAdapter.setData(displayGroupBeans);
                mAdapter.notifyDataSetChanged();
                updateCheckAllUi();
            }
        });
        getViewModel().getGroupContactSelectNumber().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                int size = getViewModel().getAllContacts().size();
                if (size > 999) {
                    getDataBinding().selectNumber.setText(String.format(getString(R.string.schedule_select_group_contact_numbers), integer, "999+"));
                } else {
                    getDataBinding().selectNumber.setText(String.format(getString(R.string.schedule_select_group_contact_numbers), integer, String.valueOf(size)));
                }
                updateCheckAllUi();
            }
        });
        getViewModel().queryGroupContactsOrderByTime();
    }

    private void updateCheckAllUi() {
        if (QMUIKeyboardHelper.isKeyboardVisible(ScheduleSelectGroupContactsActivity.this)) {
            getDataBinding().checkAll.setVisibility(View.GONE);
            getDataBinding().selectMaxHint.setVisibility(View.GONE);
        } else {
            if (getViewModel().getAllContacts().size() > 50) {
                getDataBinding().checkAll.setVisibility(View.GONE);
                if (getViewModel().getSelectedMemberSize() >= 50) {
                    getDataBinding().selectMaxHint.setVisibility(View.VISIBLE);
                } else {
                    getDataBinding().selectMaxHint.setVisibility(View.GONE);
                }
            } else {
                getDataBinding().cbUserAll.setChecked(getViewModel().getAllContacts().size() == getViewModel().getSelectedMemberSize());
                getDataBinding().checkAll.setVisibility(View.VISIBLE);
                getDataBinding().selectMaxHint.setVisibility(View.GONE);
            }
        }
    }

    private void initView() {
        mAdapter.register(DisplayGroupBean.class, R.layout.schedule_select_item_user_select, new MyMultiTypeAdapter.ItemViewBinder<ScheduleSelectItemUserSelectBinding, DisplayGroupBean>() {
            @Override
            public void bind(ScheduleSelectItemUserSelectBinding vdb, DisplayGroupBean item, int linkIndex) {
                vdb.setVariable(BR.displayGroupBean, item);
                vdb.setVariable(BR.callback, getCallback());
                vdb.setVariable(BR.searchKey, mSearchKey);
                vdb.setVariable(BR.position, linkIndex);
            }
        });

        LinearLayoutManagerWrapper layoutManagerWrapper = new LinearLayoutManagerWrapper(getApplicationContext(), LinearLayoutManager.VERTICAL, false);
        getDataBinding().contactList.setLayoutManager(layoutManagerWrapper);
        getDataBinding().contactList.setAdapter(mAdapter);

        getDataBinding().etInput.addTextChangedListener(this);
        mSearchPointUtil.setExposeListener(getDataBinding().contactList, mAdapter);
        QMUIKeyboardHelper.setVisibilityEventListener(this, new QMUIKeyboardHelper.KeyboardVisibilityEventListener() {
            @Override
            public boolean onVisibilityChanged(boolean isOpen, int heightDiff) {
                updateCheckAllUi();
                return false;
            }
        });
    }

    private void initParams() {
        Intent intent = getIntent();
        Bundle bundle = intent.getExtras();
        if (bundle != null) {
            List<String> memberIds = (List<String>) bundle.getSerializable("memberIds");
            getViewModel().setMemberIds(memberIds, bundle.getString("groupId"));
        }
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.schedule_activity_select_group_contacts;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void onBackPressed() {
        updateSelected();
    }

    private void updateSelected() {
        if (!hasCheckChanged) {
            setResult(RESULT_CANCELED);
            finish();
            return;
        }
        showProgressDialog("");
        getViewModel().requestUpdateSelectedContacts((result) -> {
            dismissProgressDialog();
            if (!result) {
                ToastUtils.failure("网络异常，请稍后重试");
                setResult(RESULT_CANCELED);
            } else {
                setResult(RESULT_OK);
            }
            finish();
            return null;
        });
    }

    @Override
    public void afterTextChanged(Editable s) {
        String text = s.toString();
        mSearchKey = text;
        mAdapter.clear();
        mAdapter.getExposedItems().clear();
        mAdapter.notifyDataSetChanged();
        if (TextUtils.isEmpty(text)) {
            mSearchPointUtil.updateSearchId("");
            getViewModel().queryGroupContactsOrderByTime();
            return;
        }
        mSearchId = getViewModel().searchGroupContactByKey(text.toLowerCase());
        mSearchPointUtil.updateSearchId(mSearchId);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        getDataBinding().etInput.removeTextChangedListener(this);
    }

    @Override
    public void onClickContact(DisplayGroupBean displayGroupBean, int position) {
        if (getViewModel().getSelectedMemberSize() > 50 || (getViewModel().getSelectedMemberSize() == 50 && !displayGroupBean.isSelected())) {
            ToastUtils.failure(R.string.schedule_max_look_busy_select_size_limit);
            return;
        }

        hasCheckChanged = true;
        getViewModel().onClickContact(displayGroupBean);
        if (displayGroupBean.isSelected()) {
            new PointUtils.BuilderV4()
                    .params("search_id", mSearchId)
                    .params("info", displayGroupBean.getContact().getUserId())
                    .params("level", "群日历")
                    .params("rank", String.valueOf(position + 1))
                    .name("search-result-click")
                    .point();
        }
        mAdapter.notifyDataSetChanged();

        if (getDataBinding().checkAll.getVisibility() == View.VISIBLE) {
            if (getViewModel().getSelectedMemberSize() == 50 || getViewModel().getSelectedMemberSize() == getViewModel().getAllContacts().size()) {
                getDataBinding().cbUserAll.setChecked(true);
            } else {
                getDataBinding().cbUserAll.setChecked(false);
            }
        }
    }

    @Override
    public void onBackClick() {
        updateSelected();
    }

    @Override
    public void onCheckAllClick() {
        if (getViewModel().getSelectedMemberSize() >= 50) {
            ToastUtils.failure(R.string.schedule_max_look_busy_select_size_limit);
            return;
        }
        boolean checkAll = getViewModel().getAllContacts().size() != getViewModel().getSelectedMemberSize();
        new PointUtils.BuilderV4().name("calendar-group-member-select").params("icon_type", checkAll ? "1" : "0").point();
        getDataBinding().cbUserAll.setChecked(checkAll);
        getViewModel().checkAllMembers(checkAll);
        mAdapter.notifyDataSetChanged();
        hasCheckChanged = true;

    }
}