package com.twl.hi.schedule.gallery.view;

import androidx.databinding.ViewDataBinding;

import com.twl.hi.foundation.base.fragment.FoundationVMFragment;
import com.twl.hi.schedule.detail.view.ScheduleBusyActivity;
import com.twl.hi.schedule.detail.view.ScheduleDetailActivity;
import com.twl.hi.schedule.gallery.viewmodel.ScheduleBaseListViewModel;

import hi.kernel.Constants;

/**
 * 日程界面的展示基类
 */
public abstract class ScheduleListBaseFragment<D extends ViewDataBinding, M extends ScheduleBaseListViewModel> extends FoundationVMFragment<D, M> {

    public void jumpToScheduleDetailPage(String scheduleId, String calendarId, int scheduleType, String currDate, int from) {
        if (scheduleType != Constants.TYPE_SCHEDULE_SYS) {
            ScheduleDetailActivity.intentStart(activity, scheduleId, calendarId, currDate, from, "");
        }
    }

    public void showScheduleBusyDetail(String calendarId, int scheduleType, String targetDate, String beginTime, String endTime) {
        if (scheduleType != Constants.TYPE_SCHEDULE_SYS) {
            ScheduleBusyActivity.showBusy(activity, calendarId, targetDate, beginTime, endTime);
        }
    }
}
