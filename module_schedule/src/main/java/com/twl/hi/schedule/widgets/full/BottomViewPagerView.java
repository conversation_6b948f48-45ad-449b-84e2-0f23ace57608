package com.twl.hi.schedule.widgets.full;

import android.content.Context;
import android.content.Intent;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.twl.hi.basic.util.ThemeUtils;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.foundation.utils.ScheduleUtils;
import com.twl.hi.schedule.R;
import com.twl.hi.schedule.editor.view.ScheduleCreateEditActivity;
import com.twl.hi.schedule.widgets.ScheduleColorUtils;

import java.util.Date;

import hi.kernel.Constants;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.CommonUtils;
import lib.twl.common.util.LList;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.views.adapter.BaseQuickAdapter;
import lib.twl.common.views.adapter.BaseViewHolder;
import lib.twl.common.views.calendar.Calendar;
import lib.twl.common.views.calendar.FullCalendarView;
import lib.twl.common.views.calendar.Scheme;

/**
 * Created by ChaiJiangpeng on 2020/5/22
 * Describe:
 */
public class BottomViewPagerView extends FrameLayout {
    private TextView mTvCreate;
    private TextView tvEmpty;
    private RecyclerView mRecyclerView;
    private BottomViewPagerViewAdapter mAdapter;
    private Calendar mCalendar;
    private FullCalendarView.OnBottomViewPagerListener mListener;

    public BottomViewPagerView(Context context) {
        this(context, null);
    }

    public BottomViewPagerView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BottomViewPagerView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    private void initView(Context context) {
        View inflate = LayoutInflater.from(context).inflate(R.layout.schedule_cv_layout_item_full_schedule_bottom, null);
        mTvCreate = inflate.findViewById(R.id.tv_create);
        if (ThemeUtils.useNewTheme) {
            Drawable startDrawable = context.getDrawable(R.drawable.hd_icon_add);
            if (startDrawable != null) {
                startDrawable.setBounds(0, 0, QMUIDisplayHelper.dpToPx(16), QMUIDisplayHelper.dpToPx(16));
                mTvCreate.setCompoundDrawables(startDrawable, null, null, null);
                mTvCreate.setCompoundDrawablePadding(QMUIDisplayHelper.dpToPx(4));
            }
            mTvCreate.setText(context.getResources().getString(R.string.schedule_new_create_schedule));
        } else {
            SpannableStringBuilder spannableBuilder = new SpannableStringBuilder(context.getResources().getString(R.string.add));
            spannableBuilder.append(" ");
            spannableBuilder.append(context.getResources().getString(R.string.schedule_new_create_schedule));
            mTvCreate.setText(spannableBuilder);
        }
        mTvCreate.setTextColor(ThemeUtils.getThemeTextColorInt());
        tvEmpty = inflate.findViewById(R.id.tv_empty);
        tvEmpty.setMovementMethod(LinkMovementMethod.getInstance());
        SpannableStringBuilder spannableBuilderEmpty = new SpannableStringBuilder(context.getResources().getString(R.string.schedule_no_arrangements));
        spannableBuilderEmpty.append("，");
        ClickableSpan clickableSpan =
                new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        if (mCalendar == null) {
                            return;
                        }
                        Intent scheduleIntent = ScheduleCreateEditActivity.createIntent(getContext(),
                                "", ScheduleUtils.yMdFormat.format(new Date(mCalendar.getTimeInMillis())), Constants.CREATE_FROM_TAB);
                        AppUtil.startActivity(getContext(), scheduleIntent);
                        new PointUtils.BuilderV4()
                                .name("calendar-new-expose")
                                .params("source", 6)
                                .point();
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        super.updateDrawState(ds);
                        ds.setColor(ThemeUtils.getThemeTextColorInt());
                        ds.setUnderlineText(false);
                    }
                };
        String clickCreate = context.getResources().getString(R.string.schedule_click_create);
        SpannableString empty = new SpannableString(clickCreate);
        empty.setSpan(clickableSpan, 0, clickCreate.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableBuilderEmpty.append(empty);
        tvEmpty.setText(spannableBuilderEmpty);
        mRecyclerView = inflate.findViewById(R.id.recycler);
        mAdapter = new BottomViewPagerViewAdapter();
        mAdapter.bindToRecyclerView(mRecyclerView);
        mTvCreate.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mCalendar == null) {
                    return;
                }
                Intent scheduleIntent = ScheduleCreateEditActivity.createIntent(getContext(),
                        "", ScheduleUtils.yMdFormat.format(new Date(mCalendar.getTimeInMillis())), Constants.CREATE_FROM_TAB);
                AppUtil.startActivity(getContext(), scheduleIntent);
                new PointUtils.BuilderV4()
                        .name("calendar-new-expose")
                        .params("source", 6)
                        .point();
            }
        });
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                Scheme item = (Scheme) adapter.getItem(position);
                if (item == null) {
                    return;
                }
                if (mListener != null) {
                    mListener.onCalendarDetail(item);
                }
            }
        });
        addView(inflate);
    }

    public void setData(Calendar calendar, FullCalendarView.OnBottomViewPagerListener listener) {
        if (calendar == null) {
            return;
        }
        this.mCalendar = calendar;
        this.mListener = listener;
        boolean isEmpty = LList.isEmpty(mCalendar.getSchemes());
        tvEmpty.setVisibility(isEmpty ? VISIBLE : INVISIBLE);
        mRecyclerView.setVisibility(isEmpty ? INVISIBLE : VISIBLE);
        mTvCreate.setVisibility(isEmpty ? INVISIBLE : VISIBLE);
        mAdapter.setNewData(mCalendar.getSchemes());
    }

    public static class BottomViewPagerViewAdapter extends BaseQuickAdapter<Scheme, BaseViewHolder> {

        public BottomViewPagerViewAdapter() {
            super(R.layout.schedule_item_bottom_view_pager_view);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, Scheme item) {
            TextView tvTitle = helper.getView(R.id.tv_title);
            TextView tvTime = helper.getView(R.id.tv_time);
            TextView tvMeeting = helper.getView(R.id.tv_meeting);
            tvTitle.setText(item.getScheme());
            tvTime.setText(item.getOther());
            boolean isScheduleExpired = ScheduleUtils.isScheduleExpired(item.getTargetDate(), item.getEndTime());
            int textColor;
            if (!isScheduleExpired) {
                textColor = ScheduleColorUtils.getTextColor(item.getCalendarId(), item.getScheduleType(), item.getEchoType());
            } else {
                textColor = ScheduleColorUtils.getOverdueTextColor(item.getCalendarId(), item.getScheduleType(), item.getEchoType());
            }
            tvTitle.setTextColor(textColor);
            tvTime.setTextColor(textColor);
            tvMeeting.setTextColor(textColor);
            if (item.getEchoType() == Constants.TYPE_SCHEDULE_ECHO_REFUSE) {
                tvTitle.setPaintFlags(tvTitle.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                tvTime.setPaintFlags(tvTitle.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
            } else {
                tvTitle.setPaintFlags(tvTitle.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                tvTime.setPaintFlags(tvTitle.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
            }
            int color = ScheduleColorUtils.getHighlightColor(item.getCalendarId(), item.getScheduleType(), isScheduleExpired, item.getEchoType());
            Drawable drawable;
            if (item.getEchoType() == Constants.TYPE_SCHEDULE_ECHO_UNDETERMINED) {
                drawable = getCircleRingDrawable(color, 7, 1);
            } else {
                drawable = CommonUtils.getCircleDrawable(color, 7);
            }
            drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
            tvTitle.setCompoundDrawables(drawable, null, null, null);
            if (TextUtils.isEmpty(item.getMeetingInfo())) {
                tvMeeting.setVisibility(GONE);
            } else {
                tvMeeting.setVisibility(VISIBLE);
                tvMeeting.setText(item.getMeetingInfo());
            }
        }

        private static Drawable getCircleRingDrawable(@ColorInt int color, int sizeForDp, int radiusForDp) {
            GradientDrawable drawable = new GradientDrawable();
            int size = QMUIDisplayHelper.dpToPx(sizeForDp);
            int radius = QMUIDisplayHelper.dpToPx(radiusForDp);
            drawable.setSize(size, size);
            drawable.setShape(GradientDrawable.OVAL);
            drawable.setStroke(radius, color);
            return drawable;
        }
    }

}
