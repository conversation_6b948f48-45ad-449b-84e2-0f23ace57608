package com.twl.hi.schedule.freetime.view;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;
import androidx.databinding.library.baseAdapters.BR;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.activity.FoundationVMActivity;
import com.twl.hi.basic.dialog.CalendarSelectDialog;
import com.twl.hi.basic.util.ThemeUtils;
import com.twl.hi.foundation.model.BigBinder;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.foundation.utils.ScheduleUtils;
import com.twl.hi.schedule.R;
import com.twl.hi.schedule.databinding.ScheduleActivityScheduleLookBusyBinding;
import com.twl.hi.schedule.freetime.view.callback.ScheduleLookBusyCallback;
import com.twl.hi.schedule.freetime.view.rvadapter.ScheduleLookBusyAdapter;
import com.twl.hi.schedule.freetime.viewmodel.ScheduleLookBusyViewModel;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import hi.kernel.BundleConstants;
import hi.kernel.HiKernel;
import hi.kernel.RequestCodeConstants;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.LDate;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.views.calendar.Calendar;

/**
 * 日程中查看闲忙状态
 */
public class ScheduleLookBusyActivity extends FoundationVMActivity<ScheduleActivityScheduleLookBusyBinding, ScheduleLookBusyViewModel> implements ScheduleLookBusyCallback {

    /**
     * 查看闲忙的最大可容纳人数
     */
    private static final int MAX_ACCEPTABLE_NUMBER = 50;
    private static final String TAG = "ScheduleLookBusyActivity";
    private ScheduleLookBusyAdapter mAdapter;
    private String today;

    public static Intent createIntent(
            Context context,
            List<String> ids,
            String targetDate,
            String beginTime,
            String endTime,
            String scheduleId,
            boolean canChangeTime
    ) {
        Intent intent = new Intent(context, ScheduleLookBusyActivity.class);
        BigBinder bigBinder = new BigBinder((Serializable)ids);
        Bundle bundle = new Bundle();
        bundle.putBinder("binder", bigBinder);
        intent.putExtra("ids", bundle);
        if (TextUtils.isEmpty(targetDate)) {
            targetDate = ScheduleUtils.getCurrentDate();
        }
        intent.putExtra("targetDate", targetDate);
        if (TextUtils.isEmpty(beginTime) || TextUtils.isEmpty(endTime)) {
            String[] time = ScheduleUtils.getServerTime(new Date());
            beginTime = time[0];
            endTime = time[1];
        }
        intent.putExtra("beginTime", beginTime);
        intent.putExtra("endTime", endTime);
        intent.putExtra("changeTime", canChangeTime);
        intent.putExtra("scheduleId", scheduleId);
        return intent;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.schedule_activity_schedule_look_busy;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
        initData();
    }

    private void initData() {
        getViewModel().getBusyLiveData().observe(this, scheduleUserBusies -> {
            float size = Math.min(getViewModel().getParticipantIds().getValue().size(), 4.6f);
            int itemWidth = (int) ((QMUIDisplayHelper.getScreenWidth(ScheduleLookBusyActivity.this)
                    - QMUIDisplayHelper.dp2px(ScheduleLookBusyActivity.this, 55)) / size);
            mAdapter.setItemWidth(itemWidth);
            mAdapter.setNewData(getViewModel().getScheduleVisibleIds());
            List<String> sortIds = new ArrayList<>(getViewModel().getScheduleVisibleIds());
            getDataBinding().viewBusy.setData(sortIds, scheduleUserBusies, getViewModel().getTargetDate());
        });
        getDataBinding().viewBusy.setSelectTime(getViewModel().getBeginTime(), getViewModel().getEndTime());
        getViewModel().requestQueryBusyTime();
    }

    private void initView() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            String myUserId = HiKernel.getHikernel().getAccount().getUserId();
            List<String> allIds = null;
            try {
                allIds = (List<String>) ((BigBinder) getIntent().getBundleExtra("ids").getBinder("binder")).getData();
            } catch (Exception e) {
                TLog.error(TAG, e.getMessage());
            }

            if (allIds != null) {
                // 若有自己，永远排在第一个
                int myIndex = allIds.indexOf(myUserId);
                if (myIndex > 0) {
                    Collections.swap(allIds, 0, myIndex);
                }
            } else {
                return;
            }
            getViewModel().setTargetDate(bundle.getString("targetDate"));
            getViewModel().setBeginTime(bundle.getString("beginTime"));
            getViewModel().setEndTime(bundle.getString("endTime"));
            getViewModel().setCanChangeTime(bundle.getBoolean("changeTime"));
            getViewModel().setScheduleId(bundle.getString("scheduleId"));
            getViewModel().setParticipantIds(allIds.size() > MAX_ACCEPTABLE_NUMBER ? allIds.subList(0, MAX_ACCEPTABLE_NUMBER) : allIds);
            if (allIds.size() >= MAX_ACCEPTABLE_NUMBER) {
                getDataBinding().tvMaxTip.setVisibility(VISIBLE);
            } else {
                getDataBinding().tvMaxTip.setVisibility(GONE);
            }
        }
        today = ScheduleUtils.getCurrentDate();
        if (TextUtils.equals(today, getViewModel().getTargetDate())) {
            getDataBinding().viewBusy.setToday(true);
            getDataBinding().viewBusy.setShowNowText(true);
        } else {
            getDataBinding().viewBusy.setToday(false);
            getDataBinding().viewBusy.setShowNowText(false);
        }
        getDataBinding().tvTitle.setText(String.format("%s %s", LDate.getTitleDate(getViewModel().getTargetDate()), ScheduleUtils.dayOfWeek(getViewModel().getTargetDate())));
        getDataBinding().tvOk.setDefaultBgColor(ThemeUtils.getThemeColorInt());
        int size = getViewModel().getScheduleVisibleIds().size() > 4 ? 5 : (getViewModel().getScheduleVisibleIds().size() + 1);
        int itemWidth = (QMUIDisplayHelper.getScreenWidth(this) - QMUIDisplayHelper.dp2px(this, 55)) / size;
        mAdapter = new ScheduleLookBusyAdapter();
        mAdapter.setItemWidth(itemWidth);
        getDataBinding().recyclerView.setLayoutManager(new LinearLayoutManager(this, RecyclerView.HORIZONTAL, false));
        mAdapter.bindToRecyclerView(getDataBinding().recyclerView);
        getDataBinding().recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                getDataBinding().viewBusy.scrollToX(dx);
            }
        });

        getDataBinding().viewBusy.setOriginDay(getViewModel().getTargetDate());
        getDataBinding().viewBusy.grantEditPrivilege(getViewModel().isCanChangeTime().get());
        getDataBinding().viewBusy.setOnScrollListener((dx, dy) -> getDataBinding().recyclerView.scrollBy(dx, 0));

        getDataBinding().viewBusy.setTimeSelectListener((beginTime, endTime) -> {
            getViewModel().setBeginTime(beginTime);
            getViewModel().setEndTime(endTime);
        });
        getDataBinding().viewBusy.getSortArray().addOnListChangedCallback(new ObservableList.OnListChangedCallback<ObservableArrayList<Integer>>() {

            @Override
            public void onChanged(ObservableArrayList<Integer> sender) {
                mAdapter.setBusyArray(sender);
            }

            @Override
            public void onItemRangeChanged(ObservableArrayList<Integer> sender, int positionStart, int itemCount) {
                mAdapter.setBusyArray(sender);
            }

            @Override
            public void onItemRangeInserted(ObservableArrayList<Integer> sender, int positionStart, int itemCount) {
                mAdapter.setBusyArray(sender);
            }

            @Override
            public void onItemRangeMoved(ObservableArrayList<Integer> sender, int fromPosition, int toPosition, int itemCount) {
                mAdapter.setBusyArray(sender);
            }

            @Override
            public void onItemRangeRemoved(ObservableArrayList<Integer> sender, int positionStart, int itemCount) {
                mAdapter.setBusyArray(sender);
            }
        });

        getDataBinding().viewBusy.setSortIdsListener(new ScheduleBusyView.SortIdsListener() {
            @Override
            public void sortIds(List<String> sortIds) {
                getViewModel().setScheduleVisibleIds(sortIds);
                mAdapter.setNewData(sortIds);
            }
        });

        getDataBinding().viewBusy.setBusyListener(isBusy
                -> getDataBinding().tvSort.setVisibility(isBusy ? VISIBLE : GONE));
    }

    @Override
    public void chooseDate() {
        new CalendarSelectDialog.Builder(this)
                .setCancelable(true)
                .setCanceledOnTouchOutside(true)
                .setShowClear(false)
                .setTargetDate(ScheduleUtils.getCalendarByTargetDate(getViewModel().getTargetDate()))
                .setItemClickListener(this)
                .create()
                .show();
    }

    @Override
    public void selectTime() {
        Intent intent = new Intent();
        intent.putExtra("targetDate", getViewModel().getTargetDate());
        intent.putExtra("beginTime", getViewModel().getBeginTime());
        intent.putExtra("endTime", getViewModel().getEndTime());
        setResult(Activity.RESULT_OK, intent);
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickSortBusy() {
        // isSortBusy 原来的勾选状态
        boolean isSortBusy = getViewModel().getIsSortBusy().get();
        getViewModel().setIsSortBusy(!isSortBusy);
        getDataBinding().viewBusy.setSort(!isSortBusy);
        if (isSortBusy) {
            //变成非排序
            getViewModel().setScheduleVisibleIds(getViewModel().getScheduleVisibleDefaultSortIds());
            mAdapter.setNewData(getViewModel().getScheduleVisibleIds());
            List<String> scheduleVisibleIds = new ArrayList<>(getViewModel().getScheduleVisibleIds());
            getDataBinding().viewBusy.setEventIds(scheduleVisibleIds);
        } else {
            //变成排序
            getDataBinding().viewBusy.sort();
        }
        new PointUtils.BuilderV4()
                .name("calendar-checkbusy-conflict-click")
                .params("type", isSortBusy ? "2" : "1")
                .point();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != RESULT_OK || data == null) {
            return;
        }
        if (requestCode == RequestCodeConstants.REQUEST_CODE_SELECT_CONTACT) {
            ArrayList<String> allIds = (ArrayList<String>) data.getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE);
            getViewModel().setParticipantIds(allIds);
            getViewModel().requestQueryBusyTime();
        }

    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    public void onCalendarSelectPositiveItemClick(Dialog dialog, View view, Calendar calendar) {
        dialog.dismiss();
        getViewModel().setTargetDate(calendar.toString());
        getDataBinding().tvTitle.setText(String.format("%s %s", LDate.getTitleDate(calendar.toString()), ScheduleUtils.dayOfWeek(calendar.toString())));
        if (TextUtils.equals(today, getViewModel().getTargetDate())) {
            getDataBinding().viewBusy.setToday(true);
            getDataBinding().viewBusy.setShowNowText(true);
        } else {
            getDataBinding().viewBusy.setToday(false);
            getDataBinding().viewBusy.setShowNowText(false);
        }
        getViewModel().requestQueryBusyTime();
    }

    @Override
    public void onCalendarSelectNegativeItemClick(Dialog dialog, View view) {

    }
}
