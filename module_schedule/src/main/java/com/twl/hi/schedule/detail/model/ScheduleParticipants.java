package com.twl.hi.schedule.detail.model;

import java.util.List;

public class ScheduleParticipants {
    public final int totalCount;
    public final List<GroupSample> groups;
    public final List<ContactSample> contacts;
    public final List<DeterminedMember> determinedMembers;

    public ScheduleParticipants(int totalCount, List<GroupSample> groups, List<ContactSample> contacts, List<DeterminedMember> determinedMembers) {
        this.totalCount = totalCount;
        this.groups = groups;
        this.contacts = contacts;
        this.determinedMembers = determinedMembers;
    }

    public static class DeterminedMember {
        public final String contactId;
        public final AcceptanceDecision acceptanceDecision;

        public DeterminedMember(String contactId, AcceptanceDecision acceptanceDecision) {
            this.contactId = contactId;
            this.acceptanceDecision = acceptanceDecision;
        }
    }

    public enum AcceptanceDecision {
        UNDECIDED, ACCEPTED, REFUSED
    }
}
