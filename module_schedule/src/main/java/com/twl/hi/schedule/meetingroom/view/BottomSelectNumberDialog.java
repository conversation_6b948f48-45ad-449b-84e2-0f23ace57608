package com.twl.hi.schedule.meetingroom.view;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.BottomSheetBehaviorBaseDialog;
import com.twl.hi.basic.util.ThemeUtils;
import com.twl.hi.schedule.R;
import com.twl.hi.schedule.databinding.ScheduleDialogNumberSelectBinding;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;

import lib.twl.common.base.BaseViewModel;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.util.ToastUtils;

/**
 * 底部弹出的多选框
 */
public class BottomSelectNumberDialog extends BottomSheetBehaviorBaseDialog<ScheduleDialogNumberSelectBinding, BaseViewModel> {

    private static final String TAG = "BottomSelectNumberDialog";
    private static final String ARG_REQUEST_CODE = "REQUEST_CODE";
    private static final String ARG_TITLE = "TITLE";
    private static final String ARG_CONTENT = "CONTENT";
    private static final String ARG_CHECKED_INDICES = "CHECKED_INDICES";
    private static final String ARG_MUTEX_INDICES = "MUTEX_INDICES";

    private final Set<Integer> mCheckedIndices = new TreeSet<>();
    private final Set<Integer> mMutexIndices = new HashSet<>();

    private int mRequestCode;
    private String mTitle;
    private String[] mContent;
    private ContentAdapter mAdapter;

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (!(getTargetFragment() instanceof OnItemCheckListener)
                && !(context instanceof OnItemCheckListener)) {
            TLog.error(TAG, "未设置targetFragment 或者 targetFragment及宿主 context 没有实现 " + OnItemCheckListener.class.getCanonicalName());
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        mAdapter = new ContentAdapter();
        mRequestCode = requireArguments().getInt(ARG_REQUEST_CODE);
        mTitle = requireArguments().getString(ARG_TITLE);
        mContent = requireArguments().getStringArray(ARG_CONTENT);
        mCheckedIndices.clear();
        int[] checkedIndices = requireArguments().getIntArray(ARG_CHECKED_INDICES);
        if (checkedIndices != null) {
            for (int index : checkedIndices) {
                mCheckedIndices.add(index);
            }
        }
        mMutexIndices.clear();
        int[] mutexIndices = requireArguments().getIntArray(ARG_MUTEX_INDICES);
        if (mutexIndices != null) {
            for (int index : mutexIndices) {
                mMutexIndices.add(index);
            }
        }
        fillInContent();
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.schedule_dialog_number_select;
    }

    @Override
    protected void initFragment() {}

    @Override
    public int getCallbackVariable() {
        return -1;
    }

    @Override
    public Object getCallback() {
        return null;
    }

    @Override
    public int getBindingVariable() {
        return -1;
    }

    @Override
    public int getBottomSheetLayoutParamsHeight() {
        return QMUIDisplayHelper.dpToPx(360);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        getDataBinding().title.setText(mTitle);
        getDataBinding().content.setItemAnimator(null);
        getDataBinding().content.setAdapter(mAdapter);
        getDataBinding().negative.setOnClickListener(v -> dismiss());
        getDataBinding().positive.setDefaultBgColor(ThemeUtils.getThemeColorInt());
        getDataBinding().positive.setOnClickListener(v -> confirm());
    }

    public void showDialog(FragmentManager fragmentManager, int requestCode, String title, String[] content, int[] checkedIndices, int[] mutexIndices) {
        Bundle bundle = new Bundle();
        bundle.putInt(ARG_REQUEST_CODE, requestCode);
        bundle.putString(ARG_TITLE, title);
        bundle.putStringArray(ARG_CONTENT, content);
        bundle.putIntArray(ARG_CHECKED_INDICES, checkedIndices);
        bundle.putIntArray(ARG_MUTEX_INDICES, mutexIndices);
        setArguments(bundle);
        show(fragmentManager, TAG);
    }

    private void confirm() {
        if (mCheckedIndices.isEmpty()) {
            ToastUtils.failure(R.string.schedule_none_selected);
            return;
        }

        int[] result = new int[mCheckedIndices.size()];
        int i = 0;
        for (Integer index : mCheckedIndices) {
            result[i++] = index;
        }
        if (getTargetFragment() instanceof OnItemCheckListener) {
            ((OnItemCheckListener) getTargetFragment()).onCheck(mRequestCode, result);
        }else if (requireContext() instanceof OnItemCheckListener) {
            ((OnItemCheckListener) requireContext()).onCheck(mRequestCode, result);
        }
        dismiss();
    }

    private void fillInContent() {
        List<ContentWithState> items = new ArrayList<>(mContent.length);
        for (int i = 0; i < mContent.length; i++) {
            items.add(new ContentWithState(mContent[i], mCheckedIndices.contains(i)));
        }
        mAdapter.submitList(items);
    }

    private void togglePosition(int position) {
        if (mMutexIndices.contains(position)) {
            mCheckedIndices.clear();
        } else {
            mCheckedIndices.removeAll(mMutexIndices);
        }

        if (!mCheckedIndices.remove(position)) {
            mCheckedIndices.add(position);
        }
        fillInContent();
    }

    private static class ContentWithState {
        public final String title;
        public final boolean checked;

        public ContentWithState(String title, boolean checked) {
            this.title = title;
            this.checked = checked;
        }
    }

    private class ContentAdapter extends ListAdapter<ContentWithState, ViewHolder> {

        protected ContentAdapter() {
            super(new DiffUtil.ItemCallback<ContentWithState>() {
                @Override
                public boolean areItemsTheSame(@NonNull ContentWithState oldItem, @NonNull ContentWithState newItem) {
                    return Objects.equals(oldItem.title, newItem.title);
                }

                @Override
                public boolean areContentsTheSame(@NonNull ContentWithState oldItem, @NonNull ContentWithState newItem) {
                    return oldItem.checked == newItem.checked;
                }
            });
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            int horizontalPadding = QMUIDisplayHelper.dpToPx(20);
            int verticalPadding = QMUIDisplayHelper.dpToPx(8);
            TextView itemView = new TextView(parent.getContext());
            itemView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
            itemView.setPadding(horizontalPadding, verticalPadding, horizontalPadding, verticalPadding);
            itemView.setTextColor(parent.getContext().getColor(R.color.color_0D0D1A));
            itemView.setTextSize(16);
            return new ViewHolder(itemView);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            ContentWithState item = getItem(position);
            TextView itemView = (TextView) holder.itemView;
            Context context = itemView.getContext();
            itemView.setText(item.title);
            if (item.checked) {
                itemView.setTextColor(ThemeUtils.getThemeTextColorInt());
                itemView.setCompoundDrawablesWithIntrinsicBounds(0, 0, ThemeUtils.useNewTheme ? R.drawable.hd_icon_check : R.drawable.hd_icon_check_old, 0);
            } else {
                itemView.setTextColor(context.getColor(R.color.color_0D0D1A));
                itemView.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
            }
        }
    }

    private class ViewHolder extends RecyclerView.ViewHolder {

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            itemView.setOnClickListener(v -> togglePosition(getAdapterPosition()));
        }
    }

    public interface OnItemCheckListener {
        void onCheck(int requestCode, int[] checkedIndices);
    }
}
