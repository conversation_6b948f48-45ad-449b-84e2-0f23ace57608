package com.twl.hi.schedule.freetime.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;

import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.schedule.freetime.model.remote.MoveUserTopRequest;
import com.twl.hi.schedule.freetime.model.remote.ScheduleGroupUserBusyRequest;
import com.twl.hi.schedule.freetime.model.remote.ScheduleUserBusyResponse;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import lib.twl.common.util.ExecutorFactory;

/**
 * Created by ChaiJiangpeng on 2020/10/15
 * Describe:
 */
public class GroupInfoLookScheduleViewModel extends BaseLookScheduleViewModel {

    private static final String TAG = "GroupInfoLookScheduleViewModel";

    private String groupId = "";

    private int groupMemberNumber = 0;
    /**
     * server端数据返回之前不允许跳转
     */
    private final MutableLiveData<Boolean> mEnableChooseMember = new MutableLiveData<>(true);

    public GroupInfoLookScheduleViewModel(Application application) {
        super(application);
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupId() {
        return groupId;
    }

    public MutableLiveData<Boolean> getEnableChooseMember() {
        return mEnableChooseMember;
    }

    /**
     * 请求群聊闲忙数据
     */
    public void requestGroupBusyTime() {
        setShowProgressBar("", false);
        ExecutorFactory.execWorkTask(new Runnable() {
            @Override
            public void run() {
                groupMemberNumber = ServiceManager.getInstance().getContactService().getGroupContactIds("", groupId).size();

                ScheduleGroupUserBusyRequest request = new ScheduleGroupUserBusyRequest(new BaseApiRequestCallback<ScheduleUserBusyResponse>() {
                    @Override
                    public void onSuccess(ApiData<ScheduleUserBusyResponse> data) {
                        mUnDisclosureUser.clear();
                        List<String> participantIds = new ArrayList<>();
                        if (data.resp != null && data.resp.userResult != null) {
                            for (ScheduleUserBusyResponse.ScheduleUserBusy scheduleUserBusy : data.resp.userResult) {
                                participantIds.add(scheduleUserBusy.userId);
                                if (scheduleUserBusy.visible == 1) {
                                    mUnDisclosureUser.add(scheduleUserBusy.userId);
                                }
                            }
                            setParticipantIds(participantIds);
                            busyLiveData.postValue(data.resp.userResult);
                        } else {
                            setParticipantIds(Collections.EMPTY_LIST);
                            busyLiveData.postValue(Collections.EMPTY_LIST);
                        }
                    }

                    @Override
                    public void onComplete() {
                        hideShowProgressBar();
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {

                    }
                });
                request.targetDate = targetDate;
                request.groupId = groupId;
                HttpExecutor.execute(request);
            }
        });
    }

    /**
     * 置顶人员
     *
     * @param item
     * @param success
     */
    public void moveUserTop(String item, Function1<? super Boolean, ? super Unit> success) {
        if (TextUtils.isEmpty(getGroupId())) {
            return;
        }
        MoveUserTopRequest request = new MoveUserTopRequest(new BaseApiRequestCallback<HttpResponse>() {
            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
            }

            @Override
            public void onComplete() {
                success.invoke(true);
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.groupId = getGroupId();
        request.userId = item;
        HttpExecutor.execute(request);
    }

    public int getGroupMemberNumber() {
        return groupMemberNumber;
    }
}
