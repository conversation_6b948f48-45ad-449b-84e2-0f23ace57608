package com.twl.hi.schedule.preference.model;

/**
 * <AUTHOR>
 * @date 2019-11-21.
 */
public class DrawerItemBean extends DrawerItemBaseBean {

    private long id;
    private boolean isChecked = true;//item是否被选中
    private boolean isOpen; //是否展开了

    public DrawerItemBean(long id, String description) {
        super(DrawerItemBaseBean.GROUP_ITEM, description);
        this.id = id;
    }

    public DrawerItemBean(int groupId, long id, String description) {
        super(groupId, description);
        this.id = id;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public boolean isChecked() {
        return isChecked;
    }

    public void setChecked(boolean checked) {
        isChecked = checked;
    }

    public boolean isOpen() {
        return isOpen;
    }

    public void setOpen(boolean open) {
        isOpen = open;
    }
}
