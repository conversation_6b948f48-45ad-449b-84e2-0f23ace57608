package com.twl.hi.schedule.gallery.view;

import android.content.Intent;
import android.graphics.RectF;
import android.text.TextUtils;
import android.util.Log;

import androidx.databinding.library.baseAdapters.BR;

import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.ScheduleBean;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.foundation.utils.ScheduleUtils;
import com.twl.hi.schedule.R;
import com.twl.hi.schedule.databinding.ScheduleFragmentScheduleShowInWeekBinding;
import com.twl.hi.schedule.editor.view.ScheduleCreateEditActivity;
import com.twl.hi.schedule.gallery.viewmodel.ScheduleWeekViewModel;
import com.twl.hi.schedule.widgets.DateTimeInterpreter;
import com.twl.hi.schedule.widgets.EnhancedWeekView;
import com.twl.hi.schedule.widgets.MonthLoader;
import com.twl.hi.schedule.widgets.ScheduleViewBaseView;
import com.twl.utils.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import hi.kernel.Constants;
import kz.log.TLog;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.CalendarUtil;
import lib.twl.common.views.calendar.Calendar;
import lib.twl.common.views.calendar.CalendarSysTimeBean;
import lib.twl.common.views.calendar.Scheme;

/**
 * 日历界面的周视图
 */
public class ScheduleShowInWeekFragment extends ScheduleListBaseFragment<ScheduleFragmentScheduleShowInWeekBinding, ScheduleWeekViewModel>
        implements EnhancedWeekView.EventClickListener, MonthLoader.MonthChangeListener, EnhancedWeekView.EventAddClickListener, EnhancedWeekView.ScrollListener {

    private static final String TAG = "ScheduleShowInWeekFragm";
    private Calendar selectedCalendar = new Calendar();
    private SimpleDateFormat weekdayNameFormat = new SimpleDateFormat("EEEEE", Locale.getDefault());
    private SimpleDateFormat format = new SimpleDateFormat("dd", Locale.getDefault());

    @Override
    public int getContentLayoutId() {
        return R.layout.schedule_fragment_schedule_show_in_week;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        initWeekView();
        initData();
    }

    private void initData() {
        ServiceManager.getInstance().getScheduleService().getSelectCalendarLiveData().observe(this, calendar -> {
            if (calendar == null) {
                return;
            }
            if (calendar.equals(selectedCalendar)) {
                return;
            }
            selectedCalendar.cloneCalendar(calendar);
            getDataBinding().weekView.goToDate(CalendarUtil.getSysCalendar(selectedCalendar));
            getViewModel().loadScheduleAround(selectedCalendar.getYear(), selectedCalendar.getMonth());
        });
        getViewModel().getRefreshLiveData().observe(this, refresh -> {
            if (refresh != null && refresh) {
                getDataBinding().weekView.notifyDatasetChanged();
            }
        });
        ServiceManager.getInstance().getScheduleService().getSelectedF2TabLiveData().observe(this, gotoCurrent -> {
            if (gotoCurrent) {
                ServiceManager.getInstance().getScheduleService().getSelectedF2TabLiveData().postValue(false);
                selectedCalendar.cloneCalendar(Calendar.getCalendarFromTime(System.currentTimeMillis()));
                ServiceManager.getInstance().getScheduleService().getSelectCalendarLiveData().setValue(selectedCalendar);
                getDataBinding().weekView.goToDate(CalendarUtil.getSysCalendar(selectedCalendar));
                TLog.info(TAG, "单/多日视图 回今天");
            }
        });

        ServiceManager.getInstance().getSettingService().getDateChangedLiveData().observe(this, updateSchedule -> {
            if (updateSchedule != null && updateSchedule) {
                ServiceManager.getInstance().getScheduleService().getSelectedF2TabLiveData().postValue(true);
            }
        });

        ServiceManager.getInstance().getSettingService().getWeekStartLiveData().observe(this, integer -> {
            if (integer != null && integer > 0) {
                ServiceManager.getInstance().getSettingService().getWeekStartLiveData().setValue(0);
                getDataBinding().weekView.setFirstDayOfWeek(integer);
            }
        });

        ServiceManager.getInstance().getScheduleService().getShowLunarLiveData().observe(this, showLunar -> {
            getViewModel().setShowLunar(showLunar);
            getDataBinding().weekView.setShowLunar(showLunar);
            getDataBinding().weekView.notifyDatasetChanged();
        });

        ServiceManager.getInstance().getScheduleService().getLunarSchemeLiveData().observe(this, map -> {
            getViewModel().setLunarMap(map);
            getDataBinding().weekView.notifyDatasetChanged();
        });
    }

    private void initWeekView() {
        getDataBinding().weekView.setFirstDayOfWeek(ServiceManager.getInstance().getSettingService().getWeekStart());
        getDataBinding().weekView.setOnEventClickListener(this);
        getDataBinding().weekView.setMonthChangeListener(this);
        getDataBinding().weekView.setOnEventAddClickListener(this);
        getDataBinding().weekView.setScrollListener(this);
        setupDateTimeInterpreter();
    }

    private void setupDateTimeInterpreter() {

        getDataBinding().weekView.setDateTimeInterpreter(new DateTimeInterpreter() {
            @Override
            public String interpretDate(java.util.Calendar date) {
                String weekday = weekdayNameFormat.format(date.getTime());
                return weekday.toUpperCase() + " " + format.format(date.getTime());
            }

            @Override
            public String interpretTime(int hour, int minute) {
                if (hour >= 24) {
                    //表示晚上24点正
                    return "24:00";
                }
                java.util.Calendar calendar = java.util.Calendar.getInstance();
                calendar.set(java.util.Calendar.HOUR_OF_DAY, hour);
                calendar.set(java.util.Calendar.MINUTE, minute);
                try {
                    return ScheduleViewBaseView.timeFormat.format(calendar.getTime());
                } catch (Exception e) {
                    e.printStackTrace();
                    return "";
                }
            }

            @Override
            public Scheme interpretScheme(java.util.Calendar date) {
                if (getViewModel().getHiSchemeHashMap() != null) {
                    String scheduleFormartDate = ScheduleUtils.getScheduleFormartDate(date);
                    return getViewModel().getHiSchemeHashMap().get(scheduleFormartDate);
                }
                return null;
            }

            @Override
            public String interpretLunar(java.util.Calendar date) {
                if (getViewModel().isShowLunar() && getViewModel().getLunarMap() != null) {
                    String scheduleFormartDate = ScheduleUtils.getScheduleFormartDate(date);
                    CalendarSysTimeBean timeBean = getViewModel().getLunarMap().get(scheduleFormartDate);
                    if (timeBean != null) {
                        String lunar;
                        if (!TextUtils.isEmpty(timeBean.lunarFestival)) {
                            lunar = timeBean.lunarFestival;
                        } else if (!TextUtils.isEmpty(timeBean.solarFestival)) {
                            lunar = timeBean.solarFestival;
                        } else if (!TextUtils.isEmpty(timeBean.name)) {
                            lunar = timeBean.name;
                        } else {
                            if (TextUtils.equals(timeBean.dayCn, "初一")) {
                                lunar = timeBean.monthCn;
                            } else {
                                lunar = timeBean.dayCn;
                            }
                        }
                        return lunar;
                    }
                }
                return null;
            }
        });
    }

    @Override
    public List<ScheduleBean> onMonthChange(int newYear, int newMonth) {
        return getViewModel().getWeekViewEvent(newYear, newMonth);
    }

    @Override
    public List<ScheduleBean> onPreMonthChange(int newYear, int newMonth) {
        return getViewModel().getWeekViewEvent(newYear, newMonth);
    }

    @Override
    public List<ScheduleBean> onNextMonthChange(int newYear, int newMonth) {
        return getViewModel().getWeekViewEvent(newYear, newMonth);
    }

    @Override
    public void onEventClick(ScheduleBean event, RectF eventRect) {
        if (StringUtils.isNotEmpty(event.getScheduleId())) {
            jumpToScheduleDetailPage(event.getScheduleId(), event.getCalenderGroupId(), event.getType(), event.getTargetDate(), Constants.CREATE_FROM_DEFAULT);
        } else {
            showScheduleBusyDetail(event.getCalenderGroupId(), event.getType(), event.getTargetDate(), event.getBeginTime(), event.getEndTime());
        }
    }

    @Override
    public void onEventAddClick(java.util.Calendar calendar, String start, String end) {
        if (calendar == null) {
            return;
        }
        Intent scheduleIntent = ScheduleCreateEditActivity.createIntent(
                getContext(), ScheduleUtils.yMdFormat.format(new Date(calendar.getTimeInMillis())), start, end, Constants.CREATE_FROM_TIME_SELECTION);
        AppUtil.startActivity(getContext(), scheduleIntent);
        new PointUtils.BuilderV4()
                .name("calendar-new-expose")
                .params("source", 5)
                .point();
    }

    @Override
    public void onFirstVisibleDayChanged(java.util.Calendar newFirstVisibleDay, java.util.Calendar oldFirstVisibleDay) {
        getViewModel().loadScheduleAround(newFirstVisibleDay.get(java.util.Calendar.YEAR), newFirstVisibleDay.get(java.util.Calendar.MONTH) + 1);
    }

    @Override
    public void onSelectedDaeChange(java.util.Calendar selectedDate) {
        selectedCalendar.cloneCalendar(Calendar.getCalendarFromTime(selectedDate.getTimeInMillis()));
        ServiceManager.getInstance().getScheduleService().getSelectCalendarLiveData().setValue(selectedCalendar);
    }
}
