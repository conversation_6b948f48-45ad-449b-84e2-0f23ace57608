package com.twl.hi.schedule.freetime.view.rvadapter;

import android.text.TextUtils;
import android.view.ViewGroup;

import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.schedule.R;
import com.twl.hi.schedule.databinding.ScheduleItemAdapterScheduleLookBusyUserBinding;
import com.twl.hi.schedule.freetime.view.callback.ScheduleItemClickCallback;
import com.twl.hi.schedule.freetime.view.callback.ScheduleLookBusyUserCallback;
import com.twl.hi.schedule.freetime.viewmodel.BaseLookScheduleViewModel;
import com.twl.hi.schedule.freetime.viewmodel.UserInfoLookScheduleViewModel;

import java.util.HashSet;
import java.util.Set;

import lib.twl.common.adapter.BaseDataBindingAdapter;
import lib.twl.common.adapter.BaseDataBindingViewHolder;

/**
 * Created by ChaiJiangpeng on 2020/10/19
 * Describe:
 */
public class ScheduleLookBusyUserAdapter extends BaseDataBindingAdapter<String, ScheduleItemAdapterScheduleLookBusyUserBinding> {
    private int itemWidth;
    private final BaseLookScheduleViewModel viewModel;
    private final ScheduleLookBusyUserCallback callback;

    private final Set<String> mBusySet = new HashSet<>();
    private Set<String> mUnDisclosureUser = new HashSet<>();

    private ScheduleItemClickCallback mScheduleItemClickCallback;
    private String mSelectedTop = "";
    private boolean mSelectedMode = false;
    private boolean isGroupSchedule = false;

    public ScheduleLookBusyUserAdapter(BaseLookScheduleViewModel viewModel, ScheduleLookBusyUserCallback callback) {
        super(R.layout.schedule_item_adapter_schedule_look_busy_user, null);
        this.viewModel = viewModel;
        this.callback = callback;
    }

    public void setGroupSchedule(boolean groupSchedule) {
        isGroupSchedule = groupSchedule;
    }

    public void setScheduleItemClickCallback(ScheduleItemClickCallback scheduleItemClickCallback) {
        mScheduleItemClickCallback = scheduleItemClickCallback;
    }

    public String getSelectedTop() {
        return mSelectedTop;
    }

    public void setSelectedTop(String selectedTop) {
        mSelectedTop = selectedTop;
    }

    public boolean isSelectedMode() {
        return mSelectedMode;
    }

    public void setSelectedMode(boolean selectedMode) {
        mSelectedMode = selectedMode;
    }

    @Override
    protected void bind(BaseDataBindingViewHolder<ScheduleItemAdapterScheduleLookBusyUserBinding> helper, ScheduleItemAdapterScheduleLookBusyUserBinding binding, String item) {
        ViewGroup.LayoutParams layoutParams = binding.getRoot().getLayoutParams();
        layoutParams.width = itemWidth;
        binding.getRoot().setLayoutParams(layoutParams);
        Contact contactById = ServiceManager.getInstance().getContactService().getContactById(item);
        binding.setContact(contactById);
        binding.setViewModel(viewModel);
        binding.setIsSelf(helper.getAdapterPosition() == 0);
        if (viewModel.hasObserved().get() != null) {
            binding.tvSubscribeStatus.setOnClickListener((v) -> callback.subscribe(!viewModel.hasObserved().get()));
        }
        binding.setIsBusy(mBusySet.contains(item));
        binding.setIsUnDisclosure(mUnDisclosureUser.contains(item));
        binding.setItem(item);
        binding.setIsGroupSchedule(isGroupSchedule);
        binding.setIsTopSelectItem(TextUtils.equals(mSelectedTop, item) && mSelectedMode && helper.getAdapterPosition() != 0);
        binding.setCallback(mScheduleItemClickCallback);
    }

    public void setItemWidth(int itemWidth) {
        this.itemWidth = itemWidth;
    }

    public void setBusySet(Set<String> array) {
        mBusySet.clear();
        mBusySet.addAll(array);
        notifyDataSetChanged();
    }

    public void setUnDisclosureUser(Set<String> unDisclosureUser) {
        this.mUnDisclosureUser = unDisclosureUser;
    }
}
