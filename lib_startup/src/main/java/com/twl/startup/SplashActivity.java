package com.twl.startup;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;

import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import lib.twl.common.activity.LActivity;

public class SplashActivity extends LActivity {
    private static final String TAG = "Splash--->SplashActivity";
    private boolean mComplete = false;
    private AtomicInteger mThreshold = new AtomicInteger(0);

    public boolean isComplete() {
        return mComplete;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        TLog.info(TAG, "onCreate " + this);
        StartupCore.getInstance().addSplash(this);
        // 如果是在Startup执行done方法之后启动的，那么手动调用一次onComplete
        if (!StartupCore.getInstance().isInitializing()) {
            onComplete();
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        TLog.info(TAG, "onNewIntent " + this);
    }

    @Override
    protected void onResume() {
        TLog.info(TAG, "onResume " + this);
        super.onResume();
    }

    @Override
    protected void onPause() {
        TLog.info(TAG, "onPause " + this);
        super.onPause();
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            StartupCore.getInstance().startBackground();
        }
    }

    @Override
    protected void onDestroy() {
        TLog.info(TAG, "onDestroy " + this);
        StartupCore.getInstance().removeSplash(this);
        super.onDestroy();
    }

    public void onComplete() {
        TLog.info(TAG, "onComplete() called " + this);
        if (mThreshold.incrementAndGet() >= getThreshold()) {
            complete();
        }
    }

    public void onCompleteFromStartup() {
        TLog.info(TAG, "onComplete() called " + this);
        if (mThreshold.incrementAndGet() >= getThreshold()) {
            complete();
        }
    }

    protected int getThreshold() {
        return 1;
    }

    // 禁用返回键，防止影响启动流程
    @Override
    public void onBackPressed() {
    }

    protected void complete() {
        TLog.info(TAG, "complete " + StartupCore.getInstance().getHackActivities().size());
        if (!mComplete) {
            mComplete = true;
            if (isFinishing()) {
                TLog.info(TAG, "complete isFinishing");
                onBackPressed();
                return;
            }
            setResult(StartupConstant.RESULT_CODE);
            List<HackActivity> list = StartupCore.getInstance().getHackActivities();
            if (list.size() == 1) {
                list.get(0).recreate();
                TLog.info(TAG, "one activity match!");
            } else {
                resetLaunchPoint();
                List<Activity> originalMain = new ArrayList<>();
                List<Activity> originalOther = new ArrayList<>();
                for (int i=0; i<list.size(); i++) {
                    HackActivity activity = list.get(i);
                    if (TextUtils.equals(activity.mOriginal, "com.twl.hi.main.MainTabActivity")) {
                        originalMain.add(activity);
                    } else {
                        originalOther.add(activity);
                    }
                    TLog.info(TAG, "iterator Hack " + activity.mOriginal);
                }

                if (originalOther.size() == 1) {
                    originalOther.get(0).recreate();
                    for (Activity activity : originalMain) {
                        activity.finish();
                    }
                } else if (originalOther.size() > 0) {
                    boolean hasRecreate = false;
                    for (int i=originalOther.size()-1; i>=0; i--) {
                        Activity activity = originalOther.get(i);
                        if (!hasRecreate) {
                            activity.recreate();
                            hasRecreate = true;
                        } else {
                            activity.finish();
                        }
                    }
                    for (Activity activity : originalMain) {
                        activity.finish();
                    }
                } else {
                    boolean hasRecreate = false;
                    for (int i=originalMain.size()-1; i>=0; i--) {
                        Activity activity = originalMain.get(i);
                        if (!hasRecreate) {
                            activity.recreate();
                            hasRecreate = true;
                        } else {
                            activity.finish();
                        }
                    }
                }
            }
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    TLog.info(TAG, "finish SplashActivity");
                    SplashActivity.this.finish();
                    overridePendingTransition(R.anim.fade_in_fast, R.anim.fade_out_fast);
                }
            }, 50);
        }
    }

    protected void resetLaunchPoint() {
    }
}
