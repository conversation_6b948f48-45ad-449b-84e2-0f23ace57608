package com.twl.startup;

import android.app.Application;
import android.os.SystemClock;

import lib.twl.common.util.ExecutorFactory;

/**
 * Startup是启动管理类
 */

public final class Startup {
    private static final String TAG = "Startup";
    private static StartupLifecycle gStartupLifecycle = null;
    private static boolean sMainProcess = false;
    private static StartupCore core;

    private Startup() {
    }

    public static void initialize(Application application, String processName, StartupStrategy startupStrategy) {
        String packageName = application.getPackageName();
        if (!processName.equals(packageName)) {
            StartupLog.d(TAG, "initialize other process : %s", processName);
            gStartupLifecycle = startupStrategy.mStartupLifecycle;
        } else {
            sMainProcess = true;
            core = startupStrategy.build();
            long time = SystemClock.elapsedRealtime();
            boolean isSupport = core.init(application);
            StartupLog.e(TAG, "initialize: [%d]", SystemClock.elapsedRealtime() - time);
            if (!isSupport) {
                StartupLog.e(TAG, "initialize: The device does not susport startup!");
                gStartupLifecycle = startupStrategy.mStartupLifecycle;
            }
        }
    }

    public static String getFirstStartActivityName() {
        if (core != null) {
            return core.getFirstStartActivityName();
        }
        return "";
    }

    public static void onAppCreate() {
        StartupLifecycle startupLifecycle = gStartupLifecycle;
        if (startupLifecycle != null) {
            startupLifecycle.onAppCreate(sMainProcess);
            // 低版本会走这，需要在子线程执行，否则会触发ANR
            ExecutorFactory.execWorkTask(new Runnable() {
                @Override
                public void run() {
                    startupLifecycle.onBackground();
                }
            });
        } else {
            StartupCore.getInstance().onAppCreate();
        }
    }


    public final static class StartupStrategy {
        private Class<? extends SplashActivity> mSplashActivityClass;
        private StartupLifecycle mStartupLifecycle;

        public StartupStrategy(Class<? extends SplashActivity> splashActivityClass) {
            mSplashActivityClass = splashActivityClass;
        }


        public StartupStrategy withLifecycle(StartupLifecycle startupLifecycle) {
            mStartupLifecycle = startupLifecycle;
            return this;
        }

        StartupCore build() {
            StartupCore startupCore = StartupCore.getInstance();
            startupCore.setStartupLifecycle(mStartupLifecycle);
            startupCore.setSpalashClass(mSplashActivityClass);
            return startupCore;
        }
    }
}
