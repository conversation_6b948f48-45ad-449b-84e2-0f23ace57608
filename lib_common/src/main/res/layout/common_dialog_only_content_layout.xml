<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:padding="12dp">

    <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_corner_12_color_white"
            android:orientation="vertical"
            android:layout_margin="12dp">

        <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginStart="24dp"
                android:layout_marginEnd="24dp"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginTop="24dp"
                android:textColor="@color/color_818188"
                android:textSize="16sp"
                tools:text="我是内容" />

        <View
                android:id="@+id/split"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="24dp"
                app:layout_constraintTop_toBottomOf="@id/tv_content"
                android:background="@color/color_E5E5E9" />

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/split"
                >

            <TextView
                    android:id="@+id/tv_cancel_common"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:textColor="@color/color_9999A3"
                    android:textSize="17sp"
                    android:gravity="center"
                    android:layout_weight="1"
                    tools:text="按钮"
                    />

            <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/color_E5E5E9"
                    android:visibility="gone"/>

            <TextView
                    android:id="@+id/tv_sure_common"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:textColor="@color/color_5D68E8"
                    android:textSize="17sp"
                    tools:text="按钮"
                    android:layout_weight="1"/>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</LinearLayout>