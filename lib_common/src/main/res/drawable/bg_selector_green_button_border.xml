<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@drawable/bg_green_button_border_press" android:state_enabled="true" android:state_focused="true" android:state_pressed="false"/>
    <item android:drawable="@drawable/bg_green_button_border_press" android:state_pressed="false" android:state_selected="true"/>
    <item android:drawable="@drawable/bg_green_button_border_press" android:state_enabled="true" android:state_pressed="true"/>
    <item android:drawable="@drawable/bg_green_button_border_press" android:state_checked="true" android:state_enabled="true"/>
    <item android:drawable="@drawable/bg_green_button_border_nor"/>
</selector>