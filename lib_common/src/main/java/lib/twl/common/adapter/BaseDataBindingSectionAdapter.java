package lib.twl.common.adapter;

import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;

import java.util.List;

import lib.twl.common.views.adapter.BaseSectionQuickAdapter;
import lib.twl.common.views.adapter.entity.SectionEntity;

/**
 * Created by <PERSON><PERSON>J<PERSON>gpeng on 2020-02-03
 * Describe:
 */
public abstract class BaseDataBindingSectionAdapter<T extends SectionEntity, V extends ViewDataBinding, H extends ViewDataBinding> extends
        BaseSectionQuickAdapter<T, BaseDataBindingViewHolder<ViewDataBinding>> {

    public BaseDataBindingSectionAdapter(int layoutResId, int sectionHeadResId, List<T> data) {
        super(layoutResId, sectionHeadResId, data);
    }

    @Override
    protected BaseDataBindingViewHolder<ViewDataBinding> onCreateDefViewHolder(ViewGroup parent, int viewType) {
        ViewDataBinding binding = DataBindingUtil.inflate(mLayoutInflater,
                viewType == SECTION_HEADER_VIEW ? mSectionHeadResId : mLayoutResId, parent, false);
        return new BaseDataBindingViewHolder<>(binding);
    }

    @Override
    protected void convertHead(BaseDataBindingViewHolder<ViewDataBinding> helper, T item) {
        H binding = (H) helper.getDataBinding();
        bindHeader(helper, binding, item);
        binding.executePendingBindings();
    }

    protected abstract void bindHeader(BaseDataBindingViewHolder<ViewDataBinding> helper, H vdb, T item);

    @Override
    protected void convert(@NonNull BaseDataBindingViewHolder<ViewDataBinding> helper, T item) {
        V binding = (V) helper.getDataBinding();
        bind(helper, binding, item);
        binding.executePendingBindings();
    }

    protected abstract void bind(BaseDataBindingViewHolder<ViewDataBinding> helper, V vdb, T item);
}
