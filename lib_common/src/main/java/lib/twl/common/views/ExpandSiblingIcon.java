package lib.twl.common.views;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import com.techwolf.lib.tlog.TLog;
import lib.twl.common.R;

/**
 * <AUTHOR>
 * 可扩展开同级别view的icon
 */
public class ExpandSiblingIcon extends androidx.appcompat.widget.AppCompatImageView {

    private static final String TAG = "ExpandSiblingIcon";

    private boolean mIconExpandedDefault;
    private long mAnimationDuration;
    private Drawable mExpandShow;
    private Drawable mClosedShow;
    private int mSiblingViewId;
    private View mSiblingView;

    // 当前是否展开状态
    private boolean mCurrentState;

    public ExpandSiblingIcon(Context context) {
        super(context);
        init(context, null);
    }

    public ExpandSiblingIcon(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public ExpandSiblingIcon(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        if (attrs != null) {
            TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.ExpandSiblingIcon);
            mIconExpandedDefault = typedArray.getBoolean(R.styleable.ExpandSiblingIcon_iconExpandedDefault, false);
            mExpandShow = typedArray.getDrawable(R.styleable.ExpandSiblingIcon_iconExpandedSrc);
            mClosedShow = typedArray.getDrawable(R.styleable.ExpandSiblingIcon_iconClosedSrc);
            mAnimationDuration = typedArray.getInteger(R.styleable.ExpandSiblingIcon_siblingAnimationDuration, 300);
            mSiblingViewId = typedArray.getResourceId(R.styleable.ExpandSiblingIcon_siblingId, -1);
            typedArray.recycle();

            if (mSiblingViewId > 0) {
                View rootView = getRootView();
                ViewParent viewParent = getParent();
                mSiblingView = rootView.findViewById(mSiblingViewId);
            }

            mCurrentState = mIconExpandedDefault;

            setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    mCurrentState = !mCurrentState;
                    initSiblingView();
                }
            });
        }

    }

    private void initSiblingView() {
        if (mSiblingView == null) {
            if (mSiblingViewId > 0) {
                View rootView = getRootView();
                mSiblingView = rootView.findViewById(mSiblingViewId);
            }
        }
        hideOrShowSiblingView(mCurrentState);
    }

    @Override
    public void onWindowFocusChanged(boolean hasWindowFocus) {
        super.onWindowFocusChanged(hasWindowFocus);
        initSiblingView();
    }

    /**
     * 控制同级view的展示和隐藏
     */
    private void hideOrShowSiblingView(boolean iconExpandedDefault) {
        if (mSiblingView == null) {
            return;
        }

        if ((mSiblingView.getVisibility() == View.VISIBLE) == iconExpandedDefault) {
            return;
        }

        Log.i(TAG, "ExpandSiblingIcon -> hideOrShowSiblingView ");
        setImageDrawable(mCurrentState ? mExpandShow : mClosedShow);

        ValueAnimator valueAnimator = ValueAnimator.ofInt(iconExpandedDefault ? mSiblingView.getHeight() : 0, iconExpandedDefault ? 0 : mSiblingView.getHeight());
        valueAnimator.setDuration(mAnimationDuration);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                ViewGroup.LayoutParams layoutParams = mSiblingView.getLayoutParams();
                layoutParams.height = (int) animation.getAnimatedValue();
                mSiblingView.setLayoutParams(layoutParams);
            }
        });
        valueAnimator.start();
    }
}
