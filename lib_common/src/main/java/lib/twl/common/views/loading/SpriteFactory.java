package lib.twl.common.views.loading;

import android.graphics.Color;

import lib.twl.common.R;
import lib.twl.common.base.BaseApplication;

public class SpriteFactory {

    public static Sprite create(Style style) {
        Sprite sprite = null;
        switch (style) {
            case DOUBLE_BOUNCE:
                sprite = new DoubleBounce(BaseApplication.getApplication().getResources().getColor(R.color.color_5D68E8), BaseApplication.getApplication().getResources().getColor(R.color.color_55D5DB));
                break;
            default:
                break;
        }
        return sprite;
    }
}
