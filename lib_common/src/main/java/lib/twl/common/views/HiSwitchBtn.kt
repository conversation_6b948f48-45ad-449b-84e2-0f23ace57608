package lib.twl.common.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.CompoundButton
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.Switch
import android.widget.TextView
import androidx.core.view.isGone
import lib.twl.common.R

/**
 * <AUTHOR>
 * @Date   2023/11/8 9:04 PM
 * @Desc   switch设置控件
 */
class HiSwitchBtn
@JvmOverloads
constructor(context: Context, attributes: AttributeSet?= null, defStyle: Int = 0) : RelativeLayout(context, attributes, defStyle){
    init {
        initView(context, attributes)
    }

    private lateinit var titleTv: TextView
    private lateinit var descTv: TextView
    private lateinit var switchBtn: Switch

    private fun initView(context: Context, attributes: AttributeSet?) {
        LayoutInflater.from(context).inflate(R.layout.hi_switch_btn_view, this)
        titleTv = findViewById(R.id.title)
        descTv = findViewById(R.id.description)
        switchBtn = findViewById(R.id.shift)
    }

    fun setTitle(titleText: String) {
        titleTv.text = titleText
    }

    fun setOpenStatus(open: Boolean) {
        switchBtn.isChecked = open
    }

    fun setDes(descText: String) {
        descTv.text = descText
        descTv.isGone = descText.isEmpty()
    }

    fun setSwitchChangeListener(listener: CompoundButton.OnCheckedChangeListener) {
        switchBtn.setOnCheckedChangeListener(listener)
    }


}