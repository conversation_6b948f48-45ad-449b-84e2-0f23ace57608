package lib.twl.common.util.level;

import android.app.Application;
import com.twl.utils.kv.KV;

/**
 * 设备分级策略
 * <AUTHOR>
 */
public class DeviceYearClass {

    private static DeviceYearClass sDeviceYearClass;
    private static final String KEY_DEVICE_LEVEL = "device_level";
    private int level = 0;

    private DeviceYearClass() {}

    public static synchronized DeviceYearClass getInstance() {
        if (sDeviceYearClass == null) {
            sDeviceYearClass = new DeviceYearClass();
        }
        return sDeviceYearClass;
    }

    public void initDeviceLevel(Application context) {
        int levelFromKv = KV.getKV(context.getPackageName()).getInt(KEY_DEVICE_LEVEL, -1);
        if (levelFromKv >= 0) {
            level = levelFromKv;
            return;
        }

        level = YearClass.getLevelByCustomStyle(context);
        KV.getKV(context.getPackageName()).putInt(KEY_DEVICE_LEVEL, level);
    }

    public int getDeviceLevel () {
        return level;
    }

    public static class DeviceLevel {
        public static final int LEVEL_OLD_RANGE_DEVICE = 1;
        public static final int LEVEL_MID_RANGE_DEVICE = 2;
        public static final int LEVEL_HIGH_RANGE_DEVICE = 3;
        public static final int LEVEL_SUPER_HIGH_DEVICE = 4;
    }
}
