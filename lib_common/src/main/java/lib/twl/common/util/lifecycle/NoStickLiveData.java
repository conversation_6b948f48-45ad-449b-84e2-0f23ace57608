package lib.twl.common.util.lifecycle;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import java.lang.reflect.Field;

/**
 * Created by ChaiJiangpeng on 2021/3/25
 * Describe:非粘性的
 */
public class NoStickLiveData<T> extends MutableLiveData<T> {
    @Override
    public void observe(@NonNull LifecycleOwner owner, @NonNull Observer<? super T> observer) {
        hookVersion(this);
        super.observe(owner, observer);
    }

    private void hookVersion(NoStickLiveData data) {
        Class<?> liveDataClass = LiveData.class;
        Field mVersion = null;
        try {
            mVersion = liveDataClass.getDeclaredField("mVersion");
            mVersion.setAccessible(true);
            int version = ((int) mVersion.get(data));
            if (version != -1) {
                mVersion.set(data, -1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
