package lib.twl.common.util;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.DrawableRes;
import androidx.annotation.StringRes;

import com.techwolf.lib.tlog.TLog;

import java.lang.ref.WeakReference;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import lib.twl.common.LBase;
import lib.twl.common.R;
import lib.twl.common.databinding.ToastDefaultViewBinding;
import lib.twl.common.databinding.ToastTextViewBinding;
import lib.twl.common.databinding.ToastViewBinding;
import lib.twl.common.ext.ViewExtKt;

/**
 * Created by 陈磊 on 2014/12/19.
 */
public class ToastUtils {
    public static final String TAG = "ToastUtils";
    /**
     * 黑底白字（默认样式）
     */
    public static final int MSG_WHAT_TOAST_BLACK_TEXT = 0;


    /**
     * 白底文字样式
     */
    public static final int MSG_WHAT_TOAST_WHITE_TEXT = 1;
    /**
     * 白底文字+图标样式
     */
    public static final int MSG_WHAT_TOAST_WHITE_IMG_TEXT = 2;
    /**
     * 隐藏
     */
    public static final int MSG_WHAT_TOAST_CANCEL = 3;

    /**
     * 黑底白字（居中样式）
     */
    public static final int MSG_WHAT_TOAST_BLACK_TEXT_CENTER = 4;

    private static WeakReference<Toast> toastRef = new WeakReference<>(null);

    private static final Handler HANDLER = new Handler(Looper.getMainLooper(), new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            ToastData data = (ToastData) msg.obj;
            if (msg.what == MSG_WHAT_TOAST_CANCEL
                    || !TextUtils.isEmpty(data.text)) {
                if (toastRef.get() != null) {
                    toastRef.get().cancel();
                }
                if (msg.what == MSG_WHAT_TOAST_CANCEL) {
                    return true;
                }
            }
            switch (msg.what) {
                case MSG_WHAT_TOAST_WHITE_TEXT:
                    toastRef = new WeakReference<>(makeToast(data.text, data.duration));
                    break;
                case MSG_WHAT_TOAST_WHITE_IMG_TEXT:
                    toastRef = new WeakReference<>(makeMyToast(data));
                    break;
                case MSG_WHAT_TOAST_BLACK_TEXT_CENTER:
                    toastRef = new WeakReference<>(makeCenterToast(data));
                    break;
                default:
                    toastRef = new WeakReference<>(makeDefaultToast(data));
                    break;
            }
            return true;
        }
    });

    /**
     * 禁止实例化
     */
    private ToastUtils() {
    }


    public static void success(@StringRes int idStr) {
        success(getText(idStr));
    }

    public static void success(String text) {
        sendToastMessage(MSG_WHAT_TOAST_WHITE_IMG_TEXT,
                R.drawable.ic_toast_success_ok,
                text,
                Toast.LENGTH_SHORT);
    }

    public static void ssd(@DrawableRes int drawable, String text) {
        sendToastMessage(MSG_WHAT_TOAST_BLACK_TEXT,
                drawable,
                text,
                Toast.LENGTH_SHORT);
    }

    public static void ssd(@DrawableRes int drawable, @StringRes int text) {
        sendToastMessage(MSG_WHAT_TOAST_BLACK_TEXT,
                drawable,
                getText(text),
                Toast.LENGTH_SHORT);
    }


    public static void failure(@StringRes int idStr) {
        failure(getText(idStr));
    }

    public static void failure(String text) {
        sendToastMessage(MSG_WHAT_TOAST_WHITE_IMG_TEXT,
                R.drawable.ic_toast_failure,
                text,
                Toast.LENGTH_SHORT);
    }

    public static void warning(@StringRes int idStr) {
        warning(getText(idStr));
    }

    public static void warning(String text) {
        sendToastMessage(MSG_WHAT_TOAST_WHITE_IMG_TEXT,
                R.drawable.ic_toast_warning,
                text,
                Toast.LENGTH_SHORT);
    }

    /**
     * 显示短Toast（白底黑字）
     */
    public static void ss(int text) {
        s(getText(text), Toast.LENGTH_SHORT);
    }

    /**
     * 显示短Toast（白底黑字）
     */
    public static void ss(CharSequence text) {
        s(text, Toast.LENGTH_SHORT);
    }

    /**
     * 显示长Toast（白底黑字）
     */
    public static void sl(int text) {
        s(getText(text), Toast.LENGTH_LONG);
    }

    /**
     * 显示长Toast（白底黑字）
     */
    public static void sl(CharSequence text) {
        s(text, Toast.LENGTH_LONG);
    }

    /**
     * 显示自定义时长Toast(自定义样式，白色图片背景黑色字体)
     */
    public static void s(CharSequence text, int duration) {
        sendToastMessage(MSG_WHAT_TOAST_WHITE_TEXT,
                0,
                text,
                duration);
    }

    public static void sst(CharSequence text, int gravity) {
        sendToastMessage(MSG_WHAT_TOAST_WHITE_IMG_TEXT,
                R.drawable.ic_toast_failure,
                text,
                Toast.LENGTH_SHORT,
                gravity, QMUIDisplayHelper.dp2px(getContext(), 40));
    }

    /**
     * 显示短Toast（黑底白字）
     */
    public static void ssd(int text) {
        ssd(getText(text));
    }

    /**
     * 显示短Toast（黑底白字）
     */
    public static void ssd(String text) {
        sendToastMessage(MSG_WHAT_TOAST_BLACK_TEXT,
                0,
                text,
                Toast.LENGTH_SHORT);
    }

    public static void ssd(String text, int margin) {
        sendToastMessage(MSG_WHAT_TOAST_BLACK_TEXT,
                0,
                text,
                Toast.LENGTH_SHORT,
                Gravity.BOTTOM,
                margin);
    }

    /**
     * 显示短Toast（黑底白字居中）
     */
    public static void ssdc(int text) {
        ssdc(getText(text));
    }

    /**
     * 显示短Toast（黑底白字居中）
     */
    public static void ssdcCenter(String text) {
        int statusBarsHeight = ViewExtKt.getStatusBarsHeightCache();
        sendToastMessage(MSG_WHAT_TOAST_BLACK_TEXT_CENTER,
                0,
                text,
                Toast.LENGTH_SHORT,
                Gravity.CENTER,
                -statusBarsHeight / 2);
    }

    /**
     * 显示短Toast（黑底白字居中）
     */
    public static void ssdc(String text) {
        sendToastMessage(MSG_WHAT_TOAST_BLACK_TEXT,
                0,
                text,
                Toast.LENGTH_SHORT,
                Gravity.CENTER);
    }

    public static void cancel() {
        ToastData data = new ToastData();
        HANDLER.obtainMessage(MSG_WHAT_TOAST_CANCEL, data).sendToTarget();
    }


    /**
     * 发送toast handler
     */
    private static void sendToastMessage(int what, int resId, CharSequence text, int duration) {
        sendToastMessage(what, resId, text, duration, Gravity.BOTTOM);
    }

    private static void sendToastMessage(int what, int resId, CharSequence text, int duration, int gravity) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        ToastData data = new ToastData();
        data.resId = resId;
        data.text = text;
        data.duration = duration;
        data.gravity = gravity;
        HANDLER.obtainMessage(what, data).sendToTarget();
    }

    private static void sendToastMessage(int what, int resId, CharSequence text, int duration, int gravity, int margin) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        ToastData data = new ToastData();
        data.resId = resId;
        data.text = text;
        data.duration = duration;
        data.gravity = gravity;
        data.margin = margin;
        HANDLER.obtainMessage(what, data).sendToTarget();
    }

    private static Context getContext() {
        return LBase.getApplication().getApplicationContext();
    }

    private static String getText(int text) {
        String res = "";
        try {
            res = getContext().getResources().getString(text);
        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
        }
        return res;
    }

    private static Toast makeDefaultToast(ToastData data) {
        ToastDefaultViewBinding binding = ToastDefaultViewBinding.inflate(LayoutInflater.from(getContext()));
        binding.tvText.setText(data.text);
        if (data.resId != 0) {
            binding.tvText.setCompoundDrawablesWithIntrinsicBounds(data.resId, 0, 0, 0);
        }
        Toast result = new Toast(getContext());
        result.setView(binding.getRoot());
        result.setGravity(data.gravity, 0, data.margin > 0 ? data.margin : QMUIDisplayHelper.dp2px(getContext(), 60));
        result.setDuration(data.duration);
        result.show();
        return result;
    }

    private static Toast makeCenterToast(ToastData data) {
        ToastDefaultViewBinding binding = ToastDefaultViewBinding.inflate(LayoutInflater.from(getContext()));
        binding.tvText.setText(data.text);
        if (data.resId != 0) {
            binding.tvText.setCompoundDrawablesWithIntrinsicBounds(data.resId, 0, 0, 0);
        }
        Toast result = new Toast(getContext());
        result.setView(binding.getRoot());
        result.setGravity(data.gravity, 0, data.margin);
        result.setDuration(data.duration);
        result.show();
        return result;
    }

    private static Toast makeToast(CharSequence text,
                                   int duration) {
        View mToastView = getToastView(text);
        Toast result = new Toast(getContext());
        result.setView(mToastView);
        result.setGravity(Gravity.BOTTOM, 0, QMUIDisplayHelper.dp2px(getContext(), 60));
        result.setDuration(duration);
        result.show();
        return result;
    }

    private static View getToastView(CharSequence text) {
        ToastTextViewBinding binding = ToastTextViewBinding.inflate(LayoutInflater.from(getContext()));

        ViewGroup.LayoutParams layoutParams = binding.tvText.getLayoutParams();
        if (layoutParams != null) {
            WindowManager mWindowManager = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
            Display display = mWindowManager.getDefaultDisplay();
            layoutParams.width = display.getWidth() - QMUIDisplayHelper.dpToPx(60);
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        }
        binding.tvText.setText(text);
        return binding.getRoot();
    }

    private static Toast makeMyToast(ToastData toastData) {
        View mToastView = getMyToastView(toastData);
        Toast result = new Toast(getContext());
        result.setView(mToastView);
        result.setGravity(toastData.gravity, 0, toastData.margin > 0 ? toastData.margin : QMUIDisplayHelper.dp2px(getContext(), 60));
        result.setDuration(toastData.duration);
        result.show();
        return result;
    }

    private static View getMyToastView(ToastData toastData) {
        ToastViewBinding binding = ToastViewBinding.inflate(LayoutInflater.from(getContext()));
        ViewGroup.LayoutParams layoutParams = binding.llToast.getLayoutParams();
        if (layoutParams != null) {
            WindowManager mWindowManager = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
            Display display = mWindowManager.getDefaultDisplay();
            layoutParams.width = display.getWidth();
        }
        binding.llToast.setGravity(Gravity.CENTER);
        binding.ivIcon.setImageResource(toastData.resId);
        binding.tvText.setText(toastData.text);
        return binding.getRoot();
    }

    public static class ToastData {
        public @DrawableRes
        int resId;
        public CharSequence text;
        public int duration = Toast.LENGTH_SHORT;
        public int gravity;
        public CharSequence clickText;
        public Function0<Unit> function;
        public int margin;
    }
}
