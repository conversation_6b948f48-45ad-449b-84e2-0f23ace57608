package lib.twl.common.util

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationManagerCompat
import androidx.fragment.app.FragmentActivity
import com.techwolf.lib.tlog.TLog
import lib.twl.common.permission.PermissionAvoidManager


/**
 *@author: musa on 2024/8/22
 *@e-mail: <EMAIL>
 *@desc:权限工具类
 */
private const val TAG = "HiPermissionUtil"
object HiPermissionUtil {

    /**
     * 读权限
     */
    @JvmStatic
    val readFilePermissions by  lazy {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.S_V2)
            arrayOf(
                Manifest.permission.READ_MEDIA_AUDIO,
                Manifest.permission.READ_MEDIA_VIDEO,
                Manifest.permission.READ_MEDIA_IMAGES
            )
        else
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
    }

    /**
     * 写权限
     */
    @JvmStatic
    val writeFilePermissions by lazy {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.S_V2)
            arrayOf()
        else
            arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)
    }

    /**
     * 读写权限
     */
    @JvmStatic
    val storagePermissions by lazy {
        readFilePermissions + writeFilePermissions
    }

    /**
     * Android10以下访问公共存储空间(Environment.getExternalStoragePublicDirectory() 返回的那几个 下载、文档什么的)需要读写权限
     * Android10以上访问除了MEDIA_AUDIO、MEDIA_VIDEO、MEDIA_IMAGES需要申请各自的读权限之外，其它的公共存储空间不再需要申请权限。
     * 而且Android10以上一般无法再访问外部存储中 除了公共存储空间 和 应用自己沙盒空间之外的文件，除非申请 MANAGE_EXTERNAL_STORAGE权限。
     */
    @JvmStatic
    val accessSharedFilePermissions by lazy {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P)
            arrayOf()
        else
            storagePermissions
    }

    @JvmStatic
    fun isGrantedAllPermissions(context: Context, permissions: Array<String>) : Boolean{
        for (permission in permissions) {
            if (ActivityCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                return false
            }
        }
        return true
    }

    @JvmStatic
    fun requestNotificationPersmission(fragmentActivity: FragmentActivity, onRequestPermissionsResult: (hasPermission: Boolean, shouldShowAllRequestPermissionRationale: Boolean) -> Unit) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val permissionAvoidManager = PermissionAvoidManager(fragmentActivity)
            permissionAvoidManager.requestPermission(
                arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                PermissionAvoidManager.OnCommonPermissionCallBack { hasPermission, shouldShowAllRequestPermissionRationale ->
                    onRequestPermissionsResult(hasPermission, shouldShowAllRequestPermissionRationale)
                })

        } else {
            NotificationManagerCompat.from(fragmentActivity).areNotificationsEnabled().let {
                onRequestPermissionsResult(it, !it)
            }
        }
    }

    @JvmStatic
    fun enableNotification(context: Context){
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            return
        }
        try {
            context.startActivity(Intent().apply {
                setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
                putExtra(Settings.EXTRA_APP_PACKAGE,context. getPackageName());
                putExtra(Settings.EXTRA_CHANNEL_ID, context.getApplicationInfo().uid);
                putExtra("app_package", context.getPackageName());
                putExtra("app_uid", context.getApplicationInfo().uid);
            });
        } catch (e : Exception) {
            TLog.error(TAG, e, "enableNotification error")
            context. startActivity(Intent().apply {
                setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                setData(Uri.fromParts("package",context. getPackageName(), null))
            });
        }
    }

    //检查麦克风权限是否开启
    @JvmStatic
    fun checkMicrophonePermission(context: Context): Boolean {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED
    }

    //检查摄像头权限是否开启
    @JvmStatic
    fun checkCameraPermission(context: Context): Boolean {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED
    }
    //检查相册权限是否开启
    @JvmStatic
    fun checkAlbumPermission(context: Context): Boolean {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
    }
    //检查位置权限是否开启
    @JvmStatic
    fun checkLocationPermission(context: Context): Boolean {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
    }
    //检查读写日历权限是否开启
    @JvmStatic
    fun checkCalendarPermission(context: Context): Boolean {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_CALENDAR) == PackageManager.PERMISSION_GRANTED
    }

    //检测附近设备权限是否开启
    @JvmStatic
    fun checkNearbyDevicePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED
        } else {
            //VERSION.SDK_INT < S
            return true
        }
    }




}