package lib.twl.common.util.selection;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.text.Layout;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.ColorRes;
import androidx.annotation.NonNull;

import lib.twl.common.util.selection.listener.OnCursorCallback;
import lib.twl.common.util.selection.listener.OnSelectionDismissListener;

import lib.twl.common.util.QMUIDisplayHelper;

/**
 * <AUTHOR>
 * @date 2020/9/24.
 */
public class Cursor extends View implements ICursor {

    private Cursor self = this;
    private boolean isLeft;
    private PopupWindow mPopupWindow;
    private Paint mPaint;
    private int mWidth;
    private int mHeight;
    private int mCircleRadius;
    private int mPadding;
    private int containerMarginTop;
    private int containerMarginBottom;
    private int screenHeight;
    private OnCursorCallback onCusorCallback;

    public void setOnCursorCallback(OnCursorCallback onCusorCallback) {
        this.onCusorCallback = onCusorCallback;
    }

    public Cursor(Context context, boolean isLeft, int cursorSize, int padding, @ColorRes int color, int containerMarginTop, final OnSelectionDismissListener onDismissListener) {
        super(context);

        this.isLeft = isLeft;
        this.containerMarginTop = containerMarginTop;
        this.containerMarginBottom = QMUIDisplayHelper.dpToPx(25);
        this.screenHeight = QMUIDisplayHelper.getScreenHeight(context);
        mWidth = cursorSize;
        mHeight = cursorSize;

        mCircleRadius = cursorSize / 2;
        mPadding = padding;

        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setColor(context.getResources().getColor(color));

        mPopupWindow = new PopupWindow(this);
        mPopupWindow.setClippingEnabled(false);
        mPopupWindow.setWidth(mWidth + mPadding * 2);
        mPopupWindow.setHeight(mHeight + mPadding / 2);

        mPopupWindow.setOutsideTouchable(true);
        mPopupWindow.setTouchInterceptor(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_OUTSIDE /*&& !mPopupWindow.isFocusable()*/) {
                    //如果焦点不在popupWindow上，且点击了外面，不再往下dispatch事件：
                    //不做任何响应,不 dismiss popupWindow
                    Log.e("mPopupWindow", "onTouch...111 " + self.hashCode());
                    if (null != onDismissListener) {
                        onDismissListener.onDismiss(true);
                    }
                    return true;
                }
                if (null != onDismissListener) {
                    onDismissListener.onDismiss(false);
                }
                //否则default，往下dispatch事件:关掉popupWindow，
                return false;
            }
        });

        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        canvas.drawCircle(mCircleRadius + mPadding, mCircleRadius, mCircleRadius, mPaint);
        if (isLeft) {
            canvas.drawRect(mCircleRadius + mPadding, 0, mCircleRadius * 2 + mPadding, mCircleRadius, mPaint);
        } else {
            canvas.drawRect(mPadding, 0, mCircleRadius + mPadding, mCircleRadius, mPaint);
        }
    }

    private int downX, downY;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                downX = (int) event.getX();
                downY = (int) event.getY();
                if (null != onCusorCallback) {
                    onCusorCallback.onDown(self);
                }
                break;
            case MotionEvent.ACTION_UP:
                if (null != onCusorCallback) {
                    onCusorCallback.onUp(self);
                }
                break;
            case MotionEvent.ACTION_MOVE:
                int rawX = (int) event.getRawX();
                int rawY = (int) event.getRawY();
                //触摸点
                int deltaX = isLeft ? rawX - downX + mWidth + 6 : rawX - downX + 6/*- mWidth*/;
                int deltaY = rawY - downY + 16/* - mHeight*/;

                if (null != onCusorCallback) {
                    onCusorCallback.onMove(self, deltaX, deltaY);
                }
                break;
            default:
                break;
        }
        return true;
    }


    /**
     * 显示在textview指定位置
     *
     * @param start 当前选择的字符起始位置
     * @param end   当前选择的字符结束位置
     */
    @Override
    public boolean show(@NonNull TextView textview, int start, int end) {
        if (start < 0 || end < 0 || null == textview.getText() || end > textview.getText().length()) {
            return false;
        }
        if (null != mPopupWindow && null != textview.getLayout()) {
            int[] popLocation = getPopLocation(textview, start, end);
            if (popLocation[1] > containerMarginTop && popLocation[1] < (screenHeight - containerMarginBottom)) {
                if (mPopupWindow.isShowing()) {
                    mPopupWindow.update(popLocation[0], popLocation[1], -1, -1);
                } else {
                    mPopupWindow.showAtLocation(textview, Gravity.NO_GRAVITY, popLocation[0], popLocation[1]);
                }
            }
            return true;
        }
        return true;
    }

    @Override
    public boolean update(@NonNull TextView textview, int start, int end) {
        if (start < 0 || end < 0 || null == textview.getText() || end > textview.getText().length()) {
            return false;
        }
        if (null != mPopupWindow && null != textview.getLayout()) {
            int[] popLocation = getPopLocation(textview, start, end);
            if (popLocation[1] > containerMarginTop && popLocation[1] < (screenHeight - containerMarginBottom)) {
                mPopupWindow.update(popLocation[0], popLocation[1], -1, -1);
                return true;
            }
        }
        return true;
    }

    private int[] getPopLocation(@NonNull TextView textview, int start, int end) {

        Layout layout = textview.getLayout();
        int index = isLeft ? start : end;
        int x = (int) layout.getPrimaryHorizontal(index);
        int lineBottom;
        if (isLeft) {
            lineBottom = layout.getLineTop(layout.getLineForOffset(index));
        } else {
            int lineE = layout.getLineForOffset(index);
            int lineEP = layout.getLineForOffset(index - 1);
            if (lineE > lineEP) {
                x = layout.getWidth();
                lineBottom = layout.getLineBottom(lineEP);
            } else {
                if (index >= textview.getText().length()) {
                    x = layout.getWidth();
                }
                lineBottom = layout.getLineBottom(lineE);
            }
        }
        int[] location = new int[2];
        textview.getLocationInWindow(location);
        int extraX = location[0] + (isLeft ? textview.getPaddingLeft() : textview.getPaddingRight()) - mPadding;
        int extraY = location[1] + textview.getPaddingTop();

        int offset = isLeft ? mWidth : 0;

        int popX = x - offset + extraX;
        int popY = lineBottom + extraY;

        return new int[]{popX, popY};
    }

    @Override
    public void dismiss() {
        if (null != mPopupWindow) {
            mPopupWindow.dismiss();
        }
    }

    @Override
    public boolean isShowing() {
        return null != mPopupWindow && mPopupWindow.isShowing();
    }

    @Override
    public boolean isLeft() {
        return isLeft;
    }

    @Override
    public void changeDirection() {
        isLeft = !isLeft;
        invalidate();
    }

    @Override
    public void changeDirection(boolean isLeft) {
        this.isLeft = isLeft;
        invalidate();
    }

}
