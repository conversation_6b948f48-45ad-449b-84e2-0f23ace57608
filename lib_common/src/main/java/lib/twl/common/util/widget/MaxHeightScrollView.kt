package lib.twl.common.util.widget

import android.content.Context
import android.util.AttributeSet
import androidx.core.widget.NestedScrollView
import com.techwolf.lib.tlog.TLog
import lib.twl.common.R
import kotlin.math.max

class MaxHeightScrollView
@JvmOverloads
constructor(c: Context, attributeSet: AttributeSet?= null, defStyle: Int = 0) : NestedScrollView(c, attributeSet, defStyle) {

    init {
        initView(c, attributeSet)
    }

    private var maxHeight = 0

    private fun initView(c: Context, attributeSet: AttributeSet?) {
        attributeSet?.run {
            val typedValue = c.obtainStyledAttributes(attributeSet, R.styleable.MaxHeightScrollView)
            maxHeight = typedValue.getDimensionPixelSize(R.styleable.MaxHeightScrollView_viewMaxHeight, 0).toInt()
            typedValue.recycle()
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        if (maxHeight > 0 && childCount > 0) {
            setMeasuredDimension(widthMeasureSpec, Math.min(maxHeight, measuredHeight))
        }
    }

}