package lib.twl.common.util.image

import android.graphics.BitmapFactory
import android.net.Uri
import com.facebook.binaryresource.FileBinaryResource
import com.facebook.common.references.CloseableReference
import com.facebook.drawee.backends.pipeline.Fresco
import com.facebook.drawee.interfaces.DraweeController
import com.facebook.drawee.view.SimpleDraweeView
import com.facebook.imagepipeline.cache.DefaultCacheKeyFactory
import com.facebook.imagepipeline.common.SourceUriType
import com.facebook.imagepipeline.core.ImagePipelineFactory
import com.facebook.imagepipeline.image.CloseableImage
import com.facebook.imagepipeline.request.ImageRequest
import lib.twl.common.views.imagesview.DraggableParamsInfo
import java.io.File

/**
 * 图片加载框架Fresco工具类
 */
object FrescoHelper {

    @JvmStatic
    fun loadGifFromNetwork(imageView: SimpleDraweeView?, url: String?, autoPlay: Boolean) {
        val builder = Fresco.newDraweeControllerBuilder()
        builder.autoPlayAnimations = autoPlay
        builder.imageRequest = ImageRequest.fromUri(Uri.parse(url)) //设置图片请求
        builder.tapToRetryEnabled = true //设置是否允许加载失败时点击再次加载
        val controller: DraweeController = builder.build()
        imageView?.controller = controller
    }

    /**
     * 从缓存中读取图片的宽高比
     */
    fun retrieveImageWhRadioFromMemoryCache(
        thumbnailImg: String,
        retrieveCallBack: (whRadio: Float) -> Unit
    ) {
        var whRadio = DraggableParamsInfo.INVALID_RATIO
        val request: ImageRequest? = ImageRequest.fromUri(Uri.parse(thumbnailImg))
        val cacheKey = DefaultCacheKeyFactory
            .getInstance()
            .getEncodedCacheKey(request, false)
        if (Fresco.getImagePipeline().isInBitmapMemoryCache(request)) {
            val image: CloseableReference<CloseableImage>? = Fresco.getImagePipeline().bitmapMemoryCache.get(Fresco.getImagePipeline().cacheKeyFactory.getBitmapCacheKey(request, null))
            if (image != null) {
                if (image.get().width > 0 && image.get().height > 0) {
                    whRadio = image.get().width * 1f / image.get().height
                }
            }
        } else if (Fresco.getImagePipeline().isInDiskCacheSync(request)) {
            val mainFileCache = ImagePipelineFactory
                .getInstance()
                .mainFileCache
            val cacheFile: File
            if (mainFileCache.hasKey(cacheKey) && mainFileCache.getResource(cacheKey) != null) {
                cacheFile = (mainFileCache.getResource(cacheKey) as FileBinaryResource).file
                if (!ImageUtils.isGif(cacheFile.path)) {
                    val options = BitmapFactory.Options()
                    options.inJustDecodeBounds = true;
                    BitmapFactory.decodeFile(cacheFile.path, options)
                    if (options.outWidth > 0 && options.outHeight > 0) {
                        whRadio = options.outWidth * 1f / options.outHeight
                    }
                }
            }
        }
        retrieveCallBack(whRadio)
    }

    fun checkImageIsInMemoryCache(url: String, callback: (inCache: Boolean) -> Unit) {
        var isCache = false
        if (url.isNotEmpty()) {
            val request: ImageRequest? = ImageRequest.fromUri(Uri.parse(url))
            if (request!!.sourceUriType == SourceUriType.SOURCE_TYPE_LOCAL_IMAGE_FILE) {
                isCache = File(Uri.parse(url).path).exists()
            }
            if (!isCache) {
                isCache = Fresco.getImagePipeline().isInBitmapMemoryCache(Uri.parse(url))
                if (!isCache) {
                    isCache = Fresco.getImagePipeline().isInDiskCacheSync(Uri.parse(url))
                }
            }
        }
        callback(isCache)
    }


    //图片是否在 内存缓存 or  磁盘缓存
    fun imageIsInCache(url: String): Boolean {
        if (url.isEmpty()) return false
        try {
            var isCache = false
            val request: ImageRequest? = ImageRequest.fromUri(Uri.parse(url))
            if (request!!.sourceUriType == SourceUriType.SOURCE_TYPE_LOCAL_IMAGE_FILE) {
                isCache = File(Uri.parse(url).path).exists()
            }
            if (!isCache) {
                isCache = Fresco.getImagePipeline().isInBitmapMemoryCache(request)
                if (!isCache) {
                    isCache = Fresco.getImagePipeline().isInDiskCacheSync(Uri.parse(url))
                }
            }
            return isCache
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    fun isImageInCache(url: String): Boolean {
        if (url.isEmpty()) return false
        try {
            var isCache = false
            val request: ImageRequest? = ImageRequest.fromUri(Uri.parse(url))
            if (request!!.sourceUriType == SourceUriType.SOURCE_TYPE_LOCAL_IMAGE_FILE) {
                isCache = File(Uri.parse(url).path).exists()
            }
            if (!isCache) {
                isCache = Fresco.getImagePipeline().isInBitmapMemoryCache(Uri.parse(url))
                if (!isCache) {
                    isCache = Fresco.getImagePipeline().isInDiskCacheSync(Uri.parse(url))
                }
            }
            return isCache
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    fun getCacheFile(url: String): File? {
        if (url.isEmpty()) {
            return null
        }
        val request: ImageRequest? = ImageRequest.fromUri(Uri.parse(url))
        if (request != null) {
            if (request.sourceUriType == SourceUriType.SOURCE_TYPE_LOCAL_IMAGE_FILE) {
                val imageFile = File(Uri.parse(url).path)
                if (imageFile.exists()) {
                    return imageFile
                }
            } else {
                val cacheKey = DefaultCacheKeyFactory
                    .getInstance()
                    .getEncodedCacheKey(request, false)
                val mainFileCache = ImagePipelineFactory
                    .getInstance()
                    .mainFileCache
                if (mainFileCache.hasKey(cacheKey) && mainFileCache.getResource(cacheKey) != null) {
                    return (mainFileCache.getResource(cacheKey) as FileBinaryResource).file
                }
            }
        }
        return null
    }

    fun isLocalFile(url: String): Boolean {
        if (url.isEmpty()) {
            return false
        }
        val request: ImageRequest? = ImageRequest.fromUri(Uri.parse(url))
        if (request!!.sourceUriType == SourceUriType.SOURCE_TYPE_LOCAL_IMAGE_FILE) {
            val imageFile = File(Uri.parse(url).path)
            if (imageFile.exists()) {
                return true
            }
        }
        return false
    }


}