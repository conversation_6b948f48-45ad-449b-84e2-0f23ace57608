package lib.twl.common.record.coder;


public class ReSampler {
    public static final int ENCODING_PCM_8BIT = 1;
    public static final int ENCODING_PCM_16BIT = 2;

    private LinearInterpolation mLinearInterpolation;
    private int bytePerSample = ENCODING_PCM_16BIT;
    private short[] mCache;

    public ReSampler(int sourceRate, int targetRate) {
        this(ENCODING_PCM_16BIT, sourceRate, targetRate);
    }

    public ReSampler(int bitsPerSample, int sourceRate, int targetRate) {
        bytePerSample = bitsPerSample;
        mLinearInterpolation = new LinearInterpolation(sourceRate, targetRate);
    }

    private short[] getCache(int len) {
        if (mCache == null || len > mCache.length) {
            mCache = new short[len];
        }
        return mCache;
    }

    private byte[] mBytes;

    private byte[] getBytebuffer(int len) {
        if (mBytes == null || len > mBytes.length) {
            mBytes = new byte[len];
        }
        return mBytes;
    }

    /**
     * Do resampling. Currently the amplitude is stored by short such that maximum bitsPerSample is 16 (bytePerSample is 2)
     *
     * @param sourceData The source data in bytes
     * @return re-sampled data
     */
    public byte[] reSample(byte[] sourceData) {

        int numSamples = sourceData.length / bytePerSample;
        short[] amplitudes = getCache(numSamples); // 16 bit, use a short to store

        int pointer = 0;
        for (int i = 0; i < numSamples; i++) {
            short amplitude = 0;
            for (int byteNumber = 0; byteNumber < bytePerSample; byteNumber++) {
                // little endian
                amplitude |= (short) ((sourceData[pointer++] & 0xFF) << (byteNumber * 8));
            }
            amplitudes[i] = amplitude;
        }
        // end make the amplitudes

        // do interpolation
        short[] targetSample = mLinearInterpolation.interpolate(amplitudes);
        int targetLength = targetSample.length;
        // end do interpolation

        // TODO: Remove the high frequency signals with a digital filter, leaving a signal containing only half-sample-rated frequency information, but still sampled at a rate of target sample rate. Usually FIR is used

        // end resample the amplitudes

        // convert the amplitude to bytes
        byte[] bytes;
        if (bytePerSample == ENCODING_PCM_8BIT) {
            bytes = getBytebuffer(targetLength);
            for (int i = 0; i < targetLength; i++) {
                bytes[i] = (byte) targetSample[i];
            }
        } else {
            // suppose bytePerSample==2
            bytes = getBytebuffer(targetLength << 1);
            for (int i = 0; i < targetSample.length; i++) {
                // little endian
                bytes[i * 2] = (byte) (targetSample[i] & 0xff);
                bytes[i * 2 + 1] = (byte) ((targetSample[i] >> 8) & 0xff);
            }
        }
        // end convert the amplitude to bytes

        return bytes;
    }
}
