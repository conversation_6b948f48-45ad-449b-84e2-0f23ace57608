package lib.twl.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import lib.twl.common.R;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.util.QMUIDisplayHelper;

/**
 * Created by ChaiJiangpeng on 2019-12-03
 * Describe:
 */
public class BaseDialog extends Dialog {

    private Context mContext;
    private int mHeight, mWidth, mGravity;
    private boolean mOnTouchCanceled;
    private View mView;
    private int mStyleAnimation;
    private int mCloseId;


    public BaseDialog(Builder builder) {
        super(builder.bContext);
    }

    public BaseDialog(Builder builder, int themeResId) {
        super(builder.bContext, themeResId);
        mContext = builder.bContext;
        mHeight = builder.bHeight;
        mWidth = builder.bWidth;
        mView = builder.bView;
        mGravity = builder.bGravity;
        mStyleAnimation = builder.bStyleAnimation;
        mOnTouchCanceled = builder.bOnTouchCanceled;
        mCloseId = builder.bCloseId;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(mView);
        setCanceledOnTouchOutside(mOnTouchCanceled);
        Window window = getWindow();
        if (mStyleAnimation != -11111) {
            window.setWindowAnimations(mStyleAnimation);
        }
        WindowManager.LayoutParams layoutParams = window.getAttributes();
        layoutParams.gravity = mGravity != -11111 ? mGravity : Gravity.CENTER;
        layoutParams.width = mWidth != -11111 ? mWidth : LinearLayout.LayoutParams.MATCH_PARENT;
        layoutParams.height = mHeight != -11111 ? mHeight : LinearLayout.LayoutParams.WRAP_CONTENT;
        window.setAttributes(layoutParams);
        if (mCloseId != -11111) {
            getView(mCloseId).setOnClickListener(s -> {
                close();
            });
        }
    }

    public static final class Builder {
        private Context bContext;
        private int bHeight = -11111, bWidth = -11111;
        private int bGravity = -11111;
        private boolean bOnTouchCanceled;
        private View bView;
        private int bThemeResId = R.style.DialogNormal;
        private int bStyleAnimation = -11111;
        private int bPaddingLeft = -11111, bPaddingTop, bPaddingRight, bPaddingBotton;
        private int bCloseId = -11111;

        public Builder(Context mContext) {
            this.bContext = mContext;
        }

        public Builder(Context mContext, int layoutRes) {
            this.bContext = mContext;
            this.bView = View.inflate(BaseApplication.getApplication(), layoutRes, null);
        }

        public Builder setViewId(int viewId) {
            this.bView = View.inflate(BaseApplication.getApplication(), viewId, null);
            return this;
        }

        public Builder setContentView(View view) {
            this.bView = view;
            return this;
        }

        public Builder setWidthHeightPx(int width, int height) {
            this.bWidth = width;
            this.bHeight = height;
            return this;
        }

        public Builder setWidthPx(int width) {
            this.bWidth = width;
            return this;
        }

        public Builder setHeightPx(int height) {
            this.bHeight = height;
            return this;
        }

        public Builder setWidthHeightDp(int width, int height) {
            this.bWidth = QMUIDisplayHelper.dpToPx(width);
            this.bHeight = QMUIDisplayHelper.dpToPx(height);
            return this;
        }

        public Builder isOnTouchCanceled(boolean var) {
            this.bOnTouchCanceled = var;
            return this;
        }

        public Builder addViewOnClickListener(int viewId, View.OnClickListener listener) {
            this.bView.findViewById(viewId).setOnClickListener(listener);
            return this;
        }

        public Builder setStyle(int themeResId) {
            this.bThemeResId = themeResId;
            return this;
        }

        public Builder setGravity(int gravity) {
            this.bGravity = gravity;
            return this;
        }

        public Builder setAnimation(int styleAnimation) {
            this.bStyleAnimation = styleAnimation;
            return this;
        }

        public Builder setCloseId(int viewId) {
            bCloseId = viewId;
            return this;
        }


        public BaseDialog builder() {
            return new BaseDialog(this, bThemeResId);
        }
    }

    public void setTextView(int resId, String str) {
        ((TextView) getView(resId)).setText(TextUtils.isEmpty(str) ? "" : str);
    }


    public void close() {
        if (!((Activity) mContext).isFinishing()) {
            ((Activity) mContext).runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (isShowing()) {
                        dismiss();
                    }
                }
            });
        }
    }

    @Override
    public void show() {
        if (isShowing()) {
            return;
        }
        super.show();
    }


    public <T extends View> T getView(int resId) {
        if (mView != null) {
            return mView.findViewById(resId);
        }
        return null;
    }


}
