package com.twl.hi.scan.router.handler

import com.sankuai.waimai.router.annotation.RouterUri
import com.sankuai.waimai.router.core.UriCallback
import com.sankuai.waimai.router.core.UriHandler
import com.sankuai.waimai.router.core.UriRequest
import com.sankuai.waimai.router.core.UriResult
import com.twl.hi.router.base.RouterBaseConstant
import com.twl.hi.scan.ScanningActivity
import com.twl.hi.scan.login.QRScanLoginActivity
import hi.kernel.Constants
import lib.twl.common.util.AppUtil

/**
 *@author: musa on 2024/1/7
 *@e-mail: <EMAIL>
 *@desc: 扫码登录
 */
@RouterUri(
    scheme = RouterBaseConstant.OLD_SCHEME,
    host = RouterBaseConstant.OLD_HOST,
    path = [Constants.SCAN_LOGIN_WEB_LOGIN, Constants.SCAN_LOGIN_ADMIN_LOGIN, Constants.SCAN_LOGIN_THIRD_LOGIN, Constants.SCAN_SAFE_LOGIN]
)
class ScanLoginHandler : UriHandler() {

    override fun shouldHandle(request: UriRequest) = true

    override fun handleInternal(request: UriRequest, callback: UriCallback) {
        val type = request.uri.getQueryParameter("type") ?: ""
        val qrcode = request.uri.getQueryParameter("qrCode") ?: ""
        val isFromShortCut = request.getBooleanField(ScanningActivity.FROM_SHORTCUT, false)

        if (type.isEmpty() || qrcode.isEmpty()) {
            callback.onComplete(UriResult.CODE_ERROR)
            return
        }
        QRScanLoginActivity.jumpToQRLoginActivity(
            request.context,
            type,
            qrcode,
            QRScanLoginActivity.LOGIN_STATUS_NONE,
            !ScanningActivity.hasLaunchMainPage && isFromShortCut
        )
        AppUtil.finishActivity(request.context)
        callback.onComplete(UriResult.CODE_SUCCESS)
    }
}