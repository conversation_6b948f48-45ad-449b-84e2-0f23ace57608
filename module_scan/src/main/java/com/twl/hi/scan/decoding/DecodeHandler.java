/*
 * Copyright (C) 2010 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.twl.hi.scan.decoding;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.google.zxing.BinaryBitmap;
import com.google.zxing.DecodeHintType;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.Result;
import com.google.zxing.common.HybridBinarizer;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.basic.apm.ApmAnalyticsAction;
import com.twl.hi.basic.mlkit.BarcodeScannerProcessor;
import com.twl.hi.basic.mlkit.core.FrameMetadata;
import com.twl.hi.scan.R;
import com.twl.hi.scan.ScanningActivity;
import com.twl.hi.scan.camera.CameraManager;
import com.twl.hi.scan.camera.PlanarYUVLuminanceSource;

import java.nio.ByteBuffer;
import java.util.Hashtable;

import lib.twl.common.util.ExecutorFactory;
import lib.twl.common.util.LList;

public final class DecodeHandler extends Handler {

    private static final String TAG = "DecodeHandler";

    private final ScanningActivity activity;
    private final MultiFormatReader multiFormatReader;

    private BarcodeScannerProcessor mBarcodeScannerProcessor;

    private boolean isFirstTime = true;

    DecodeHandler(ScanningActivity activity, Hashtable<DecodeHintType, Object> hints) {
        multiFormatReader = new MultiFormatReader();
        multiFormatReader.setHints(hints);
        this.activity = activity;
        initMLKit();
    }

    private void initMLKit() {
        mBarcodeScannerProcessor = new BarcodeScannerProcessor();
    }

    @Override
    public void handleMessage(Message message) {
        if (message.what == R.id.decode) {
            TLog.debug(TAG, "----------------new frame return----------------");
            decodeByMLKit((byte[]) message.obj, message.arg1, message.arg2);
        } else if (message.what == R.id.quit) {
            Looper.myLooper().quit();
            if (mBarcodeScannerProcessor != null) {
                mBarcodeScannerProcessor.stop();
            }
        }
    }

    private void decodeByMLKit(byte[] frameData, int width, int height) {
        try {
            if (mBarcodeScannerProcessor != null) {
                long startTimeMillis = System.currentTimeMillis();
                mBarcodeScannerProcessor.processByteBuffer(
                        ByteBuffer.wrap(frameData),
                        new FrameMetadata.Builder()
                                .setWidth(width)
                                .setHeight(height)
                                .setRotation(0)
                                .build(),
                        barcodeList -> {
                            TLog.info(TAG, "decodeByMLKit,result size:" + LList.getCount(barcodeList) +
                                    ",cost time:" + (System.currentTimeMillis() - startTimeMillis));
                            if (LList.isNotEmpty(barcodeList)) {
                                DecodeResult decodeResult = new DecodeResult(
                                        barcodeList.get(0).getDisplayValue(),
                                        barcodeList.get(0).getFormat() + ""
                                );
                                decodeResult.setSource(ApmAnalyticsAction.ACTION_SCAN_TYPE_MLKIT);
                                notifyDecodeResult(decodeResult);
                            } else {
                                if (isFirstTime) {
                                    isFirstTime = false;
                                    notifyFailedAndRetry();
                                } else {
                                    ExecutorFactory.execWorkTask(new Runnable() {
                                        @Override
                                        public void run() {
                                            decodeByZXing(frameData, width, height);
                                        }
                                    });
                                }
                            }
                        }
                );
            }
        } catch (Exception ignored) {
        }
    }

    /**
     * Decode the data within the viewfinder rectangle, and time how long it took. For efficiency,
     * reuse the same reader objects from one decode to the next.
     *
     * @param data   The YUV preview frame.
     * @param width  The width of the preview frame.
     * @param height The height of the preview frame.
     */
    private void decodeByZXing(byte[] data, int width, int height) {

        Result rawResult = null;

        long startTime = System.currentTimeMillis();

        //modify here
        byte[] rotatedData = new byte[data.length];
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++)
                rotatedData[x * height + height - y - 1] = data[x + y * width];
        }

        int tmp = width; // Here we are swapping, that's the difference to #11
        width = height;
        height = tmp;

        try {
            PlanarYUVLuminanceSource source = CameraManager.get().buildLuminanceSource(rotatedData, width, height);
            BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
            rawResult = multiFormatReader.decodeWithState(bitmap);
        } catch (Exception re) {
            // continue
            TLog.error(TAG,
                    "decodeByZXing,no result:" + re + ",cost time:" + (System.currentTimeMillis() - startTime) + "," + re.getMessage()+",hasSurface:"+activity.hasSurface);
        } finally {
            multiFormatReader.reset();
        }
        Result finalRawResult = rawResult;
        ExecutorFactory.execMainTask(() -> {
            if (finalRawResult != null) {
                TLog.info(TAG, "decodeByZXing," + ",cost time:" + (System.currentTimeMillis() - startTime));
                DecodeResult decodeResult = new DecodeResult(finalRawResult.getText(), finalRawResult.getBarcodeFormat().name());
                decodeResult.setSource(ApmAnalyticsAction.ACTION_SCAN_TYPE_ZXING);
                notifyDecodeResult(decodeResult);
            } else {
                notifyFailedAndRetry();
            }
        });
    }

    /**
     * 通知扫码结果
     */
    private void notifyDecodeResult(DecodeResult decodeResult) {

        mBarcodeScannerProcessor.stop();
        multiFormatReader.reset();
        activity.getHandler().removeMessages(R.id.decode_failed);

        Message message = Message.obtain(activity.getHandler(), R.id.decode_succeeded, decodeResult);
        message.sendToTarget();
    }

    private void notifyFailedAndRetry() {
        if (!activity.hasSurface) {
            //扫一扫页面退到后台再切换回前台时无法预览bug修复
            //问题的原因是退到后台后ScanningActivity.surfaceDestroyed回调会先触发,
            //然后执行后面的getHandler方法就会在后台提前初始化ScanningActivityHandler并开启预览,这时候是会预览失败的
            //再次回到前台之后由于ScanningActivityHandler不为空,就不会重新初始化+预览,导致黑屏
            return;
        }
        Message message = Message.obtain(activity.getHandler(), R.id.decode_failed);
        message.sendToTarget();
    }

}
