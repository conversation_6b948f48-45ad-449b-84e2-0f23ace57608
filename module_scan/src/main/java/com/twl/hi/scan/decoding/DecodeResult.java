package com.twl.hi.scan.decoding;

/**
 * 二维码识别结果
 */
public class DecodeResult {

    private String source;

    private String text;

    private String format;

    public DecodeResult(String text, String format) {
        this.text = text;
        this.format = format;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }
}
