"use strict";
window.addEventListener('error', function(event) {
	// event对象包含了错误的相关信息
	console.log('捕获到未处理的错误:', event.message);
	console.log('错误发生的文件:', event.filename);
	console.log('错误发生的行号:', event.lineno);
	console.log('错误发生的列号:', event.colno);
	console.log('错误的堆栈信息:', event.error ? event.error.stack : '无堆栈信息');

	// 阻止默认的错误处理行为
	event.preventDefault();
    setTimeout(function() {
        window.WebViewJavascriptBridge.callHandler("onJsRuntimeError", {
            'errorCode': 1000,
            'errorMsg': event.message,
            'errorStack': event.error ? event.error.stack : '无堆栈信息'
        }, function(resp) {});
    }, 1000); // 1秒后, WebViewJavascriptBridge注册后再输出
});

function connectWebViewJavascriptBridge(callback) {
    if (window.WebViewJavascriptBridge && WebViewJavascriptBridge.inited) {
        callback(WebViewJavascriptBridge)
    } else {
        document.addEventListener(
            'WebViewJavascriptBridgeReady'
            , function() {
                callback(WebViewJavascriptBridge)
            },
            false
        );
    }
}

connectWebViewJavascriptBridge(function(bridge) {
    bridge.init(function(message, responseCallback) {
        console.log('JS got a message', message);
        var data = {
            'Javascript Responds': '测试中文!'
        };

        if (responseCallback) {
            console.log('JS responding with', data);
            responseCallback(data);
        }
    });
})