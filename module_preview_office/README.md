# module_preview_office

## 基础信息

### 模块名称和主要用途
module_preview_office 是办公文档预览业务模块，提供对Office文档（Word、Excel、PowerPoint等）的预览和基本操作功能。

### 系统定位和作用
- 作为项目的核心业务模块之一
- 依赖module_foundation_business等基础模块
- 提供Office文档预览相关功能
- 管理文档预览数据和状态

### 技术栈和框架
- MVVM架构
- Kotlin协程
- DataBinding
- Lifecycle组件
- Room数据库
- 第三方Office预览引擎
- 文件解析
- 渲染控件
- 缓存管理

### 核心依赖项
- module_foundation_business：业务基础模块
- lib_foundation_service：基础服务
- lib_http：网络请求
- lib_file：文件处理
- lib_cache：缓存服务
- lib_secret：加密服务
- export_module_office_preview：预览模块导出接口

## 功能描述

### 主要功能
1. Word文档预览
   - 文档加载
   - 页面渲染
   - 文本搜索
   - 目录导航
2. Excel表格预览
   - 表格显示
   - 单元格查看
   - 工作表切换
   - 公式显示
3. PowerPoint演示预览
   - 幻灯片显示
   - 切换效果
   - 缩略图模式
   - 全屏查看
4. 通用文档管理
   - 文档列表
   - 预览历史
   - 收藏标记
   - 下载与分享

### 关键业务流程
1. 文档预览流程
   - 文件加载
   - 格式解析
   - 内容渲染
   - 交互响应
2. 预览控制流程
   - 预览初始化
   - 视图控制
   - 操作处理
   - 状态同步
3. 文档搜索流程
   - 关键词输入
   - 内容匹配
   - 结果高亮
   - 位置跳转
4. 预览状态管理
   - 状态记录
   - 数据持久化
   - 意外恢复
   - 多文档切换

### 业务规则和约束
1. 预览规则
   - 支持格式
   - 文件大小限制
   - 页数限制
   - 性能优化
2. 操作规则
   - 权限控制
   - 操作限制
   - 预览时长
   - 缓存清理
3. 安全规则
   - 文档加密
   - 水印处理
   - 内容保护
   - 安全检查
4. 存储规则
   - 缓存策略
   - 空间管理
   - 过期处理
   - 清理机制

### 与其他模块交互
- 依赖基础模块
  - module_foundation_business
  - lib_foundation_service
- 依赖功能模块
  - lib_http
  - lib_file
  - lib_cache
  - lib_secret
- 对外暴露接口
  - export_module_office_preview

## 技术架构

### 整体架构设计
```
module_foundation_business (业务基础模块)
            ↑
module_preview_office (Office预览模块)
    ↑    ↑    ↑
UI层  业务层  数据层
```

### 核心组件及关系
1. UI组件
   - 文档预览组件
   - 工具栏组件
   - 导航组件
   - 搜索组件
2. 业务组件
   - 预览管理器
   - 格式解析器
   - 渲染管理器
   - 操作管理器
3. 数据组件
   - 预览状态存储
   - 配置存储
   - 缓存存储
   - 历史记录存储
4. 工具组件
   - 文档解析工具
   - 渲染工具
   - 搜索工具
   - 导航工具

### 数据流转过程
1. 文档加载处理
   - 文件获取
   - 格式识别
   - 内容解析
   - 渲染准备
2. 预览渲染处理
   - 视图创建
   - 内容布局
   - 样式应用
   - 交互绑定
3. 操作处理
   - 操作捕获
   - 命令解析
   - 状态更新
   - 视图刷新

### 设计模式使用
1. MVVM模式：界面交互
2. 单例模式：管理器类
3. 策略模式：预览策略
4. 观察者模式：状态监听
5. 工厂模式：预览器创建
6. 建造者模式：配置构建
7. 命令模式：操作处理

## 代码结构

### 目录组织
```
module_preview_office/
├── src/main/java/com/twl/hi/preview/office/
│   ├── ui/             # UI实现
│   │   ├── word/       # Word预览
│   │   ├── excel/      # Excel预览
│   │   ├── ppt/        # PPT预览
│   │   └── common/     # 通用组件
│   ├── business/       # 业务实现
│   │   ├── preview/    # 预览业务
│   │   ├── parser/     # 解析业务
│   │   ├── render/     # 渲染业务
│   │   └── operation/  # 操作业务
│   ├── data/           # 数据处理
│   │   ├── db/         # 数据库
│   │   ├── cache/      # 缓存
│   │   └── config/     # 配置
│   ├── utils/          # 工具类
│   └── model/          # 数据模型
```

### 关键类说明
- OfficePreviewManager: 预览管理器
- WordPreviewActivity: Word预览界面
- ExcelPreviewActivity: Excel预览界面
- PowerPointPreviewActivity: PPT预览界面
- DocumentParserManager: 文档解析管理器
- RenderManager: 渲染管理器
- OperationManager: 操作管理器
- PreviewDB: 预览数据库

### 代码分层
1. 表现层
   - 界面实现
   - 交互处理
2. 业务层
   - 预览管理
   - 文档解析
3. 数据层
   - 状态数据
   - 配置数据
4. 工具层
   - 业务工具
   - 通用工具 