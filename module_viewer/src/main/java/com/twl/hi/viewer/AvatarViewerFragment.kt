package com.twl.hi.viewer

import android.app.Activity
import android.content.Intent
import android.graphics.Matrix
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentActivity
import com.common.AvoidOnResult
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.callback.AvatarCallback
import com.twl.hi.basic.databinding.PopupChangeAvatarBinding
import com.twl.hi.viewer.core.DraggableImageView
import com.twl.hi.viewer.core.DraggableImageView.RealLongClickListener
import com.twl.hi.viewer.core.SCROLL_STATE_DRAGGING
import com.twl.hi.viewer.bean.MediaViewerListener
import com.twl.hi.viewer.core.photoview.OnScaleChangedListener
import com.twl.hi.viewer.databinding.ViewerFragmentAvatarViewerBinding
import com.twl.hi.viewer.viewmodel.AvatarViewerViewModel
import com.twl.kzmp.load
import com.zhihu.matisse.Matisse
import hi.kernel.BundleConstants
import hi.kernel.HiKernel
import lib.twl.common.photoselect.MatisseUtils
import lib.twl.common.photoselect.PhotoSelectManager
import lib.twl.common.util.AppUtil
import lib.twl.common.util.QMUIDisplayHelper
import lib.twl.common.util.widget.HiPopupWindow
import lib.twl.common.views.imagesview.Image
import lib.twl.common.views.imagesview.ViewerOptionsClickListener
import java.io.File
import java.net.URI
import java.net.URISyntaxException

/**
 * Author : Xuweixiang .
 * Date   : On 2023/7/19
 * Email  : Contact <EMAIL>
 * Desc   :
 *
 */

fun newInstance(image: Image) = AvatarViewerFragment().apply {
    arguments = Bundle().apply {
        putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, image)
    }
}

private const val TAG = "AvatarViewerFragment"

class AvatarViewerFragment :
    AbsViewerFragment<ViewerFragmentAvatarViewerBinding, AvatarViewerViewModel>(),
    ViewerOptionsClickListener, AvatarCallback {

    private var mMediaViewerListener: MediaViewerListener? = null

    private var mPopupWindow: HiPopupWindow? = null

    fun setMediaViewerListener(mediaViewerListener: MediaViewerListener?) {
        mMediaViewerListener = mediaViewerListener
    }

    private
    var isSelf: Boolean = false

    private var lastScaleX = 1f

    override fun getContentLayoutId(): Int {
        return R.layout.viewer_fragment_avatar_viewer
    }

    override fun initFragment() {
        super.initFragment()
        mDataBinding.callback = this
        viewModel.observeContact(mImageInfo?.userId ?: "", mImageInfo?.avatarDecorationAppLink)
        isSelf = mImageInfo?.userId == HiKernel.getHikernel().account.userId
        viewModel.getImageInfoLiveData().observe(this) { image ->
            if (image != null) {
                mDataBinding.imageView.showImageWithAnimator(image)
                if (isSelf) {
                    if (!TextUtils.isEmpty(image.avatarDecoration)) {
                        mDataBinding.ivPendant.load(image.avatarDecoration)
                        mDataBinding.groupPendant.visibility = View.VISIBLE
                    } else {
                        mDataBinding.groupPendant.visibility = View.GONE
                    }
                    // 根据是否展示头像挂件计算头像的偏移量
                    val verticalOffset = if (!TextUtils.isEmpty(image.avatarDecoration)) {
                        -QMUIDisplayHelper.dp2px(activity, 120) * 1.0f
                    } else 0f
                    mDataBinding.imageView.setupOriginOffset(Matrix().apply {
                        postTranslate(0f, verticalOffset)
                    })
                } else {
                    mDataBinding.groupPendant.visibility = View.GONE
                }
            } else {
                mDataBinding.groupPendant.visibility = View.GONE
            }
            dismissProgressDialog()
        }
        if (isSelf) {
            mDataBinding.imageView.getScrollStateLiveData().observe(this) { state ->
                val matrix = Matrix()
                mDataBinding.imageView.getPhotoView().getSuppMatrix(matrix)
                val floatArray = FloatArray(9)
                matrix.getValues(floatArray)
                val scaleX = floatArray[Matrix.MSCALE_X]
                val isScaled = scaleX != 1f
                // 触摸操作时隐藏底部控件，反之展示
                if (isScaled) {
                    mDataBinding.groupPendant.visibility = View.INVISIBLE
                    mDataBinding.groupOperator.visibility = View.INVISIBLE
                } else {
                    when (state) {
                        SCROLL_STATE_DRAGGING -> {
                            mDataBinding.groupPendant.visibility = View.INVISIBLE
                            mDataBinding.groupOperator.visibility = View.INVISIBLE
                        }

                        else -> {
                            if (!TextUtils.isEmpty(viewModel.getImageInfoLiveData().value?.avatarDecoration)) {
                                mDataBinding.groupPendant.visibility = View.VISIBLE
                            }
                            mDataBinding.groupOperator.visibility = View.VISIBLE
                        }
                    }
                }
            }
            if (!TextUtils.isEmpty(viewModel.getImageInfoLiveData().value?.avatarDecoration)) {
                mDataBinding.imageView.setupOriginOffset(Matrix().apply {
                    postTranslate(0f, -QMUIDisplayHelper.dp2px(activity, 100) * 1.0f)
                })
            }
            // 自己的头像才添加缩放监听
            dataBinding.imageView.mScaleChangedListener =
                OnScaleChangedListener { _, _, _, suppMatrix ->
                    // 当图片处于缩放状态时隐藏底部控件，反之显示
                    val floatArray = FloatArray(9)
                    suppMatrix.getValues(floatArray)
                    val scaleX = floatArray[Matrix.MSCALE_X]
                    if (scaleX == 1f && lastScaleX != 1f) {
                        // 恢复原来尺寸，且之前处于缩放状态
                        if (!TextUtils.isEmpty(viewModel.getImageInfoLiveData().value?.avatarDecoration)) {
                            mDataBinding.groupPendant.visibility = View.VISIBLE
                        }
                        mDataBinding.groupOperator.visibility = View.VISIBLE
                    } else {
                        mDataBinding.groupPendant.visibility = View.INVISIBLE
                        mDataBinding.groupOperator.visibility = View.INVISIBLE
                    }
                    lastScaleX = scaleX
                }
        } else {
            mDataBinding.groupOperator.visibility = View.INVISIBLE
        }
        dataBinding.imageView.apply {
            onImageDragActionListener = object : DraggableImageView.OnImageDragActionListener {
                override fun onLoadSuccess(imageFilePath: String?, source: String) {
                    imageFilePath ?: return
                    handleImageQRCodeFindByZXing(mDataBinding.imageView, viewModel, imageFilePath, source)
                }

                override fun onDragToExit() {
                    mMediaViewerListener?.onViewerClose()
                }

                override fun onDragToRestore() {
                    mMediaViewerListener?.onImageDragRestore()
                }
            }
            longClickListener = object : RealLongClickListener {
                override fun click() {
                    showBottomDialog(viewModel)
                }
            }
        }
        mDataBinding.tvPendantEdit.setOnClickListener {
            viewModel.getImageInfoLiveData().value?.avatarDecorationAppLink?.takeIf { it.isNotEmpty() }
                ?.let { applink ->
                    AppUtil.startUri(activity, applink)
                }
        }
    }

    override fun onMoreClick() {
        showBottomDialog(viewModel)
    }

    override fun changeAvatar() {
        if (mPopupWindow == null) {
            activity.run {
                val binding: PopupChangeAvatarBinding = DataBindingUtil.inflate(
                    LayoutInflater.from(this),
                    R.layout.popup_change_avatar,
                    null,
                    false
                )
                binding.photo = resources.getString(R.string.photo)
                binding.album = resources.getString(R.string.album)
                binding.callback = this@AvatarViewerFragment
                mPopupWindow = HiPopupWindow.Builder(this).setContentView(binding.root)
                    .setAnimationStyle(R.style.pop_anim_style).setShadow(window, 0.6f).build()
            }
        }
        mPopupWindow!!.showAtLocation(dataBinding.root, Gravity.BOTTOM, 0, 0)
    }

    override fun selectFromAlbum() {
        if (mPopupWindow != null) {
            mPopupWindow!!.dismiss()
        }
        val requestCode = 1001
        val callback =
            AvoidOnResult.Callback { code: Int, resultCode: Int, data: Intent? ->
                if (code == requestCode && resultCode == Activity.RESULT_OK) {
                    val uploadFile =
                        MatisseUtils.getFileFromSelectPhotoResult(data) ?: return@Callback
                    showProgressDialog("正在上传头像...")
                    viewModel.changeAvatar(uploadFile)
                }
            }
        PhotoSelectManager.chooseImageFromAlbum(activity as FragmentActivity, requestCode, callback)
    }

    override fun selectFromPhoto() {
        if (mPopupWindow != null) {
            mPopupWindow!!.dismiss()
        }

        val callback =
            AvoidOnResult.Callback { _: Int, resultCode: Int, data: Intent? ->
                if (resultCode == Activity.RESULT_OK) {
                    try {
                        showProgressDialog("正在上传头像...")
                        viewModel.changeAvatar(
                            File(
                                URI(
                                    Matisse.obtainResult(
                                        data
                                    )[0].toString()
                                )
                            )
                        )
                    } catch (e: URISyntaxException) {
                        TLog.error(
                            TAG,
                            "get image file fail : " + e.message
                        )
                    }
                }
            }

        PhotoSelectManager.chooseImageFromPhoto(activity as FragmentActivity, callback)
    }

    override fun getCurrentDisplayImageUrl(): String {
        return mDataBinding.imageView.getDisplayImageUrl()
    }

    override fun dismissPop() {
        mPopupWindow?.dismiss()
    }

}