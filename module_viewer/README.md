# module_viewer

## 基础信息

### 模块名称和主要用途
module_viewer 是查看器业务模块，提供各种文件、图片、文档等内容的查看和预览功能。

### 系统定位和作用
- 作为项目的核心业务模块之一
- 依赖module_foundation_business等基础模块
- 提供文件查看和预览相关功能
- 管理预览数据和状态

### 技术栈和框架
- MVVM架构
- Kotlin协程
- DataBinding
- Lifecycle组件
- Room数据库
- 自定义控件
- 图片处理
- 文档解析
- 缓存管理

### 核心依赖项
- module_foundation_business：业务基础模块
- lib_foundation_service：基础服务
- lib_http：网络请求
- lib_file：文件处理
- lib_image：图片处理
- lib_cache：缓存服务
- lib_secret：加密服务

## 功能描述

### 主要功能
1. 图片查看
   - 多图浏览
   - 缩放功能
   - 手势操作
   - 分享保存
2. 文档查看
   - 常见文档格式支持
   - 滚动和翻页
   - 目录跳转
   - 搜索功能
3. 音视频预览
   - 缩略图显示
   - 基本控制
   - 详情信息
   - 打开方式
4. 查看管理
   - 历史记录
   - 收藏标记
   - 最近查看
   - 浏览设置

### 关键业务流程
1. 文件查看流程
   - 资源加载
   - 格式解析
   - 内容渲染
   - 交互响应
2. 图片浏览流程
   - 图片加载
   - 内存管理
   - 手势处理
   - 特效应用
3. 文档浏览流程
   - 文档解析
   - 分页处理
   - 内容显示
   - 搜索处理
4. 查看历史管理
   - 记录保存
   - 查询统计
   - 清理过期
   - 关联推荐

### 业务规则和约束
1. 查看规则
   - 格式支持
   - 大小限制
   - 安全检查
   - 性能优化
2. 操作规则
   - 权限控制
   - 手势定义
   - 超时处理
   - 中断恢复
3. 存储规则
   - 缓存策略
   - 清理机制
   - 空间限制
   - 加密要求
4. 安全规则
   - 内容审查
   - 防泄漏措施
   - 水印处理
   - 复制限制

### 与其他模块交互
- 依赖基础模块
  - module_foundation_business
  - lib_foundation_service
- 依赖功能模块
  - lib_http
  - lib_file
  - lib_image
  - lib_cache
  - lib_secret

## 技术架构

### 整体架构设计
```
module_foundation_business (业务基础模块)
            ↑
module_viewer (查看器模块)
    ↑    ↑    ↑
UI层  业务层  数据层
```

### 核心组件及关系
1. UI组件
   - 图片查看组件
   - 文档查看组件
   - 音视频预览组件
   - 通用查看组件
2. 业务组件
   - 查看管理器
   - 格式解析器
   - 加载管理器
   - 历史管理器
3. 数据组件
   - 查看记录存储
   - 配置存储
   - 缓存存储
   - 统计存储
4. 工具组件
   - 图片处理工具
   - 文档解析工具
   - 编解码工具
   - 格式转换工具

### 数据流转过程
1. 查看处理
   - 文件解析
   - 格式转换
   - 内容渲染
   - 交互响应
2. 图片处理
   - 图片解码
   - 内存优化
   - 特效应用
   - 显示输出
3. 历史处理
   - 记录保存
   - 数据分析
   - 推荐生成
   - 展示应用

### 设计模式使用
1. MVVM模式：界面交互
2. 单例模式：管理器类
3. 策略模式：查看策略
4. 观察者模式：状态监听
5. 工厂模式：查看器创建
6. 建造者模式：配置构建
7. 命令模式：操作处理

## 代码结构

### 目录组织
```
module_viewer/
├── src/main/java/com/twl/hi/viewer/
│   ├── ui/              # UI实现
│   │   ├── image/       # 图片查看
│   │   ├── document/    # 文档查看
│   │   ├── media/       # 媒体预览
│   │   └── common/      # 通用组件
│   ├── business/        # 业务实现
│   │   ├── viewer/      # 查看业务
│   │   ├── parser/      # 解析业务
│   │   ├── loader/      # 加载业务
│   │   └── history/     # 历史业务
│   ├── data/            # 数据处理
│   │   ├── db/          # 数据库
│   │   ├── cache/       # 缓存
│   │   └── config/      # 配置
│   ├── utils/           # 工具类
│   └── model/           # 数据模型
```

### 关键类说明
- ViewerManager: 查看管理器
- ImageViewerActivity: 图片查看界面
- DocumentViewerActivity: 文档查看界面
- MediaPreviewActivity: 媒体预览界面
- ParserManager: 解析管理器
- LoaderManager: 加载管理器
- HistoryManager: 历史管理器
- ViewerDB: 查看数据库

### 代码分层
1. 表现层
   - 界面实现
   - 交互处理
2. 业务层
   - 查看管理
   - 格式解析
3. 数据层
   - 历史数据
   - 配置数据
4. 工具层
   - 业务工具
   - 通用工具 