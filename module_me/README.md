# module_me

## 基础信息

### 模块名称和主要用途
module_me 是个人中心业务模块，提供用户信息展示、个人设置、账号管理等个人中心相关功能。

### 系统定位和作用
- 作为项目的核心业务模块之一
- 依赖module_foundation_business等基础模块
- 提供个人信息管理功能
- 管理用户设置和状态

### 技术栈和框架
- MVVM架构
- Kotlin协程
- DataBinding
- Lifecycle组件
- Room数据库
- 图片加载
- 状态管理
- 缓存管理

### 核心依赖项
- module_foundation_business：业务基础模块
- lib_foundation_service：基础服务
- lib_http：网络请求
- lib_image：图片处理
- lib_cache：缓存服务
- lib_secret：加密服务
- export_module_organization：组织架构导出
- export_module_setting：设置导出

## 功能描述

### 主要功能
1. 个人信息
   - 基本信息
   - 详细资料
   - 头像管理
   - 二维码名片
2. 账号管理
   - 账号设置
   - 密码修改
   - 手机号绑定
   - 邮箱绑定
3. 个人设置
   - 通知设置
   - 隐私设置
   - 安全设置
   - 偏好设置
4. 数据管理
   - 存储管理
   - 清理缓存
   - 数据备份
   - 恢复出厂

### 关键业务流程
1. 个人信息管理流程
   - 信息获取
   - 信息编辑
   - 信息验证
   - 信息保存
2. 账号设置流程
   - 设置获取
   - 设置修改
   - 设置验证
   - 设置保存
3. 头像管理流程
   - 图片选择
   - 图片裁剪
   - 图片上传
   - 图片更新
4. 安全设置流程
   - 身份验证
   - 安全项设置
   - 安全验证
   - 结果保存

### 业务规则和约束
1. 信息规则
   - 修改权限
   - 必填项要求
   - 格式验证
   - 变更审核
2. 账号规则
   - 密码强度
   - 验证流程
   - 绑定限制
   - 安全策略
3. 隐私规则
   - 信息可见性
   - 数据共享
   - 隐私控制
   - 授权管理
4. 安全规则
   - 数据加密
   - 登录保护
   - 操作验证
   - 风险控制

### 与其他模块交互
- 依赖基础模块
  - module_foundation_business
  - lib_foundation_service
- 依赖功能模块
  - lib_http
  - lib_image
  - lib_cache
  - lib_secret
- 依赖导出接口
  - export_module_organization
  - export_module_setting

## 技术架构

### 整体架构设计
```
module_foundation_business (业务基础模块)
            ↑
module_me (个人中心模块)
    ↑    ↑    ↑
UI层  业务层  数据层
```

### 核心组件及关系
1. UI组件
   - 个人信息界面
   - 账号设置界面
   - 隐私设置界面
   - 安全设置界面
2. 业务组件
   - 信息管理器
   - 账号管理器
   - 设置管理器
   - 安全管理器
3. 数据组件
   - 用户存储
   - 设置存储
   - 缓存存储
   - 安全存储
4. 工具组件
   - 图片工具
   - 验证工具
   - 加密工具
   - 通用工具

### 数据流转过程
1. 个人信息处理
   - 信息获取
   - 信息展示
   - 信息编辑
   - 信息保存
2. 设置处理
   - 设置获取
   - 设置展示
   - 设置修改
   - 设置保存
3. 安全处理
   - 安全验证
   - 操作授权
   - 操作执行
   - 结果反馈

### 设计模式使用
1. MVVM模式：界面交互
2. 单例模式：管理器类
3. 策略模式：设置策略
4. 观察者模式：状态监听
5. 工厂模式：界面创建
6. 建造者模式：请求构建
7. 命令模式：操作处理

## 代码结构

### 目录组织
```
module_me/
├── src/main/java/com/twl/hi/me/
│   ├── ui/           # UI实现
│   │   ├── profile/  # 个人信息界面
│   │   ├── account/  # 账号设置界面
│   │   ├── privacy/  # 隐私设置界面
│   │   └── security/ # 安全设置界面
│   ├── business/     # 业务实现
│   │   ├── profile/  # 个人信息业务
│   │   ├── account/  # 账号设置业务
│   │   ├── privacy/  # 隐私设置业务
│   │   └── security/ # 安全设置业务
│   ├── data/         # 数据处理
│   │   ├── db/       # 数据库
│   │   ├── cache/    # 缓存
│   │   └── config/   # 配置
│   ├── utils/        # 工具类
│   └── model/        # 数据模型
```

### 关键类说明
- ProfileManager: 个人信息管理器
- AccountManager: 账号管理器
- PrivacyManager: 隐私管理器
- SecurityManager: 安全管理器
- ProfileActivity: 个人信息界面
- AccountActivity: 账号设置界面
- PrivacyActivity: 隐私设置界面
- SecurityActivity: 安全设置界面

### 代码分层
1. 表现层
   - 界面实现
   - 交互处理
2. 业务层
   - 信息管理
   - 设置管理
3. 数据层
   - 用户数据
   - 设置数据
4. 工具层
   - 业务工具
   - 通用工具 