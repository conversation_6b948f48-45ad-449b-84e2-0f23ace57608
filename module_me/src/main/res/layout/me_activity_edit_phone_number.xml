<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="callback"
            type="com.twl.hi.me.callback.EditPhoneNumberCallback" />

        <variable
            name="viewModel"
            type="com.twl.hi.me.viewmodel.EditPhoneNumberViewModel" />

        <variable
            name="user"
            type="com.twl.hi.foundation.model.User" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            app:callback="@{callback}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title="@{@string/me_phone_number_edit}" />

        <LinearLayout
            android:id="@+id/layout_change_mobile"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@android:color/white"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_weight="1"
                android:text="@string/me_change_phone_num_title"
                android:textColor="@color/color_0D0D1A"
                android:textSize="15sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{user.phone}"
                android:textColor="@color/color_818188"
                android:textSize="14sp"
                tools:text="13111111111" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/color_F6F6F8"
            android:paddingHorizontal="20dp"
            android:paddingVertical="7dp"
            android:text="@string/me_change_phone_tip"
            android:textColor="@color/color_B1B1B8"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_change_mobile" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@android:color/white"
            android:gravity="center_vertical"
            android:onClick="@{()->callback.changePhoneScope()}"
            android:orientation="horizontal"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_tips">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_weight="1"
                android:text="@string/me_setting_mobile"
                android:textColor="@color/color_0D0D1A"
                android:textSize="15sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right_s"
                android:drawablePadding="9dp"
                android:textColor="@color/color_818188"
                android:textSize="14sp"
                app:phoneScope="@{user.phoneScope}"
                tools:text="小组内与上级可见" />
        </LinearLayout>

        <FrameLayout
            android:id="@+id/layout_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>