<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".me.NickNameSettingFragment">

    <data>

        <import type="android.text.TextUtils" />

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.twl.hi.me.viewmodel.MeSettingViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.me.callback.NickNameSettingCallback" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#fff8f8f8"
        android:clickable="true"
        android:focusable="true"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_height"
            android:background="@android:color/white">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:onClick="@{()->callback.back()}"
                android:paddingLeft="16dp"
                android:text="@string/cancel"
                android:textColor="#FF212121"
                android:textSize="17sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/me_set_nick_name"
                android:textColor="#212121"
                android:textSize="17sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:enabled="@{TextUtils.isEmpty(viewModel.nickInput) ? true : !TextUtils.equals(viewModel.nickInput,viewModel.nickName)}"
                android:gravity="center_vertical"
                android:onClick="@{()->callback.done()}"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:text="@string/save"
                android:textColor="@color/sel_change_nick_name_color"
                android:textSize="17sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_alignParentBottom="true"
                android:background="@color/color_CFCFCF" />
        </RelativeLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <EditText
                android:id="@+id/tv_input"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@color/app_white"
                android:hint="@string/me_nickname_change_hint"
                android:paddingLeft="20dp"
                android:paddingRight="34dp"
                android:singleLine="true"
                android:text="@={viewModel.nickInput}" />

            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_gravity="right|center_vertical"
                android:layout_marginRight="16dp"
                android:onClick="@{()->callback.clear()}"
                android:src="@drawable/me_ic_icon_del"
                android:visibility="@{TextUtils.isEmpty(viewModel.nickInput) ? View.GONE : View.VISIBLE}" />

        </FrameLayout>

    </LinearLayout>


</layout>