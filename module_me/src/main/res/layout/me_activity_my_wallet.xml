<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".chat.SingleRedEnvelopeFragment">

    <data>

        <import type="com.twl.hi.basic.util.ThemeUtils" />

        <variable
            name="viewModel"
            type="com.twl.hi.me.viewmodel.MyWalletViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.me.callback.MyWalletCallback" />
    </data>

    <LinearLayout
        android:id="@+id/fragmentMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white"
        android:clickable="true"
        android:focusable="true"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            app:callback="@{callback}"
            app:title="@{@string/me_wallet}" />

        <ImageView
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="96dp"
            android:src="@drawable/me_ic_coin" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="bottom|center_horizontal"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/balance_tv_unit_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/din"
                android:text="@string/rmb_icon"
                android:textColor="@color/color_15181D"
                android:textSize="36sp"
                android:textStyle="bold"
                app:layout_constraintBaseline_toBaselineOf="@+id/balance_tv"
                app:layout_constraintEnd_toStartOf="@id/balance_tv"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/balance_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginEnd="20dp"
                android:fontFamily="@font/din"
                android:textColor="@color/color_15181D"
                android:textSize="48sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/balance_tv_unit_tv"
                tools:text="206.00" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:text="@string/me_wallet_balance"
            android:textColor="@color/color_858A99"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/withdrawn_cash_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="128dp"
            android:background="@{ThemeUtils.getThemeButtonBgRes()}"
            android:clickable="true"
            android:enabled="true"
            android:focusable="true"
            android:gravity="center"
            android:onClick="@{()->callback.onWithdrawClick()}"
            android:paddingHorizontal="64dp"
            android:paddingVertical="13dp"
            android:text="@string/me_withdraw"
            android:textColor="@color/app_white"
            android:textSize="16sp"
            tools:background="@drawable/bg_selector_common_button_primary" />
    </LinearLayout>
</layout>