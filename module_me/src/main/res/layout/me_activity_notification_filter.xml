<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.twl.hi.me.viewmodel.NotificationFilterViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.basic.callback.TitleBarCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            app:callback="@{callback}"
            app:left='@{" "}'
            app:right="@{@string/save}"
            app:rightEnabled="@{viewModel.saveEnabled}"
            app:title="@{@string/me_push_option_filtered}" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_notify_options"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/app_white"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintTop_toBottomOf="@id/title_bar"
            tools:itemCount="5"
            tools:listitem="@layout/me_item_notification_filter" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
