<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/app_white">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            app:hideBackIv="@{true}"
            app:left='@{@string/cancel}'
            app:right='@{@string/done}'
            app:title="@{@string/me_font_scale}" />

        <include
            android:id="@+id/user_avatar"
            layout="@layout/item_avatar2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_bar"
            app:roundAsCircle="true"
            tools:actualImageResource="@drawable/ic_app" />

        <TextView
            android:id="@+id/preview_send"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="60dp"
            android:layout_marginEnd="10dp"
            android:background="@drawable/bg_corner_8_chat_message_to"
            android:paddingHorizontal="12dp"
            android:paddingVertical="9dp"
            android:text="@string/me_font_scale_hint1"
            android:textColor="@color/color_0D0D1A"
            android:textSize="16sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/user_avatar"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/user_avatar"
            tools:text="说明文案是相对较长的文本内容时，应能够正确换行显示" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/send_barrier"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="preview_send,user_avatar" />

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/receiver1"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="20dp"
            app:actualImageResource="@mipmap/ic_logo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/send_barrier"
            app:roundAsCircle="true" />

        <TextView
            android:id="@+id/receiver_msg1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="60dp"
            android:background="@drawable/bg_corner_8_chat_message_from"
            android:paddingHorizontal="12dp"
            android:paddingVertical="9dp"
            android:text="@string/me_font_scale_hint2"
            android:textColor="@color/color_0D0D1A"
            android:textSize="16sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/receiver1"
            app:layout_constraintTop_toTopOf="@id/receiver1"
            tools:text="说明文案显示" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/receive1_barrier"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="receiver1,receiver_msg1" />

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/receiver2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="20dp"
            app:actualImageResource="@mipmap/ic_logo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/receive1_barrier"
            app:roundAsCircle="true" />

        <TextView
            android:id="@+id/receiver_msg2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="60dp"
            android:background="@drawable/bg_corner_8_chat_message_from"
            android:paddingHorizontal="12dp"
            android:paddingVertical="9dp"
            android:text="@string/me_font_scale_hint3"
            android:textColor="@color/color_0D0D1A"
            android:textSize="16sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/receiver2"
            app:layout_constraintTop_toTopOf="@id/receiver2"
            tools:text="说明文案是相对较长的文本内容时，应能够正确换行显示" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="@string/me_font_scale_hint4"
            android:textColor="@color/color_C4C6CD"
            app:layout_constraintBottom_toTopOf="@id/controller"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/controller"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/app_white"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:elevation="10dp"
            android:padding="20dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/scale_standard"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:text="@string/me_font_scale_level_standard"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/scale_big"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:text="@string/me_font_scale_level_big"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/scale_huge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:text="@string/me_font_scale_level_huge"
                android:textSize="16sp" />

            <androidx.constraintlayout.helper.widget.Flow
                android:id="@+id/label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="scale_standard,scale_big,scale_huge"
                app:flow_horizontalStyle="spread_inside"
                app:flow_wrapMode="chain"
                app:layout_constraintBottom_toTopOf="@id/scale_change_bar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <SeekBar
                android:id="@+id/scale_change_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="5dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/me_bg_font_scale_track"
                android:max="2"
                android:progressDrawable="@android:color/transparent"
                android:thumb="@drawable/me_ic_font_scale_thumb"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
