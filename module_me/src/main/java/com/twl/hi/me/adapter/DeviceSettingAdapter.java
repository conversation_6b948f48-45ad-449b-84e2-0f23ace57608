package com.twl.hi.me.adapter;

import androidx.databinding.ViewDataBinding;

import com.twl.hi.me.BR;
import com.twl.hi.me.R;
import com.twl.hi.me.api.response.bean.DeviceInfoBean;
import com.twl.hi.me.callback.DeviceCallback;

import lib.twl.common.adapter.BaseDataBindingMultiAdapter;
import lib.twl.common.adapter.BaseDataBindingViewHolder;

/**
 * 设备管理页面recyclerview对应的adapter
 */
public class DeviceSettingAdapter extends BaseDataBindingMultiAdapter<DeviceInfoBean, ViewDataBinding> {

    private DeviceCallback mCallback;
    public DeviceSettingAdapter(DeviceCallback callback) {
        super(null);
        mCallback = callback;
    }

    @Override
    protected int getMultiItemViewType(DeviceInfoBean item) {
        return item.type;
    }

    @Override
    protected int getLayoutId(int viewType) {
        if (viewType == DeviceInfoBean.TYPE_TITLE_LOGIN) {
            return R.layout.me_device_setting_login_item;
        } else if (viewType == DeviceInfoBean.TYPE_TITLE_LOGOUT) {
            return R.layout.me_device_setting_logout_item;
        } else if (viewType == DeviceInfoBean.TYPE_DEVICE) {
            return R.layout.me_device_setting_item;
        }
        return 0;
    }

    @Override
    protected void bind(BaseDataBindingViewHolder<ViewDataBinding> helper, ViewDataBinding binding, DeviceInfoBean item) {
        binding.setVariable(BR.bean, item);
        binding.setVariable(BR.callback, mCallback);
    }
}
