package com.twl.hi.me.api.request;

import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

public class LogoutRequest extends BaseApiRequest<HttpResponse> {

    public LogoutRequest(BaseApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_SMS_LOGOUT;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
