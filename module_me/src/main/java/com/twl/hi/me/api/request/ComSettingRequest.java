package com.twl.hi.me.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.callback.AbsRequestCallback;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

/**
 * <AUTHOR>
 * @date 2020/7/15.
 */
public class ComSettingRequest extends BaseApiRequest<HttpResponse> {
    @Expose
    public Integer workStatusId;//工作状态id
    @Expose
    public Integer replyType;//0-无，1-系统默认, 2-自定义
    @Expose
    public String replyText;//自定义自动回复文案
    @Expose
    public String expiration;//截止时间 。 一直持续不传该值 or 传值为空
    @Expose
    public Integer expirationType;//0 其他时间 1 30分钟 2 1小时 3 2小时 4 一天 5 本周内 6 考勤下班

    @Expose
    public String workStatus;//工作状态文案，目前仅用于设置自定义回复文案提交时候的参数

    public ComSettingRequest(AbsRequestCallback<HttpResponse> mCallback) {
        super(mCallback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_COM_SETTING;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
