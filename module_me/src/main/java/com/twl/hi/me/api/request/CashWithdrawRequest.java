package com.twl.hi.me.api.request;

import com.google.gson.annotations.Expose;
import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.base.URLConfig;
import com.twl.http.client.BaseApiRequest;
import com.twl.http.client.HttpResponse;
import com.twl.http.config.RequestMethod;

/**
 * 余额提现请求
 */
public class CashWithdrawRequest extends BaseApiRequest<HttpResponse> {

    @Expose
    public long money;

    /**
     * 提现渠道,2:微信 4:支付宝
     */
    @Expose
    public int channel;

    public CashWithdrawRequest(BaseApiRequestCallback<HttpResponse> callback) {
        super(callback);
    }

    @Override
    public String getUrl() {
        return URLConfig.URL_CASH_WITHDRAW;
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }
}
