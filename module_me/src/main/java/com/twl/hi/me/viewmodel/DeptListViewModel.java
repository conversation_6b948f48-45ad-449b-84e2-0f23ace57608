package com.twl.hi.me.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.databinding.ObservableBoolean;
import androidx.lifecycle.MutableLiveData;

import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.hi.me.api.request.DeptListRequest;
import com.twl.hi.me.api.request.UpdateDeptListRequest;
import com.twl.hi.me.api.response.DeptListResponse;
import com.twl.hi.me.api.response.bean.DeptStatusInfo;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.List;
import java.util.Observable;

import lib.twl.common.util.LList;
@Deprecated
public class DeptListViewModel extends FoundationViewModel {
    private List<DeptStatusInfo> visibleDeptList = new ArrayList<>();
    private List<DeptStatusInfo> invisibleDeptList = new ArrayList<>();
    private MutableLiveData<Boolean> getDeptListSuccess = new MutableLiveData<>();
    private MutableLiveData<Boolean> updateSuccess = new MutableLiveData<>(null);

    public DeptListViewModel(Application application) {
        super(application);
    }

    public void getDeptList() {
        setShowProgressBar();
        DeptListRequest request = new DeptListRequest(new BaseApiRequestCallback<DeptListResponse>() {
            @Override
            public void onSuccess(ApiData<DeptListResponse> data) {
                if (data.resp != null && !LList.isEmpty(data.resp.result)) {
                    for (DeptStatusInfo deptStatusInfo : data.resp.result) {
                        if (deptStatusInfo.show > 0) {
                            visibleDeptList.add(deptStatusInfo);
                        } else {
                            invisibleDeptList.add(deptStatusInfo);
                        }
                    }
                    getDeptListSuccess.setValue(true);
                }
            }

            @Override
            public void onComplete() {
                hideShowProgressBar();
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }

    public void updateDeptList(List<String> deptIds, List<String> invisibleList) {
        setShowProgressBar();
        UpdateDeptListRequest request = new UpdateDeptListRequest(new BaseApiRequestCallback<HttpResponse>() {
            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                updateSuccess.postValue(true);
            }

            @Override
            public void onComplete() {
                hideShowProgressBar();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                updateSuccess.postValue(false);
            }
        });
        StringBuilder deptIdsStringBuilder = new StringBuilder();
        for (int i = 0; i < deptIds.size(); i++) {
            deptIdsStringBuilder.append(deptIds.get(i));
            if (i != deptIds.size() - 1) {
                deptIdsStringBuilder.append(",");
            }
        }
        request.deptIds = deptIdsStringBuilder.toString();

        StringBuilder invisibleDeptIdsStringBuilder = new StringBuilder();
        for (int i = 0; i < invisibleList.size(); i++) {
            invisibleDeptIdsStringBuilder.append(invisibleList.get(i));
            if (i != invisibleList.size() - 1) {
                invisibleDeptIdsStringBuilder.append(",");
            }
        }
        request.hideDeptIds = invisibleDeptIdsStringBuilder.toString();
        HttpExecutor.execute(request);
    }

    public List<DeptStatusInfo> getVisibleDeptList() {
        return visibleDeptList;
    }

    public List<DeptStatusInfo> getInvisibleDeptList() {
        return invisibleDeptList;
    }

    public MutableLiveData<Boolean> getGetDeptListSuccess() {
        return getDeptListSuccess;
    }

    public MutableLiveData<Boolean> getUpdateSuccess() {
        return updateSuccess;
    }
}
