package com.twl.hi.me.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;

import com.twl.hi.basic.adapter.DataBoundListAdapter;
import com.twl.hi.foundation.api.response.bean.TeamBean;
import com.twl.hi.me.callback.CompanyListCallback;
import com.twl.hi.me.databinding.MeItemCompanyBinding;
import com.twl.utils.StringUtils;

import hi.kernel.HiKernel;

/**
 * create by sunyangyang
 * on 2020/3/17
 */
public class CompanyAdapter extends DataBoundListAdapter<TeamBean, MeItemCompanyBinding> {

    private CompanyListCallback mCallback;

    public CompanyAdapter(Context context, CompanyListCallback callback) {
        super(new DiffUtil.ItemCallback<TeamBean>() {
            @Override
            public boolean areItemsTheSame(@NonNull TeamBean oldItem, @NonNull TeamBean newItem) {
                return StringUtils.isEquals(oldItem.companyId, newItem.companyId);
            }

            @Override
            public boolean areContentsTheSame(@NonNull TeamBean oldItem, @NonNull TeamBean newItem) {
                return TextUtils.equals(oldItem.companyName, newItem.companyName);
            }
        });
        mCallback = callback;
    }

    @Override
    protected MeItemCompanyBinding createBinding(ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        return MeItemCompanyBinding.inflate(inflater, parent, false);
    }

    @Override
    protected void bind(MeItemCompanyBinding vdb, TeamBean item, int position) {
        vdb.setCompany(item);
        vdb.setCallback(mCallback);
        vdb.setSelect(TextUtils.equals(item.companyId, HiKernel.getHikernel().getAccount().getCompanyId()));
    }
}
