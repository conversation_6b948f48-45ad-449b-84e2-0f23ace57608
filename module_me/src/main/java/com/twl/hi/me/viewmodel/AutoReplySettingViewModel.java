package com.twl.hi.me.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableInt;
import androidx.lifecycle.MutableLiveData;

import com.twl.hi.foundation.api.base.BaseApiRequestCallback;
import com.twl.hi.foundation.api.response.bean.CompanySetting;
import com.twl.hi.foundation.base.FoundationViewModel;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.model.User;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.hi.me.R;
import com.twl.hi.me.api.request.ComSettingRequest;
import com.twl.hi.me.model.AutoReplyBean;
import com.twl.hi.me.model.ExpirationTimeBean;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.HttpResponse;
import com.twl.http.error.ErrorReason;
import com.twl.utils.SettingBuilder;
import com.twl.utils.status.WorkStatusBean;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import lib.twl.common.util.LDate;
import lib.twl.common.util.LList;

/**
 * <AUTHOR>
 * @date 2021/12/17.
 */
public class AutoReplySettingViewModel extends FoundationViewModel {
    public ObservableInt sourceAutoReplyType = new ObservableInt(AutoReplyBean.AUTO_REPLY_TYPE_NONE);
    public ObservableInt selectAutoReplyType = new ObservableInt(AutoReplyBean.AUTO_REPLY_TYPE_NONE);
    private MutableLiveData<List<AutoReplyBean>> autoReplyLiveData = new MutableLiveData<>();

    /**
     * 正在操作的状态相关数据
     */
    int mWorkStatueId;
    int mExpirationType;//非当前状态进入，提交工作状态时候，要保存的截止时间类型

    int userWorkStatueId;//用户当前工作状态id
    int userReplyType;//当前用户的自动回复状态
    long userExpiration;//用户当前状态的截止时间
    int userExpirationType;//用户当前状态的截止时间的类型

    long clockOutTime;
    public ObservableBoolean hasSelfDefined = new ObservableBoolean(false);
    private final MutableLiveData<Boolean> workStatueUpdateResultLiveData = new MutableLiveData<>();
    private boolean hasChangedReplyType = false;
    public String mSource;
    public String mDefaultAutoReplyText;

    public AutoReplySettingViewModel(Application application) {
        super(application);
    }

    public void init(int workStatueId, int expirationType, long clockOutTime, String defaultAutoReplyText) {
        this.mWorkStatueId = workStatueId;
        this.mExpirationType = expirationType;
        this.clockOutTime = clockOutTime;
        mDefaultAutoReplyText = defaultAutoReplyText;
        initInBackground();
    }

    @Override
    protected void initData() {
        super.initData();
        User user = ServiceManager.getInstance().getProfileService().getUser().getValue();
        List<AutoReplyBean> result = new ArrayList<>();
        AutoReplyBean none = new AutoReplyBean(AutoReplyBean.AUTO_REPLY_TYPE_NONE, getApplication().getResources().getString(R.string.nothing));
        AutoReplyBean sys = new AutoReplyBean(AutoReplyBean.AUTO_REPLY_TYPE_SYS, getSystemAutoReplyText());
        result.add(none);
        result.add(sys);
        if (user != null) {
            this.userWorkStatueId = user.getWorkStatusId();
            this.userReplyType = user.getReplyType();
            this.userExpiration = user.getExpiration();
            this.userExpirationType = user.getExpirationType();
            String replyText = user.getReplyText();//自定义自动回复文案
            AutoReplyBean self = null;
            if (!TextUtils.isEmpty(replyText)) {
                self = new AutoReplyBean(AutoReplyBean.AUTO_REPLY_TYPE_SELF_DEFINED, replyText);
                result.add(self);
                hasSelfDefined.set(true);
            } else {
                hasSelfDefined.set(false);
            }
            if (mWorkStatueId == userWorkStatueId) {
                sourceAutoReplyType.set(userReplyType);
                selectAutoReplyType.set(userReplyType);
            } else {
                sourceAutoReplyType.set(AutoReplyBean.AUTO_REPLY_TYPE_NO_DEFINED);//进入自动回复选中界面，非当前工作状态，3个自动回复都可以选择
                if (selectAutoReplyType.get() == AutoReplyBean.AUTO_REPLY_TYPE_SELF_DEFINED) {
                    selectAutoReplyType.set(AutoReplyBean.AUTO_REPLY_TYPE_NONE);
                }
            }
        }
        autoReplyLiveData.postValue(result);
    }

    private String getSystemAutoReplyText() {
        if (!TextUtils.isEmpty(mDefaultAutoReplyText)) {
            return mDefaultAutoReplyText;
        }
        String workStatue = "";
        WorkStatusBean workStatusBean = SettingBuilder.getInstance().getWorkStatus(AutoReplySettingViewModel.this.mWorkStatueId);
        if (workStatusBean != null) {
            workStatue = workStatusBean.getStatusText();
        }
        return getApplication().getResources().getString(R.string.me_default_reply, workStatue);
    }

    public MutableLiveData<List<AutoReplyBean>> getAutoReplyLiveData() {
        return autoReplyLiveData;
    }

    public MutableLiveData<Boolean> getWorkStatueUpdateResultLiveData() {
        return workStatueUpdateResultLiveData;
    }

    public boolean isHasChangedReplyType() {
        return hasChangedReplyType;
    }

    public void refreshSelfDefinedReply(String selfDefinedReply) {
        List<AutoReplyBean> oldList = autoReplyLiveData.getValue();
        if (!LList.isEmpty(oldList)) {
            List<AutoReplyBean> result = new ArrayList<>(3);
            result.addAll(oldList);
            if (result.size() == 3) {//有自定义回复
                AutoReplyBean selfDefinedReplyBean = result.remove(2);
                if (selfDefinedReplyBean != null) {
                    AutoReplyBean newSelfDefinedReplyBean = new AutoReplyBean(selfDefinedReplyBean.getAutoReplyType(), selfDefinedReply);
                    result.add(newSelfDefinedReplyBean);
                }
            } else {//无自定义回复
                AutoReplyBean newSelfDefinedReplyBean = new AutoReplyBean(AutoReplyBean.AUTO_REPLY_TYPE_SELF_DEFINED, selfDefinedReply);
                result.add(newSelfDefinedReplyBean);
            }
            hasSelfDefined.set(true);
            selectAutoReplyType.set(AutoReplyBean.AUTO_REPLY_TYPE_SELF_DEFINED);
            autoReplyLiveData.postValue(result);
            return;
        }

    }

    public void requestUpdateWorkStatue() {
        final int replyType = selectAutoReplyType.get();
        final long expiration = getStatusExpiration();
        ComSettingRequest request = new ComSettingRequest(new BaseApiRequestCallback<HttpResponse>() {
            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);
                User user = ServiceManager.getInstance().getProfileService().getUser().getValue();
                if (user != null) {
                    CompanySetting companySetting = user.getCompanySetting();
                    if (companySetting != null) {
                        companySetting.workStatusId = AutoReplySettingViewModel.this.mWorkStatueId;
                        companySetting.replyType = replyType;
                        companySetting.expiration = expiration;
                        companySetting.expirationType = mExpirationType;
                    }
                    ServiceManager.getInstance().getProfileService().setUser(user);
                    new PointUtils.BuilderV4()
                            .name("mycenter-status-edit")
                            .params("type",4)
                            .params("source", mSource)
                            .point();

                }
                workStatueUpdateResultLiveData.postValue(true);
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.workStatusId = mWorkStatueId;
        request.replyType = replyType;
        request.expirationType = mExpirationType;
        request.expiration = LDate.getDate(expiration, LDate.yMdHmFormat);
        User user = ServiceManager.getInstance().getProfileService().getUser().getValue();
        if (user != null) {
            request.replyText = user.getReplyText();
        }
        HttpExecutor.execute(request);
    }

    public long getStatusExpiration() {
        final long expiration;
        if (mWorkStatueId == userWorkStatueId) {
            if (userExpirationType != ExpirationTimeBean.EXPIRATION_TYPE_OFF_WORK
                    && userExpiration < System.currentTimeMillis()) {
                expiration = getUpdateExpirationByType(mExpirationType);
            } else {
                expiration = userExpiration;
            }
        } else {
            expiration = getUpdateExpirationByType(mExpirationType);
        }
        return expiration;
    }

    /**
     * 删除自定义回复
     */
    public void delSelfAutoReply() {
        final int changedReplyType;
        final boolean change = this.userReplyType == AutoReplyBean.AUTO_REPLY_TYPE_SELF_DEFINED;
        if (change) {//用户选择，当前的自动回复，是自定义回复
            changedReplyType = AutoReplyBean.AUTO_REPLY_TYPE_NONE;
        } else {
            changedReplyType = userReplyType;
        }
        ComSettingRequest request = new ComSettingRequest(new BaseApiRequestCallback<HttpResponse>() {
            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);
                User user = ServiceManager.getInstance().getProfileService().getUser().getValue();
                if (change) {
                    hasChangedReplyType = true;
                }
                if (user != null) {
                    CompanySetting companySetting = user.getCompanySetting();
                    if (companySetting != null) {
                        companySetting.replyType = changedReplyType;
                        companySetting.replyText = "";
                    }
                    ServiceManager.getInstance().getProfileService().setUser(user);
                }
                initData();
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.workStatusId = userWorkStatueId;
        request.expiration = LDate.getDate(getStatusExpiration(), LDate.yMdHmFormat);
        request.replyText = "";
        request.replyType = changedReplyType;
        HttpExecutor.execute(request);
    }


    /**
     * 获取提交截止时间
     *
     * @param expirationType 截止时间类型
     */
    public long getUpdateExpirationByType(int expirationType) {
        Calendar calendar = java.util.Calendar.getInstance();
        long expiration = 0L;
        switch (expirationType) {
            case ExpirationTimeBean.EXPIRATION_TYPE_30:
                calendar.add(java.util.Calendar.MINUTE, 30);
                expiration = calendar.getTimeInMillis();
                break;
            case ExpirationTimeBean.EXPIRATION_TYPE_60:
                calendar.add(java.util.Calendar.MINUTE, 60);
                expiration = calendar.getTimeInMillis();
                break;
            case ExpirationTimeBean.EXPIRATION_TYPE_120:
                calendar.add(java.util.Calendar.MINUTE, 120);
                expiration = calendar.getTimeInMillis();
                break;
            case ExpirationTimeBean.EXPIRATION_TYPE_DAY:
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                calendar.add(Calendar.HOUR_OF_DAY, 24);
                expiration = calendar.getTimeInMillis();
                break;
            case ExpirationTimeBean.EXPIRATION_TYPE_WEEK:
                int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
                int dayOffset = 0;
                switch (dayOfWeek) {
                    case Calendar.MONDAY:
                        dayOffset = 7;
                        break;
                    case Calendar.TUESDAY:
                        dayOffset = 6;
                        break;
                    case Calendar.WEDNESDAY:
                        dayOffset = 5;
                        break;
                    case Calendar.THURSDAY:
                        dayOffset = 4;
                        break;
                    case Calendar.FRIDAY:
                        dayOffset = 3;
                        break;
                    case Calendar.SATURDAY:
                        dayOffset = 2;
                        break;
                    case Calendar.SUNDAY:
                        dayOffset = 1;
                        break;
                    default:

                }
                calendar.add(Calendar.DAY_OF_YEAR, dayOffset);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                expiration = calendar.getTimeInMillis();
                break;
            case ExpirationTimeBean.EXPIRATION_TYPE_OFF_WORK:
                expiration = clockOutTime;
                break;


        }
        return expiration;
    }
}
