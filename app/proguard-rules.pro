# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /Users/<USER>/Library/Android/sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose
-dontskipnonpubliclibraryclassmembers
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*
-dontwarn dalvik.**
-ignorewarnings


-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable
-printmapping proguard.txt

-keep public class * extends android.app.Activity
-keep public class * extends android.app.Fragment
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.app.job.JobService
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class com.android.vending.licensing.ILicensingService
-keep public class * implements android.content.ServiceConnection
-keep class android.arch.lifecycle.** {* ;}
-keep public class * extends android.arch.lifecycle.AndroidViewModel{
    public <init>(android.app.Application);
}
#混淆app工程下的网络请求返回
-keep class com.twl.hi.api.response.** {* ;}
##混淆foundation工程下的网络请求返回
-keep class com.twl.hi.foundation.model.** {* ;}
#-keep class com.twl.hi.foundation.api.response.** {* ;}
#-keep class com.twl.hi.basic.api.response.** {* ;}
##混淆select工程下的网络请求返回
#-keep class com.twl.hi.select.api.response.** {* ;}
##混淆organization工程下的网络请求返回
#-keep class com.twl.hi.organization.api.response.** {* ;}
##混淆workflow工程下的网络请求返回
#-keep class com.twl.hi.workflow.api.response.** {* ;}
##混淆work工程下的网络请求返回
#-keep class com.twl.hi.work.api.response.** {* ;}
##混淆me工程下的网络请求返回
#-keep class com.twl.hi.me.api.response.** {* ;}
##混淆schedule工程下的网络请求返回
#-keep class com.twl.hi.schedule.api.response.** {* ;}

-keep class com.twl.hi.**.api.response.** {* ;}

-keep public class * implements java.io.Serializable {* ;}
-keep public class * extends java.io.Serializable {* ;}
-keep enum * {* ;}
-keep class com.zhipin.bosshi.mqtt.codec.** {* ;}
-keep class message.handler.dao.convert.MessageProtocol {* ;}

-keep class com.twl.utils.jurisdiction.** {* ;}

-keep public class android.net.http.SslError

-dontwarn com.tencent.smtt.sdk.WebView
-dontwarn android.net.http.SslError
-dontwarn com.tencent.smtt.sdk.WebViewClient

#腾讯x5
-dontwarn dalvik.**
-dontwarn com.tencent.smtt.**

-keep class com.tencent.smtt.** {
    *;
}
-keep class com.tencent.tbs.** {
    *;
}

-keep class com.twl.startup.** {* ;}

#混淆LBASE
-keep class com.monch.lbase.** {* ;}
-keep class org.apache.http.** { *; }
-dontwarn org.apache.http.**
-dontwarn android.net.**


#混淆R文件
-keep public class com.twl.hi.R$*{
    public static final int *;
}

-keep public class com.twl.hi.*.R$*{
    public static final int *;
}

#混淆 WebView 和 JS
-keepclassmembers class com.twl.hi.webview.* {
   public *;
}
-keepattributes *Annotation*
-keepattributes *JavascriptInterface*

-keepclasseswithmembernames class * {
    native <methods>;
}
-keepclasseswithmembernames class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}
-keepclasseswithmembernames class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
-keepclasseswithmembers class * {
	public <init>(android.content.Context, android.util.AttributeSet);
}
-keepclasseswithmembers class * {
	public <init>(android.content.Context, android.util.AttributeSet, int);
}
-keepclasseswithmembernames class *{
	native <methods>;
}

-keepclasseswithmembers class * {
    ... *JNI*(...);
}
-keepclasseswithmembernames class * {
	... *JRI*(...);
}
-keep class **JNI* {*;}

# v4包混淆
-dontwarn android.support.v4.**
-dontwarn org.apache.commons.net.**
-dontwarn **CompatHoneycomb
-dontwarn **CompatHoneycombMR2
-dontwarn **CompatCreatorHoneycombMR2
-keep interface android.support.v4.app.** { *; }
-keep interface android.support.v7.** { *; }
-keep class android.support.v4.** { *; }
-keep class android.support.v7.** { *; }
-keep public class * extends android.support.v4.**
-keep public class * extends android.support.v7.**
-keep public class * extends android.app.Fragment

-keep class * extends android.app.Fragment {
 public void setUserVisibleHint(boolean);
 public void onHiddenChanged(boolean);
 public void onResume();
 public void onPause();
}
-keep class android.support.v4.app.Fragment {
 public void setUserVisibleHint(boolean);
 public void onHiddenChanged(boolean);
 public void onResume();
 public void onPause();
}
-keep class * extends android.support.v4.app.Fragment {
 public void setUserVisibleHint(boolean);
 public void onHiddenChanged(boolean);
 public void onResume();
 public void onPause();
}

# AndroidX 防止混淆
-keep class com.google.android.material.** {*;}
-keep class androidx.** {*;}
-keep public class * extends androidx.**
-keep interface androidx.** {*;}
-keep @androidx.annotation.Keep class *
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}

# RxJava RxAndroid
-dontwarn sun.misc.**
-keepclassmembers class rx.internal.util.unsafe.*ArrayQueue*Field* {
    long producerIndex;
    long consumerIndex;
}
-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueProducerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode producerNode;
}
-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueConsumerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode consumerNode;
}

#=============EventBus 3.0混淆 start===============
-keepattributes *Annotation*
-keepclassmembers class ** {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }
# 对于带有回调函数的onXXEvent、**On*Listener的，不能被混淆
-keepclassmembers class * {
    void *(**On*Event);
    void *(**On*Listener);
}
# Only required if you use AsyncExecutor
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

# 友盟混淆
-keepclassmembers class * {
   public <init>(org.json.JSONObject);
}

# fresco混淆
-keep,allowobfuscation @interface com.facebook.common.internal.DoNotStrip
-keep @com.facebook.common.internal.DoNotStrip class *
-keepclassmembers class * {
    @com.facebook.common.internal.DoNotStrip *;
}
-keepclassmembers class * {
    native <methods>;
}
-dontwarn okio.**
-dontwarn javax.annotation.**
-dontwarn com.android.volley.toolbox.**
-dontwarn com.facebook.**
-keep enum com.facebook.**
-keep class com.facebook.** {*;}
-keep interface com.facebook.** {*;}


# 微信分享混淆
-keep class com.tencent.mm.sdk.** {*;}
-keep class com.tencent.mm.opensdk.** {*;}
-keep class com.tencent.wxop.** {*;}

# mqtt
-keep class org.eclipse.paho.** {* ;}
-keep class com.google.protobuf.** {* ;}

# 二维码
-dontwarn com.google.zxing.**
-keep  class com.google.zxing.** {* ;}

# CropView
-keep class com.isseiaoki.simplecropview.** {* ;}

-dontwarn com.hianalytics.android.**
-keep class com.hianalytics.android.**{*;}
-dontwarn com.baidu.mapapi.**
-keep class com.baidu.mapapi.**{*;}
#Bugly
-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}
# Other
-keepattributes Exceptions,InnerClasses
-keepattributes Signature
-keepattributes *Annotation*
-keep class **$Properties
-dontwarn org.eclipse.jdt.annotation.**
-dontnote com.xiaomi.** 
-dontnote com.huawei.android.pushagent.** 

-keepclassmembers class ** {
	public void onEventBusMessage(**);
    public void onLading(**);
}
#tinker
-keep class com.hpbr.bosszhipin.SimpleApplication {*;}
-keep class com.hpbr.bosszhipin.base.App {*;}
-keep class com.tencent.tinker.** {*;}

-keep class com.tencent.mars.** {
   *;
}

-keep class com.tencent.mars.** {
   *;
}

-keep class com.twl.net.** {
   *;
}

# signer
-keep class com.monch.lib.signer.** {*;}

#uCrop
-dontwarn com.yalantis.ucrop**
-keep class com.yalantis.ucrop** { *; }
-keep interface com.yalantis.ucrop** { *; }

# 支付宝
#-libraryjars libs/alipaySdk-20170710.jar
-keep class com.alipay.android.app.IAlixPay{*;}
-keep class com.alipay.android.app.IAlixPay$Stub{*;}
-keep class com.alipay.android.app.IRemoteServiceCallback{*;}
-keep class com.alipay.android.app.IRemoteServiceCallback$Stub{*;}
-keep class com.alipay.sdk.app.PayTask{ public *;}
-keep class com.alipay.sdk.app.AuthTask{ public *;}

# HttpDNS
-keep class com.tencent.msdk.dns.**{*;}
-keep class com.tencent.beacon.**{*;}

-keepclassmembers class * extends com.tencent.smtt.sdk.WebChromeClient{
       public void openFileChooser(...);
}

# OkHttp3
-dontwarn com.squareup.okhttp3.**
-keep class com.squareup.okhttp3.** { *;}

# Okio
-dontwarn com.squareup.**
-dontwarn okio.**
-keep public class org.codehaus.* { *; }
-keep public class java.nio.* { *; }

#混淆LibHttp
-keep class com.twl.http.chuck.** {* ;}
-keep class android.support.v7.widget.SearchView { *; }

-keep class * extends com.twl.http.client.AbsFileDownloadReuest {* ;}
-keep class * extends com.twl.http.client.AbsCommonApiRequest {* ;}
-keep class * extends com.twl.http.client.AbsBatchApiRequest {* ;}
-keep class * extends com.twl.http.client.AbsUploadApiRequest {* ;}

# lib_security
-keep class com.twl.signer.YZWG{*;}


#混淆Lib_Twl_Ui
-keep class com.twl.hi.basic.views.** {* ;}
-keep class lib.twl.common.views.** {* ;}
-keep class lib.twl.common.model.** {* ;}
-keep class lib.twl.common.adapter.** {* ;}
-keep public class * extends android.view.View {* ;}
-keep class * extends lib.twl.common.views.adapter.BaseViewHolder {* ;}


#tecent wcdb
-keep class com.tencent.wcdb.**{*;}

#视频认证
-keep class org.webrtc.** {*;}
-keep class com.demo.** {*;}
-keep class com.kanzhun.** {*;}
-keep interface com.kanzhun.** {*;}

# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt dependency is available.
-dontwarn okhttp3.internal.platform.ConscryptPlatform

#数盟混淆
-keep class cn.shuzilm.core.** {*;}

### greenDAO 3
-keepclassmembers class * extends org.greenrobot.greendao.AbstractDao {
public static java.lang.String TABLENAME;
}
-keep class **$Properties

# If you do not use SQLCipher:
-dontwarn org.greenrobot.greendao.database.**
# If you do not use RxJava:
-dontwarn rx.**

### greenDAO 2
-keepclassmembers class * extends de.greenrobot.dao.AbstractDao {
public static java.lang.String TABLENAME;
}
-keep class **$Properties
#时间监测
-keep class lib.twl.time.monitor.** {* ;}

#友盟
-keep class com.umeng.** {*;}

-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-ignorewarnings
-keepattributes *Annotation*
-keepattributes Exceptions
-keepattributes InnerClasses
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable
-keep class com.huawei.hianalytics.**{*;}
-keep class com.huawei.updatesdk.**{*;}
-keep class com.huawei.hms.**{*;}
-keep class com.hihonor.push.**{*;}

-keep class org.webrtc.** {*;}
-keep class com.demo.** {*;}
-keep class com.kanzhun.** {*;}
-keep interface com.kanzhun.** {*;}
-keep class com.boss.zprtc.** {*;}
-keep class com.boss.zpim.** {*;}
-keep class com.zpnrtcengine.** {*;}
-keep class org.webrtcold.** {*;}

# 高德地图-定位
-dontwarn com.amap.api.**
-dontwarn com.aps.**
#高德相关混淆文件
#3D 地图
-keep class com.amap.api.maps.**{*;}
-keep class com.autonavi.**{*;}
-keep class com.amap.api.trace.**{*;}
#Location
-keep class com.amap.api.location.**{*;}
-keep class com.amap.api.fence.**{*;}
-keep class com.loc.**{*;}
-keep class com.autonavi.aps.amapapi.model.**{*;}
#Service
-keep class com.amap.api.services.**{*;}
#2D地图
-keep class com.amap.api.maps2d.**{*;}
-keep class com.amap.api.mapcore2d.**{*;}

# 使用了RouterService注解的实现类，需要避免Proguard把构造方法、方法等成员移除(shrink)或混淆(obfuscate)，导致无法反射调用。实现类的类名可以混淆。
-keepclassmembers @com.sankuai.waimai.router.annotation.RouterService class * { *; }

# 保留ServiceLoaderInit类，需要反射调用
-keep class com.sankuai.waimai.router.generated.ServiceLoaderInit { *; }

# 避免注解在shrink阶段就被移除，导致obfuscate阶段注解失效、实现类仍然被混淆
-keep @interface com.sankuai.waimai.router.annotation.RouterService

#Apm混淆
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt dependency is available.shiyixia
-dontwarn okhttp3.internal.platform.ConscryptPlatform

### greenDAO 3
-keepclassmembers class * extends org.greenrobot.greendao.AbstractDao {
public static java.lang.String TABLENAME;
}
-keep class **$Properties

# If you do not use SQLCipher:
-dontwarn org.greenrobot.greendao.database.**
# If you do not use RxJava:
-dontwarn rx.**

### greenDAO 2
-keepclassmembers class * extends de.greenrobot.dao.AbstractDao {
public static java.lang.String TABLENAME;
}
-keep class **$Properties

### 性能监控（内存泄露、卡顿监控）
-keep class leakcanary.** { *; }
-keep class com.tencent.matrix.** { *; }
-keep class com.kwai.koom.** { *; }
-keep class com.hpbr.apm.settings.monitor.** { *; }
### 日志捞取上报相关
-keep class com.techwolf.lib.tlog.** { *; }
-keep class net.jpountz.** { *; }

-keep class com.twl.hi.chat.voice.**{*;}
# kz-app
-keep public class * extends io.dcloud.common.DHInterface.IPlugin
-keep public class * extends io.dcloud.common.DHInterface.IFeature
-keep public class * extends io.dcloud.common.DHInterface.IBoot
-keep public class * extends io.dcloud.common.DHInterface.IReflectAble

-keep class io.dcloud.application.** {*;}
-keep class io.dcloud.feature.speech.** {*;}
-keep class io.dcloud.feature.gg.** {*;}
-keep class io.dcloud.feature.ad.juhe360.** {*;}
-keep class io.dcloud.feature.nativeObj.** {*;}
-keep class io.dcloud.net.** {*;}
-keep class io.dcloud.common.constant.** {*;}
-keep class io.dcloud.common.sonic.** {*;}
-keep class io.dcloud.common.DHInterface.** {*;}
-keep class io.dcloud.common.util.** {*;}
-keep class io.dcloud.common.adapter.** {*;}
-keep class io.dcloud.feature.internal.reflect.** {*;}
-keep class io.dcloud.feature.internal.sdk.** {*;}
-keep class io.dcloud.feature.encryption.EncryptionFeatureImpl {*;}

-keep class io.dcloud.nineoldandroids.** {*;}
-keep class io.dcloud.ep.K {*;}
-keep class io.dcloud.feature.audio.** {*;}
-keep class io.dcloud.base.** {*;}
-keep class io.dcloud.streamdownload** {*;}

-keep class io.dcloud.media.video.ijkplayer.media.** {*;}

-keep class vi.com.gdi.** {*;}
-keep class androidx.** {*;}
-keep class io.dcloud.feature.barcode2.** {*;}

-keep class io.dcloud.common.core.ui.keyboard.** {*;}
-keep class io.dcloud.common.core.ui.KZKeyboardManager {*;}
-keep class io.dcloud.common.core.ui.TabBarWebviewMgr {*;}
-keep class io.dcloud.common.core.ui.TabBarWebview {*;}
-keep class io.dcloud.common.core.permission.PermissionControler {*;}
-keep class io.dcloud.feature.ui.navigator.QueryNotchTool {*;}
# weex barcode模块所需文件不混淆
#-keep class io.dcloud.feature.barcode2.decoding.CaptureActivityHandler
#-keep class io.dcloud.feature.barcode2.camera.CameraManager
#-keep class io.dcloud.feature.barcode2.decoding.IBarHandler
#-keep class io.dcloud.feature.barcode2.decoding.InactivityTimer
#-keep class io.dcloud.feature.barcode2.view.DetectorViewConfig
#-keep class io.dcloud.feature.barcode2.view.ViewfinderView


-keep class io.dcloud.appstream.StreamAppManager
-keepclasseswithmembers class io.dcloud.appstream.StreamAppManager {
    public protected <methods>;
}

-keepclasseswithmembers class io.dcloud.js.gallery.GalleryFeatureImpl {
    public protected <methods>;
}

-keepclasseswithmembers class io.dcloud.common.core.a.a {
    public <methods>;
}

-keep class io.dcloud.appstream.share.ActionSheet {*;}


-keep class io.dcloud.common.DHInterface.IReflectAble
-keep public class * extends io.dcloud.common.DHInterface.IReflectAble{
  public protected <methods>;
  public protected *;
}
-keep class **.R
-keep class **.R$* {
    public static <fields>;
}
-keep public class * extends io.dcloud.common.DHInterface.IJsInterface{
  public protected <methods>;
  public protected *;
}

-keepclasseswithmembers class io.dcloud.EntryProxy {
    <methods>;
}

-keep class * implements android.os.IInterface {
  <methods>;
}

-keepclasseswithmembers class *{
  public static java.lang.String getJsContent();
}

-keepclasseswithmembers class *{
  public static io.dcloud.share.AbsWebviewClient getWebviewClient(io.dcloud.share.ShareAuthorizeView);
}

-keepattributes Exceptions,InnerClasses,Signature,Deprecated,LineNumberTable,*Annotation*,EnclosingMethod

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keep public class * extends android.app.Application{
  public static <methods>;
  public *;
}

-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
   public static <methods>;
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

-keep class kz.** {*;}          # 私有化okhttp防混淆
-keep class com.** {*;}
-keep class okio.**{*;}
-keep class org.apache.** {*;}
-keep class org.json.** {*;}
-keep class net.ossrs.** {*;}

-keep class io.dcloud.feature.weex.WeexInstanceMgr{*;}
-keep class io.dcloud.feature.weex.WeexDevtoolImpl{*;}
-keep class io.dcloud.feature.weex.adapter.** {*;}
-keep class * implements com.taobao.weex.IWXObject{*;}
-keep public class * extends com.taobao.weex.common.WXModule{*;}


-keepattributes Signature

-dontwarn org.codehaus.mojo.**
-dontwarn org.apache.commons.**
-dontwarn com.amap.**
-dontwarn com.sina.weibo.sdk.**
-dontwarn com.alipay.**
-dontwarn com.lucan.ajtools.**
-dontwarn pl.droidsonroids.gif.**

-keep class androidtranscoder.** {*;}
-keep class org.mozilla.universalchardet.** {*;}

#小程序SDK混淆配置
-keep class io.dcloud.feature.sdk.Interface.IKZMPAppSplashView {*;}
-keep class io.dcloud.feature.kzmp.KZMPJSCallback {public <methods>;}
-keep class io.dcloud.feature.sdk.KZMPPermissionUtil {public <methods>;}
-keep class io.dcloud.feature.sdk.MenuActionSheetItem {public <methods>;}
-keep class io.dcloud.feature.sdk.Interface.** {*;}
-keep class io.dcloud.feature.sdk.KZSDKInitConfig  {*;}
-keep class io.dcloud.feature.sdk.KZSDKInitConfig$Builder  {*;}
-keep class io.dcloud.feature.sdk.KZMPCapsuleButtonStyle {*;}


###alita播放器
-keep class org.alita.**{*;}

# WebpDecoder
-keep public class com.bumptech.glide.integration.webp.WebpImage { *; }
-keep public class com.bumptech.glide.integration.webp.WebpFrame { *; }
-keep public class com.bumptech.glide.integration.webp.WebpBitmapFactory { *; }