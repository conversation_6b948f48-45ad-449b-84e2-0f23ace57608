package com.twl.hi;

import androidx.test.espresso.ViewInteraction;
import androidx.test.espresso.contrib.RecyclerViewActions;
import androidx.test.filters.LargeTest;
import androidx.test.filters.RequiresDevice;
import androidx.test.rule.ActivityTestRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.twl.hi.helper.RecyclerViewHelper;

import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.action.ViewActions.closeSoftKeyboard;
import static androidx.test.espresso.action.ViewActions.typeText;
import static androidx.test.espresso.assertion.ViewAssertions.matches;
import static androidx.test.espresso.contrib.RecyclerViewActions.scrollToPosition;
import static androidx.test.espresso.matcher.ViewMatchers.isDisplayed;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static androidx.test.espresso.matcher.ViewMatchers.withText;
import static org.hamcrest.core.AllOf.allOf;

@RunWith(AndroidJUnit4.class)
@LargeTest
public class CreateConversation {
    @Rule
    public ActivityTestRule<HiSplashActivity> mActivityRule = new ActivityTestRule<>(
            HiSplashActivity.class);

    @Test
    @RequiresDevice
    public void myTest() {
        try {
            Thread.sleep(5000);
            clickTab3();
            Thread.sleep(2000);
            clickContacts();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
    }

    private void clickTab3() {
        onView(allOf(withText("通讯录"), isDisplayed())).perform(click()); //点击联系人tab
    }

    private void clickContacts(){
        ViewInteraction viewInteraction = onView(allOf(withId(R.id.recyclerView), isDisplayed()));
        int count = RecyclerViewHelper.getCount(viewInteraction);
        for (int i = 3; i < count; i++) {
            try {
                tapRecyclerViewItem(viewInteraction, i);
                Thread.sleep(1000);
                try {
                    clickChat();
                    sendMessage(i);
                    clickBack();
                } catch (Exception e) {
                }
                clickBack();
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }

    private void sendMessage(int i) throws Throwable {
        try {
            onView(withId(R.id.et_input)).perform(typeText( String.valueOf(i)), closeSoftKeyboard());//输入消息
            do {
                try {
                    onView(withId(R.id.tv_send)).check(matches(isDisplayed()));
                    break;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }while (true);
            onView(withId(R.id.tv_send)).perform(click());//发送
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private void clickBack(){
        onView(allOf(withId(R.id.iv_back), isDisplayed())).perform(click());
    }

    private void clickChat(){
        onView(allOf(withId(R.id.tv_send), isDisplayed())).perform(click());

    }
    public static void tapRecyclerViewItem(ViewInteraction viewInteraction, int position) {
        viewInteraction.perform(scrollToPosition(position));
        viewInteraction.perform(RecyclerViewActions.actionOnItemAtPosition(position,click()));//RecyclerView 第二行
    }


}
