package com.twl.hi.kzmp

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONException
import com.alibaba.fastjson.JSONObject
import com.techwolf.lib.tlog.TLog

/**
 *@author: musa on 2023/2/21
 *@e-mail: yang<PERSON><PERSON><PERSON>@kanzhun.com
 *@desc: fastJson安全检查扩展
 */

private const val TAG = "JsonExt"

fun JSONObject.safeGetJsonArray(key: String): JSONArray? =
    try {
        getJSONArray(key)
    } catch (e: JSONException) {
        TLog.error(TAG, e.message)
        null
    }

fun JSONObject.safeGetBoolean(key: String): Boolean? =
    try {
        getBoolean(key)
    } catch (e: JSONException) {
        TLog.error(TAG, e.message)
        null
    }

fun JSONObject.safeGetInteger(key: String): Int? =
    try {
        getInteger(key)
    } catch (e: JSONException) {
        TLog.error(TAG, e.message)
        null
    }
