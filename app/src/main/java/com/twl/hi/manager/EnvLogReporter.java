package com.twl.hi.manager;

import android.content.Context;
import android.text.TextUtils;
import com.hpbr.apm.event.ApmAnalyzer;
import com.techwolf.lib.tlog.TLog;
import com.twl.anti.EnvironmentCollect;
import com.twl.hi.App;
import com.twl.hi.basic.apm.ApmAnalyticsAction;
import com.twl.hi.basic.util.UploadFileUtil;
import com.twl.hi.foundation.api.base.HostConfig;

import java.io.File;

import hi.kernel.Constants;
import hi.kernel.HiKernel;
import lib.twl.common.util.ProcessHelper;
import okio.BufferedSink;
import okio.GzipSink;
import okio.Okio;
import okio.Sink;


/**
 * 上报环境日志
 */
public final class EnvLogReporter {
    private static final String TAG = "EnvLogReporter";

    private static final int ONE_DAY_IN_MILLIS = 24 * 60 * 60 * 1000;


    //region 上传环境日志
    public static void uploadLogThenReport() {
        boolean shouldReport = shouldDoReport();
        // 用户从未上报过
        boolean neverReport = getLastReportTimeByUser() == 0;
        TLog.info(TAG, "uploadLogThenReport shouldReport=[%b], neverReport=[%b]", shouldReport, neverReport);
        if (shouldReport || neverReport /* 从未上报过的用户，执行上报 */) {
            innerUpload(neverReport);
            //nlp 打点全量，一天一次
//            reportNlp(ApmAnalyticsAction.TYPE_ENV_INFO);
        } else {
            //nlp 打点可变的，不传TYPE_ENV_INFO的话，每次都传
//            reportNlp(ApmAnalyticsAction.TYPE_VAR_INFO);
        }
    }

    private static void innerUpload(boolean neverReport) {
        try {
            Context context = App.getApplication();
            File cacheDir = context.getCacheDir(); // 使用 App 私有缓存目录
            File file = File.createTempFile("BossHi_", ".txt.gz", cacheDir);
            //noinspection ResultOfMethodCallIgnored
            file.createNewFile();

            Sink sink = Okio.sink(file);
            // 使用 Gzip 压缩，减小环境日志文件体积
            GzipSink gzipSink = new GzipSink(sink);
            BufferedSink bufferedSink = Okio.buffer(gzipSink);
            new EnvironmentCollect(App.getApplication(), null).printAll(bufferedSink);
            bufferedSink.close();

            UploadFileUtil.getInstance().uploadFile(file, new UploadFileUtil.UploadFileCallback() {
                @Override
                public void uploadFile(String url) {
                    if (!TextUtils.isEmpty(url)) {
                        ApmAnalyzer.create()
                                .action(ApmAnalyticsAction.ACTION_SECURITY_LOG)
                                .p2(HostConfig.getCONFIG().getName())
                                .p3(HiKernel.getHikernel().getAccount().getUserName())
                                .p4(url)
                                .p5(String.valueOf(neverReport ? 0 : 1)) // 是否是首次上传环境数据。0 没传过，1 传过
                                .report();

                        setLastReportTimeByUser();
                    }
                    file.delete();
                }
            });
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private static boolean shouldDoReport() { // 上报策略
//        // 按天上报
        boolean isMoreThanOneDay = isMoreThanOneDay();
//
//        // 高峰时间段
//        boolean isFastigium = isFastigium();
//
//        // 50%几率的上报机会（减半上报）
//        boolean isSelectedUser = isSelectedUser();
//
//        if (isMoreThanOneDay) { // 上报前提是距离上次上报超过1天
//            if (isFastigium) { // 高峰期减半上报
//                return isSelectedUser;
//            }
//            return true;
//        }
        return isMoreThanOneDay;
    }

    private static boolean isMoreThanOneDay() {
        long lastTime = getLastReportTimeByUser();
        return System.currentTimeMillis() - lastTime > ONE_DAY_IN_MILLIS;
    }

    private static long getLastReportTimeByUser() {
        return ProcessHelper.getUserPreferences().getLong(Constants.KEY_UPLOAD_SECTURITY_LOG_FREQUENCY, 0L);
    }

    /**
     * 重置环境数据上传标记
     */
    public static void resetLastReportTime() {
        TLog.info(TAG, "resetLastReportTime");
        setLastReportTimeByUser(0);
    }

    private static void setLastReportTimeByUser() {
        setLastReportTimeByUser(System.currentTimeMillis());
    }

    private static void setLastReportTimeByUser(long lastReportTime) {
        ProcessHelper.getUserPreferences().edit().putLong(Constants.KEY_UPLOAD_SECTURITY_LOG_FREQUENCY, lastReportTime).apply();
    }

}
