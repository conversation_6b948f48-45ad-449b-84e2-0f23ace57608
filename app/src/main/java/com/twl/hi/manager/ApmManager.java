package com.twl.hi.manager;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.UiThread;
import androidx.appcompat.app.AppCompatActivity;

import com.hpbr.apm.Apm;
import com.hpbr.apm.common.Consumer;
import com.hpbr.apm.common.Predicate;
import com.hpbr.apm.common.Supplier;
import com.hpbr.apm.common.net.UrlConfig;
import com.hpbr.apm.config.Config;
import com.hpbr.apm.config.ConsumerKey;
import com.hpbr.apm.config.PredicateKey;
import com.hpbr.apm.config.RunnableKey;
import com.hpbr.apm.config.SupplierKey;
import com.hpbr.apm.config.content.ContentSupplier;
import com.hpbr.apm.config.content.bean.pub.ExtendInfo;
import com.hpbr.apm.upgrade.exp.ExceptionUtil;
import com.hpbr.apm.upgrade.sec.SecApkStore;
import com.tencent.bugly.beta.tinker.TinkerManager;
import com.tencent.bugly.crashreport.CrashReport;
import com.tencent.tinker.lib.tinker.Tinker;
import com.tencent.tinker.lib.tinker.TinkerInstaller;
import com.twl.anti.CheatChecker;
import com.twl.anti.ExtendExecutor;
import com.twl.hi.api.HttpLogInterceptor;
import com.twl.hi.basic.api.config.base.HttpParameters;
import com.twl.hi.foundation.api.base.HostConfig;
import com.twl.hi.utils.ApmKeyUtil;
import com.twl.http.chuck.ChuckInterceptor;
import com.twl.utils.SettingBuilder;

import java.io.File;

import hi.kernel.HiKernel;
import lib.twl.common.util.DeviceIdUtils;
import lib.twl.common.util.ProcessHelper;

/**
 * Created by zhangxiangdong on 2019/4/17 14:45.
 */
public class ApmManager {

    public static void start(@NonNull Context context) {
        final Context cxt = context.getApplicationContext();
        boolean isDebug = SettingBuilder.getInstance().isDebug();
        StringBuilder fileProvider = new StringBuilder(cxt.getPackageName());
        fileProvider.append(".fileprovider");

        String hostType = getApmHostType();// 动态选择APM网络环境（目前有：线上、QA）
        Config config = getConfig(context, cxt, isDebug, fileProvider, hostType, UrlConfig.Host.ONLINE);

        Apm.get()
                .config(config)
                .debug(isDebug)
                .start();
    }

    private static String getApmHostType() {
        if (TextUtils.equals(SettingBuilder.getInstance().getFlavor(), "prod")) {
            return UrlConfig.Host.ONLINE;
        } else if (TextUtils.equals(SettingBuilder.getInstance().getFlavor(), "pre")) {
            return UrlConfig.Host.ONLINE;
        } else {
            HostConfig.Addr addr = HostConfig.getCONFIG();
            if(addr == HostConfig.Addr.ONLINE){
                return UrlConfig.Host.ONLINE;
            }else if (addr == HostConfig.Addr.PRE) {
                return UrlConfig.Host.ONLINE;
            }else{
                return UrlConfig.Host.QA;
            }
        }
    }

    @NonNull
    private static Config getConfig(@NonNull Context context, Context cxt, boolean isDebug, StringBuilder fileProvider, String hostType, String finalHostType) {
        Config config = Config.newBuilder()
                .authority(fileProvider.toString())
                .sKey(ApmKeyUtil.getApmKey(hostType)) // BossHi-Android
                .ivParameter(ApmKeyUtil.getApmParameter(hostType)) // BossHi-Android
                .addInterceptor(isDebug ? new HttpLogInterceptor() : null)
                .addInterceptor(isDebug ? new ChuckInterceptor(context) : null)
                .userIdSupplier(() -> HiKernel.getHikernel().getAccount().getExposedUserId()) // 登录用户id
                .clientInfoSupplier(HttpParameters::getApmClientInfo) // 设备信息
                .uniqueIdSupplier(() -> DeviceIdUtils.getDeviceId())
                .tinkerIdSupplier(new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        try {
                            return TinkerManager.getTinkerId();
                        } catch (Exception e) {
                            return "";
                        }
                    }
                })
                .newTinkerIdSupplier(new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        try {
                            return TinkerManager.getNewTinkerId();
                        } catch (Exception e) {
                            return "";
                        }
                    }
                })

                .putConsumer(ConsumerKey.KEY_ON_INSTALL_PATCH, new Consumer<File>() {
                    @Override
                    public void accept(@Nullable File patchFile) {
                        if (patchFile == null) return;
                        try {
                            TinkerInstaller.onReceiveUpgradePatch(cxt, patchFile.getAbsolutePath());
                        } catch (Exception e) {
                            ExceptionUtil.reportPatchException(String.valueOf(e));
                        }
                    }
                })
                .putConsumer(ConsumerKey.KEY_ON_REMOVE_PATCH, new Consumer<Object>() {
                    @Override
                    public void accept(@Nullable Object o) {
                        Tinker.with(Apm.getContext()).cleanPatch();
                    }
                })
                .putConsumer(ConsumerKey.KEY_ON_APM_CONNECT_ERROR, new Consumer<String>() {
                    @Override
                    public void accept(@Nullable String s) {
                        // 上报到 bugly
                        CrashReport.putUserData(context, "key_apm_connect_error", "APM CONNECT ERROR: " + s);
                        CrashReport.postCatchedException(new Exception(), Thread.currentThread(), true);
                    }
                })

                .putSupplier(UrlConfig.KEY_HOST, new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        return finalHostType;
                    }
                })
                .putSupplier(SupplierKey.KEY_SEC_APK_VERSION_CODE, () -> {
                    // 宿主初始安全补丁版本，用于 APM 首次检测获取升级安全补丁
                    return String.valueOf(CheatChecker.VERSION_CODE);
                })
                // 数盟ID， 目前未接入数盟， 先用设备id代替
                .putSupplier(SupplierKey.DID, new Supplier<Object>() {
                    @NonNull
                    @Override
                    public Object get() {
                        String did = DeviceIdUtils.getDeviceId();
                        return did == null ? "" : did;
                    }
                })
                .putRunnable(RunnableKey.KEY_GET_USER_CONFIG_DONE, new ApmConfigRunnable())
                .putSupplier(SupplierKey.KEY_TINKER_ENABLED, () -> true)
                .putPredicate(PredicateKey.KEY_TINKER_ID_IN_BLACKLIST, new Predicate<Object>() {

                    @Override
                    public boolean test(@Nullable Object o) {
                        return false;
                    }
                })
                .putRunnable(RunnableKey.KEY_SEC_APK_EXECUTOR, () -> SecApkStore.loadLocalVerifiedSecApkClassLoader(secInfo -> {
                    try {
                        new ExtendExecutor(context, secInfo.getClassLoader(), secInfo.getPackageInfo()).run();
                        return true;
                    } catch (Throwable e) {
                        return false; // 运行失败
                    }
                }))
                .build();
        return config;
    }

    @Nullable
    public static ExtendInfo getExtendInfo() {
        return ContentSupplier.get().getExtendInfo();
    }

    @UiThread
    public static void sync() { // 手动触发同步配置、升级信息获取等逻辑
        try {
            Apm.get().sync();
        } catch (Exception e) {
            if (SettingBuilder.getInstance().isDebug()) {
                throw e;
            }
        }
    }

    public static void checkForUpgrade(@NonNull AppCompatActivity host) {
        // 兜底
        if (Apm.getContext() == null) {
            start(ProcessHelper.getContext());
        }
        Apm.checkForUpgrade(host);
    }

}
