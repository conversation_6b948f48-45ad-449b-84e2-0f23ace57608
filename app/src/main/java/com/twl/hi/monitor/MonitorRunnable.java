package com.twl.hi.monitor;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.hi.foundation.utils.PointUtils;
import com.twl.utils.SettingBuilder;

import lib.twl.common.BuildConfig;

/**
 * 卡顿上报
 * 消息调用栈日志打印
 */
public class MonitorRunnable implements Runnable {

    private static final String TAG = "MonitorRunnable";
    private Thread thread;
    private int stackLen;

    public MonitorRunnable(Thread thread, int stackLen) {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            Log.e(TAG, "thread should be main thread!");
            return;
        }
        this.thread = thread;
        this.stackLen = Math.max(stackLen, 10);
    }

    @Override
    public void run() {
        if (thread == null) {
            return;
        }

        // 队栈打印
        StackTraceElement[] stackTraceElements = thread.getStackTrace();
        if (stackTraceElements != null && stackTraceElements.length > 0) {
            StringBuilder stackBuilder = new StringBuilder("Message Process Time More Than Limit: \n");
            for (int i = 0; i < stackTraceElements.length; i++) {
                stackBuilder.append(stackTraceElements[i]).append("\n");
                if (i >= stackLen - 1) {
                    break;
                }
            }

            String message = stackBuilder.toString();

            // release版本, 且prod环境才上报卡顿
            if (!BuildConfig.DEBUG && TextUtils.equals("prod", SettingBuilder.getInstance().getFlavor())) {
                CrashReport.postCatchedException(new Exception(message));

                PointUtils.BuilderV3 builderV3 = new PointUtils.BuilderV3();
                builderV3.name("app-error").params("main_type", "0").point();
            }
            // bugly 堆栈显示不全，需要记录到日志中
            TLog.error(TAG, message);
        }

    }
}
