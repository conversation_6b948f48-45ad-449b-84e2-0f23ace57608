package com.twl.hi.main.performance

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.viewModelScope
import com.twl.hi.App
import com.twl.hi.basic.performance.AbsPerformance
import com.twl.hi.main.MainTabActivity
import com.twl.hi.point.AppPointReporter
import com.twl.hi.utils.MainTabPermissionHelper

class MainPermissionPerformance(private val activity: MainTabActivity) : AbsPerformance() {
    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        //用户登录成功，有默认团队或者没有团队
        MainTabPermissionHelper(activity).startRequestPermission()
        //上报权限相关的埋点
        AppPointReporter.reportAppPermissions(App.getApplication().applicationContext, activity.viewModel.viewModelScope)
    }
}