package com.twl.hi.utils;

import com.hpbr.apm.common.net.UrlConfig;

/**
 * <AUTHOR>
 * @date 2020/7/20.
 */
public class ApmKeyUtil {
    public static final String RELEASE_KEY = "lshrOVwmrKMB80G2";
    public static final String RELEASE_PARAMETER = "Ul51cGqvjRy3bmJV";

    public static final String DEBUG_KEY = "GBaKdAdqmuh1Kw0G";
    public static final String DEBUG_PARAMETER = "Qb4u1AihKhaWS4Ui";

    public static String getApmKey(String hostType) {
        if (UrlConfig.Host.ONLINE.equals(hostType)) {
        return RELEASE_KEY;
        }
        return DEBUG_KEY;
    }

    public static String getApmParameter(String hostType) {
        if (UrlConfig.Host.ONLINE.equals(hostType)) {
        return RELEASE_PARAMETER;
        }
        return DEBUG_PARAMETER;
    }
}
