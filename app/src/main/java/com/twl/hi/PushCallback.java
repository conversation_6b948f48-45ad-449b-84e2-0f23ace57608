package com.twl.hi;

import android.content.Intent;
import android.text.TextUtils;

import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.annotation.RouterProvider;
import com.sankuai.waimai.router.annotation.RouterService;
import com.techwolf.lib.tlog.TLog;
import com.twl.hi.export.main.router.AppPageRouter;
import com.twl.hi.foundation.logic.ServiceManager;
import com.twl.hi.foundation.media.IMeetingService;
import com.twl.hi.foundation.media.MediaConstants;
import com.twl.hi.foundation.utils.point.MailPoint;
import com.twl.hi.push.IPushCallback;
import com.twl.hi.util.PushConstants;
import com.twl.hi.videomeeting.MeetingChatService;
import com.twl.http.config.HttpConfig;
import com.twl.utils.URLUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import hi.kernel.Constants;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.CommonUtils;
import lib.twl.common.util.PointClearUtils;
import lib.twl.common.util.TimeDifferenceUtil;
import lib.twl.common.util.TimeTag;

@RouterService(interfaces = {IPushCallback.class}, key = {PushConstants.PUSH_SERVICE})
public class PushCallback implements IPushCallback {

    public static final String TAG = "PushCallback";

    private SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S");

    private static final PushCallback instance = new PushCallback();

    @RouterProvider
    public static PushCallback getInstance() {
        return instance;
    }

    private PushCallback() {
    }

    @Override
    public void onReceiveMessage(int type, String message) {
        TLog.info(TAG, "onReceiveMessage, type: %s, message: %s", type, message);

        AppUtil.getDefaultUriRequest(App.getApplication(), AppPageRouter.MAIN_TAB_ACTIVITY)
                .setIntentFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                .start();
    }

    @Override
    public void onSystemNotificationClicked(int type, Map<String, String> message) {
        Map<String, Object> map = new HashMap<>();
        IMeetingService service = Router.getService(IMeetingService.class, MediaConstants.MEETING_ENGINE_LISTENER_KEY);
        if (service.isConnected() && CommonUtils.canDrawOverlays(App.getApplication())) {
            service.unStashVideoView();
            Intent serviceVideoIntent = new Intent(App.getApplication(), MeetingChatService.class);
            App.getApplication().startService(serviceVideoIntent);
        }
        try {
            if (message != null) {
                if (message.get(PushConstants.PUSH_MSG_FROM) != null) {
                    map.put(Constants.FROM_ID, message.get(PushConstants.PUSH_MSG_FROM));
                }
                if (message.get(PushConstants.PUSH_MSG_TO) != null) {
                    map.put(Constants.TO_ID, message.get(PushConstants.PUSH_MSG_TO));
                }
                if (message.get(PushConstants.PUSH_MSG_ID) != null) {
                    map.put(Constants.MSG_ID, Long.valueOf(message.get(PushConstants.PUSH_MSG_ID)));
                }
            }
        } catch (Exception e) {
            TLog.info(TAG, e.getMessage());
        }
        map.put(Constants.S_DATE_TIME, formatter.format(new Date()));
        notifyJumpPageRefresh(message);
        // push消息进入时不进行耗时统计
        ServiceManager.getInstance().getSettingService().setLaunchLoginFirst(true);

        try {
            if (HttpConfig.getInstance().paramsPipeline != null) {
                // 小米推送点击走广播回调，这里 pipeline 还没有初始化完成
            } else {
                MiPushClickPointManager.getInstance().setMiPushMsgClickPointMap(map);
            }
        } catch (Exception e) {
            TLog.error(TAG, "point error! " + e.getMessage());
        }

        AppUtil.getDefaultUriRequest(App.getApplication(), AppPageRouter.MAIN_TAB_ACTIVITY)
                .setIntentFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                .start();
    }

    /**通知界面刷新
    * @param message
    */
    private void notifyJumpPageRefresh (Map<String, String> message) {
        if (message == null) {
            return;
        }
        // 拦截邮箱内容协议
        // bosshi://bosshi.app/openwith?type=mail&folderId=***&mailId=***
        String protocol = message.get(PushConstants.PUSH_PROTOCOL);
        boolean foreground = App.getApplication().isForeground();
        TLog.info(TAG, "PushCallback -> notifyJumpPageRefresh " + protocol + ", " + foreground);
        if (!TextUtils.isEmpty(protocol) && protocol.startsWith(PushConstants.PUSH_PROTOCOL_URL_HEADER)) {
            Map<String, String> params = URLUtils.parseUrlParams(protocol);
            String type = params.get(PushConstants.PUSH_PROTOCOL_OPEN_TYPE);
            // 邮件push
            if (TextUtils.equals(Constants.AGREEMENT_BH_MAIL, type)) {
                if (foreground) {
                    TimeDifferenceUtil.getInstance().start(TimeTag.MAIL_LOAD_MAIL_DETAILS);
                } else {
                    PointClearUtils.INSTANCE.resetOnBackground();
                }
                String folderId = params.get(PushConstants.PUSH_PROTOCOL_MAIL_FOLDER_ID);
                String mailId = params.get(PushConstants.PUSH_PROTOCOL_MAIL_ID);
                String mailAddress = params.get(PushConstants.PUSH_PROTOCOL_MAIL_ADDRESS);
                Map<String, String> stringMap = new HashMap<>();
                stringMap.put(PushConstants.PUSH_PROTOCOL_MAIL_FOLDER_ID, folderId);
                stringMap.put(PushConstants.PUSH_PROTOCOL_MAIL_ID, mailId);
                stringMap.put(PushConstants.PUSH_PROTOCOL_MAIL_ADDRESS, mailAddress);
                ServiceManager.getInstance().getEmailService().setEmailNotify(stringMap);
                MailPoint.INSTANCE.pointMailNotifyMessage(mailId);
                return;
            }
        }
        if (foreground) {
            if (!TextUtils.isEmpty(protocol) && protocol.contains("type=msg")) {
                TimeDifferenceUtil.getInstance().start(TimeTag.PUSH_JUMP);
            }
        } else {
            PointClearUtils.INSTANCE.resetOnBackground();
        }
        ServiceManager.getInstance().getConversationService().setChatNotify(message);
    }

}