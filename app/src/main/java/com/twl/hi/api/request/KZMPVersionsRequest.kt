package com.twl.hi.api.request

import com.twl.hi.api.response.KZMPVersionsResponse
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.base.URLConfig
import com.twl.http.client.BaseApiRequest
import com.twl.http.config.RequestMethod

/**
 * Author : <PERSON><PERSON>xia<PERSON> .
 * Date   : On 2022/7/25
 * Email  : Contact <EMAIL>
 * Desc   : 小程序历史基础库版本管理
 *
 */

class KZMPVersionsRequest(callback: BaseApiRequestCallback<KZMPVersionsResponse>) :
    BaseApiRequest<KZMPVersionsResponse>(callback) {

    override fun getUrl(): String {
        return URLConfig.URL_KZMP_VERSIONS
    }

    override fun getMethod(): RequestMethod {
        return RequestMethod.GET
    }
}