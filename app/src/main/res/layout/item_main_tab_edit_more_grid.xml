<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/itemContainer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    >

    <Space
        android:id="@+id/spaceTop"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        app:layout_constraintTop_toTopOf="parent"
        />

    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/appIcon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        app:roundedCornerRadius="14dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/spaceTop"
        android:layout_marginTop="5dp"
        />

    <TextView
        android:id="@+id/tvNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/guideline"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="12dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/bg_corner_9_color_fb4f63"
        android:gravity="center"
        android:maxLines="1"
        android:minWidth="16dp"
        android:minHeight="16dp"
        android:paddingHorizontal="5dp"
        android:textColor="@color/app_white"
        android:textSize="11sp"
        />

    <!-- 水平引导线 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <TextView
        android:id="@+id/appName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textSize="12sp"
        android:textColor="#0A1B33"
        tools:text="消息"
        app:layout_constraintStart_toStartOf="@id/appIcon"
        app:layout_constraintEnd_toEndOf="@id/appIcon"
        app:layout_constraintTop_toBottomOf="@id/appIcon"
        android:maxLines="1"
        android:ellipsize="end"
        />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="match_parent"
        android:layout_height="15dp"
        app:layout_constraintTop_toBottomOf="@id/appName"
        />

</androidx.constraintlayout.widget.ConstraintLayout>