package com.twl.hi.kit

import android.app.Activity
import android.content.Context
import com.twl.hi.debugger.R
import com.twl.hi.export.main.router.AppPageRouter
import lib.twl.common.util.AppUtil

class SwitchEnvironmentKit(override val icon: Int = R.mipmap.dk_switch_config, override val name: Int = R.string.debug_kit_switch_env) : BaseKit() {
    override fun onAppInit(context: Context?) {

    }

    override fun onClickWithReturn(activity: Activity): Boolean {
        AppUtil.startUri(activity, AppPageRouter.CUSTOM_CONFIG_ACTIVITY)
        return true
    }

}