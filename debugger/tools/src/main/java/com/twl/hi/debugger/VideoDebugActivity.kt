package com.twl.hi.debugger

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity

import com.twl.hi.video.HiVideoView
import com.twl.hi.video.view.OnFirstFrameListener
import com.twl.hi.video.view.OnPlayErrorListener
import com.twl.hi.video.view.OnPlayEventListener
import com.twl.hi.video.view.OnProgressListener
import kz.log.TLog

/**
 * 视频播放测试 Activity
 * 用于测试 HiVideoView 的视频点播功能
 */
class VideoDebugActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "VideoDebugActivity"

        // 预设的测试视频 URL
        //https://bosshi-admin-qa.weizhipin.com/api/media/download/v/1CCC8A950B9A4F339A7A6D2D94B89B89.mp4
        private val SAMPLE_URLS = arrayOf(
            "https://eo-bosshi-nebula-1251955568.zhipin.com/448e9064dcf446a1bc6278cd8dd48788/3650/20250626/1D070C912F60C02043D9C7FAC4F689461_a76ea952c3514e059e7f29f70fef550a-OSS27.mp4/.edgeone-video/template=SystemPresetAvcAac720p/media.m3u8?t=685e0a9e&sign=d09217b0b8e5d1978197c02fd17086c62c17950e",
            "https://1500004624.vod2.myqcloud.com/961d69f0vodbj1500004624/7f7183b73560136622005235773/Nw5BZJiQvMYA.mov?t=68d725a4&exper=0&rlimit=9&sign=78214347f97ba2c72d198cca848c1e58",
            "https://1500004624.vod2.myqcloud.com/961d69f0vodbj1500004624/7f7183b73560136622005235773/Nw5BZJiQvMYA.mov?t=68d725a4&exper=0&rlimit=9&sign=78214347f97ba2c72d198cca848c1e58"
        )

        fun start(context: Context) {
            val intent = Intent(context, VideoDebugActivity::class.java)
            context.startActivity(intent)
        }
    }

    // UI 组件
    private lateinit var ivBack: ImageView
    private lateinit var etVideoUrl: EditText
    private lateinit var btnPlay: Button
    private lateinit var btnStop: Button
    private lateinit var btnSample1: Button
    private lateinit var btnSample2: Button
    private lateinit var btnSample3: Button
    private lateinit var videoView: HiVideoView
    private lateinit var tvStatus: TextView
    private lateinit var tvInfo: TextView
    private lateinit var btnPause: Button
    private lateinit var btnResume: Button
    private lateinit var btnMute: Button

    // 状态变量
    private var isMuted = false
    private var currentUrl: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_video_debug)

        initViews()
        initVideoView()
        setupClickListeners()

        // 添加生命周期观察
        lifecycle.addObserver(videoView)

        TLog.info(TAG, "VideoDebugActivity created")
    }

    private fun initViews() {
        ivBack = findViewById(R.id.iv_back)
        etVideoUrl = findViewById(R.id.et_video_url)
        btnPlay = findViewById(R.id.btn_play)
        btnStop = findViewById(R.id.btn_stop)
        btnSample1 = findViewById(R.id.btn_sample_1)
        btnSample2 = findViewById(R.id.btn_sample_2)
        btnSample3 = findViewById(R.id.btn_sample_3)
        videoView = findViewById(R.id.video_view)
        tvStatus = findViewById(R.id.tv_status)
        tvInfo = findViewById(R.id.tv_info)
        btnPause = findViewById(R.id.btn_pause)
        btnResume = findViewById(R.id.btn_resume)
        btnMute = findViewById(R.id.btn_mute)
    }

    private fun initVideoView() {
        // 设置视频播放监听器
        videoView.setOnProgressListener(object : OnProgressListener {
            override fun onProgressPlayBegin() {
                runOnUiThread {
                    updateStatus("播放开始")
                    updateInfo("状态：播放中")
                }
                TLog.info(TAG, "Video play begin")
            }

            override fun onProgressBuffering() {

            }

            override fun onProgressBufferEnd() {
            }

            override fun onProgressPlaying(current: Long, duration: Long) {
                runOnUiThread {
                    val currentMin = (current / 60).toInt()
                    val currentSec = (current % 60).toInt()
                    val durationMin = (duration / 60).toInt()
                    val durationSec = (duration % 60).toInt()
                    updateInfo("播放进度：${currentMin}:${String.format("%02d", currentSec)} / ${durationMin}:${String.format("%02d", durationSec)}")
                }
            }

            override fun onProgressPlayEnd() {
                runOnUiThread {
                    updateStatus("播放结束")
                    updateInfo("状态：播放完成")
                }
                TLog.info(TAG, "Video play end")
            }
        })

        videoView.setOnFirstFrameListener(object : OnFirstFrameListener {
            override fun onFirstFrame() {
                runOnUiThread {
                    updateStatus("首帧渲染完成")
                }
                TLog.info(TAG, "First frame rendered")
            }
        })

        videoView.setOnPlayErrorListener(object : OnPlayErrorListener {
            override fun onError(eventId: Int) {
                runOnUiThread {
                    updateStatus("播放错误：$eventId")
                    updateInfo("状态：播放失败")
                    Toast.makeText(this@VideoDebugActivity, "播放错误：$eventId", Toast.LENGTH_SHORT).show()
                }
                TLog.error(TAG, "Video play error: $eventId")
            }
        })

        videoView.setOnPlayEventListener(object : OnPlayEventListener {
            override fun onEvent(eventId: Int) {
                TLog.info(TAG, "Video play event: $eventId")
            }
        })
    }

    private fun setupClickListeners() {
        ivBack.setOnClickListener {
            finish()
        }

        btnPlay.setOnClickListener {
            val url = etVideoUrl.text.toString().trim()
            if (TextUtils.isEmpty(url)) {
                Toast.makeText(this, "请输入视频 URL", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            startPlay(url)
        }

        btnStop.setOnClickListener {
            stopPlay()
        }

        btnSample1.setOnClickListener {
            etVideoUrl.setText(SAMPLE_URLS[0])
        }

        btnSample2.setOnClickListener {
            etVideoUrl.setText(SAMPLE_URLS[1])
        }

        btnSample3.setOnClickListener {
            etVideoUrl.setText(SAMPLE_URLS[2])
        }

        btnPause.setOnClickListener {
            videoView.pause()
            updateInfo("状态：已暂停")
            TLog.info(TAG, "Video paused")
        }

        btnResume.setOnClickListener {
            videoView.resume()
            updateInfo("状态：播放中")
            TLog.info(TAG, "Video resumed")
        }

        btnMute.setOnClickListener {
            isMuted = !isMuted
            videoView.setMute(isMuted)
            btnMute.text = if (isMuted) "取消静音" else "静音"
            TLog.info(TAG, "Video mute: $isMuted")
        }
    }

    private fun startPlay(url: String) {
        currentUrl = url
        updateStatus("正在加载...")
        updateInfo("状态：准备播放")

        TLog.info(TAG, "Start playing video: $url")
        videoView.startPlay(url)
    }

    private fun stopPlay() {
        videoView.stopPlay(true)
        updateStatus("请输入视频 URL 并点击播放")
        updateInfo("状态：已停止")
        currentUrl = ""
        TLog.info(TAG, "Video stopped")
    }

    private fun updateStatus(status: String) {
        tvStatus.text = status
    }

    private fun updateInfo(info: String) {
        tvInfo.text = info
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            videoView.release()
            TLog.info(TAG, "VideoView released")
        } catch (e: Exception) {
            TLog.error(TAG, "Error releasing video view: ${e.message}")
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }
}
