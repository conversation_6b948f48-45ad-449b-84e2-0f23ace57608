package com.twl.hi.work.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.DiffUtil;

import com.twl.hi.basic.adapter.DataBoundListAdapter;
import com.twl.hi.work.BR;
import com.twl.hi.work.callback.ICloudItemSelectCallback;
import com.twl.hi.work.databinding.WorkItemICloudSelectBinding;
import com.twl.hi.work.databinding.WorkItemSpaceBinding;
import com.twl.hi.work.model.SelectICloudFileBean;

public class ICloudSelectAdapter extends DataBoundListAdapter<SelectICloudFileBean, ViewDataBinding> {
    private ICloudItemSelectCallback callback;
    private boolean showCheckBox;

    public ICloudSelectAdapter(ICloudItemSelectCallback callback, boolean showCheckBox) {
        super(new DiffUtil.ItemCallback<SelectICloudFileBean>() {
            @Override
            public boolean areItemsTheSame(SelectICloudFileBean oldItem, SelectICloudFileBean newItem) {
                return SelectICloudFileBean.areItemsTheSame(oldItem, newItem);
            }

            @Override
            public boolean areContentsTheSame(SelectICloudFileBean oldItem, SelectICloudFileBean newItem) {
                return SelectICloudFileBean.areContentsTheSame(oldItem, newItem);
            }
        });
        this.callback = callback;
        this.showCheckBox = showCheckBox;
    }

    @Override
    public int getItemViewType(int position) {
        SelectICloudFileBean selectBean = getItem(position);
        return selectBean.getResourceBean().isSpace ? 0 : 1;
    }

    @Override
    protected ViewDataBinding createBinding(ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        if (viewType == 0) {
            return WorkItemSpaceBinding.inflate(inflater, parent, false);
        } else {
            return WorkItemICloudSelectBinding.inflate(inflater, parent, false);
        }
    }

    @Override
    protected void bind(ViewDataBinding vdb, SelectICloudFileBean item, int position) {
        if (vdb instanceof WorkItemICloudSelectBinding) {
            WorkItemICloudSelectBinding itemBinding = (WorkItemICloudSelectBinding) vdb;
            vdb.setVariable(BR.callback, callback);
            vdb.setVariable(BR.bean, item);
            itemBinding.setShowCheckBox(showCheckBox && !item.getResourceBean().isDirectory());
        } else if (vdb instanceof WorkItemSpaceBinding) {
            vdb.setVariable(BR.bean, item);
            vdb.setVariable(BR.callback, callback);
        }
    }
}
