package com.twl.hi.work;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;

import com.twl.hi.basic.views.LineItemDecoration;
import com.twl.hi.foundation.base.fragment.FoundationVMShareFragment;
import com.twl.hi.work.adapter.GroupAttendanceTypeAdapter;
import com.twl.hi.work.api.response.bean.WeeklyOrMonthlyGroupAttendanceBean;
import com.twl.hi.work.attendance.AttendanceActivity;
import com.twl.hi.work.callback.GroupAttendanceTypeCallback;
import com.twl.hi.work.databinding.WorkFragmentGroupAttendanceTypeBinding;
import com.twl.hi.work.viewmodel.GroupAttendanceTypeViewModel;
import com.twl.hi.work.viewmodel.GroupAttendanceViewModel;

import java.util.List;

import hi.kernel.BundleConstants;
import hi.kernel.Constants;
import lib.twl.common.util.QMUIDisplayHelper;
import lib.twl.common.views.watermark.WatermarkBg;


public class GroupAttendanceTypeFragment extends FoundationVMShareFragment<WorkFragmentGroupAttendanceTypeBinding, GroupAttendanceTypeViewModel, GroupAttendanceViewModel> implements GroupAttendanceTypeCallback {
    public static final String GROUP_ATTENDANCE_TYPE = "groupAttendanceType";
    public static final String TIME_RANGE = "timeRange";
    public static final String STATISTICS_TYPE = "statisticsType";
    private String timeRange;
    private GroupAttendanceTypeAdapter adapter;
    private int month;
    private int year;

    public static GroupAttendanceTypeFragment create(int groupAttendanceType, String timeRange, int statisticsType, int year, int month) {
        GroupAttendanceTypeFragment fragment = new GroupAttendanceTypeFragment();
        Bundle args = new Bundle();
        args.putInt(GROUP_ATTENDANCE_TYPE, groupAttendanceType);
        args.putString(TIME_RANGE, timeRange);
        args.putInt(STATISTICS_TYPE, statisticsType);
        args.putInt(BundleConstants.BUNDLE_YEAR, year);
        args.putInt(BundleConstants.BUNDLE_MONTH, month);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected void initFragment() {
        getViewModel().init(getActivityViewModel());
        int groupAttendanceType = getArguments().getInt(GROUP_ATTENDANCE_TYPE, Constants.ATTENDANCE_LATE_1);
        int statisticsType = getArguments().getInt(STATISTICS_TYPE, GroupAttendanceViewModel.WEEK_STATISTICS);
        year = getArguments().getInt(BundleConstants.BUNDLE_YEAR);
        month = getArguments().getInt(BundleConstants.BUNDLE_MONTH);
        getViewModel().setGroupAttendanceType(groupAttendanceType);
        getViewModel().setStatisticsType(statisticsType);
        timeRange = getArguments().getString(TIME_RANGE, getString(R.string.work_late1));
        getDataBinding().setGroupAttendanceType(groupAttendanceType);
        getDataBinding().setTimeRange(timeRange);
        getDataBinding().setSortType(getViewModel().getSortType());
        WatermarkBg waterMarkBg = new WatermarkBg(getContext(), getActivityViewModel().getWaterMarkContentList(), 40, 13);
        getDataBinding().rv.setBackground(waterMarkBg);
        LineItemDecoration itemDecoration = new LineItemDecoration(getContext(), RecyclerView.VERTICAL, R.drawable.work_decoration_h_half_color_cfcfcf);
        itemDecoration.setmPaddingLeft(QMUIDisplayHelper.dp2px(getContext(), 20));
        getDataBinding().rv.addItemDecoration(itemDecoration);
        adapter = new GroupAttendanceTypeAdapter(this);
        getDataBinding().rv.setAdapter(adapter);
        getViewModel().getSortResultLiveData().observe(this, new Observer<List<WeeklyOrMonthlyGroupAttendanceBean>>() {
            @Override
            public void onChanged(@Nullable List<WeeklyOrMonthlyGroupAttendanceBean> searchResults) {
                if (searchResults == null) {
                    return;
                }
                adapter.submitList(searchResults);
            }
        });
        getViewModel().sortData();
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return 0;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.work_fragment_group_attendance_type;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }


    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {
        ((FragmentActivity) activity).getSupportFragmentManager().popBackStack();
    }

    @Override
    public void clickRight(View view) {
        if (getViewModel().getSortType() == GroupAttendanceViewModel.SORT_UP) {
            getViewModel().setSortType(GroupAttendanceViewModel.SORT_DOWN);
            getDataBinding().setSortType(GroupAttendanceViewModel.SORT_DOWN);
        } else {
            getViewModel().setSortType(GroupAttendanceViewModel.SORT_UP);
            getDataBinding().setSortType(GroupAttendanceViewModel.SORT_UP);
        }
        getViewModel().sortData();
    }

    @Override
    public void onGroupAttendanceItemClick(WeeklyOrMonthlyGroupAttendanceBean bean) {
        AttendanceActivity.goAttendanceActivity(activity, year, month, bean.getUserId(), 1);
    }
}

