package com.twl.hi.work.plugin.model.remote.wrapper

import com.twl.hi.foundation.base.NullSafeWrapper
import com.twl.hi.foundation.model.workbench.AppBv
import com.twl.hi.foundation.model.workbench.WorkbenchPlugin

/**
 *@author: musa on 2023/2/6
 *@e-mail: yangpeng<PERSON>@kanzhun.com
 *@desc: Workbench的空安全包装
 */
data class WorkbenchPluginNullSafeWrapper(
    val canOperate:Int = 0, //是否可添加到导航
    val appId: String?, //应用唯一id
    val name: String?,
    val icon: String?,
    val num: Int?, //应用红点数,
    val description: String?,
    var appBv: AppBv? = null
): NullSafeWrapper<WorkbenchPlugin>() {

    override fun internalToTargetBean(): WorkbenchPlugin =
        WorkbenchPlugin(
            appId!!, name!!, icon!!, num!!, description ?:"", appBv,canOperate
        )
}