package com.twl.hi.work.adapter;

import androidx.recyclerview.widget.DiffUtil;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import com.twl.hi.work.api.response.bean.DailyGroupAttendanceBean;
import com.twl.hi.work.BR;
import com.twl.hi.basic.adapter.DataBoundListAdapter;
import com.twl.hi.work.callback.DailyStatisticsCallback;
import com.twl.hi.work.databinding.WorkItemDailyGroupAttendanceBinding;

public class DailyGroupAttendanceAdapter extends DataBoundListAdapter<DailyGroupAttendanceBean, WorkItemDailyGroupAttendanceBinding> {
    private DailyStatisticsCallback callback;

    public DailyGroupAttendanceAdapter(DailyStatisticsCallback callback) {
        super(new DiffUtil.ItemCallback<DailyGroupAttendanceBean>() {
            @Override
            public boolean areItemsTheSame(DailyGroupAttendanceBean oldItem, DailyGroupAttendanceBean newItem) {
                return false;
            }

            @Override
            public boolean areContentsTheSame(DailyGroupAttendanceBean oldItem, DailyGroupAttendanceBean newItem) {
                return false;
            }
        });
        this.callback = callback;
    }

    @Override
    protected WorkItemDailyGroupAttendanceBinding createBinding(ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        return WorkItemDailyGroupAttendanceBinding.inflate(inflater, parent, false);
    }

    @Override
    protected void bind(WorkItemDailyGroupAttendanceBinding vdb, DailyGroupAttendanceBean item, int position) {
        vdb.setVariable(BR.callback, callback);
        vdb.setVariable(BR.bean, item);
    }

}
