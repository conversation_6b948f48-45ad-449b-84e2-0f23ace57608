package com.twl.hi.work.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.DiffUtil;

import com.twl.hi.basic.adapter.DataBoundListAdapter;
import com.twl.hi.foundation.api.response.bean.SystemPluginBean;
import com.twl.hi.work.BR;
import com.twl.hi.work.R;
import com.twl.hi.work.callback.WorkCallback;
import com.twl.hi.work.databinding.WorkItemAddBinding;
import com.twl.hi.work.databinding.WorkItemNewBinding;

import java.util.Objects;

import hi.kernel.Constants;

/**
 * 工作台列表单个应用适配器
 */
public class PluginAdapter extends DataBoundListAdapter<SystemPluginBean, ViewDataBinding> {

    private static final String TAG = "PluginAdapter";
    private WorkCallback mCallback;

    public PluginAdapter(WorkCallback callback) {
        super(new DiffUtil.ItemCallback<SystemPluginBean>() {
            @Override
            public boolean areItemsTheSame(SystemPluginBean oldItem, SystemPluginBean newItem) {
                return Objects.equals(oldItem.appId, newItem.appId);
            }

            @Override
            public boolean areContentsTheSame(SystemPluginBean oldItem, SystemPluginBean newItem) {
                return oldItem.num == newItem.num
                        && TextUtils.equals(oldItem.appName, newItem.appName)
                        && TextUtils.equals(oldItem.appLogo, newItem.appLogo);
            }
        });
        mCallback = callback;
    }

    @Override
    protected ViewDataBinding createBinding(ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ViewDataBinding viewDataBinding;
        if (viewType == 1) {
            viewDataBinding = WorkItemAddBinding.inflate(inflater, parent, false);
        } else {
            viewDataBinding = WorkItemNewBinding.inflate(inflater, parent, false);
        }
        viewDataBinding.getRoot().setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                SystemPluginBean systemPluginBean = (SystemPluginBean) v.getTag();
                if (!Constants.WORK_FAVORITE_ADD.equals(systemPluginBean.appId)) {
                    if(viewDataBinding instanceof WorkItemNewBinding){
                        mCallback.onItemLongClick(v.findViewById(R.id.sdv), systemPluginBean);

                    }
                }
                return true;
            }
        });
        return viewDataBinding;

    }

    @Override
    protected void bind(ViewDataBinding vdb, SystemPluginBean item, int position) {
        vdb.getRoot().setTag(item);
        vdb.setVariable(BR.callback, mCallback);
        vdb.setVariable(BR.bean, item);
    }

    @Override
    public int getItemViewType(int position) {
        SystemPluginBean systemPluginBean = getItem(position);
        if (Constants.WORK_FAVORITE_ADD.equals(systemPluginBean.appId)) {
            return 1;
        }
        return 0;
    }
}
