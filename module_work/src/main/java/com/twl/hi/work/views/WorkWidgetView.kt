package com.twl.hi.work.views

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.animation.Animation
import android.view.animation.RotateAnimation
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.utils.PointUtils
import com.twl.hi.work.R
import com.twl.hi.work.adapter.WorkWidgetItemAdapter
import com.twl.hi.work.adapter.WorkWidgetTabAdapter
import com.twl.hi.work.api.request.WorkWidgetTabDetailRequest
import com.twl.hi.work.api.response.WorkWidgetTabDetailResponse
import com.twl.hi.work.databinding.WorkViewWidgetBinding
import com.twl.hi.work.model.WidgetTab
import com.twl.hi.work.model.WorkWidgetDataItem
import com.twl.hi.work.model.WorkWidgetItem
import com.twl.http.ApiData
import com.twl.http.HttpExecutor
import com.twl.http.error.ErrorReason
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import lib.twl.common.ext.dp
import lib.twl.common.ext.isScrollToBottom
import lib.twl.common.ext.rotate
import java.util.concurrent.TimeUnit

/**
 * Author : Xuweixiang .
 * Date   : On 2024/6/5
 * Email  : Contact <EMAIL>
 * Desc   : 工作台组建
 */
class WorkWidgetView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs) {

    companion object {

        private const val TAG = "WorkWidgetView"

        private const val MIN_LOADING_DURATION = 300L
    }

    private val binding: WorkViewWidgetBinding by lazy {
        WorkViewWidgetBinding.inflate(
            LayoutInflater.from(context),
            this,
            true
        )
    }

    private val spanCount = 3

    private val itemAdapter = WorkWidgetItemAdapter()

    private val tabAdapter = WorkWidgetTabAdapter()

    var datasourceCode: String? = null
        private set

    var datasourceIndex: Int = 0
        private set

    var title: String? = ""
        private set

    var listener: AppLinkListener? = null

    private var tabIndex = 0

    private var lifecycleOwner: LifecycleOwner? = null

    init {
        binding.rvWidgets.layoutManager = GridLayoutManager(context, spanCount)
        binding.rvWidgets.adapter = itemAdapter
        binding.rvWidgets.addItemDecoration(
            GridSpacingItemDecoration(
                spanCount,
                8f.dp.toInt(),
                false
            )
        )
        binding.rvTab.layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
        binding.rvTab.adapter = tabAdapter
        binding.rvTab.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                binding.ivCover.isVisible = !recyclerView.isScrollToBottom()
            }
        })
        val gradientDrawable = GradientDrawable(
            GradientDrawable.Orientation.LEFT_RIGHT,
            intArrayOf(
                Color.parseColor("#00FFFFFF"),
                Color.parseColor("#FFFFFF")
            )
        )
        binding.ivCover.background = gradientDrawable
        binding.ivLoading.rotate()
    }

    fun bindLifecycleOwner(lifecycleOwner: LifecycleOwner) {
        this.lifecycleOwner = lifecycleOwner
    }

    fun refresh() {
        onLoading()
        loadData(this.datasourceCode ?: "", title, this.datasourceIndex)
    }

    fun loadData(code: String, name: String? = "", index: Int) {
        val startTime = System.currentTimeMillis()
        binding.tvTitle.text = name
        datasourceCode = code
        title = name
        if (datasourceIndex != index) {
            tabIndex = 0
        }
        datasourceIndex = index
        lifecycleOwner?.lifecycleScope?.launch {
            val apiData = executeWorkTask(code)
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime
            if (duration < MIN_LOADING_DURATION) {
                delay(MIN_LOADING_DURATION - duration)
            }
            if (apiData == null) {
                onLoadErr()
            } else {
                if (apiData.resp.isSuccess) {
                    if (apiData.resp.tabList.isNullOrEmpty()) {
                        onLoadEmpty()
                    } else {
                        onLoadSuccess(
                            apiData.resp.tabList!!,
                            apiData.resp.linkText(),
                            apiData.resp.linkUrl()
                        )
                    }
                } else {
                    onLoadErr()
                }
            }
        }
    }

    private suspend fun executeWorkTask(code: String) = withContext(Dispatchers.IO) {
        try {
            withTimeout(TimeUnit.SECONDS.toMillis(10)) { // 设置超时时间为10秒
                val response = WorkWidgetTabDetailRequest(object :
                    BaseApiRequestCallback<WorkWidgetTabDetailResponse>() {
                    override fun onSuccess(data: ApiData<WorkWidgetTabDetailResponse>?) {

                    }

                    override fun onFailed(reason: ErrorReason?) {

                    }
                }).apply {
                    datasourceCode = code
                }

                // 执行 HTTP 请求
                HttpExecutor.executeSync(response)
            }
        } catch (e: Exception) {
            // 其他异常处理
            onLoadErr()
            null
        }
    }

    fun currentTabAppLinkUrl(): String? {
        return tabAdapter.data.getOrNull(tabIndex)?.appLinkUrl
    }

    private fun onLoading() {
        binding.groupContent.isVisible = false
        binding.clLoading.isVisible = true
    }

    private fun onLoadErr() {
        binding.clLoading.isVisible = false
        binding.groupContent.isVisible = true
        binding.clLoadErr.visibility = View.VISIBLE
        binding.clLoadEmpty.visibility = View.GONE
        binding.rvTab.visibility = View.GONE
        binding.rvWidgets.visibility = View.GONE
    }

    private fun onLoadEmpty() {
        binding.clLoading.isVisible = false
        binding.groupContent.isVisible = true
        binding.clLoadErr.visibility = View.GONE
        binding.clLoadEmpty.visibility = View.VISIBLE
        binding.rvTab.visibility = View.GONE
        binding.rvWidgets.visibility = View.GONE
    }

    private fun onLoadSuccess(
        dataList: List<WorkWidgetItem>,
        appLinkText: String?,
        appLinkUrl: String?
    ) {
        binding.clLoading.isVisible = false
        binding.groupContent.isVisible = true
        binding.clLoadErr.visibility = View.GONE
        binding.clLoadEmpty.visibility = View.GONE
        // init tab
        binding.rvTab.visibility = if (dataList.isEmpty()) View.GONE else View.VISIBLE
        val tabs = dataList.mapIndexed { index, workWidgetItem ->
            WidgetTab(workWidgetItem.name ?: "", workWidgetItem.linkUrl(), index == tabIndex)
        }.take(30)
        tabAdapter.setNewData(tabs)
        binding.rvTab.post {
            binding.rvTab.scrollToPosition(0)
        }
        binding.ivCover.post {
            binding.ivCover.isVisible = !binding.rvTab.isScrollToBottom()
        }
        tabAdapter.setOnItemClickListener { adapter, view, position ->
            tabIndex = position
            val data = tabAdapter.data
            val preSelectIndex = data.indexOfFirst { it.select }
            val tab = data[position]
            data[preSelectIndex].select = false
            tabAdapter.notifyItemChanged(preSelectIndex)
            tab.select = true
            tabAdapter.notifyItemChanged(position)
            val item = dataList[position]
            item.fillInData()
            checkAppLink(item, appLinkUrl, appLinkText)
            // 点击切换 tab 埋点
            PointUtils.pointV4("workbench-databoard-tabswitch", null)
        }
        dataList.getOrNull(tabIndex)?.fillInData() ?: dataList.first().fillInData()
        // init more action
        binding.tvErrDesc.movementMethod = LinkMovementMethod.getInstance()
        binding.tvErrDesc.text =
            SpannableStringBuilder(context.getString(R.string.work_widget_load_err_desc)).apply {
                setSpan(object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        datasourceCode?.let {
                            onLoading()
                            loadData(it, title, index = <EMAIL>)
                        }
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.color = resources.getColor(R.color.color_5D68E8)
                        ds.isUnderlineText = false
                    }
                }, length - 4, length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        checkAppLink(dataList.first(), appLinkUrl, appLinkText)
    }

    private fun checkAppLink(item: WorkWidgetItem, appLinkUrl: String?, appLinkText: String?) {
        val isDataAreaEmpty = item.dataArea.isNullOrEmpty()
        binding.clLoadEmpty.visibility = if (isDataAreaEmpty) View.VISIBLE else View.GONE
        binding.rvWidgets.visibility = if (isDataAreaEmpty) View.GONE else View.VISIBLE
        binding.clLoadErr.visibility = View.GONE

        binding.vClick.setOnClickListener {
            if (!appLinkUrl.isNullOrEmpty()) {
                listener?.handler(appLinkUrl)
            }
            // 点击查看更多入口埋点
            PointUtils.pointV4("workbench-databoard-moreclick", null)
        }

        updateMoreSection(appLinkUrl, appLinkText)
    }

    private fun updateMoreSection(appLinkUrl: String?, appLinkText: String?) {
        binding.tvMore.text = appLinkText
        if (appLinkUrl.isNullOrEmpty()) {
            binding.tvMore.setTextColor(resources.getColor(R.color.color_868F9E))
            binding.groupMoreDecoration.visibility = View.GONE
        } else {
            binding.tvMore.setTextColor(resources.getColor(R.color.color_404A59))
            binding.groupMoreDecoration.visibility = View.VISIBLE
        }
    }


    private fun WorkWidgetItem.fillInData() {
        if (dataArea.isNullOrEmpty()) {
            binding.clLoadEmpty.visibility = View.VISIBLE
            binding.rvWidgets.visibility = View.GONE
            binding.clLoadErr.visibility = View.GONE
        } else {
            binding.clLoadEmpty.visibility = View.GONE
            binding.rvWidgets.visibility = View.VISIBLE
            binding.clLoadErr.visibility = View.GONE
            itemAdapter.setNewData(dataArea.take(6))
        }
        itemAdapter.setOnItemClickListener { adapter, view, position ->
            // 点击切换 tab 数据源入口 埋点
            PointUtils.pointV4("workbench-databoard-tabdata", null)
            val item = adapter.getItem(position) as WorkWidgetDataItem
            // 热区优先级：WorkWidgetDataItem.appLinkUrl > WorkWidgetItem.appLinkUrl
            val targetLinkUrl = if (!item.linkUrl().isNullOrEmpty()) {
                item.linkUrl()
            } else this.linkUrl()
            listener?.handler(targetLinkUrl ?: "")
        }
    }

    fun titleArrowRotateUp() {
        val rotateAnimation = RotateAnimation(
            0f, -180f,
            Animation.RELATIVE_TO_SELF, 0.5f,
            Animation.RELATIVE_TO_SELF, 0.5f
        )
        rotateAnimation.duration = 300
        rotateAnimation.fillAfter = true
        binding.ivArrow.startAnimation(rotateAnimation)
    }

    fun titleArrowRotateDown() {
        val rotateAnimation = RotateAnimation(
            -180f, 0f,
            Animation.RELATIVE_TO_SELF, 0.5f,
            Animation.RELATIVE_TO_SELF, 0.5f
        )
        rotateAnimation.duration = 300
        rotateAnimation.fillAfter = true
        binding.ivArrow.startAnimation(rotateAnimation)
    }

    interface AppLinkListener {
        fun handler(appLink: String)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        lifecycleOwner = null
    }

}
