package com.twl.hi.work.bindadapter;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.graphics.Typeface;
import android.text.Spannable;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.text.style.StyleSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.databinding.BindingAdapter;

import com.twl.hi.basic.views.HiTextClock;
import com.twl.hi.foundation.model.Contact;
import com.twl.hi.work.R;
import com.twl.hi.work.model.WidgetTab;
import com.twl.hi.work.model.WorkWidgetCategoryBean;

import java.util.List;

import hi.kernel.Constants;
import hi.kernel.HiKernel;
import lib.twl.common.base.BaseApplication;
import lib.twl.common.ext.ViewExtKt;
import lib.twl.common.util.CommonUtils;
import lib.twl.common.util.LDate;

/**
 * <AUTHOR>
 * @date 2021/7/1.
 */
public class WorkBindingAdapters {

    @BindingAdapter("folderTime")
    public static void setFolderTime(TextView textView, long time) {
        StringBuilder stringBuilder = new StringBuilder();
        if (LDate.isToday(time)) {
            stringBuilder.append(BaseApplication.getApplication().getResources().getString(R.string.today));
            stringBuilder.append(" ");
            stringBuilder.append(DateFormat.format("HH:mm", time));
            textView.setText(stringBuilder.toString());
            return;
        }
        if (LDate.isYesterday(time)) {
            stringBuilder.append(BaseApplication.getApplication().getResources().getString(R.string.yesterday));
            stringBuilder.append(" ");
            stringBuilder.append(DateFormat.format("HH:mm", time));
            textView.setText(stringBuilder.toString());
            return;
        }
        textView.setText(DateFormat.format("yyyy-MM-dd HH:mm", time));
    }

    @BindingAdapter({"groupAttendanceWorkTypeItemTimes"})
    public static void setGroupAttendanceWorkTypeItemTimes(TextView textView, int times) {
        textView.setText(times + BaseApplication.getApplication().getResources().getString(R.string.work_times));
    }

    @BindingAdapter({"dailyGroupAttendanceClockTime"})
    public static void setDailyGroupAttendanceClockTime(TextView textView, long time) {
        String clockTime;
        if (time <= 0) {
            clockTime = "--:--";
        } else {
            clockTime = LDate.getDate(time, LDate.HmFormat);
        }
        textView.setText(clockTime);
    }

    @BindingAdapter("applyCreateTime")
    public static void setApplyCreateTime(TextView textView, long createTime) {
        String time = LDate.getTime(createTime, LDate.yMdFormat);
        textView.setText(String.format(BaseApplication.getApplication().getResources().getString(R.string.work_apply_create_time), time));
    }

    @BindingAdapter("applyCreator")
    public static void setApplyCreator(TextView textView, Contact contact) {
        if (contact == null) {
            return;
        }
        String userName = contact.getUserName();
        String department = contact.getShowDeptNames();
        textView.setText(String.format(BaseApplication.getApplication().getResources().getString(R.string.work_apply_creator),
                TextUtils.isEmpty(userName) ? "" : userName,
                TextUtils.isEmpty(department) ? "" : department));
    }

    @BindingAdapter({"groupAttendanceWorkTypeTitle"})
    public static void setGroupAttendanceWorkTypeTitle(TextView textView, int type) {
        String workType;
        switch (type) {
            case Constants.ATTENDANCE_LATE:
                workType = BaseApplication.getApplication().getResources().getString(R.string.work_late);
                break;
            case Constants.ATTENDANCE_LATE_1:
                workType = BaseApplication.getApplication().getResources().getString(R.string.work_late1);
                break;
            case Constants.ATTENDANCE_LATE_2:
                workType = BaseApplication.getApplication().getResources().getString(R.string.work_late2);
                break;
            case Constants.ATTENDANCE_LEAVE_EARLY:
                workType = BaseApplication.getApplication().getResources().getString(R.string.work_leave_early);
                break;
            case Constants.ATTENDANCE_LEAVE_EARLY_1:
                workType = BaseApplication.getApplication().getResources().getString(R.string.work_leave_early1);
                break;
            case Constants.ATTENDANCE_LEAVE_EARLY_2:
                workType = BaseApplication.getApplication().getResources().getString(R.string.work_leave_early2);
                break;
            case Constants.ATTENDANCE_ABSENTEEISM_1:
                workType = BaseApplication.getApplication().getResources().getString(R.string.work_absenteeism1);
                break;
            case Constants.ATTENDANCE_ABSENTEEISM_2:
                workType = BaseApplication.getApplication().getResources().getString(R.string.work_absenteeism2);
                break;
            case Constants.ATTENDANCE_ABSENTEEISM_HALF_DAY:
                workType = BaseApplication.getApplication().getResources().getString(R.string.work_absenteeism_half_day);
                break;
            case Constants.ATTENDANCE_ABSENTEEISM_ALL_DAY:
                workType = BaseApplication.getApplication().getResources().getString(R.string.work_absenteeism_all_day);
                break;
            case Constants.ATTENDANCE_MISSING_CARD:
                workType = BaseApplication.getApplication().getResources().getString(R.string.work_missing_card);
                break;
            default:
                workType = "";
                break;
        }

        textView.setText(workType);
    }

    @BindingAdapter(value = {"setDayTimeWithWeek", "format"})
    public static void setDayTimeWithWeek(TextView textView, long dayTime, String format) {
        if (TextUtils.isEmpty(format)) {
            format = "yyyy年M月d日";
        }
        textView.setText(LDate.getTimeInPattern(dayTime, format) + "  " + LDate.dayOfWeek(dayTime));
    }

    @BindingAdapter({"hiTextClockTime"})
    public static void setHiTextClockTime(HiTextClock hiTextClock, long time) {
        hiTextClock.setCurrentMillisTime(time);
    }

    @BindingAdapter({"abnormalAppealFlag", "abnormalUserId"})
    public static void setAbnormalStatus(ViewGroup viewGroup, int appealFlag, String userId) {
        View arrow = viewGroup.findViewById(R.id.iv_arrow);
        if (TextUtils.equals(HiKernel.getHikernel().getAccount().getUserId(), userId) && appealFlag == 1) {
            viewGroup.setEnabled(true);
            arrow.setVisibility(View.VISIBLE);
        } else {
            viewGroup.setEnabled(false);
            arrow.setVisibility(View.INVISIBLE);
        }
    }

    @BindingAdapter("applyStatus")
    public static void setApplyStatus(TextView textView, int status) {
        Context context = BaseApplication.getApplication();
        Resources resource = context.getResources();
        switch (status) {
            case Constants.APPROVAL_DURING:
                textView.setText(context.getResources().getString(R.string.work_approval_during));
                textView.setTextColor(resource.getColor(R.color.color_575CD7));
                break;
            case Constants.APPROVAL_OVER:
                textView.setText(context.getResources().getString(R.string.work_approval_over));
                textView.setTextColor(resource.getColor(R.color.color_9B9B9B));
                break;
            case Constants.APPROVAL_REJECT:
                textView.setText(context.getResources().getString(R.string.work_approval_reject));
                textView.setTextColor(resource.getColor(R.color.color_FF374E));
                break;
            case Constants.APPROVAL_WITHDRAW:
                textView.setText(context.getResources().getString(R.string.work_approval_withdraw));
                textView.setTextColor(resource.getColor(R.color.color_9B9B9B));
                break;
            default:
                textView.setText(context.getResources().getString(R.string.work_approval_default));
                textView.setTextColor(resource.getColor(R.color.color_575CD7));
                break;
        }
    }

    /**
     * 当目标为空时 展示
     */
    @BindingAdapter("replaceTo")
    public static void replaceTo(View view, List<?> target) {
        if (null == target || target.size() == 0) {
            view.setVisibility(View.VISIBLE);
        } else {
            view.setVisibility(View.GONE);
        }
    }

    @BindingAdapter("spannableText")
    public static void setSpannableText(TextView view, String item) {
        Spannable spannableString = ViewExtKt.createDividerSpannableString(item, () -> new StyleSpan(Typeface.BOLD), '\u200b', '\u2060');
        view.setText(spannableString);
    }

    @BindingAdapter("tab")
    public static void tab(TextView textView, WidgetTab tab) {
        if (tab == null) {
            return;
        }
        Context context = BaseApplication.getApplication();
        Resources resources = context.getResources();
        int textColor = tab.getSelect() ? resources.getColor(R.color.work_widget_tab_text_selected_color) : resources.getColor(R.color.work_widget_tab_text_color);
        Drawable bg = tab.getSelect() ? resources.getDrawable(R.drawable.bg_corner_6_color_f0f2f5) : null;
        textView.setTextColor(textColor);
        textView.setBackground(bg);
        final int limit = 30;
        String tabName;
        if (CommonUtils.getFilterLength(tab.getName()) >= limit) {
            tabName = CommonUtils.subString(tab.getName(), limit);
        } else {
            tabName = tab.getName();
        }
        textView.setText(tabName);
    }
}
