package com.twl.hi.work.select;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.twl.hi.basic.model.select.ICloudSelectParams;
import com.twl.hi.foundation.api.response.bean.ResourceBean;
import com.twl.hi.foundation.base.fragment.FoundationVMShareFragment;
import com.twl.hi.foundation.model.message.MessageForFile;
import com.twl.hi.foundation.model.message.MessageForVideo;
import com.twl.hi.work.BR;
import com.twl.hi.work.R;
import com.twl.hi.work.adapter.ICloudSelectAdapter;
import com.twl.hi.work.callback.ICloudFragmentSelectCallback;
import com.twl.hi.work.databinding.WorkFragmentICloudSelectBinding;
import com.twl.hi.work.model.SelectICloudFileBean;
import com.twl.hi.work.viewmodel.ICloudSelectFragmentViewModel;
import com.twl.hi.work.viewmodel.ICloudSelectViewModel;

import java.io.Serializable;
import java.util.Stack;

import hi.kernel.BundleConstants;
import kotlin.Triple;
import lib.twl.common.util.AppUtil;
import lib.twl.common.util.LList;
import lib.twl.common.util.ToastUtils;
import lib.twl.common.views.HiDefaultItemAnimator;

public class ICloudSelectFragment extends FoundationVMShareFragment<WorkFragmentICloudSelectBinding, ICloudSelectFragmentViewModel, ICloudSelectViewModel>
        implements ICloudFragmentSelectCallback {

    private static final int SELECT_FILE_LIMIT = 100;
    ICloudSelectAdapter adapter;
    private ICloudSelectParams iCloudSelectParams;

    private ResourceBean mResourceBean;

    private String mVisitCode;

    public static ICloudSelectFragment newInstance(String visitCode, ICloudSelectParams iCloudSelectParams, ResourceBean third) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_DATA_STRING, visitCode);
        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, iCloudSelectParams);
        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE_1, third);
        ICloudSelectFragment fragment = new ICloudSelectFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void initFragment() {
        if (getArguments() != null) {
            iCloudSelectParams = (ICloudSelectParams) getArguments().getSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE);
            mResourceBean = (ResourceBean) getArguments().getSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE_1);
            String visitCode = getArguments().getString(BundleConstants.BUNDLE_DATA_STRING, "");
            mVisitCode = visitCode;
            // 不是根目录，或者根目录的一级目录可以保存
            if (mResourceBean != null) {
                if (mResourceBean.isSpace) {
                    getDataBinding().setEnableSaveFile(mResourceBean.canSave == 1);
                } else {
                    getDataBinding().setEnableSaveFile(!TextUtils.isEmpty(visitCode));
                }
            } else {
                getDataBinding().setEnableSaveFile(!TextUtils.isEmpty(visitCode));
            }
            getViewModel().init(visitCode, getActivityViewModel(), iCloudSelectParams);
            getDataBinding().setIsMultiSelect(iCloudSelectParams.isMultiSelect);
        }
        adapter = new ICloudSelectAdapter(this, iCloudSelectParams.isMultiSelect);
        getDataBinding().recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        getDataBinding().recyclerView.setAdapter(adapter);
        getDataBinding().recyclerView.setItemAnimator(new HiDefaultItemAnimator());
        getDataBinding().smartRefreshLayout.setEnableLoadMore(true);
        getDataBinding().smartRefreshLayout.setEnableRefresh(true);
        getDataBinding().smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> getViewModel().loadData(false, iCloudSelectParams.getActionType()));
        getDataBinding().smartRefreshLayout.setOnRefreshListener(refreshLayout -> getViewModel().loadData(true, iCloudSelectParams.getActionType()));
        getViewModel().getFoldersLiveData().observe(this, folderBaseBeans -> {
            getDataBinding().smartRefreshLayout.finishLoadMore();
            getDataBinding().smartRefreshLayout.finishRefresh();
            getDataBinding().smartRefreshLayout.setEnableLoadMore(getViewModel().isHasMore());
            adapter.submitList(folderBaseBeans);
            if (!LList.isEmpty(folderBaseBeans)) {
                getDataBinding().smartRefreshLayout.setVisibility(View.VISIBLE);
                getDataBinding().tvEmpty.setVisibility(View.GONE);
            } else {
                getDataBinding().smartRefreshLayout.setVisibility(View.GONE);
                getDataBinding().tvEmpty.setVisibility(View.VISIBLE);
            }
        });
        getViewModel().getLoadFinishLiveData().observe(this, aBoolean -> {
            if (aBoolean != null) {
                getDataBinding().smartRefreshLayout.finishLoadMore();
                getDataBinding().smartRefreshLayout.finishRefresh();
            }
        });
        getViewModel().homePage().observe(this, goHomePage -> {
            if (goHomePage) {
                getActivityViewModel().goHomePage();
            }
        });
        getViewModel().getPostScrollLiveData().observe(this, integer -> {
            if (integer != null) {
                postScrollToPosition(integer);
            }
        });
        getViewModel().getUpLoadFileLiveData().observe(this, aBoolean -> {
            if (aBoolean != null && aBoolean) {
                ToastUtils.ss(R.string.save_success);
                AppUtil.finishActivity(activity);
            }
        });

    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return 0;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.work_fragment_i_cloud_select;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }


    @Override
    public Object getCallback() {
        return this;
    }


    @Override
    public boolean onLongClick(SelectICloudFileBean folderBaseBean) {
        return false;
    }

    @Override
    public void onItemClick(SelectICloudFileBean folderBaseBean) {
        if (folderBaseBean.getResourceBean().isSpace || folderBaseBean.getResourceBean().isDirectory()) {
            Triple<String, String, ResourceBean> triple = new Triple<>(folderBaseBean.getResourceBean().getId(), folderBaseBean.getResourceBean().name, folderBaseBean.getResourceBean());
            Stack<Triple<String, String, ResourceBean>> value = getActivityViewModel().getDeptPath().getValue();
            value.push(triple);
            getActivityViewModel().setDeptPath(value);
        } else {
            if (getViewModel().getSelectedICloudFileMutableModel().getAddIds().size() >= SELECT_FILE_LIMIT) {
                ToastUtils.ss(String.format(getResources().getString(R.string.work_max_select_file), SELECT_FILE_LIMIT));
                return;
            }
            if (iCloudSelectParams.isMultiSelect) {
                if (folderBaseBean.isAlreadySelected) {
                    return;
                }
                boolean newSelect = !folderBaseBean.isSelected();
                folderBaseBean.setSelected(newSelect);
            }
        }
    }

    @Override
    public void saveFileToFolder() {
        if (iCloudSelectParams != null) {
            Serializable content = iCloudSelectParams.getContent();
            switch (iCloudSelectParams.getCategory()) {
                case ResourceBean.CATEGORY_PIC:
                    if (content instanceof String) {
                        String originalUrl = (String) content;
                        if (TextUtils.isEmpty(originalUrl) || !originalUrl.startsWith("http")) {
                            ToastUtils.failure(R.string.work_miss_pic_info);
                        } else {
                            getViewModel().saveResource(originalUrl, mVisitCode, iCloudSelectParams.getMessageId());
                        }
                    }
                    break;
                case ResourceBean.CATEGORY_VIDEO:
                    if (content instanceof MessageForVideo.VideoInfo) {
                        MessageForVideo.VideoInfo videoInfo = ((MessageForVideo.VideoInfo) content);
                        if (TextUtils.isEmpty(videoInfo.getUrl())) {
                            ToastUtils.failure(R.string.work_miss_video_info);
                            return;
                        }
                        getViewModel().saveResource(videoInfo.getUrl(), mVisitCode, iCloudSelectParams.getMessageId());

                    }
                    break;
                case ResourceBean.CATEGORY_FOLDER_FILE:
                    if (content instanceof MessageForFile.FileInfo) {
                        MessageForFile.FileInfo fileInfo = ((MessageForFile.FileInfo) content);
                        if (TextUtils.isEmpty(fileInfo.getUrl()) || !fileInfo.getUrl().startsWith("http")) {
                            ToastUtils.failure(R.string.work_miss_file_info);
                        } else {
                            getViewModel().saveResource(fileInfo.getUrl(), mVisitCode, iCloudSelectParams.getMessageId());
                        }
                    }
                    break;
            }
        }
    }

    @Override
    public void clickLeft(View view) {

    }

    @Override
    public void clickRight(View view) {

    }

    private void postScrollToPosition(final int position) {
        getDataBinding().recyclerView.postDelayed(() -> {
            int scrollPosition = position;
            int totalCount = adapter.getItemCount();
            if (scrollPosition > totalCount) {
                scrollPosition = totalCount - 1;
            }
            if (scrollPosition < 0) {
                scrollPosition = 0;
            }
            getDataBinding().recyclerView.getLayoutManager().scrollToPosition(scrollPosition);
        }, 200);
    }
}

