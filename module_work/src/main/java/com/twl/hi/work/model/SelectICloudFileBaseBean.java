package com.twl.hi.work.model;

import android.text.TextUtils;

import androidx.databinding.ObservableBoolean;

import com.twl.hi.basic.model.select.SelectBaseInterface;
import com.twl.hi.foundation.api.response.bean.ResourceBean;

public class SelectICloudFileBaseBean implements SelectBaseInterface {
    private ResourceBean resourceBean;
    private SelectICloudFileProxy mSelectProxy;
    public ObservableBoolean mSelected = new ObservableBoolean(false) {
        private static final long serialVersionUID = 7878477301520106971L;

        @Override
        public void set(boolean value) {
            SelectICloudFileProxy selectProxy = mSelectProxy;
            if (selectProxy != null) {
                selectProxy.setSelect(resourceBean, value);
            }
            super.set(value);
        }

        @Override
        public boolean get() {
            SelectICloudFileProxy selectProxy = mSelectProxy;
            if (selectProxy != null) {
                return selectProxy.isSelect(resourceBean);
            }
            return super.get();
        }
    };

    protected SelectICloudFileBaseBean(ResourceBean resourceBean, SelectICloudFileProxy selectProxy) {
        this.resourceBean = resourceBean;
        mSelectProxy = selectProxy;
    }

    public ResourceBean getResourceBean() {
        return resourceBean;
    }

    @Override
    public boolean isSelected() {
        return mSelected.get();
    }

    @Override
    public void setSelected(boolean selected) {
        mSelected.set(selected);
    }

    public static boolean areItemsTheSame(SelectICloudFileBaseBean oldItem, SelectICloudFileBaseBean newItem) {
        ResourceBean oldF = oldItem.getResourceBean();
        ResourceBean newF = newItem.getResourceBean();
        if (oldF == null || newF == null) {
            return false;
        }
        return oldF.getCategory() == newF.getCategory() && TextUtils.equals(oldF.getId(), newF.getId());
    }

    public static boolean areContentsTheSame(SelectICloudFileBaseBean oldItem, SelectICloudFileBaseBean newItem) {
        ResourceBean oldF = oldItem.getResourceBean();
        ResourceBean newF = newItem.getResourceBean();
        if (oldF == null || newF == null) {
            return false;
        }
        return TextUtils.equals(oldF.getId(), newF.getId())
                && oldF.visitCode == newF.visitCode
                && oldF.icon == newF.icon
                && oldF.url == newF.url
                && oldF.updateTime == newF.updateTime
                && TextUtils.equals(oldF.name, newF.name);
    }
}
