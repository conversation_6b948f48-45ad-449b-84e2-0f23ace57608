package com.twl.hi.work.viewmodel;

import android.app.Application;

import com.twl.hi.work.api.response.bean.DailyGroupAttendanceBean;
import com.twl.hi.foundation.base.FoundationViewModel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import lib.twl.common.util.ExecutorFactory;

public class DailyStatisticsViewModel extends FoundationViewModel {
    private static final String TAG = "DailyStatisticsViewModel";
    //排序类型，默认是上班的升序
    private int sortType = GroupAttendanceViewModel.SORT_DOWN;
    private int onWorkSortType = GroupAttendanceViewModel.SORT_DOWN;
    private int offWorkSortType = GroupAttendanceViewModel.SORT_UP;
    private int workType = GroupAttendanceViewModel.WORK_ON;
    private GroupAttendanceViewModel groupAttendanceViewModel;
    private List<String> filters = new ArrayList<>();

    public DailyStatisticsViewModel(Application application) {
        super(application);
    }

    public void init(GroupAttendanceViewModel viewModel) {
        groupAttendanceViewModel = viewModel;
    }

    public void filterData(List<String> longs) {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                filters.clear();
                filters.addAll(longs);
                filterAndSortDailyData();
            }
        });
    }

    public void sortDailyData() {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                filterAndSortDailyData();
            }
        });
    }

    private void filterAndSortDailyData() {
        List<DailyGroupAttendanceBean> filterResults = new ArrayList<>();
        List<DailyGroupAttendanceBean> source;
        if (workType == GroupAttendanceViewModel.WORK_ON) {
            source = groupAttendanceViewModel.onWorkList;
        } else {
            source = groupAttendanceViewModel.offWorkList;
        }
        if (filters.isEmpty()) {
            filterResults.addAll(source);
        } else {
            for (DailyGroupAttendanceBean bean : source) {
                if (bean.contact == null) {
                    continue;
                }
                List<String> deptIds = bean.contact.getDeptIds();
                if (deptIds == null) {
                    continue;
                }
                for (String deptId : deptIds) {
                    if (filters.contains(deptId)) {
                        filterResults.add(bean);
                        break;
                    }
                }
            }
        }
        if (workType == GroupAttendanceViewModel.WORK_ON) {
            if (sortType == GroupAttendanceViewModel.SORT_UP) {//上班状态，排序为降序，反转数据集
                Collections.reverse(filterResults);
            }
        } else {
            if (sortType == GroupAttendanceViewModel.SORT_DOWN) {//下班状态，排序为升序，反转数据集
                Collections.reverse(filterResults);
            }
        }

        groupAttendanceViewModel.getDailyListLiveData().postValue(filterResults);
    }

    public int getSortType() {
        return sortType;
    }

    /**
     * 设置排序类型时,根据上下班状态更新期排序类型
     *
     * @param sortType
     */
    public void setSortType(int sortType) {
        this.sortType = sortType;
        if (workType == GroupAttendanceViewModel.WORK_ON) {
            onWorkSortType = sortType;
        } else {
            offWorkSortType = sortType;
        }
    }

    public int getWorkType() {
        return workType;
    }

    /**
     * 设置上下班时，根据相应的排序类型，更新界面排序规则
     *
     * @param workType
     */
    public void setWorkType(int workType) {
        this.workType = workType;
        if (workType == GroupAttendanceViewModel.WORK_ON) {
            sortType = onWorkSortType;
        } else {
            sortType = offWorkSortType;
        }
    }
}