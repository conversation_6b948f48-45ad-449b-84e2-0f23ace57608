package com.twl.hi.work.attendance

import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.twl.hi.basic.model.TemporaryTokenBean
import com.twl.hi.basic.model.WebViewBean
import com.twl.hi.basic.util.CalendarDataTransactor
import com.twl.hi.export.webview.WebViewPageRouter
import com.twl.hi.foundation.base.fragment.FoundationVMFragment
import com.twl.hi.foundation.utils.PointUtils
import com.twl.hi.work.BR
import com.twl.hi.work.R
import com.twl.hi.work.adapter.AttendanceAbnormalAdapter
import com.twl.hi.work.api.response.bean.AttendanceAbnormalBean
import com.twl.hi.work.attendance.callback.AttendanceStatisticCallback
import com.twl.hi.work.attendance.viewmodel.AttendanceStatisticViewModel
import com.twl.hi.work.databinding.WorkFragmentAttendanceAbnormalStatisticBinding
import com.twl.hi.work.databinding.WorkPopupApprovalTypeBinding
import com.twl.hi.work.model.ApprovalPopItemBean
import hi.kernel.BundleConstants
import hi.kernel.Constants
import lib.twl.common.adapter.CommonAdapter
import lib.twl.common.ext.getStatusBarsHeight
import lib.twl.common.util.AppUtil
import lib.twl.common.util.QMUIDisplayHelper
import lib.twl.common.util.ToastUtils
import lib.twl.common.util.widget.HiPopupWindow
import lib.twl.common.views.calendar.CalendarView.OnMonthChangeListener
import lib.twl.common.views.calendar.CalendarView.OnViewChangeListener

/**
 *@author: musa on 2022/5/9
 *@e-mail: <EMAIL>
 *@desc: 打卡统计页
 */
class AttendanceAbnormalStatisticFragment :
    FoundationVMFragment<WorkFragmentAttendanceAbnormalStatisticBinding, AttendanceStatisticViewModel>(),
    AttendanceStatisticCallback, OnMonthChangeListener, OnViewChangeListener{

    /**选中的考勤异常bean*/
    private var selectedAttendanceAbnormalBean: AttendanceAbnormalBean? = null

    private val adapter by lazy {
        AttendanceAbnormalAdapter(context, this, viewModel.userId.get())
    }
    /**底部弹窗*/
    private val bottomPopupWindow by lazy {
        val windowDataBinding = DataBindingUtil.inflate<WorkPopupApprovalTypeBinding>(
            LayoutInflater.from(context),
            R.layout.work_popup_approval_type, null, false
        )
        windowDataBinding.callback = this
        windowDataBinding.approvals.adapter = CommonAdapter<ApprovalPopItemBean>(
            R.layout.work_item_approval_popup,
            BR.bean
        ).also {
            it.addCallback(BR.callback, this)
            it.submitList(viewModel.getApprovalWorkViewBeans())
        }
        HiPopupWindow.Builder(context)
            .setContentView(windowDataBinding.root)
            .setAnimationStyle(R.style.pop_anim_style)
            .setShadow(getActivity()?.window, 0.6f)
            .build()
    }

    companion object{
        fun newInstance(year: Int, month: Int, userId: String) = AttendanceAbnormalStatisticFragment().apply {
            arguments = Bundle().apply {
                putInt(BundleConstants.BUNDLE_DATA_INT, year)
                putInt(BundleConstants.BUNDLE_DATA_INT_1, month)
                putString(BundleConstants.BUNDLE_DATA_LONG, userId)
            }
        }
    }

    override fun getContentLayoutId() = R.layout.work_fragment_attendance_abnormal_statistic

    override fun getCallbackVariable() = BR.callback

    override fun getCallback() = this

    override fun getBindingVariable() = BR.viewModel

    override fun initFragment() {
        super.initFragment()
        dataBinding.llContainer.setPadding(0,activity.getStatusBarsHeight(),
            0, 0)
        dataBinding.titleBar.ivRight.setImageResource(R.drawable.work_ic_attendace_abnormal)
        dataBinding.titleBar.showIvRight = true
        initArguments()
        dataBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        dataBinding.recyclerView.adapter = adapter

        dataBinding.calendarView.setOnMonthChangeListener(this)
        dataBinding.calendarView.setOnViewChangeListener(this)
        dataBinding.calendarView.setRange(2012, 1, -1,
            dataBinding.calendarView.curYear, dataBinding.calendarView.curMonth, -1)

        viewModel.calendarData.observe(this,
            Observer<CalendarDataTransactor?> { calendarDataTransactor ->
                dataBinding.calendarView.setSchemeDate(
                    calendarDataTransactor!!.schemaMap
                )
            })

        viewModel.abnormal.observe(this,
            Observer<List<AttendanceAbnormalBean>> { attendanceAbnormalBeans ->
                val list: MutableList<Any> = ArrayList()
                attendanceAbnormalBeans?.apply {
                    if (attendanceAbnormalBeans.isNotEmpty()){
                        list.add(attendanceAbnormalBeans.size)
                    } else {
                        list.add("empty")
                    }
                }
                list.addAll(attendanceAbnormalBeans)
                adapter.submitList(list)
            })

        viewModel.externalScanResult.observe(this,
            Observer { temporaryTokenBean ->
                temporaryTokenBean?.run {
                    var webViewBean = WebViewBean().apply {
                        url = temporaryTokenBean.url
                        style = temporaryTokenBean.style
                    }
                    AppUtil.startUri(
                        context,
                        WebViewPageRouter.WEB_VIEW_ACTIVITY,
                        Bundle().apply {
                            putSerializable(Constants.DATA_WEB_BEAN, webViewBean)
                        }
                    )
                }
            })

    }

    private fun initArguments() = arguments?.run {
        val year = getInt(BundleConstants.BUNDLE_DATA_INT)
        val month = getInt(BundleConstants.BUNDLE_DATA_INT_1)
        viewModel.userId.set(getString(BundleConstants.BUNDLE_DATA_LONG).apply {
            if (this?.isNotEmpty() == true) {
                //如果传递用户参数，年月，将日历移动该月
                dataBinding.calendarView.scrollToCalendar(year, month, 1)
            } else {
                dataBinding.calendarView.scrollToCurrent()
            }
        })
    }

    override fun onMonthChange(year: Int, month: Int) {
        viewModel.getData(year, month)
    }

    override fun onViewChange(isMonthView: Boolean) {
        //nothing
    }

    override fun clickLeft(view: View?) {
        AppUtil.finishActivity(getActivity())
    }

    override fun clickRight(view: View?) {
        viewModel.queryAttendanceRulesWebToken()
        PointUtils.BuilderV3()
            .name("work-clockin-rule-click")
            .params("type", 2)
            .point()
    }

    override fun onApprovalTypeItemClick(bean: ApprovalPopItemBean?) {
        dismissPop()
        if (TextUtils.isEmpty(bean?.url)) {
            ToastUtils.ssdc("网络地址异常")
            return
        }
        val temporaryTokenBean = TemporaryTokenBean(StringBuilder().apply{
            append(bean?.url)
            append("?noHead=1&appeal=1")
            append("&date=")
            append(selectedAttendanceAbnormalBean?.workDate)
            append("&dateType=")
            append(selectedAttendanceAbnormalBean?.timeSpan)
        }.toString())
        temporaryTokenBean.style = Constants.WEB_STYLE_HAS_NO_TITLE_AND_TRANSLUCENT
        viewModel.getTemporaryToken(temporaryTokenBean, "7Awt41Z1")
    }

    override fun dismissPop() {
        bottomPopupWindow.dismiss()
    }

    override fun onAbnormalItemClick(bean: AttendanceAbnormalBean?) {
        bottomPopupWindow.showAtLocation(dataBinding.root, Gravity.BOTTOM, 0, 0)
        selectedAttendanceAbnormalBean = bean
    }
}