package com.twl.hi.work.api.response.bean;

import com.twl.hi.foundation.model.Contact;


public class DailyGroupAttendanceBean {

    public String userId;
    public long timestamp;
    public int workType;
    public String workTypeText;//考勤文案

    public Contact contact;

    public String getUserId() {
        return userId;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public int getWorkType() {
        return workType;
    }

    public Contact getContact() {
        return contact;
    }

    @Override
    public String toString() {
        return "DailyGroupAttendanceBean{" +
                "userId=" + userId +
                ", timestamp=" + timestamp +
                ", workType=" + workType +
                ", workTypeText='" + workTypeText + '\'' +
                ", contact=" + contact +
                '}';
    }
}