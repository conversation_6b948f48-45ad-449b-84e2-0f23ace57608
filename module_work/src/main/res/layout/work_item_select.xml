<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <data>

        <import type="android.view.View" />

        <variable
            name="bean"
            type="com.twl.hi.work.attendance.model.SelectedBean" />

        <variable
            name="callback"
            type="com.twl.hi.work.attendance.callback.AttendanceSettingCallback" />

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="20dp"
        android:onClick="@{() -> callback.onSelectNoticeType(bean)}">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{bean.title}"
            android:textStyle="bold"
            android:textColor="@color/app_black"
            tools:text="普通消息"/>

        <ImageView
            android:id="@+id/iv_state"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentEnd="true"
            android:src="@drawable/ic_icon_addressbook_check"
            android:visibility="@{bean.isChosen ? View.VISIBLE : View.GONE}" />

    </RelativeLayout>

</layout>