<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <data>

        <variable
            name="item"
            type="String" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:src="@drawable/work_proxy_punch_point"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="6dp"/>

        <TextView
            android:id="@+id/TvContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:spannableText="@{item}"
            android:textColor="@color/color_1C1737"
            android:lineSpacingExtra="4dp"
            android:letterSpacing="0.05"
            tools:text="系统检测到当前不是您的常用考勤设备，如用本机打卡，请承诺本机归您所有、没有代人/请人或使用非本人设备打卡。" />

    </LinearLayout>
</layout>
