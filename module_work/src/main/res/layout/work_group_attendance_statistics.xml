<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="hi.kernel.Constants" />

        <import type="android.text.TextUtils" />

        <variable
            name="attTypeStatus"
            type="com.twl.hi.work.model.ObsWOrMGroupAttTypeStatus" />

        <variable
            name="callback"
            type="com.twl.hi.work.callback.GroupAttendanceStatisticsCallback" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/work_abnormal_attendance_empty"
            android:layout_gravity="center"
            android:layout_marginTop="160dp"
            android:visibility="gone"
            app:visibleGone="@{attTypeStatus.isEmpty}"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_B1B1B8"
            android:text="@string/work_no_attendance_exception"
            android:layout_gravity="center"
            android:visibility="gone"
            app:visibleGone="@{attTypeStatus.isEmpty}"/>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:visibleGone="@{!TextUtils.isEmpty(attTypeStatus.lateTimes) &amp;&amp; !TextUtils.equals(attTypeStatus.def, attTypeStatus.lateTimes)}"
            android:onClick="@{()->callback.onGroupAttendanceClick(Constants.ATTENDANCE_LATE)}"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:text="@string/work_late"
                android:textColor="@color/color_212121"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="20dp"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:text="@{attTypeStatus.lateTimes}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                tools:text="3人3次" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:background="@color/color_CFCFCF" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:visibleGone="@{!TextUtils.isEmpty(attTypeStatus.lateTimes1) &amp;&amp; !TextUtils.equals(attTypeStatus.def, attTypeStatus.lateTimes1)}"
            android:onClick="@{()->callback.onGroupAttendanceClick(Constants.ATTENDANCE_LATE_1)}"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:text="@string/work_late1"
                android:textColor="@color/color_212121"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="20dp"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:text="@{attTypeStatus.lateTimes1}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                tools:text="3人3次" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:background="@color/color_CFCFCF" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:visibleGone="@{!TextUtils.isEmpty(attTypeStatus.lateTimes2) &amp;&amp; !TextUtils.equals(attTypeStatus.def, attTypeStatus.lateTimes2)}"
            android:onClick="@{()->callback.onGroupAttendanceClick(Constants.ATTENDANCE_LATE_2)}"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:text="@string/work_late2"
                android:textColor="@color/color_212121"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_late"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="20dp"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:text="@{attTypeStatus.lateTimes2}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                tools:text="3人3次" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:background="@color/color_CFCFCF" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:visibleGone="@{!TextUtils.isEmpty(attTypeStatus.leaveEarlyTimes) &amp;&amp; !TextUtils.equals(attTypeStatus.def, attTypeStatus.leaveEarlyTimes)}"
            android:onClick="@{()->callback.onGroupAttendanceClick(Constants.ATTENDANCE_LEAVE_EARLY)}"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:text="@string/work_leave_early"
                android:textColor="@color/color_212121"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="20dp"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:text="@{attTypeStatus.leaveEarlyTimes}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                tools:text="3人3次" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:background="@color/color_CFCFCF" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:visibleGone="@{!TextUtils.isEmpty(attTypeStatus.leaveEarlyTimes1) &amp;&amp; !TextUtils.equals(attTypeStatus.def, attTypeStatus.leaveEarlyTimes1)}"
            android:onClick="@{()->callback.onGroupAttendanceClick(Constants.ATTENDANCE_LEAVE_EARLY_1)}"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:text="@string/work_leave_early1"
                android:textColor="@color/color_212121"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="20dp"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:text="@{attTypeStatus.leaveEarlyTimes1}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                tools:text="3人3次" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:background="@color/color_CFCFCF" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:visibleGone="@{!TextUtils.isEmpty(attTypeStatus.leaveEarlyTimes2) &amp;&amp; !TextUtils.equals(attTypeStatus.def, attTypeStatus.leaveEarlyTimes2)}"
            android:onClick="@{()->callback.onGroupAttendanceClick(Constants.ATTENDANCE_LEAVE_EARLY_2)}"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:text="@string/work_leave_early2"
                android:textColor="@color/color_212121"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_leave_early"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="20dp"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:text="@{attTypeStatus.leaveEarlyTimes2}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                tools:text="3人3次" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:background="@color/color_CFCFCF" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:visibleGone="@{!TextUtils.isEmpty(attTypeStatus.absenceTimes1) &amp;&amp; !TextUtils.equals(attTypeStatus.def, attTypeStatus.absenceTimes1)}"
            android:onClick="@{()->callback.onGroupAttendanceClick(Constants.ATTENDANCE_ABSENTEEISM_1)}"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:text="@string/work_absenteeism1"
                android:textColor="@color/color_212121"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="20dp"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:text="@{attTypeStatus.absenceTimes1}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                tools:text="3人3次" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:background="@color/color_CFCFCF" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:visibleGone="@{!TextUtils.isEmpty(attTypeStatus.absenceTimes2) &amp;&amp; !TextUtils.equals(attTypeStatus.def, attTypeStatus.absenceTimes2)}"
            android:onClick="@{()->callback.onGroupAttendanceClick(Constants.ATTENDANCE_ABSENTEEISM_2)}"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:text="@string/work_absenteeism2"
                android:textColor="@color/color_212121"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="20dp"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:text="@{attTypeStatus.absenceTimes2}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                tools:text="3人3次" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:background="@color/color_CFCFCF" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:visibleGone="@{!TextUtils.isEmpty(attTypeStatus.absenceTimesHalfDay) &amp;&amp; !TextUtils.equals(attTypeStatus.def, attTypeStatus.absenceTimesHalfDay)}"
            android:onClick="@{()->callback.onGroupAttendanceClick(Constants.ATTENDANCE_ABSENTEEISM_HALF_DAY)}"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:text="@string/work_absenteeism_half_day"
                android:textColor="@color/color_212121"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="20dp"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:text="@{attTypeStatus.absenceTimesHalfDay}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                tools:text="3人3次" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:background="@color/color_CFCFCF" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:visibleGone="@{!TextUtils.isEmpty(attTypeStatus.absenceTimesALLDay) &amp;&amp; !TextUtils.equals(attTypeStatus.def, attTypeStatus.absenceTimesALLDay)}"
            android:onClick="@{()->callback.onGroupAttendanceClick(Constants.ATTENDANCE_ABSENTEEISM_ALL_DAY)}"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:text="@string/work_absenteeism_all_day"
                android:textColor="@color/color_212121"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="20dp"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:text="@{attTypeStatus.absenceTimesALLDay}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                tools:text="3人3次" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:background="@color/color_CFCFCF" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:visibleGone="@{!TextUtils.isEmpty(attTypeStatus.missClockTimes) &amp;&amp; !TextUtils.equals(attTypeStatus.def, attTypeStatus.missClockTimes)}"
            android:onClick="@{()->callback.onGroupAttendanceClick(Constants.ATTENDANCE_MISSING_CARD)}"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:text="@string/work_missing_card"
                android:textColor="@color/color_212121"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:layout_marginRight="20dp"
                android:drawableRight="@drawable/ic_icon_gray_arrow_right"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:text="@{attTypeStatus.missClockTimes}"
                android:textColor="@color/color_9B9B9B"
                android:textSize="16sp"
                tools:text="3人3次" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_height"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:background="@color/color_CFCFCF" />
        </FrameLayout>

    </LinearLayout>
</layout>