<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="callback"
            type="com.twl.hi.work.callback.WorkCallback" />

        <variable
            name="todo"
            type="com.twl.hi.foundation.api.response.bean.TodoBean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/view_item_latest_indicate"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:layout_marginStart="5dp"
            android:background="@drawable/work_bg_circle_c9c9ce"
            app:layout_constraintBottom_toBottomOf="@id/tv_latest_item_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_latest_item_title" />

        <TextView
            android:id="@+id/tv_latest_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:text="@{todo.platformName}"
            android:textColor="@color/color_1d2026"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="在线收集" />

        <TextView
            android:id="@+id/tv_latest_item_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:text="@{todo.title}"
            android:textColor="@color/color_A8ABB3"
            android:textSize="12sp"
            app:layout_constraintLeft_toLeftOf="@id/tv_latest_item_title"
            app:layout_constraintRight_toLeftOf="@id/tv_item_latest_action"
            app:layout_constraintTop_toBottomOf="@id/tv_latest_item_title"
            android:layout_marginEnd="10dp"
            android:ellipsize="end"
            android:lines="1"
            tools:text="北京员工出勤安排计划的建议方案…北京员工出勤安排计划的建议方案北京员工出勤安排计划的建议方案北京员工出勤安排计划的建议方案" />

        <TextView
            android:id="@+id/tv_item_latest_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/work_bg_radius_16_f2f4f7"
            android:onClick="@{() -> callback.clickTodo(todo)}"
            android:paddingStart="16dp"
            android:paddingTop="8dp"
            android:paddingEnd="16dp"
            android:paddingBottom="8dp"
            android:text="@{todo.button}"
            android:textColor="@color/color_5760FA"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/tv_latest_item_desc"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_latest_item_title"
            tools:text="去审批" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>