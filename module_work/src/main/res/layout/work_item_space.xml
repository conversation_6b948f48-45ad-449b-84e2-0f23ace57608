<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="com.twl.hi.work.model.SelectICloudFileBean" />

        <variable
            name="callback"
            type="com.twl.hi.work.callback.ICloudItemSelectCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:paddingStart="20dp"
        android:onClick="@{()->callback.onItemClick(bean)}"
        android:paddingTop="10dp"
        android:paddingEnd="20dp"
        android:paddingBottom="10dp">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            app:imageUrlWrap="@{bean.resourceBean.icon}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@tools:sample/avatars" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@{bean.resourceBean.name}"
            android:textColor="@color/color_15181D"
            android:textSize="16sp"
            tools:text="阿萨德伤筋动骨好的咖啡馆的风格"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_icon_gray_arrow_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>