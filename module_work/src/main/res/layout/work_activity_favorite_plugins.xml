<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="user"
            type="com.twl.hi.foundation.model.User" />

        <variable
            name="viewModel"
            type="com.twl.hi.work.viewmodel.FavoritePluginsViewModel" />

        <variable
            name="callback"
            type="com.twl.hi.work.callback.FavoritePluginsCallback" />
    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_F9F9FB"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            app:callback="@{callback}"
            app:left='@{" "}'
            app:right="@{@string/done}"
            app:rightEnabled="@{true}"
            app:title="@{@string/work_add_favorite_plugin}" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginRight="15dp"
                    android:layout_marginBottom="6dp"
                    android:background="@drawable/bg_corner_6_color_ffffff"
                    android:paddingLeft="15dp"
                    android:paddingTop="20dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="30dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="bottom"
                        android:text="@string/work_favorite_plugins"
                        android:textColor="@color/color_0D0D1A"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="70dp"
                        android:layout_marginTop="35dp"
                        android:background="@drawable/bg_corner_4_color_transparent_dash_stroke_b1b1b8"
                        android:gravity="center"
                        android:text="@string/work_favorite_plugin_empty"
                        android:textColor="@color/color_9999A3"
                        android:textSize="13sp"
                        app:visibleGone="@{!viewModel.showFavorite}" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_favorites"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="30dp"
                        android:overScrollMode="never"
                        android:visibility="visible"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="4"
                        app:visibleGone="@{viewModel.showFavorite}" />
                </FrameLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_plugins"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never"
                    app:visibleGone="@{viewModel.showAll}" />
            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

</layout>