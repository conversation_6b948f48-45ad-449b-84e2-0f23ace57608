<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="callback"
            type="com.twl.hi.work.callback.WorkCallback" />

        <variable
            name="item"
            type="com.twl.hi.foundation.model.workbench.ItemWorkStationFavorite" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        android:background="@drawable/bg_corner_6_color_ffffff"
        android:paddingLeft="15dp"
        android:paddingTop="15dp"
        android:orientation="vertical"
        android:paddingRight="15dp"
        android:paddingBottom="15dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingBottom="10dp"
            android:text="@{item.name}"
            android:textColor="@color/color_0D0D1A"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="@string/work_favorite_plugins" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            android:overScrollMode="never"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="4" />
    </LinearLayout>
</layout>
