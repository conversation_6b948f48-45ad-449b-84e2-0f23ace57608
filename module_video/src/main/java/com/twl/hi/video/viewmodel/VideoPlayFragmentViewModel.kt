package com.twl.hi.video.viewmodel

import android.app.Application
import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.techwolf.lib.tlog.TLog
import com.twl.hi.basic.api.request.VideoPlayInfoRequest
import com.twl.hi.basic.api.request.VideoPlayInfoResponse
import com.twl.hi.basic.model.SelectBottomBean
import com.twl.hi.basic.viewmodel.FileDownLoadViewModel
import com.twl.hi.foundation.api.base.BaseApiRequestCallback
import com.twl.hi.foundation.api.request.BaseFileDownloadCallback
import com.twl.hi.foundation.logic.ServiceManager
import com.twl.hi.video.R
import com.twl.http.ApiData
import com.twl.http.FileProgress
import com.twl.http.HttpExecutor
import com.twl.http.OkHttpClientFactory
import com.twl.http.error.ErrorReason
import com.twl.utils.StringUtils
import com.twl.utils.file.FileUtils
import hi.kernel.Constants
import lib.twl.common.util.ExecutorFactory
import lib.twl.common.util.ToastUtils
import lib.twl.common.views.imagesview.Video
import java.io.File
import kotlinx.coroutines.*
import kotlin.coroutines.resume

/**
 * <AUTHOR>
 * @date 2022/04/07 20:44
 */

private const val TAG = "VideoPlayFragmentViewModel"

class VideoPlayFragmentViewModel(application: Application) : FileDownLoadViewModel(application) {
    val mPlayUrl = MutableLiveData<String>()
    val mCoverUrl = MutableLiveData<String>()
    val mSaveFileLiveData = MutableLiveData<File>()
    var mLocalPath = ""
    var mVideoDownloadUrl = ""
    var mChatId = ""

    var urlSource: Int = 0
    var fileId: String? = ""
    var retryCount: Int = 0

    // 添加Video对象到ViewModel中
    var mVideo: Video? = null
        private set

    // 添加回退到下载模式的回调
    var onFallbackToDownloadMode: (() -> Unit)? = null

    // 添加需要开始下载的回调
    var onNeedStartDownload: (() -> Unit)? = null

    /**
     * 获取Video对象
     */
    fun getVideo(): Video? = mVideo

    // 保存到手机的待处理状态
    private var isPendingSaveToPhone: Boolean = false
    private var pendingSaveName: String? = null

    // 下载完成的协程信号
    private var downloadCompletionCallback: ((String) -> Unit)? = null

    /**
     * 初始化Video对象并设置相关参数
     */
    fun initializeVideo(video: Video) {
        TLog.debug(TAG, "initializeVideo called, video: $video")
        mVideo = video

        // 设置ViewModel的相关属性
        setCover(video.thumbnail?.url)
        mVideoDownloadUrl = video.url ?: ""
        mChatId = video.chatId
        urlSource = video.urlSource
        fileId = video.vurl

        // 检查本地文件是否存在
        video.localPath = ServiceManager.getInstance().fileService.fileExists(video.url, video.localPath, "")
        TLog.debug(TAG, "init localPath = ${video.localPath}")

        // 根据播放模式初始化
        if (!isDirectPlay()) {
            initAsOldVersionMode()
        } else {
            initAsDirectPlayMode()
        }
    }

    // 是否直接播放(点播)
    fun isDirectPlay(): Boolean {
        return urlSource == 1
    }

    /**
     * 传统模式初始化（下载后播放）
     */
    private fun initAsOldVersionMode() {
        mVideo?.let { video ->
            mLocalPath = video.localPath
            TLog.info(TAG, "传统模式初始化 - localPath: ${video.localPath}")

            // 检查文件是否已存在且完整
            val file = File(video.localPath)
            if (file.exists() && file.length() > 0) {
                // 检查文件大小是否完整
                if (file.length() >= video.size) {
                    setProgress(100)
                    preparePlayUrl(video.localPath)
                    TLog.info(TAG, "传统模式文件已存在且完整，直接使用: ${video.localPath}")
                } else {
                    // 文件不完整，删除并重新下载
                    file.delete()
                    TLog.info(TAG, "传统模式文件不完整，删除并重新下载")
                    onNeedStartDownload?.invoke()
                }
            } else {
                // 文件不存在，需要下载
                TLog.info(TAG, "传统模式文件不存在，需要下载")
                onNeedStartDownload?.invoke()
            }
        }
    }

    /**
     * 点播模式初始化
     */
    private fun initAsDirectPlayMode() {
        mVideo?.let { video ->
            mLocalPath = video.localPath
            TLog.info(TAG, "点播模式初始化 - expectedLocalPath: ${video.localPath}")

            // 检查文件是否已经下载过
            val file = File(video.localPath)
            if (file.exists() && file.length() > 0) {
                // 文件已存在，设置为已下载状态
                setProgress(100)
                preparePlayUrl(video.localPath)
                TLog.info(TAG, "点播文件已存在，直接使用: ${video.localPath}")
            } else {
                // 文件不存在，获取播放URL
                getDirectPlayUrlFormServer()
            }
        }
    }

    /**
     * 设置待保存到手机的状态
     */
    fun setPendingSaveToPhone(pending: Boolean, name: String?) {
        isPendingSaveToPhone = pending
        pendingSaveName = name
    }

    /**
     * 是否有待保存到手机的任务
     */
    fun isPendingSaveToPhone(): Boolean {
        return isPendingSaveToPhone
    }

    /**
     * 获取待保存的文件名
     */
    fun getPendingSaveName(): String? {
        return pendingSaveName
    }

    /**
     * 协程方式下载文件并保存
     */
    suspend fun downloadAndSaveToPhone(fileName: String?): Boolean {
        return try {
            TLog.info(TAG, "开始协程下载并保存: $fileName")

            // 设置保存标记
            setPendingSaveToPhone(true, fileName)

            // 使用 withTimeout 和 suspendCancellableCoroutine 等待下载完成
            val downloadedPath = withTimeout(30000) { // 30秒超时
                suspendCancellableCoroutine<String> { continuation ->
                    // 设置下载完成回调
                    downloadCompletionCallback = { localPath ->
                        TLog.info(TAG, "协程收到下载完成通知: $localPath")
                        if (!continuation.isCompleted) {
                            continuation.resume(localPath)
                        }
                    }

                    // 在主线程开始下载
                    GlobalScope.launch(Dispatchers.Main) {
                        downloadFile()
                    }

                    // 设置取消处理
                    continuation.invokeOnCancellation {
                        downloadCompletionCallback = null
                        TLog.error(TAG, "下载协程被取消")
                    }
                }
            }

            // 验证下载的文件
            if (!TextUtils.isEmpty(downloadedPath)) {
                val file = File(downloadedPath)
                if (file.exists() && file.length() > 0) {
                    // 在主线程执行保存操作
                    withContext(Dispatchers.Main) {
                        saveFile(file, fileName ?: file.name)
                        TLog.info(TAG, "保存操作已提交: ${file.absolutePath}")
                    }

                    // 清除保存标记
                    setPendingSaveToPhone(false, null)
                    true
                } else {
                    TLog.error(TAG, "下载完成但文件不存在: $downloadedPath")
                    setPendingSaveToPhone(false, null)
                    withContext(Dispatchers.Main) {
                        ToastUtils.failure("下载完成但文件异常")
                    }
                    false
                }
            } else {
                TLog.error(TAG, "下载完成但路径为空")
                setPendingSaveToPhone(false, null)
                withContext(Dispatchers.Main) {
                    ToastUtils.failure("下载失败")
                }
                false
            }
        } catch (e: TimeoutCancellationException) {
            TLog.error(TAG, "下载超时")
            setPendingSaveToPhone(false, null)
            downloadCompletionCallback = null
            withContext(Dispatchers.Main) {
                ToastUtils.failure("下载超时，请重试")
            }
            false
        } catch (e: Exception) {
            TLog.error(TAG, "下载保存过程出错", e)
            setPendingSaveToPhone(false, null)
            downloadCompletionCallback = null
            withContext(Dispatchers.Main) {
                ToastUtils.failure("下载失败: ${e.message}")
            }
            false
        }
    }

    private val mVideoOptionList = mutableListOf<SelectBottomBean>()

    override fun getLocalPath() = mLocalPath

    override fun getUrl() = mVideoDownloadUrl

    override fun downLoadDone(localPath: String) {
        TLog.info(TAG, "downLoadDone called - localPath: '$localPath', current mLocalPath: '$mLocalPath', isDirectPlay: ${isDirectPlay()}")

        // 更新本地路径
        if (TextUtils.isEmpty(mLocalPath)) {
            mLocalPath = localPath
        }

        // 准备播放URL但不自动播放
        preparePlayUrl(localPath)

        // 通知协程下载完成
        downloadCompletionCallback?.invoke(localPath)

        TLog.info(TAG, "Download completed successfully, final mLocalPath: '$mLocalPath'")
    }

    fun setCover(url: String?) {
        mCoverUrl.postValue(url.orEmpty())
    }

    fun startPlay() {
        // 手动触发播放时才设置播放URL
        val url = mPlayUrl.value ?: ""
        if (url.isBlank()) {
            mPlayUrl.postValue(localPath)
        } else {
            // URL已经准备好，可以直接播放
            TLog.info(TAG, "Play URL is ready: $url")
        }
    }

    /**
     * 准备播放URL但不自动播放
     */
    fun preparePlayUrl(url: String) {
        TLog.info(TAG, "Prepare play URL: $url")
        mPlayUrl.postValue(url)
    }

    fun saveFile(file: File, finalName: String) {
        ExecutorFactory.execLocalTask {
            val dirs =
                ServiceManager.getInstance().fileService.pictureDownloadPath
            if (!TextUtils.isEmpty(dirs)) {
                val copyFile = File(dirs + finalName)
                if (copyFile.exists()) {
                    mSaveFileLiveData.postValue(copyFile)
                } else {
                    if (FileUtils.copyFile(file, copyFile)) {
                        mSaveFileLiveData.postValue(copyFile)
                    }
                }
            }
        }
    }

    override fun downloadFile() {
        // 确定下载URL：点播模式使用原始下载URL，传统模式使用当前URL
        val downloadUrl = if (isDirectPlay()) {
            mVideoDownloadUrl // 使用原始的下载URL
        } else {
            url // 使用当前URL
        }

        TLog.info(TAG, "准备下载文件 - isDirectPlay: ${isDirectPlay()}, downloadUrl: $downloadUrl, currentUrl: $url")

        //没有下载记录
        val noRecord = OkHttpClientFactory.get().setProgressListener(downloadUrl)
        val progressLiveData: LiveData<FileProgress>? = OkHttpClientFactory.get().getProgress(downloadUrl)
        var progress = -1f
        if (progressLiveData != null) {
            val fileProgress = progressLiveData.value
            if (fileProgress != null) {
                progress = fileProgress.progress
            }
        }
        //没有下载记录 或者 不是下载失败记录 才重新下载
        if (noRecord || progress < 0) {
            setProgress(0)
            ServiceManager.getInstance().fileService.downLoadFile(downloadUrl, object : BaseFileDownloadCallback() {
                override fun onFail(url: String?, reason: ErrorReason?) {
                    TLog.error(TAG, "视频下载出错，%s", reason?.errReason)
                    // 下载失败时清除保存标记
                    if (isPendingSaveToPhone()) {
                        setPendingSaveToPhone(false, null)
                        ToastUtils.failure("视频下载失败，无法保存")
                    }
                }

                override fun onSuccess(url: String?, file: File?) {
                    val filePath = file?.path ?: ""
                    downLoadDone(filePath)
                }

            })
            // 使用 postValue 而不是 setValue，避免后台线程问题
            setStartDownload(true)
            TLog.info(TAG, "开始下载文件 url:$downloadUrl")
        }
    }

    fun initVideoOptionList() {
        mVideoOptionList.clear()
        mVideoOptionList.add(
            SelectBottomBean(
                Constants.SEND_FILE,
                resources.getString(R.string.forward),
                R.drawable.hd_icon_share
            )
        )
        mVideoOptionList.add(
            SelectBottomBean(
                Constants.SAVE_TO_PHONE,
                getString(R.string.save_to_local),
                R.drawable.hd_icon_save
            )
        )
        mVideoOptionList.add(
            SelectBottomBean(
                Constants.OPEN_WITH_OTHER_APP,
                resources.getString(R.string.file_open_with_other),
                R.drawable.hd_icon_open_outer
            )
        )
        if (StringUtils.isNotEmpty(mChatId)) {
            mVideoOptionList.add(
                SelectBottomBean(
                    Constants.CHECK_CONTENT,
                    resources.getString(R.string.check_content),
                    R.drawable.hd_icon_find
                )
            )
        }
    }

    fun getVideoOptionList(): List<SelectBottomBean> {
        return mVideoOptionList
    }


    fun getDirectPlayUrlFormServer() {
        val request = VideoPlayInfoRequest(object : BaseApiRequestCallback<VideoPlayInfoResponse>() {

            override fun onSuccess(data: ApiData<VideoPlayInfoResponse?>?) {
                val url = data?.resp?.fileUrl ?: ""
                if (url.isBlank()) {
                    onFailed(null)
                } else {
                    // 重置重试计数
                    retryCount = 0
                    setProgress(100)
                    // 准备播放URL但不自动播放
                    preparePlayUrl(url)
                }
            }

            override fun onFailed(reason: ErrorReason?) {
                if (retryCount < 5) {
                    retryCount++
                    TLog.info(TAG, "获取播放URL失败，重试第${retryCount}次")
                    getDirectPlayUrlFormServer()
                } else {
                    TLog.error(TAG, "获取播放URL失败达到5次，回退到下载模式")
                    ToastUtils.ss("切换到下载模式")
                    // 重置重试计数
                    retryCount = 0
                    // 回退到传统下载模式
                    fallbackToDownloadMode()
                }
            }
        })
        request.fileId = fileId
        HttpExecutor.execute(request)
    }

    /**
     * 回退到下载模式
     */
    private fun fallbackToDownloadMode() {
        TLog.info(TAG, "fallbackToDownloadMode: 从点播模式回退到下载模式")

        // 修改urlSource为传统模式
        urlSource = 0
        mVideo?.urlSource = 0

        // 调用传统模式初始化
        initAsOldVersionMode()

        // 通知Fragment更新UI模式
        onFallbackToDownloadMode?.invoke()
    }

}